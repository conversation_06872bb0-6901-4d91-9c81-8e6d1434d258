package com.example.gateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.common.entity.IPFlowRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * IP流量规则Mapper
 */
@Mapper
public interface IPFlowRuleMapper extends BaseMapper<IPFlowRule> {

	/**
	 * 获取所有启用的IP规则
	 */
	@Select("SELECT * FROM ip_flow_rule WHERE status = 1 AND deleted = 0 ORDER BY priority ASC, create_time ASC")
	List<IPFlowRule> selectEnabledRules();

	/**
	 * 根据租户ID获取启用的IP规则
	 */
	@Select("SELECT * FROM ip_flow_rule WHERE tenant_id = #{tenantId} AND status = 1 AND deleted = 0 ORDER BY priority ASC, create_time ASC")
	List<IPFlowRule> selectEnabledRulesByTenant(String tenantId);

	/**
	 * 根据规则类型获取启用的IP规则
	 */
	@Select("SELECT * FROM ip_flow_rule WHERE rule_type = #{ruleType} AND status = 1 AND deleted = 0 ORDER BY priority ASC, create_time ASC")
	List<IPFlowRule> selectEnabledRulesByType(String ruleType);

	/**
	 * 根据租户ID和规则类型获取启用的IP规则
	 */
	@Select("SELECT * FROM ip_flow_rule WHERE tenant_id = #{tenantId} AND rule_type = #{ruleType} AND status = 1 AND deleted = 0 ORDER BY priority ASC, create_time ASC")
	List<IPFlowRule> selectEnabledRulesByTenantAndType(String tenantId, String ruleType);
}
