<template>
	<div class="flow-control-management">
		<layout>
			<div class="flow-control-content">
				<div class="page-header">
					<h1>流量控制管理</h1>
					<p>管理租户、接口、IP地址三维度的流量控制规则</p>
				</div>

				<!-- 标签页 -->
				<el-tabs v-model="activeTab" @tab-click="handleTabClick">
					<!-- 租户管理 -->
					<el-tab-pane label="租户管理" name="tenant">
						<tenant-management-tab
							:tenants="tenants"
							:loading="loading"
							:pagination="pagination"
							@load-tenants="loadTenants"
							@add-tenant="showAddTenantDialog"
							@edit-tenant="editTenant"
							@delete-tenant="deleteTenant"
							@size-change="handleSizeChange"
							@current-change="handleCurrentChange"
						/>
					</el-tab-pane>

					<!-- 接口规则管理 -->
					<el-tab-pane label="接口规则" name="interface">
						<interface-rules-tab
							:rules="interfaceRules"
							:loading="loading"
							:pagination="pagination"
							@load-rules="loadInterfaceRules"
							@add-rule="showAddInterfaceRuleDialog"
							@edit-rule="editInterfaceRule"
							@delete-rule="deleteInterfaceRule"
							@size-change="handleSizeChange"
							@current-change="handleCurrentChange"
						/>
					</el-tab-pane>

					<!-- IP规则管理 -->
					<el-tab-pane label="IP规则" name="ip">
						<ip-rules-tab
							:rules="ipRules"
							:loading="loading"
							:pagination="pagination"
							@load-rules="loadIpRules"
							@add-rule="showAddIpRuleDialog"
							@edit-rule="editIpRule"
							@delete-rule="deleteIpRule"
							@size-change="handleSizeChange"
							@current-change="handleCurrentChange"
						/>
					</el-tab-pane>
				</el-tabs>

				<!-- 租户对话框 -->
				<tenant-dialog
					:visible="tenantDialogVisible"
					:form="tenantForm"
					:is-edit="isTenantEdit"
					:submitting="submitting"
					@close="closeTenantDialog"
					@submit="submitTenantForm"
				/>

				<!-- 接口规则对话框 -->
				<interface-rule-dialog
					:visible="interfaceRuleDialogVisible"
					:form="interfaceRuleForm"
					:is-edit="isInterfaceRuleEdit"
					:submitting="submitting"
					:tenants="tenants"
					@close="closeInterfaceRuleDialog"
					@submit="submitInterfaceRuleForm"
				/>

				<!-- IP规则对话框 -->
				<ip-rule-dialog
					:visible="ipRuleDialogVisible"
					:form="ipRuleForm"
					:is-edit="isIpRuleEdit"
					:submitting="submitting"
					@close="closeIpRuleDialog"
					@submit="submitIpRuleForm"
				/>
			</div>
		</layout>
	</div>
</template>

<script>
import Layout from '../components/Layout.vue';
import TenantManagementTab from '../components/TenantManagementTab.vue';
import InterfaceRulesTab from '../components/InterfaceRulesTab.vue';
import IpRulesTab from '../components/IpRulesTab.vue';
import TenantDialog from '../components/TenantDialog.vue';
import InterfaceRuleDialog from '../components/InterfaceRuleDialog.vue';
import IpRuleDialog from '../components/IpRuleDialog.vue';

export default {
	name: 'FlowControlManagement',
	components: {
		Layout,
		TenantManagementTab,
		InterfaceRulesTab,
		IpRulesTab,
		TenantDialog,
		InterfaceRuleDialog,
		IpRuleDialog,
	},
	data() {
		return {
			activeTab: 'tenant',
			loading: false,
			submitting: false,

			// 分页
			pagination: {
				current: 1,
				size: 20,
				total: 0,
			},

			// 租户数据
			tenants: [],
			tenantDialogVisible: false,
			isTenantEdit: false,
			tenantForm: {
				tenantId: '',
				tenantName: '',
				description: '',
				contactPerson: '',
				contactEmail: '',
				contactPhone: '',
				status: 1,
			},

			// 接口规则数据
			interfaceRules: [],
			interfaceRuleDialogVisible: false,
			isInterfaceRuleEdit: false,
			interfaceRuleForm: {
				ruleName: '',
				tenantId: '',
				resourceName: '',
				limitMode: 0, // 0-QPS，1-并发数
				threshold: null,
				behavior: 0, // 0-快速失败，1-预热，2-排队等待
				warmUpPeriod: null,
				queueTimeout: null,
				status: 1,
			},

			// IP规则数据
			ipRules: [],
			ipRuleDialogVisible: false,
			isIpRuleEdit: false,
			ipRuleForm: {
				ruleName: '',
				ipValue: '',
				listType: 'LIMIT', // LIMIT-限制，ALLOW-允许
				limitMode: 0, // 0-QPS，1-并发数
				limitCount: null,
				status: 1,
			},
		};
	},

	mounted() {
		this.loadCurrentTabData();
	},

	methods: {
		// 处理标签页切换
		handleTabClick(tab) {
			this.activeTab = tab.name;
			this.resetPagination();
			this.loadCurrentTabData();
		},

		// 加载当前标签页数据
		loadCurrentTabData() {
			switch (this.activeTab) {
				case 'tenant':
					this.loadTenants();
					break;
				case 'interface':
					this.loadInterfaceRules();
					break;
				case 'ip':
					this.loadIpRules();
					break;
			}
		},

		// 重置分页
		resetPagination() {
			this.pagination = {
				current: 1,
				size: 20,
				total: 0,
			};
		},

		// 租户管理方法
		async loadTenants() {
			this.loading = true;
			try {
				const response = await this.$api.tenants.getList({
					current: this.pagination.current,
					size: this.pagination.size,
				});

				if (response.data.success) {
					this.tenants = response.data.data.records || [];
					this.pagination.total = response.data.data.total || 0;
				} else {
					this.$message.error('获取租户列表失败');
				}
			} catch (error) {
				console.error('Error loading tenants:', error);
				this.$message.error('获取租户列表失败');
			} finally {
				this.loading = false;
			}
		},

		showAddTenantDialog() {
			this.isTenantEdit = false;
			this.tenantDialogVisible = true;
		},

		editTenant(tenant) {
			this.isTenantEdit = true;
			this.tenantForm = { ...tenant };
			this.tenantDialogVisible = true;
		},

		async deleteTenant(tenant) {
			try {
				await this.$confirm(
					`确定要删除租户 "${tenant.tenantName}" 吗？`,
					'确认删除',
					{
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning',
					}
				);

				const response = await this.$api.tenants.delete(tenant.id);
				if (response.data.success) {
					this.$message.success('删除成功');
					this.loadTenants();
				} else {
					this.$message.error('删除失败');
				}
			} catch (error) {
				if (error !== 'cancel') {
					console.error('Error deleting tenant:', error);
					this.$message.error('删除失败');
				}
			}
		},

		closeTenantDialog() {
			this.tenantDialogVisible = false;
			this.resetTenantForm();
		},

		async submitTenantForm(formData) {
			this.submitting = true;
			try {
				let response;
				if (this.isTenantEdit) {
					response = await this.$api.tenants.update(formData.id, formData);
				} else {
					response = await this.$api.tenants.create(formData);
				}

				if (response.data.success) {
					this.$message.success(this.isTenantEdit ? '更新成功' : '创建成功');
					this.tenantDialogVisible = false;
					this.loadTenants();
				} else {
					this.$message.error(response.data.message || '操作失败');
				}
			} catch (error) {
				console.error('Error saving tenant:', error);
				this.$message.error('操作失败');
			} finally {
				this.submitting = false;
			}
		},

		resetTenantForm() {
			this.tenantForm = {
				tenantId: '',
				tenantName: '',
				description: '',
				contactPerson: '',
				contactEmail: '',
				contactPhone: '',
				status: 1,
			};
		},

		// 接口规则管理方法
		async loadInterfaceRules() {
			this.loading = true;
			try {
				const response = await this.$api.tenantFlowRules.getList({
					current: this.pagination.current,
					size: this.pagination.size,
				});

				if (response.data.success) {
					this.interfaceRules = response.data.data.records || [];
					this.pagination.total = response.data.data.total || 0;
				} else {
					this.$message.error('获取接口规则失败');
				}
			} catch (error) {
				console.error('Error loading interface rules:', error);
				this.$message.error('获取接口规则失败');
			} finally {
				this.loading = false;
			}
		},

		showAddInterfaceRuleDialog() {
			this.isInterfaceRuleEdit = false;
			this.interfaceRuleDialogVisible = true;
		},

		editInterfaceRule(rule) {
			this.isInterfaceRuleEdit = true;
			this.interfaceRuleForm = { ...rule };
			this.interfaceRuleDialogVisible = true;
		},

		async deleteInterfaceRule(rule) {
			try {
				await this.$confirm(
					`确定要删除规则 "${rule.ruleName}" 吗？`,
					'确认删除',
					{
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning',
					}
				);

				const response = await this.$api.tenantFlowRules.delete(rule.id);
				if (response.data.success) {
					this.$message.success('删除成功');
					this.loadInterfaceRules();
				} else {
					this.$message.error('删除失败');
				}
			} catch (error) {
				if (error !== 'cancel') {
					console.error('Error deleting interface rule:', error);
					this.$message.error('删除失败');
				}
			}
		},

		closeInterfaceRuleDialog() {
			this.interfaceRuleDialogVisible = false;
			this.resetInterfaceRuleForm();
		},

		async submitInterfaceRuleForm(formData) {
			this.submitting = true;
			try {
				let response;
				if (this.isInterfaceRuleEdit) {
					response = await this.$api.tenantFlowRules.update(formData.id, formData);
				} else {
					response = await this.$api.tenantFlowRules.create(formData);
				}

				if (response.data.success) {
					this.$message.success(this.isInterfaceRuleEdit ? '更新成功' : '创建成功');
					this.interfaceRuleDialogVisible = false;
					this.loadInterfaceRules();
				} else {
					this.$message.error(response.data.message || '操作失败');
				}
			} catch (error) {
				console.error('Error saving interface rule:', error);
				this.$message.error('操作失败');
			} finally {
				this.submitting = false;
			}
		},

		resetInterfaceRuleForm() {
			this.interfaceRuleForm = {
				ruleName: '',
				tenantId: '',
				resourceName: '',
				limitMode: 0,
				threshold: null,
				behavior: 0,
				warmUpPeriod: null,
				queueTimeout: null,
				status: 1,
			};
		},

		// IP规则管理方法
		async loadIpRules() {
			this.loading = true;
			try {
				const response = await this.$api.ipFlowRules.getList({
					current: this.pagination.current,
					size: this.pagination.size,
				});

				if (response.data.success) {
					this.ipRules = response.data.data.records || [];
					this.pagination.total = response.data.data.total || 0;
				} else {
					this.$message.error('获取IP规则失败');
				}
			} catch (error) {
				console.error('Error loading IP rules:', error);
				this.$message.error('获取IP规则失败');
			} finally {
				this.loading = false;
			}
		},

		showAddIpRuleDialog() {
			this.isIpRuleEdit = false;
			this.ipRuleDialogVisible = true;
		},

		editIpRule(rule) {
			this.isIpRuleEdit = true;
			this.ipRuleForm = { ...rule };
			this.ipRuleDialogVisible = true;
		},

		async deleteIpRule(rule) {
			try {
				await this.$confirm(
					`确定要删除IP规则 "${rule.ruleName}" 吗？`,
					'确认删除',
					{
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning',
					}
				);

				const response = await this.$api.ipFlowRules.delete(rule.id);
				if (response.data.success) {
					this.$message.success('删除成功');
					this.loadIpRules();
				} else {
					this.$message.error('删除失败');
				}
			} catch (error) {
				if (error !== 'cancel') {
					console.error('Error deleting IP rule:', error);
					this.$message.error('删除失败');
				}
			}
		},

		closeIpRuleDialog() {
			this.ipRuleDialogVisible = false;
			this.resetIpRuleForm();
		},

		async submitIpRuleForm(formData) {
			this.submitting = true;
			try {
				let response;
				if (this.isIpRuleEdit) {
					response = await this.$api.ipFlowRules.update(formData.id, formData);
				} else {
					response = await this.$api.ipFlowRules.create(formData);}],"thought":"更新FlowControlManagement.vue文件中的API调用"}}}
				}

				if (response.data.success) {
					this.$message.success(this.isIpRuleEdit ? '更新成功' : '创建成功');
					this.ipRuleDialogVisible = false;
					this.loadIpRules();
				} else {
					this.$message.error(response.data.message || '操作失败');
				}
			} catch (error) {
				console.error('Error saving IP rule:', error);
				this.$message.error('操作失败');
			} finally {
				this.submitting = false;
			}
		},

		resetIpRuleForm() {
			this.ipRuleForm = {
				ruleName: '',
				ipValue: '',
				listType: 'LIMIT',
				limitMode: 0,
				limitCount: null,
				status: 1,
			};
		},

		// 分页处理
		handleSizeChange(size) {
			this.pagination.size = size;
			this.pagination.current = 1;
			this.loadCurrentTabData();
		},

		handleCurrentChange(current) {
			this.pagination.current = current;
			this.loadCurrentTabData();
		},
	},
};
</script>

<style scoped>
.flow-control-content {
	padding: 20px;
}

.page-header {
	margin-bottom: 20px;
}

.page-header h1 {
	margin: 0 0 8px 0;
	font-size: 24px;
	color: #303133;
}

.page-header p {
	margin: 0;
	color: #909399;
	font-size: 14px;
}
</style>