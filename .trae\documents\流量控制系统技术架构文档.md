# 流量控制系统技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[用户浏览器] --> B[Vue前端应用]
    B --> C[Spring Cloud Gateway]
    C --> D[后台管理服务]
    C --> E[业务服务]
    D --> F[MySQL数据库]
    D --> G[Redis缓存]
    C --> H[Sentinel控制台]
    
    subgraph "前端层"
        B
    end
    
    subgraph "网关层"
        C
        H
    end
    
    subgraph "服务层"
        D
        E
    end
    
    subgraph "数据层"
        F
        G
    end
```

## 2. 技术描述

- **前端**: Vue@2 + Element UI@2.15 + Axios + Vue Router
- **网关**: Spring Cloud Gateway@3.1 + Sentinel@1.8
- **后端**: Spring Boot@2.7 + MyBatis Plus@3.5 + Spring Security
- **数据库**: MySQL@8.0 + Redis@6.0
- **监控**: Sentinel Dashboard + Spring Boot Actuator

## 3. 路由定义

| 路由 | 用途 |
|------|------|
| /login | 登录页面，用户身份验证 |
| /dashboard | 仪表板页面，系统概览和统计信息 |
| /tenant-rules | 租户限流规则管理页面 |
| /interface-rules | 接口限流规则管理页面 |
| /ip-rules | IP限流规则管理页面 |
| /ip-blacklist | IP黑白名单管理页面 |
| /monitoring | 系统监控页面，实时数据展示 |

## 4. API定义

### 4.1 租户限流规则API

**获取租户限流规则列表**
```
GET /api/tenant-rules
```

请求参数:
| 参数名 | 参数类型 | 是否必需 | 描述 |
|--------|----------|----------|------|
| page | int | false | 页码，默认1 |
| size | int | false | 每页大小，默认10 |
| tenantId | string | false | 租户ID筛选 |

响应:
| 参数名 | 参数类型 | 描述 |
|--------|----------|------|
| code | int | 响应状态码 |
| data | object | 分页数据 |
| message | string | 响应消息 |

**创建租户限流规则**
```
POST /api/tenant-rules
```

请求体:
```json
{
  "tenantId": "tenant001",
  "qps": 1000,
  "burstCapacity": 1200,
  "flowBehavior": 0,
  "warmUpPeriodSec": 10,
  "maxQueueingTimeMs": 500
}
```

### 4.2 接口限流规则API

**获取接口限流规则列表**
```
GET /api/interface-rules
```

**创建接口限流规则**
```
POST /api/interface-rules
```

请求体:
```json
{
  "tenantId": "tenant001",
  "resource": "/api/user/info",
  "limitMode": 1,
  "count": 100,
  "strategy": 0,
  "controlBehavior": 0
}
```

### 4.3 IP限流规则API

**获取IP限流规则列表**
```
GET /api/ip-rules
```

**创建IP限流规则**
```
POST /api/ip-rules
```

请求体:
```json
{
  "ipAddress": "*************",
  "ruleType": 1,
  "qps": 50,
  "burstCapacity": 60,
  "timeWindowSec": 1
}
```

### 4.4 IP黑白名单API

**获取IP黑名单列表**
```
GET /api/ip-blacklist
```

**添加IP到黑名单**
```
POST /api/ip-blacklist
```

**获取IP白名单列表**
```
GET /api/ip-whitelist
```

**添加IP到白名单**
```
POST /api/ip-whitelist
```

## 5. 服务架构图

```mermaid
graph TD
    A[Controller层] --> B[Service层]
    B --> C[Repository层]
    C --> D[(MySQL数据库)]
    B --> E[(Redis缓存)]
    B --> F[Sentinel规则推送]
    
    subgraph "Service层组件"
        G[TenantRuleService]
        H[InterfaceRuleService]
        I[IPRuleService]
        J[BlacklistService]
        K[MonitoringService]
    end
    
    B --> G
    B --> H
    B --> I
    B --> J
    B --> K
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    TENANT_FLOW_RULE ||--o{ FLOW_RULE : "belongs to"
    TENANT_CONFIG ||--o{ TENANT_FLOW_RULE : "has"
    TENANT_CONFIG ||--o{ FLOW_RULE : "has"
    IP_FLOW_RULE }|--|| ACCESS_LOG : "generates"
    IP_BLACKLIST }|--|| ACCESS_LOG : "blocks"
    IP_WHITELIST }|--|| ACCESS_LOG : "allows"
    
    TENANT_CONFIG {
        string id PK
        string tenant_name
        int status
        datetime create_time
        datetime update_time
    }
    
    TENANT_FLOW_RULE {
        string id PK
        string tenant_id FK
        int qps
        int burst_capacity
        int flow_behavior
        int warm_up_period_sec
        int max_queueing_time_ms
        int enabled
        datetime create_time
        datetime update_time
    }
    
    FLOW_RULE {
        string id PK
        string tenant_id FK
        string resource
        int limit_mode
        double count
        int strategy
        int control_behavior
        int warm_up_period_sec
        int max_queueing_time_ms
        int enabled
        datetime create_time
        datetime update_time
    }
    
    IP_FLOW_RULE {
        string id PK
        string ip_address
        int rule_type
        int qps
        int burst_capacity
        int time_window_sec
        int flow_behavior
        int enabled
        datetime create_time
        datetime update_time
    }
    
    IP_BLACKLIST {
        string id PK
        string ip_address
        int ip_type
        string ip_range_start
        string ip_range_end
        int priority
        int enabled
        datetime create_time
        datetime update_time
    }
    
    IP_WHITELIST {
        string id PK
        string ip_address
        int ip_type
        string ip_range_start
        string ip_range_end
        int priority
        datetime effective_time
        datetime expire_time
        int enabled
        datetime create_time
        datetime update_time
    }
    
    ACCESS_LOG {
        string id PK
        string tenant_id
        string ip_address
        string resource
        string method
        int status_code
        long response_time
        int is_limited
        datetime access_time
    }
```

### 6.2 数据定义语言

**租户配置表 (tenant_config)**
```sql
-- 创建租户配置表
CREATE TABLE tenant_config (
    id VARCHAR(32) PRIMARY KEY COMMENT '租户ID',
    tenant_name VARCHAR(100) NOT NULL COMMENT '租户名称',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户配置表';

-- 创建索引
CREATE INDEX idx_tenant_config_name ON tenant_config(tenant_name);
CREATE INDEX idx_tenant_config_status ON tenant_config(status);

-- 初始化数据
INSERT INTO tenant_config (id, tenant_name, status, create_by) VALUES
('tenant001', '测试租户1', 1, 'admin'),
('tenant002', '测试租户2', 1, 'admin'),
('tenant003', '演示租户', 1, 'admin');
```

**租户限流规则表 (tenant_flow_rule)**
```sql
-- 创建租户限流规则表
CREATE TABLE tenant_flow_rule (
    id VARCHAR(32) PRIMARY KEY COMMENT '规则ID',
    tenant_id VARCHAR(32) NOT NULL COMMENT '租户ID',
    qps INT DEFAULT 1000 COMMENT 'QPS限制',
    burst_capacity INT DEFAULT 0 COMMENT '突发容量',
    flow_behavior TINYINT DEFAULT 0 COMMENT '流控行为：0-快速失败，1-预热，2-排队等待',
    warm_up_period_sec INT DEFAULT 10 COMMENT '预热时长（秒）',
    max_queueing_time_ms INT DEFAULT 500 COMMENT '最大排队时间（毫秒）',
    enabled TINYINT DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户限流规则表';

-- 创建索引
CREATE UNIQUE INDEX uk_tenant_flow_rule_tenant ON tenant_flow_rule(tenant_id, deleted);
CREATE INDEX idx_tenant_flow_rule_enabled ON tenant_flow_rule(enabled);

-- 初始化数据
INSERT INTO tenant_flow_rule (id, tenant_id, qps, burst_capacity, flow_behavior, create_by) VALUES
('rule001', 'tenant001', 1000, 1200, 0, 'admin'),
('rule002', 'tenant002', 500, 600, 1, 'admin'),
('rule003', 'tenant003', 2000, 2500, 2, 'admin');
```

**接口限流规则表 (flow_rule)**
```sql
-- 创建接口限流规则表
CREATE TABLE flow_rule (
    id VARCHAR(32) PRIMARY KEY COMMENT '规则ID',
    tenant_id VARCHAR(32) NOT NULL COMMENT '租户ID',
    resource VARCHAR(200) NOT NULL COMMENT '资源名称',
    limit_mode TINYINT DEFAULT 1 COMMENT '限流模式：1-QPS，2-并发数',
    count DOUBLE DEFAULT 100 COMMENT '限流阈值',
    strategy TINYINT DEFAULT 0 COMMENT '流控策略：0-直接，1-关联，2-链路',
    control_behavior TINYINT DEFAULT 0 COMMENT '流控行为：0-快速失败，1-预热，2-排队等待',
    warm_up_period_sec INT DEFAULT 10 COMMENT '预热时长（秒）',
    max_queueing_time_ms INT DEFAULT 500 COMMENT '最大排队时间（毫秒）',
    enabled TINYINT DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接口限流规则表';

-- 创建索引
CREATE UNIQUE INDEX uk_flow_rule_tenant_resource ON flow_rule(tenant_id, resource, deleted);
CREATE INDEX idx_flow_rule_enabled ON flow_rule(enabled);
CREATE INDEX idx_flow_rule_resource ON flow_rule(resource);

-- 初始化数据
INSERT INTO flow_rule (id, tenant_id, resource, limit_mode, count, strategy, control_behavior, create_by) VALUES
('fr001', 'tenant001', '/api/user/info', 1, 100, 0, 0, 'admin'),
('fr002', 'tenant001', '/api/user/list', 1, 50, 0, 1, 'admin'),
('fr003', 'tenant002', '/api/order/create', 1, 20, 0, 0, 'admin'),
('fr004', 'tenant002', '/api/order/query', 1, 80, 0, 2, 'admin');
```

**IP限流规则表 (ip_flow_rule)**
```sql
-- 创建IP限流规则表
CREATE TABLE ip_flow_rule (
    id VARCHAR(32) PRIMARY KEY COMMENT '规则ID',
    ip_address VARCHAR(50) NOT NULL COMMENT 'IP地址',
    rule_type TINYINT DEFAULT 1 COMMENT 'IP类型：1-单个IP，2-IP段，3-CIDR',
    ip_range_start VARCHAR(50) COMMENT 'IP段起始地址',
    ip_range_end VARCHAR(50) COMMENT 'IP段结束地址',
    qps INT DEFAULT 100 COMMENT 'QPS限制',
    burst_capacity INT DEFAULT 0 COMMENT '突发容量',
    time_window_sec INT DEFAULT 1 COMMENT '时间窗口（秒）',
    flow_behavior TINYINT DEFAULT 0 COMMENT '流控行为：0-快速失败，1-预热，2-排队等待',
    warm_up_period_sec INT DEFAULT 10 COMMENT '预热时长（秒）',
    max_queueing_time_ms INT DEFAULT 500 COMMENT '最大排队时间（毫秒）',
    enabled TINYINT DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP限流规则表';

-- 创建索引
CREATE INDEX idx_ip_flow_rule_ip ON ip_flow_rule(ip_address);
CREATE INDEX idx_ip_flow_rule_enabled ON ip_flow_rule(enabled);
CREATE INDEX idx_ip_flow_rule_type ON ip_flow_rule(rule_type);

-- 初始化数据
INSERT INTO ip_flow_rule (id, ip_address, rule_type, qps, burst_capacity, flow_behavior, create_by) VALUES
('ipr001', '*************', 1, 50, 60, 0, 'admin'),
('ipr002', '***********/24', 3, 1000, 1200, 1, 'admin'),
('ipr003', '********', 1, 200, 250, 2, 'admin');
```

**IP黑名单表 (ip_blacklist)**
```sql
-- 创建IP黑名单表
CREATE TABLE ip_blacklist (
    id VARCHAR(32) PRIMARY KEY COMMENT '记录ID',
    ip_address VARCHAR(50) NOT NULL COMMENT 'IP地址',
    ip_type TINYINT DEFAULT 1 COMMENT 'IP类型：1-单个IP，2-IP段，3-CIDR',
    ip_range_start VARCHAR(50) COMMENT 'IP段起始地址',
    ip_range_end VARCHAR(50) COMMENT 'IP段结束地址',
    priority INT DEFAULT 1 COMMENT '优先级，数字越大优先级越高',
    reason VARCHAR(500) COMMENT '加入黑名单原因',
    enabled TINYINT DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP黑名单表';

-- 创建索引
CREATE INDEX idx_ip_blacklist_ip ON ip_blacklist(ip_address);
CREATE INDEX idx_ip_blacklist_enabled ON ip_blacklist(enabled);
CREATE INDEX idx_ip_blacklist_priority ON ip_blacklist(priority DESC);

-- 初始化数据
INSERT INTO ip_blacklist (id, ip_address, ip_type, priority, reason, create_by) VALUES
('bl001', '*************', 1, 5, '恶意攻击IP', 'admin'),
('bl002', '10.0.0.0/8', 3, 3, '内网IP段限制', 'admin');
```

**IP白名单表 (ip_whitelist)**
```sql
-- 创建IP白名单表
CREATE TABLE ip_whitelist (
    id VARCHAR(32) PRIMARY KEY COMMENT '记录ID',
    ip_address VARCHAR(50) NOT NULL COMMENT 'IP地址',
    ip_type TINYINT DEFAULT 1 COMMENT 'IP类型：1-单个IP，2-IP段，3-CIDR',
    ip_range_start VARCHAR(50) COMMENT 'IP段起始地址',
    ip_range_end VARCHAR(50) COMMENT 'IP段结束地址',
    priority INT DEFAULT 1 COMMENT '优先级，数字越大优先级越高',
    effective_time DATETIME COMMENT '生效时间',
    expire_time DATETIME COMMENT '过期时间',
    reason VARCHAR(500) COMMENT '加入白名单原因',
    enabled TINYINT DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP白名单表';

-- 创建索引
CREATE INDEX idx_ip_whitelist_ip ON ip_whitelist(ip_address);
CREATE INDEX idx_ip_whitelist_enabled ON ip_whitelist(enabled);
CREATE INDEX idx_ip_whitelist_priority ON ip_whitelist(priority DESC);
CREATE INDEX idx_ip_whitelist_time ON ip_whitelist(effective_time, expire_time);

-- 初始化数据
INSERT INTO ip_whitelist (id, ip_address, ip_type, priority, effective_time, reason, create_by) VALUES
('wl001', '127.0.0.1', 1, 10, NOW(), '本地测试IP', 'admin'),
('wl002', '***********', 1, 8, NOW(), '管理员IP', 'admin'),
('wl003', '**********/16', 3, 5, NOW(), '办公网段', 'admin');
```

**访问日志表 (access_log)**
```sql
-- 创建访问日志表
CREATE TABLE access_log (
    id VARCHAR(32) PRIMARY KEY COMMENT '日志ID',
    tenant_id VARCHAR(32) COMMENT '租户ID',
    ip_address VARCHAR(50) NOT NULL COMMENT '访问IP',
    resource VARCHAR(200) COMMENT '访问资源',
    method VARCHAR(10) COMMENT 'HTTP方法',
    user_agent VARCHAR(500) COMMENT '用户代理',
    status_code INT COMMENT '响应状态码',
    response_time BIGINT COMMENT '响应时间（毫秒）',
    is_limited TINYINT DEFAULT 0 COMMENT '是否被限流：0-否，1-是',
    limit_type TINYINT COMMENT '限流类型：1-租户限流，2-接口限流，3-IP限流',
    access_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问日志表';

-- 创建索引
CREATE INDEX idx_access_log_tenant ON access_log(tenant_id);
CREATE INDEX idx_access_log_ip ON access_log(ip_address);
CREATE INDEX idx_access_log_time ON access_log(access_time DESC);
CREATE INDEX idx_access_log_limited ON access_log(is_limited, limit_type);
```

## 7. 部署配置

### 7.1 网关配置

**application.yml**
```yaml
server:
  port: 8080

spring:
  application:
    name: flow-control-gateway
  cloud:
    gateway:
      routes:
        - id: admin-service
          uri: http://localhost:8081
          predicates:
            - Path=/api/admin/**
          filters:
            - StripPrefix=2
        - id: test-service
          uri: http://localhost:8082
          predicates:
            - Path=/api/test/**
          filters:
            - StripPrefix=2
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"

# Sentinel配置
sentinel:
  transport:
    dashboard: localhost:8080
  datasource:
    flow:
      nacos:
        server-addr: localhost:8848
        dataId: flow-rules
        groupId: SENTINEL_GROUP
        rule-type: flow
```

### 7.2 后台管理服务配置

**application.yml**
```yaml
server:
  port: 8081

spring:
  application:
    name: flow-control-admin
  datasource:
    url: ******************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: localhost
    port: 6379
    database: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```