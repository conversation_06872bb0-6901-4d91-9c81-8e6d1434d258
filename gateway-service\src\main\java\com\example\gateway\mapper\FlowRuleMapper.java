package com.example.gateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.common.entity.FlowRuleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 流量规则Mapper
 */
@Mapper
public interface FlowRuleMapper extends BaseMapper<FlowRuleEntity> {

	/**
	 * 获取所有启用的流量规则
	 */
	@Select("SELECT * FROM flow_rule WHERE status = 1 AND deleted = 0 ORDER BY priority ASC, create_time ASC")
	List<FlowRuleEntity> selectEnabledRules();

	/**
	 * 根据租户ID获取启用的流量规则
	 */
	@Select("SELECT * FROM flow_rule WHERE tenant_id = #{tenantId} AND status = 1 AND deleted = 0 ORDER BY priority ASC, create_time ASC")
	List<FlowRuleEntity> selectEnabledRulesByTenant(String tenantId);

	/**
	 * 根据资源名称获取启用的流量规则
	 */
	@Select("SELECT * FROM flow_rule WHERE resource_name = #{resourceName} AND status = 1 AND deleted = 0 ORDER BY priority ASC, create_time ASC")
	List<FlowRuleEntity> selectEnabledRulesByResource(String resourceName);

	/**
	 * 根据租户ID和资源名称获取启用的流量规则
	 */
	@Select("SELECT * FROM flow_rule WHERE tenant_id = #{tenantId} AND resource_name = #{resourceName} AND status = 1 AND deleted = 0 ORDER BY priority ASC, create_time ASC")
	List<FlowRuleEntity> selectEnabledRulesByTenantAndResource(String tenantId, String resourceName);
}
