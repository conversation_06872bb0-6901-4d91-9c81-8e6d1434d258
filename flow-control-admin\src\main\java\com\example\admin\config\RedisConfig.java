package com.example.admin.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

/**
 * Redis配置类 配置Redis连接、序列化、缓存管理等
 */
@Configuration
@EnableCaching
@ConditionalOnProperty(name = "spring.redis.enabled", havingValue = "true", matchIfMissing = true)
public class RedisConfig {

	private static final Logger log = LoggerFactory.getLogger(RedisConfig.class);

	/**
	 * 配置RedisTemplate 设置key和value的序列化方式
	 */
	@Bean
	public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
		RedisTemplate<String, Object> template = new RedisTemplate<>();
		template.setConnectionFactory(connectionFactory);

		// 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.registerModule(new JavaTimeModule());
		objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
		objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
		Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(
				objectMapper, Object.class);

		// 使用StringRedisSerializer来序列化和反序列化redis的key值
		StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

		// 设置key和hash key的序列化方式
		template.setKeySerializer(stringRedisSerializer);
		template.setHashKeySerializer(stringRedisSerializer);

		// 设置value和hash value的序列化方式
		template.setValueSerializer(jackson2JsonRedisSerializer);
		template.setHashValueSerializer(jackson2JsonRedisSerializer);

		// 设置默认序列化方式
		template.setDefaultSerializer(jackson2JsonRedisSerializer);
		template.afterPropertiesSet();

		log.info("RedisTemplate configuration completed");
		return template;
	}

	/**
	 * 配置缓存管理器 设置缓存的默认过期时间和序列化方式
	 */
	@Bean
	public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
		// 配置序列化
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.registerModule(new JavaTimeModule());
		objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
		objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
		Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(
				objectMapper, Object.class);

		// 配置缓存
		RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
				// 设置缓存的默认过期时间，也是使用Duration设置
				.entryTtl(Duration.ofMinutes(30))
				// 设置key为string序列化
				.serializeKeysWith(
						RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
				// 设置value为json序列化
				.serializeValuesWith(
						RedisSerializationContext.SerializationPair.fromSerializer(jackson2JsonRedisSerializer))
				// 不缓存空值
				.disableCachingNullValues();

		RedisCacheManager cacheManager = RedisCacheManager.builder(connectionFactory).cacheDefaults(config)
				// 配置同步修改或删除，put/evict
				.transactionAware().build();

		log.info("Redis cache manager configuration completed");
		return cacheManager;
	}

	/**
	 * 配置专用于流控规则的RedisTemplate 使用较短的过期时间，适合频繁更新的数据
	 */
	@Bean("flowRuleRedisTemplate")
	public RedisTemplate<String, Object> flowRuleRedisTemplate(RedisConnectionFactory connectionFactory) {
		RedisTemplate<String, Object> template = new RedisTemplate<>();
		template.setConnectionFactory(connectionFactory);

		// JSON序列化配置
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.registerModule(new JavaTimeModule());
		objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
		objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
		Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(
				objectMapper, Object.class);

		StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

		template.setKeySerializer(stringRedisSerializer);
		template.setHashKeySerializer(stringRedisSerializer);
		template.setValueSerializer(jackson2JsonRedisSerializer);
		template.setHashValueSerializer(jackson2JsonRedisSerializer);
		template.setDefaultSerializer(jackson2JsonRedisSerializer);
		template.afterPropertiesSet();

		log.info("Flow control rules RedisTemplate configuration completed");
		return template;
	}

	/**
	 * 配置专用于监控数据的RedisTemplate 使用较长的过期时间，适合统计数据
	 */
	@Bean("monitorRedisTemplate")
	public RedisTemplate<String, Object> monitorRedisTemplate(RedisConnectionFactory connectionFactory) {
		RedisTemplate<String, Object> template = new RedisTemplate<>();
		template.setConnectionFactory(connectionFactory);

		// JSON序列化配置
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.registerModule(new JavaTimeModule());
		objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
		objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
		Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(
				objectMapper, Object.class);

		StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

		template.setKeySerializer(stringRedisSerializer);
		template.setHashKeySerializer(stringRedisSerializer);
		template.setValueSerializer(jackson2JsonRedisSerializer);
		template.setHashValueSerializer(jackson2JsonRedisSerializer);
		template.setDefaultSerializer(jackson2JsonRedisSerializer);
		template.afterPropertiesSet();

		log.info("Monitoring data RedisTemplate configuration completed");
		return template;
	}

	/**
	 * 配置专用于系统配置的RedisTemplate 使用更长的过期时间，适合配置数据
	 */
	@Bean("configRedisTemplate")
	public RedisTemplate<String, Object> configRedisTemplate(RedisConnectionFactory connectionFactory) {
		RedisTemplate<String, Object> template = new RedisTemplate<>();
		template.setConnectionFactory(connectionFactory);

		// JSON序列化配置
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.registerModule(new JavaTimeModule());
		objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
		objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
		Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(
				objectMapper, Object.class);

		StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

		template.setKeySerializer(stringRedisSerializer);
		template.setHashKeySerializer(stringRedisSerializer);
		template.setValueSerializer(jackson2JsonRedisSerializer);
		template.setHashValueSerializer(jackson2JsonRedisSerializer);
		template.setDefaultSerializer(jackson2JsonRedisSerializer);
		template.afterPropertiesSet();

		log.info("System configuration RedisTemplate configuration completed");
		return template;
	}

	/**
	 * Redis缓存配置常量
	 */
	public static class CacheNames {
		/** 流控规则缓存 */
		public static final String FLOW_RULE = "flow_rule";
		/** IP流控规则缓存 */
		public static final String IP_FLOW_RULE = "ip_flow_rule";
		/** 租户信息缓存 */
		public static final String TENANT_INFO = "tenant_info";
		/** 系统配置缓存 */
		public static final String SYSTEM_CONFIG = "system_config";
		/** 监控统计缓存 */
		public static final String MONITOR_STATISTICS = "monitor_statistics";
		/** 流控日志缓存 */
		public static final String FLOW_CONTROL_LOG = "flow_control_log";
	}

	/**
	 * Redis键前缀常量
	 */
	public static class KeyPrefix {
		/** 流控规则键前缀 */
		public static final String FLOW_RULE = "flow:rule:";
		/** IP流控规则键前缀 */
		public static final String IP_FLOW_RULE = "flow:ip_rule:";
		/** 租户信息键前缀 */
		public static final String TENANT_INFO = "tenant:info:";
		/** 系统配置键前缀 */
		public static final String SYSTEM_CONFIG = "system:config:";
		/** 监控统计键前缀 */
		public static final String MONITOR_STATISTICS = "monitor:statistics:";
		/** 流控日志键前缀 */
		public static final String FLOW_CONTROL_LOG = "flow:log:";
		/** 分布式锁键前缀 */
		public static final String DISTRIBUTED_LOCK = "lock:";
		/** 限流计数器键前缀 */
		public static final String RATE_LIMIT_COUNTER = "rate_limit:counter:";
		/** 会话键前缀 */
		public static final String SESSION = "session:";
	}

	/**
	 * Redis过期时间常量（秒）
	 */
	public static class ExpireTime {
		/** 流控规则过期时间：5分钟 */
		public static final long FLOW_RULE = 300L;
		/** IP流控规则过期时间：5分钟 */
		public static final long IP_FLOW_RULE = 300L;
		/** 租户信息过期时间：30分钟 */
		public static final long TENANT_INFO = 1800L;
		/** 系统配置过期时间：1小时 */
		public static final long SYSTEM_CONFIG = 3600L;
		/** 监控统计过期时间：10分钟 */
		public static final long MONITOR_STATISTICS = 600L;
		/** 流控日志过期时间：1小时 */
		public static final long FLOW_CONTROL_LOG = 3600L;
		/** 分布式锁过期时间：30秒 */
		public static final long DISTRIBUTED_LOCK = 30L;
		/** 限流计数器过期时间：1分钟 */
		public static final long RATE_LIMIT_COUNTER = 60L;
		/** 会话过期时间：30分钟 */
		public static final long SESSION = 1800L;
	}
}