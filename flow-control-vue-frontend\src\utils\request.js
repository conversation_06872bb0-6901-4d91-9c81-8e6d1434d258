import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import store from '@/store'
import router from '@/router'

// 创建axios实例
const service = axios.create({
	baseURL: process.env.NODE_ENV === 'development' ? '' : (process.env.VUE_APP_BASE_API || 'http://localhost:8081'),
	timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
	(config) => {
		// 在发送请求之前做些什么
		if (store.getters.token) {
			// 让每个请求携带token
			config.headers['Authorization'] = `Bearer ${store.getters.token}`
		}
		return config
	},
	(error) => {
		// 对请求错误做些什么
		console.error('Request error:', error)
		return Promise.reject(error)
	}
)

// 响应拦截器
service.interceptors.response.use(
	(response) => {
		const res = response.data

		// 如果自定义代码不是200，则判断为错误
		if (res.code !== 200) {
			Message({
				message: res.message || 'Error',
				type: 'error',
				duration: 5 * 1000
			})

			// 401: 未授权
			if (res.code === 401) {
				// 重新登录
				MessageBox.confirm(
					'您已被登出，可以取消继续留在该页面，或者重新登录',
					'确定登出',
					{
						confirmButtonText: '重新登录',
						cancelButtonText: '取消',
						type: 'warning'
					}
				).then(() => {
					store.dispatch('auth/logout').then(() => {
						location.reload()
					})
				})
			}

			return Promise.reject(new Error(res.message || 'Error'))
		} else {
			return res
		}
	},
	(error) => {
		console.error('Response error:', error)

		let message = '网络错误'

		if (error.response) {
			switch (error.response.status) {
				case 400:
					message = '请求错误'
					break
				case 401:
					message = '未授权，请登录'
					store.dispatch('auth/logout').then(() => {
						router.push('/login')
					})
					break
				case 403:
					message = '拒绝访问'
					break
				case 404:
					message = '请求地址出错'
					break
				case 408:
					message = '请求超时'
					break
				case 500:
					message = '服务器内部错误'
					break
				case 501:
					message = '服务未实现'
					break
				case 502:
					message = '网关错误'
					break
				case 503:
					message = '服务不可用'
					break
				case 504:
					message = '网关超时'
					break
				case 505:
					message = 'HTTP版本不受支持'
					break
				default:
					message = `连接错误${error.response.status}`
			}
		} else if (error.code === 'ECONNABORTED') {
			message = '请求超时'
		} else if (error.message.includes('Network Error')) {
			message = '网络连接异常'
		}

		Message({
			message,
			type: 'error',
			duration: 5 * 1000
		})

		return Promise.reject(error)
	}
)

export default service

// 通用请求方法
export const request = {
	get (url, params) {
		return service.get(url, { params })
	},

	post (url, data) {
		return service.post(url, data)
	},

	put (url, data) {
		return service.put(url, data)
	},

	delete (url, params) {
		return service.delete(url, { params })
	}
}