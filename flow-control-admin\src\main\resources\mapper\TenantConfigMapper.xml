<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.TenantConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.common.entity.TenantConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="contact_person" property="contactPerson" jdbcType="VARCHAR"/>
        <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR"/>
        <result column="contact_email" property="contactEmail" jdbcType="VARCHAR"/>

        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, tenant_id, tenant_name, description, contact_person, contact_phone, contact_email, 
        status, create_by, create_time, update_by, update_time, deleted
    </sql>

    <!-- 分页查询租户配置 -->
    <select id="selectTenantConfigPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tenant_info
        WHERE 1=1
        <if test="tenantName != null and tenantName != ''">
            AND tenant_name LIKE CONCAT('%', #{tenantName}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据租户ID查询租户配置 -->
    <select id="selectByTenantId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tenant_info
        WHERE tenant_id = #{tenantId}
        LIMIT 1
    </select>

    <!-- 根据租户名称查询租户配置（用于重名检查） -->
    <select id="selectByTenantName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tenant_info
        WHERE tenant_name = #{tenantName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>

    <!-- 查询所有启用的租户配置 -->
    <select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tenant_info
        WHERE status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新租户配置状态 -->
    <update id="batchUpdateStatus">
        UPDATE tenant_info
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 统计租户总数 -->
    <select id="countTotal" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tenant_info
    </select>

    <!-- 根据状态统计租户数量 -->
    <select id="countByStatus" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tenant_info WHERE status = #{status}
    </select>

    <!-- 统计启用的租户数量 -->
    <select id="countEnabled" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tenant_info WHERE status = 1
    </select>

    <!-- 检查租户ID是否存在 -->
    <select id="existsByTenantId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tenant_info 
        WHERE tenant_id = #{tenantId}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查租户名称是否存在 -->
    <select id="existsByTenantName" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tenant_info 
        WHERE tenant_name = #{tenantName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 获取启用的租户配置数量 -->
    <select id="getEnabledCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tenant_info WHERE status = 1
    </select>

</mapper>