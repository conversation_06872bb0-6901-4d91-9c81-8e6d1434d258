import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response?.status === 401) {
      // 清除token并跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export default {
  // 认证相关
  auth: {
    login: (credentials) => api.post('/api/auth/login', credentials),
    logout: () => api.post('/api/auth/logout'),
    refreshToken: () => api.post('/api/auth/refresh'),
    getCurrentUser: () => api.get('/api/auth/me')
  },

  // 租户流量规则管理
  tenantFlowRules: {
    // 分页查询
    getList: (params) => {
      return api.get('/api/tenant-flow-rules', { params })
    },

    // 根据ID查询
    getById: (id) => {
      return api.get(`/api/tenant-flow-rules/${id}`)
    },

    // 创建规则
    create: (data) => {
      return api.post('/api/tenant-flow-rules', data)
    },

    // 更新规则
    update: (id, data) => {
      return api.put(`/api/tenant-flow-rules/${id}`, data)
    },

    // 删除规则
    delete: (id) => {
      return api.delete(`/api/tenant-flow-rules/${id}`)
    },

    // 批量删除
    batchDelete: (ids) => {
      return api.delete('/api/tenant-flow-rules/batch', { data: ids })
    },

    // 启用/禁用规则
    updateStatus: (id, enabled) => {
      return api.put(`/api/tenant-flow-rules/${id}/status`, null, { params: { enabled } })
    },

    // 批量启用/禁用
    batchUpdateStatus: (ids, enabled) => {
      return api.put('/api/tenant-flow-rules/batch/status', ids, { params: { enabled } })
    },

    // 根据租户ID和资源模式查询
    getByTenantAndResource: (tenantId, resourcePattern) => {
      return api.get('/api/tenant-flow-rules/tenant-resource', { params: { tenantId, resourcePattern } })
    },

    // 获取有效规则
    getValidRules: (tenantId) => {
      return api.get('/api/tenant-flow-rules/valid', { params: { tenantId } })
    },

    // 按优先级排序查询
    getByPriority: (tenantId, minPriority, maxPriority) => {
      return api.get('/api/tenant-flow-rules/priority', { params: { tenantId, minPriority, maxPriority } })
    },

    // 批量创建
    batchCreate: (rules) => {
      return api.post('/api/tenant-flow-rules/batch', rules)
    },

    // 复制规则
    copy: (id, data) => {
      return api.post(`/api/tenant-flow-rules/${id}/copy`, data)
    },

    // 批量复制到其他租户
    batchCopyToTenant: (ids, targetTenantId) => {
      return api.post('/api/tenant-flow-rules/batch/copy-to-tenant', ids, { params: { targetTenantId } })
    },

    // 导入规则
    import: (file) => {
      const formData = new FormData()
      formData.append('file', file)
      return api.post('/api/tenant-flow-rules/import', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
    },

    // 导出规则
    export: (params) => {
      return api.get('/api/tenant-flow-rules/export', {
        params,
        responseType: 'blob'
      })
    },

    // 验证规则
    validate: (data) => {
      return api.post('/api/tenant-flow-rules/validate', data)
    },

    // 获取统计信息
    getStats: () => {
      return api.get('/api/tenant-flow-rules/stats')
    },

    // 按启用状态统计
    getStatsByEnabled: () => {
      return api.get('/api/tenant-flow-rules/stats/enabled')
    },

    // 按租户分布统计
    getStatsByTenant: () => {
      return api.get('/api/tenant-flow-rules/stats/tenant')
    },

    // 按流控行为统计
    getStatsByBehavior: () => {
      return api.get('/api/tenant-flow-rules/stats/behavior')
    },

    // 获取最大优先级
    getMaxPriority: (tenantId) => {
      return api.get('/api/tenant-flow-rules/max-priority', { params: { tenantId } })
    },

    // 按优先级范围查询
    getByPriorityRange: (tenantId, minPriority, maxPriority) => {
      return api.get('/api/tenant-flow-rules/priority-range', { params: { tenantId, minPriority, maxPriority } })
    },

    // 获取即将过期的规则
    getExpiringSoon: (days) => {
      return api.get('/api/tenant-flow-rules/expiring-soon', { params: { days } })
    },

    // 获取当前有效规则
    getCurrentlyValid: (tenantId) => {
      return api.get('/api/tenant-flow-rules/currently-valid', { params: { tenantId } })
    },

    // 禁用过期规则
    disableExpired: () => {
      return api.put('/api/tenant-flow-rules/disable-expired')
    },

    // 获取租户和全局规则汇总
    getSummary: (tenantId) => {
      return api.get('/api/tenant-flow-rules/summary', { params: { tenantId } })
    },

    // 同步规则到网关
    syncToGateway: () => {
      return api.post('/api/tenant-flow-rules/sync')
    }
  },

  // IP白名单管理
  ipWhitelists: {
    // 分页查询
    getList: (params) => {
      return api.get('/api/ip-whitelists', { params })
    },

    // 根据ID查询
    getById: (id) => {
      return api.get(`/api/ip-whitelists/${id}`)
    },

    // 创建白名单
    create: (data) => {
      return api.post('/api/ip-whitelists', data)
    },

    // 更新白名单
    update: (id, data) => {
      return api.put(`/api/ip-whitelists/${id}`, data)
    },

    // 删除白名单
    delete: (id) => {
      return api.delete(`/api/ip-whitelists/${id}`)
    },

    // 批量删除
    batchDelete: (ids) => {
      return api.delete('/api/ip-whitelists/batch', { data: ids })
    },

    // 启用/禁用白名单
    updateStatus: (id, enabled) => {
      return api.put(`/api/ip-whitelists/${id}/status`, null, { params: { enabled } })
    },

    // 批量启用/禁用白名单状态
    batchUpdateStatus: (ids, enabled) => {
      return api.put('/api/ip-whitelists/batch/status', ids, { params: { enabled } })
    },

    // 根据租户ID查询白名单
    getByTenantId: (tenantId, enabled, limit) => {
      return api.get(`/api/ip-whitelists/tenant/${tenantId}`, { params: { enabled, limit } })
    },

    // 批量创建白名单
    batchCreate: (data) => {
      return api.post('/api/ip-whitelists/batch', data)
    },

    // 复制白名单
    copy: (id, data) => {
      return api.post(`/api/ip-whitelists/${id}/copy`, data)
    },

    // 批量复制到其他租户
    batchCopyToTenant: (ids, targetTenantId) => {
      return api.post('/api/ip-whitelists/batch/copy-to-tenant', ids, { params: { targetTenantId } })
    },

    // 导入白名单
    import: (data, overwrite) => {
      return api.post('/api/ip-whitelists/import', data, { params: { overwrite } })
    },

    // 导出白名单
    export: (tenantId, enabled) => {
      return api.get('/api/ip-whitelists/export', { params: { tenantId, enabled } })
    },

    // 从文件导入IP地址
    importFromFile: (formData) => {
      return api.post('/api/ip-whitelists/import/file', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
    },

    // 导出IP地址到文件
    exportToFile: (tenantId, enabled, format) => {
      return api.get('/api/ip-whitelists/export/file', {
        params: { tenantId, enabled, format },
        responseType: 'blob'
      })
    },

    // 验证白名单
    validate: (data) => {
      return api.post('/api/ip-whitelists/validate', data)
    },

    // 按启用状态统计
    getStatsByEnabled: () => {
      return api.get('/api/ip-whitelists/stats/enabled')
    },

    // 按IP类型统计
    getStatsByIpType: () => {
      return api.get('/api/ip-whitelists/stats/ip-type')
    },

    // 按租户分布统计
    getStatsByTenant: () => {
      return api.get('/api/ip-whitelists/stats/tenant')
    },

    // 获取最大优先级
    getMaxPriority: (tenantId) => {
      return api.get('/api/ip-whitelists/max-priority', { params: { tenantId } })
    },

    // 按优先级范围查询
    getByPriorityRange: (tenantId, minPriority, maxPriority) => {
      return api.get('/api/ip-whitelists/priority-range', { params: { tenantId, minPriority, maxPriority } })
    },

    // 获取即将过期的白名单
    getExpiringSoon: (days) => {
      return api.get('/api/ip-whitelists/expiring-soon', { params: { days } })
    },

    // 获取当前有效白名单
    getCurrentlyValid: (tenantId) => {
      return api.get('/api/ip-whitelists/currently-valid', { params: { tenantId } })
    },

    // 禁用过期白名单
    disableExpired: () => {
      return api.put('/api/ip-whitelists/disable-expired')
    },

    // 获取租户白名单汇总
    getTenantSummary: (tenantId) => {
      return api.get('/api/ip-whitelists/tenant-summary', { params: { tenantId } })
    },

    // 获取全局白名单汇总
    getGlobalSummary: () => {
      return api.get('/api/ip-whitelists/global-summary')
    },

    // 同步白名单到网关
    syncToGateway: () => {
      return api.post('/api/ip-whitelists/sync')
    },

    // IP地址匹配检查
    checkMatch: (ip, tenantId) => {
      return api.post('/api/ip-whitelists/check', null, { params: { ip, tenantId } })
    },

    // 批量IP地址匹配检查
    batchCheckMatch: (ips, tenantId) => {
      return api.post('/api/ip-whitelists/batch-check', ips, { params: { tenantId } })
    }
  },

  // IP黑名单管理
  ipBlacklists: {
    // 分页查询
    getList: (params) => {
      return api.get('/api/ip-blacklists', { params })
    },

    // 根据ID查询
    getById: (id) => {
      return api.get(`/api/ip-blacklists/${id}`)
    },

    // 创建黑名单
    create: (data) => {
      return api.post('/api/ip-blacklists', data)
    },

    // 更新黑名单
    update: (id, data) => {
      return api.put(`/api/ip-blacklists/${id}`, data)
    },

    // 删除黑名单
    delete: (id) => {
      return api.delete(`/api/ip-blacklists/${id}`)
    },

    // 批量删除
    batchDelete: (ids) => {
      return api.delete('/api/ip-blacklists/batch', { data: ids })
    },

    // 启用/禁用黑名单
    updateStatus: (id, enabled) => {
      return api.put(`/api/ip-blacklists/${id}/status`, null, { params: { enabled } })
    },

    // 批量启用/禁用黑名单状态
    batchUpdateStatus: (ids, enabled) => {
      return api.put('/api/ip-blacklists/batch/status', ids, { params: { enabled } })
    },

    // 根据租户ID查询黑名单
    getByTenantId: (tenantId, enabled, limit) => {
      return api.get(`/api/ip-blacklists/tenant/${tenantId}`, { params: { enabled, limit } })
    },

    // 批量创建黑名单
    batchCreate: (data) => {
      return api.post('/api/ip-blacklists/batch', data)
    },

    // 复制黑名单
    copy: (id, data) => {
      return api.post(`/api/ip-blacklists/${id}/copy`, data)
    },

    // 批量复制到其他租户
    batchCopyToTenant: (ids, targetTenantId) => {
      return api.post('/api/ip-blacklists/batch/copy-to-tenant', ids, { params: { targetTenantId } })
    },

    // 导入黑名单
    import: (data, overwrite) => {
      return api.post('/api/ip-blacklists/import', data, { params: { overwrite } })
    },

    // 导出黑名单
    export: (tenantId, enabled) => {
      return api.get('/api/ip-blacklists/export', { params: { tenantId, enabled } })
    },

    // 从文件导入IP地址
    importFromFile: (formData) => {
      return api.post('/api/ip-blacklists/import/file', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
    },

    // 导出IP地址到文件
    exportToFile: (tenantId, enabled, format) => {
      return api.get('/api/ip-blacklists/export/file', {
        params: { tenantId, enabled, format },
        responseType: 'blob'
      })
    },

    // 验证黑名单
    validate: (data) => {
      return api.post('/api/ip-blacklists/validate', data)
    },

    // 按启用状态统计
    getStatsByEnabled: () => {
      return api.get('/api/ip-blacklists/stats/enabled')
    },

    // 按IP类型统计
    getStatsByIpType: () => {
      return api.get('/api/ip-blacklists/stats/ip-type')
    },

    // 按租户分布统计
    getStatsByTenant: () => {
      return api.get('/api/ip-blacklists/stats/tenant')
    },

    // 获取最大优先级
    getMaxPriority: (tenantId) => {
      return api.get('/api/ip-blacklists/max-priority', { params: { tenantId } })
    },

    // 按优先级范围查询
    getByPriorityRange: (tenantId, minPriority, maxPriority) => {
      return api.get('/api/ip-blacklists/priority-range', { params: { tenantId, minPriority, maxPriority } })
    },

    // 获取即将过期的黑名单
    getExpiringSoon: (days) => {
      return api.get('/api/ip-blacklists/expiring-soon', { params: { days } })
    },

    // 获取当前有效黑名单
    getCurrentlyValid: (tenantId) => {
      return api.get('/api/ip-blacklists/currently-valid', { params: { tenantId } })
    },

    // 禁用过期黑名单
    disableExpired: () => {
      return api.put('/api/ip-blacklists/disable-expired')
    },

    // 获取租户黑名单汇总
    getTenantSummary: (tenantId) => {
      return api.get('/api/ip-blacklists/tenant-summary', { params: { tenantId } })
    },

    // 获取全局黑名单汇总
    getGlobalSummary: () => {
      return api.get('/api/ip-blacklists/global-summary')
    },

    // 同步黑名单到网关
    syncToGateway: () => {
      return api.post('/api/ip-blacklists/sync')
    },

    // IP地址匹配检查
    checkMatch: (ip, tenantId) => {
      return api.post('/api/ip-blacklists/check', null, { params: { ip, tenantId } })
    },

    // 批量IP地址匹配检查
    batchCheckMatch: (ips, tenantId) => {
      return api.post('/api/ip-blacklists/batch-check', ips, { params: { tenantId } })
    }
  },

  // IP流量规则管理
  ipFlowRules: {
    // 分页查询
    getList: (params) => {
      return api.get('/api/ip-flow-rules', { params })
    },

    // 根据ID查询
    getById: (id) => {
      return api.get(`/api/ip-flow-rules/${id}`)
    },

    // 创建规则
    create: (data) => {
      return api.post('/api/ip-flow-rules', data)
    },

    // 更新规则
    update: (id, data) => {
      return api.put(`/api/ip-flow-rules/${id}`, data)
    },

    // 删除规则
    delete: (id) => {
      return api.delete(`/api/ip-flow-rules/${id}`)
    },

    // 批量删除
    batchDelete: (ids) => {
      return api.delete('/api/ip-flow-rules/batch', { data: ids })
    },

    // 启用/禁用规则
    updateStatus: (id, enabled) => {
      return api.put(`/api/ip-flow-rules/${id}/status`, null, { params: { enabled } })
    },

    // 批量启用/禁用规则状态
    batchUpdateStatus: (ids, enabled) => {
      return api.put('/api/ip-flow-rules/batch/toggle-status', ids, { params: { enabled } })
    },

    // 根据租户ID查询规则
    getByTenantId: (tenantId) => {
      return api.get(`/api/ip-flow-rules/tenant/${tenantId}`)
    },

    // 同步规则到Sentinel
    syncToSentinel: () => {
      return api.post('/api/ip-flow-rules/sync')
    },

    // 导入规则
    import: (data, overwrite) => {
      return api.post('/api/ip-flow-rules/import', data, { params: { overwrite } })
    },

    // 导出规则
    export: (tenantId, enabled) => {
      return api.get('/api/ip-flow-rules/export', { params: { tenantId, enabled } })
    },

    // 获取统计信息
    getStatistics: () => {
      return api.get('/api/ip-flow-rules/statistics')
    },

    // 验证规则
    validate: (data) => {
      return api.post('/api/ip-flow-rules/validate', data)
    },

    // 刷新规则缓存
    refreshCache: (tenantId) => {
      return api.post('/api/ip-flow-rules/refresh-cache', null, { params: { tenantId } })
    },

    // 全局刷新规则缓存
    refreshGlobalCache: () => {
      return api.post('/api/ip-flow-rules/refresh-global-cache')
    }
  },

	// 监控相关
  monitor: {
    getDashboard: (tenantId) => {
      return api.get('/monitor/dashboard', { params: { tenantId } })
    },

    getRealtime: (tenantId, resourceName, timeRange) => {
      return api.get('/monitor/realtime', { params: { tenantId, resourceName, timeRange } })
    },

    getStatistics: (params) => {
      return api.get('/monitor/statistics', { params })
    },

    getTrend: (params) => {
      return api.get('/monitor/trend', { params })
    },

    getQpsRanking: (params) => {
      return api.get('/monitor/ranking/qps', { params })
    },

    getResponseTimeRanking: (params) => {
      return api.get('/monitor/ranking/rt', { params })
    },

    getTenantStatistics: (tenantId, startTime, endTime) => {
      return api.get(`/monitor/tenant/${tenantId}/statistics`, { params: { startTime, endTime } })
    },

    getOverview: (startTime, endTime) => {
      return api.get('/monitor/overview', { params: { startTime, endTime } })
    },

    batchInsert: (data) => {
      return api.post('/monitor/batch', data)
    },

    deleteExpired: (retentionDays) => {
      return api.delete('/monitor/expired', { params: { retentionDays } })
    },

    getAbnormal: (params) => {
      return api.get('/monitor/abnormal', { params })
    },

    export: (params) => {
      return api.get('/monitor/export', { params })
    },

    getHotspot: (params) => {
      return api.get('/monitor/hotspot', { params })
    },

    getPerformance: (params) => {
      return api.get('/monitor/performance', { params })
    },

    cleanup: (retentionDays) => {
      return api.post('/monitor/cleanup', null, {
        params: { retentionDays }
      })
    },

    getStorageStatistics: () => {
      return api.get('/monitor/storage/statistics')
    }
  },

	// 租户管理相关
	tenants: {
		getList: (params) => {
			return api.get('/tenants', { params })
		},

		getById: (id) => {
			return api.get(`/tenants/${id}`)
		},

		getByTenantId: (tenantId) => {
			return api.get(`/tenants/tenant/${tenantId}`)
		},

		create: (data) => {
			return api.post('/tenants', data)
		},

		update: (id, data) => {
			return api.put(`/tenants/${id}`, data)
		},

		delete: (id) => {
			return api.delete(`/tenants/${id}`)
		},

		batchDelete: (ids) => {
			return api.post('/tenants/batch', ids)
		},

		updateStatus: (id, status) => {
			return api.put(`/tenants/${id}/status?status=${status}`)
		},

		batchUpdateStatus: (ids, status) => {
			return api.put('/tenants/batch/status', ids, {
				params: { status }
			})
		},

		getStatistics: () => {
			return api.get('/tenants/statistics')
		},

		export: (params) => {
			return api.get('/tenants/export', { params })
		},

		import: (data, overwrite = false) => {
			return api.post('/tenants/import', data, {
				params: { overwrite }
			})
		},

		validate: (data) => {
			return api.post('/tenants/validate', data)
		},

		getTotalRules: (tenantId) => {
			return api.get(`/tenants/${tenantId}/total-rules`)
		},

		updateTotalRules: (tenantId, data) => {
			return api.put(`/tenants/${tenantId}/total-rules`, data)
		}
	},

	// 系统配置相关
	systemConfig: {
		getList: (params) => {
			return api.get('/system-config', { params })
		},

		getById: (id) => {
			return api.get(`/system-config/${id}`)
		},

		getByKey: (configKey) => {
			return api.get(`/system-config/key/${configKey}`)
		},

		getByType: (configType) => {
			return api.get(`/system-config/type/${configType}`)
		},

		getByTenant: (tenantId) => {
			return api.get(`/system-config/tenant/${tenantId}`)
		},

		create: (data) => {
			return api.post('/system-config', data)
		},

		update: (id, data) => {
			return api.put(`/system-config/${id}`, data)
		},

		delete: (id) => {
			return api.delete(`/system-config/${id}`)
		},

		batchDelete: (ids) => {
			return api.post('/system-config/batch', ids)
		},

		updateStatus: (id, status) => {
			return api.put(`/system-config/${id}/status?status=${status}`)
		},

		batchUpdateStatus: (ids, status) => {
			return api.put('/system-config/batch/status', ids, {
				params: { status }
			})
		},

		exists: (configKey) => {
			return api.get(`/system-config/exists/${configKey}`)
		},

		getCount: (configType) => {
			return api.get('/system-config/count', { params: { configType } })
		},

		export: (params) => {
			return api.get('/system-config/export', { params })
		},

		import: (data) => {
			return api.post('/system-config/import', data)
		},

		getStatistics: () => {
			return api.get('/system-config/statistics')
		},

		validate: (data) => {
			return api.post('/system-config/validate', data)
		}
	},

	// 告警管理
	alerts: {
		// 获取告警列表
		getList: (params) => {
			return api.get('/api/alerts', { params })
		},

		// 根据ID查询告警
		getById: (id) => {
			return api.get(`/api/alerts/${id}`)
		},

		// 创建告警
		create: (data) => {
			return api.post('/api/alerts', data)
		},

		// 更新告警
		update: (id, data) => {
			return api.put(`/api/alerts/${id}`, data)
		},

		// 删除告警
		delete: (id) => {
			return api.delete(`/api/alerts/${id}`)
		},

		// 批量删除告警
		batchDelete: (ids) => {
			return api.delete('/api/alerts/batch', { data: ids })
		},

		// 确认告警
		acknowledge: (id, data) => {
			return api.put(`/api/alerts/${id}/acknowledge`, data)
		},

		// 批量确认告警
		batchAcknowledge: (ids, data) => {
			return api.put('/api/alerts/batch/acknowledge', { ids, ...data })
		},

		// 关闭告警
		close: (id, data) => {
			return api.put(`/api/alerts/${id}/close`, data)
		},

		// 批量关闭告警
		batchClose: (ids, data) => {
			return api.put('/api/alerts/batch/close', { ids, ...data })
		},

		// 获取告警统计
		getStatistics: (params) => {
			return api.get('/api/alerts/statistics', { params })
		},

		// 获取告警趋势
		getTrend: (params) => {
			return api.get('/api/alerts/trend', { params })
		},

		// 获取告警分布
		getDistribution: (params) => {
			return api.get('/api/alerts/distribution', { params })
		},

		// 导出告警
		export: (params) => {
			return api.get('/api/alerts/export', { params, responseType: 'blob' })
		}
	},

	// 告警规则管理
	alertRules: {
		// 获取告警规则列表
		getList: (params) => {
			return api.get('/api/alert-rules', { params })
		},

		// 根据ID查询告警规则
		getById: (id) => {
			return api.get(`/api/alert-rules/${id}`)
		},

		// 创建告警规则
		create: (data) => {
			return api.post('/api/alert-rules', data)
		},

		// 更新告警规则
		update: (id, data) => {
			return api.put(`/api/alert-rules/${id}`, data)
		},

		// 删除告警规则
		delete: (id) => {
			return api.delete(`/api/alert-rules/${id}`)
		},

		// 批量删除告警规则
		batchDelete: (ids) => {
			return api.delete('/api/alert-rules/batch', { data: ids })
		},

		// 启用/禁用告警规则
		updateStatus: (id, enabled) => {
			return api.put(`/api/alert-rules/${id}/status`, null, { params: { enabled } })
		},

		// 批量启用/禁用告警规则
		batchUpdateStatus: (ids, enabled) => {
			return api.put('/api/alert-rules/batch/status', ids, { params: { enabled } })
		},

		// 测试告警规则
		test: (data) => {
			return api.post('/api/alert-rules/test', data)
		},

		// 复制告警规则
		copy: (id, data) => {
			return api.post(`/api/alert-rules/${id}/copy`, data)
		},

		// 获取告警规则统计
		getStatistics: () => {
			return api.get('/api/alert-rules/statistics')
		},

		// 验证告警规则
		validate: (data) => {
			return api.post('/api/alert-rules/validate', data)
		},

		// 导入告警规则
		import: (file) => {
			const formData = new FormData()
			formData.append('file', file)
			return api.post('/api/alert-rules/import', formData, {
				headers: { 'Content-Type': 'multipart/form-data' }
			})
		},

		// 导出告警规则
		export: (params) => {
			return api.get('/api/alert-rules/export', { params, responseType: 'blob' })
		}
	},

	// 告警通知管理
	alertNotifications: {
		// 获取通知配置列表
		getList: (params) => {
			return api.get('/api/alert-notifications', { params })
		},

		// 根据ID查询通知配置
		getById: (id) => {
			return api.get(`/api/alert-notifications/${id}`)
		},

		// 创建通知配置
		create: (data) => {
			return api.post('/api/alert-notifications', data)
		},

		// 更新通知配置
		update: (id, data) => {
			return api.put(`/api/alert-notifications/${id}`, data)
		},

		// 删除通知配置
		delete: (id) => {
			return api.delete(`/api/alert-notifications/${id}`)
		},

		// 批量删除通知配置
		batchDelete: (ids) => {
			return api.delete('/api/alert-notifications/batch', { data: ids })
		},

		// 启用/禁用通知配置
		updateStatus: (id, enabled) => {
			return api.put(`/api/alert-notifications/${id}/status`, null, { params: { enabled } })
		},

		// 测试通知配置
		test: (id, data) => {
			return api.post(`/api/alert-notifications/${id}/test`, data)
		},

		// 发送测试通知
		sendTest: (data) => {
			return api.post('/api/alert-notifications/send-test', data)
		},

		// 获取通知历史
		getHistory: (params) => {
			return api.get('/api/alert-notifications/history', { params })
		},

		// 获取通知统计
		getStatistics: (params) => {
			return api.get('/api/alert-notifications/statistics', { params })
		},

		// 验证功能
		validateConfig: (config) => {
			return api.post('/api/alert-notifications/validate', config)
		}
	},

	// 系统日志管理
	systemLogs: {
		// 基础查询操作
		getList: (params) => {
			return api.get('/api/system-logs', { params })
		},

		// 根据ID查询日志
		getById: (id) => {
			return api.get(`/api/system-logs/${id}`)
		},

		// 另一个查询方法
		get: (id) => {
			return api.get(`/api/system-logs/${id}`)
		},

		// 列表查询方法
		list: (params) => {
			return api.get('/api/system-logs', { params })
		},

		// 日志管理
		clear: () => {
			return api.delete('/api/system-logs/clear')
		},

		// 归档日志
		archive: (params) => {
			return api.post('/api/system-logs/archive', params)
		},

		// 统计和分析
		getStatistics: (params) => {
			return api.get('/api/system-logs/statistics', { params })
		},

		// 获取日志趋势
		getTrend: (params) => {
			return api.get('/api/system-logs/trend', { params })
		},

		// 获取日志分布
		getDistribution: (params) => {
			return api.get('/api/system-logs/distribution', { params })
		},

		// 获取日志级别分布
		getLevelDistribution: (params) => {
			return api.get('/api/system-logs/level-distribution', { params })
		},

		// 导出功能
		export: (params) => {
			return api.get('/api/system-logs/export', { params, responseType: 'blob' })
		},

		// 按级别导出
		exportByLevel: (level, params) => {
			return api.get(`/api/system-logs/export/${level}`, { params, responseType: 'blob' })
		},

		// 搜索和过滤
		search: (keyword, params) => {
			return api.get('/api/system-logs/search', { params: { keyword, ...params } })
		},

		// 按级别过滤
		filterByLevel: (level, params) => {
			return api.get(`/api/system-logs/level/${level}`, { params })
		},

		// 按日期范围过滤
		filterByDateRange: (startDate, endDate, params) => {
			return api.get('/api/system-logs/date-range', { 
				params: { startDate, endDate, ...params } 
			})
		},

		// 获取实时日志
		getRealtimeLogs: (params) => {
			return api.get('/api/system-logs/realtime', { params })
		},

		// WebSocket连接实时日志
		subscribeRealtimeLogs: (callback) => {
			const ws = new WebSocket(`${process.env.VUE_APP_WS_URL || 'ws://localhost:8080'}/ws/system-logs`);
			ws.onmessage = (event) => {
				const logData = JSON.parse(event.data);
				callback(logData);
			};
			return ws;
		}
	},

	// 统计数据管理
	statistics: {
		// 概览统计
		getOverview: (params) => {
			return api.get('/api/statistics/overview', { params })
		},

		// 详细统计数据
		getDetail: (params) => {
			return api.get('/api/statistics/detail', { params })
		},

		// 流量统计
		getTrafficStats: (params) => {
			return api.get('/api/statistics/traffic', { params })
		},

		// 性能统计
		getPerformanceStats: (params) => {
			return api.get('/api/statistics/performance', { params })
		},

		// 错误统计
		getErrorStats: (params) => {
			return api.get('/api/statistics/error', { params })
		},

		// QPS统计
		getQpsStats: (params) => {
			return api.get('/api/statistics/qps', { params })
		},

		// 响应时间统计
		getResponseTimeStats: (params) => {
			return api.get('/api/statistics/response-time', { params })
		},

		// 成功率统计
		getSuccessRateStats: (params) => {
			return api.get('/api/statistics/success-rate', { params })
		},

		// 租户统计
		getTenantStats: (params) => {
			return api.get('/api/statistics/tenant', { params })
		},

		// API路径统计
		getApiPathStats: (params) => {
			return api.get('/api/statistics/api-path', { params })
		},

		// 时间趋势统计
		getTrendStats: (params) => {
			return api.get('/api/statistics/trend', { params })
		},

		// 分布统计
		getDistributionStats: (params) => {
			return api.get('/api/statistics/distribution', { params })
		},

		// 实时统计
		getRealtimeStats: (params) => {
			return api.get('/api/statistics/realtime', { params })
		},

		// 导出统计数据
		export: (params) => {
			return api.get('/api/statistics/export', { params, responseType: 'blob' })
		},

		// 按类型导出
		exportByType: (type, params) => {
			return api.get(`/api/statistics/export/${type}`, { params, responseType: 'blob' })
		},

		// 获取统计配置
		getConfig: () => {
			return api.get('/api/statistics/config')
		},

		// 更新统计配置
		updateConfig: (config) => {
			return api.put('/api/statistics/config', config)
		},

		// WebSocket连接实时统计
		subscribeRealtimeStats: (callback) => {
			const ws = new WebSocket(`${process.env.VUE_APP_WS_URL || 'ws://localhost:8080'}/ws/statistics`);
			ws.onmessage = (event) => {
				const statsData = JSON.parse(event.data);
				callback(statsData);
			};
			return ws;
		}
	}
}