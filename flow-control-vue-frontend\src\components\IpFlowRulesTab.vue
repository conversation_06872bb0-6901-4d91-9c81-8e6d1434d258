<template>
	<div class="ip-flow-rules-tab">
		<!-- 操作栏 -->
		<div class="header-actions">
			<el-button
				type="danger"
				:disabled="!hasSelectedRules"
				@click="handleBatchDelete"
			>
				批量删除 ({{ selectedRulesCount }})
			</el-button>
			<el-button type="warning" @click="showImportDialog">
				批量导入
			</el-button>
			<el-button type="info" @click="handleExport">
				导出规则
			</el-button>
			<el-button type="success" @click="handleSyncToSentinel">
				同步到Sentinel
			</el-button>
			<el-button
				type="primary"
				icon="el-icon-plus"
				@click="showCreateDialog"
			>
				新增规则
			</el-button>
			<el-button icon="el-icon-refresh" @click="fetchRules">
				刷新
			</el-button>
		</div>

		<!-- 搜索和筛选 -->
		<div class="search-filters">
			<el-row :gutter="20">
				<el-col :span="5">
					<el-input
						v-model="searchKeyword"
						placeholder="搜索IP地址或规则名称"
						prefix-icon="el-icon-search"
						clearable
						@input="handleSearch"
					/>
				</el-col>
				<el-col :span="3">
					<el-select
						v-model="filters.status"
						placeholder="状态筛选"
						clearable
						@change="handleFilterChange"
					>
						<el-option label="启用" value="enabled" />
						<el-option label="禁用" value="disabled" />
					</el-select>
				</el-col>
				<el-col :span="3">
					<el-select
						v-model="filters.grade"
						placeholder="限流模式"
						clearable
						@change="handleFilterChange"
					>
						<el-option label="QPS" :value="1" />
						<el-option label="线程数" :value="0" />
					</el-select>
				</el-col>
				<el-col :span="3">
					<el-select
						v-model="filters.controlBehavior"
						placeholder="流控行为"
						clearable
						@change="handleFilterChange"
					>
						<el-option label="快速失败" :value="0" />
						<el-option label="Warm Up" :value="1" />
						<el-option label="排队等待" :value="2" />
					</el-select>
				</el-col>
				<el-col :span="6">
					<div class="stats-info">
						<span>启用: {{ enabledRulesCount }}</span>
						<span>禁用: {{ disabledRulesCount }}</span>
						<span>总计: {{ pagination.total }}</span>
					</div>
				</el-col>
			</el-row>
		</div>

		<!-- 规则表格 -->
		<div class="rules-table">
			<el-table
				:data="paginatedRules"
				:loading="isLoading"
				style="width: 100%"
				stripe
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="55"></el-table-column>
				<el-table-column prop="id" label="ID" width="80"></el-table-column>
				<el-table-column
					prop="resource"
					label="资源名称"
					min-width="150"
					show-overflow-tooltip
				></el-table-column>
				<el-table-column
					prop="ipAddress"
					label="IP地址"
					min-width="150"
					show-overflow-tooltip
				></el-table-column>
				<el-table-column prop="grade" label="限流模式" width="100">
					<template slot-scope="scope">
						<el-tag size="mini" :type="scope.row.grade === 1 ? 'primary' : 'info'">
							{{ scope.row.grade === 1 ? 'QPS' : '线程数' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="count" label="阈值" width="100">
					<template slot-scope="scope">
						<el-tag size="mini" type="warning">
							{{ scope.row.count }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="controlBehavior" label="流控行为" width="120">
					<template slot-scope="scope">
						<el-tag size="mini" :type="getControlBehaviorColor(scope.row.controlBehavior)">
							{{ getControlBehaviorText(scope.row.controlBehavior) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="priority" label="优先级" width="100">
					<template slot-scope="scope">
						<el-tag
							size="mini"
							:type="getPriorityColor(scope.row.priority)"
						>
							{{ scope.row.priority }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="enabled" label="状态" width="100">
					<template slot-scope="scope">
						<el-switch
							v-model="scope.row.enabled"
							@change="handleStatusChange(scope.row)"
						></el-switch>
					</template>
				</el-table-column>
				<el-table-column
					prop="createTime"
					label="创建时间"
					width="180"
				>
					<template slot-scope="scope">
						{{ formatTime(scope.row.createTime) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="200">
					<template slot-scope="scope">
						<el-button
							size="mini"
							type="primary"
							@click="editRule(scope.row)"
						>
							编辑
						</el-button>
						<el-button
							size="mini"
							type="info"
							@click="validateRule(scope.row)"
						>
							验证
						</el-button>
						<el-button
							size="mini"
							type="danger"
							@click="deleteRule(scope.row)"
						>
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 分页 -->
		<div class="pagination">
			<el-pagination
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				:current-page="pagination.currentPage"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="pagination.pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="pagination.total"
			>
			</el-pagination>
		</div>

		<!-- 新增/编辑对话框 -->
		<el-dialog
			:title="dialogTitle"
			:visible.sync="dialogVisible"
			width="700px"
			@close="resetForm"
		>
			<el-form
				ref="ruleForm"
				:model="ruleForm"
				:rules="computedFormRules"
				label-width="120px"
			>
				<el-form-item label="资源名称" prop="resource">
					<el-input
						v-model="ruleForm.resource"
						placeholder="请输入资源名称"
					></el-input>
				</el-form-item>
				<el-form-item label="IP地址" prop="ipAddress">
					<el-input
						v-model="ruleForm.ipAddress"
						placeholder="支持单个IP、IP段、CIDR格式"
					></el-input>
				</el-form-item>
				<el-form-item label="限流模式" prop="grade">
					<el-radio-group v-model="ruleForm.grade">
						<el-radio :label="1">QPS</el-radio>
						<el-radio :label="0">线程数</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="阈值" prop="count">
					<el-input-number
						v-model="ruleForm.count"
						:min="1"
						:max="10000"
					></el-input-number>
				</el-form-item>
				<el-form-item label="流控行为" prop="controlBehavior">
					<el-select v-model="ruleForm.controlBehavior" style="width: 100%">
						<el-option label="快速失败" :value="0" />
						<el-option label="Warm Up" :value="1" />
						<el-option label="排队等待" :value="2" />
					</el-select>
				</el-form-item>
				<el-form-item label="预热时长" prop="warmUpPeriodSec" v-if="ruleForm.controlBehavior === 1">
					<el-input-number
						v-model="ruleForm.warmUpPeriodSec"
						:min="1"
						:max="3600"
					></el-input-number>
					<span style="margin-left: 10px; color: #999;">秒</span>
				</el-form-item>
				<el-form-item label="排队超时" prop="maxQueueingTimeMs" v-if="ruleForm.controlBehavior === 2">
					<el-input-number
						v-model="ruleForm.maxQueueingTimeMs"
						:min="1"
						:max="60000"
					></el-input-number>
					<span style="margin-left: 10px; color: #999;">毫秒</span>
				</el-form-item>
				<el-form-item label="优先级" prop="priority">
					<el-input-number
						v-model="ruleForm.priority"
						:min="1"
						:max="100"
					></el-input-number>
				</el-form-item>
				<el-form-item label="状态">
					<el-switch
						v-model="ruleForm.enabled"
						active-text="启用"
						inactive-text="禁用"
					></el-switch>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="saveRule">确定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import validationRules from '@/utils/validation'

export default {
	name: 'IpFlowRulesTab',
	beforeCreate() {
		// 注册验证规则到Vue实例
		this.$validation = validationRules;
	},
	data() {
		return {
			searchKeyword: '',
			filters: {
				status: '',
				grade: '',
				controlBehavior: ''
			},
			selectedRules: [],
			dialogVisible: false,
			isEdit: false,
			ruleForm: {
				id: null,
				resource: '',
				ipAddress: '',
				grade: 1,
				count: 10,
				controlBehavior: 0,
				warmUpPeriodSec: 10,
				maxQueueingTimeMs: 500,
				priority: 10,
				enabled: true
			},
			formRules: {}
		}
	},
	computed: {
		...mapGetters('ipFlowRules', [
			'allRules',
			'isLoading',
			'pagination',
			'selectedRuleIds',
			'hasSelectedRules',
			'selectedRulesCount',
			'enabledRulesCount',
			'disabledRulesCount'
		]),
		
		// 动态计算表单验证规则
		computedFormRules() {
			const rules = {
				resource: this.$validation.ipFlowRuleRules.resourceName || this.$validation.commonRules.resourceName,
				ipAddress: this.$validation.ipFlowRuleRules.ipValue,
				grade: this.$validation.ipFlowRuleRules.limitMode || this.$validation.commonRules.status,
				count: this.$validation.ipFlowRuleRules.qpsLimit,
				controlBehavior: this.$validation.ipFlowRuleRules.flowBehavior,
				warmUpPeriodSec: this.$validation.ipFlowRuleRules.warmUpPeriod,
				maxQueueingTimeMs: this.$validation.ipFlowRuleRules.queueTimeout,
				priority: this.$validation.commonRules.priority
			};
			
			// 根据控制行为动态调整验证规则
			if (this.ruleForm.controlBehavior === 1 || this.ruleForm.controlBehavior === 3) {
				// Warm Up 或 Warm Up + 排队等待模式需要预热时长
				rules.warmUpPeriodSec = this.$validation.ipFlowRuleRules.warmUpPeriod;
			} else {
				// 其他模式不需要预热时长
				rules.warmUpPeriodSec = [];
			}
			
			if (this.ruleForm.controlBehavior === 2 || this.ruleForm.controlBehavior === 3) {
				// 排队等待 或 Warm Up + 排队等待模式需要排队超时时间
				rules.maxQueueingTimeMs = this.$validation.ipFlowRuleRules.queueTimeout;
			} else {
				// 其他模式不需要排队超时时间
				rules.maxQueueingTimeMs = [];
			}
			
			return rules;
		},
		
		filteredRules() {
			let rules = this.allRules;
			
			// 关键词搜索
			if (this.searchKeyword) {
				const keyword = this.searchKeyword.toLowerCase();
				rules = rules.filter(rule => 
					rule.resource.toLowerCase().includes(keyword) ||
					rule.ipAddress.toLowerCase().includes(keyword)
				);
			}
			
			// 状态筛选
			if (this.filters.status !== '') {
				const enabled = this.filters.status === 'enabled';
				rules = rules.filter(rule => rule.enabled === enabled);
			}
			
			// 阈值类型筛选
			if (this.filters.grade !== '') {
				const grade = parseInt(this.filters.grade);
				rules = rules.filter(rule => rule.grade === grade);
			}
			
			// 控制行为筛选
			if (this.filters.controlBehavior !== '') {
				const behavior = parseInt(this.filters.controlBehavior);
				rules = rules.filter(rule => rule.controlBehavior === behavior);
			}
			
			return rules;
		},
		paginatedRules() {
			const start = (this.pagination.currentPage - 1) * this.pagination.pageSize
			const end = start + this.pagination.pageSize
			return this.filteredRules.slice(start, end)
		},
		dialogTitle() {
			return this.isEdit ? '编辑IP流量规则' : '新增IP流量规则'
		}
	},
	mounted() {
		this.fetchRules()
	},
	methods: {
		...mapActions('ipFlowRules', [
			'fetchRules',
			'createRule',
			'updateRule',
			'deleteRule',
			'batchDeleteRules',
			'updateStatus',
			'syncToSentinel',
			'exportRules',
			'validateRule'
		]),
		showCreateDialog() {
			this.isEdit = false
			this.dialogVisible = true
		},
		editRule(rule) {
			this.isEdit = true
			this.ruleForm = { ...rule }
			this.dialogVisible = true
		},
		async saveRule() {
			this.$refs.ruleForm.validate(async (valid) => {
				if (valid) {
					try {
						if (this.isEdit) {
							await this.updateRule(this.ruleForm)
							this.$message.success('更新成功')
						} else {
							await this.createRule(this.ruleForm)
							this.$message.success('创建成功')
						}
						this.dialogVisible = false
						this.fetchRules()
					} catch (error) {
						this.$message.error('操作失败')
					}
				}
			})
		},
		async deleteRule(rule) {
			try {
				await this.$confirm('确定要删除这条规则吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
				await this.deleteRule(rule.id)
				this.$message.success('删除成功')
				this.fetchRules()
			} catch (error) {
				if (error !== 'cancel') {
					this.$message.error('删除失败')
				}
			}
		},
		async validateRule(rule) {
			try {
				await this.validateRule(rule.id)
				this.$message.success('规则验证通过')
			} catch (error) {
				this.$message.error('规则验证失败')
			}
		},
		async handleStatusChange(rule) {
			try {
				await this.updateStatus({ id: rule.id, enabled: rule.enabled })
				this.$message.success(`已${rule.enabled ? '启用' : '禁用'}`)
			} catch (error) {
				rule.enabled = !rule.enabled
				this.$message.error('状态切换失败')
			}
		},
		handleSearch() {
			// 搜索逻辑在computed中处理
		},
		handleFilterChange() {
			// 筛选逻辑在computed中处理
		},
		handleSelectionChange(selection) {
			this.selectedRules = selection
		},
		async handleBatchDelete() {
			if (this.selectedRules.length === 0) {
				this.$message.warning('请选择要删除的规则')
				return
			}
			try {
				await this.$confirm(`确定要删除选中的 ${this.selectedRules.length} 条规则吗？`, '批量删除', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
				const ids = this.selectedRules.map(rule => rule.id)
				await this.batchDeleteRules(ids)
				this.$message.success(`成功删除 ${ids.length} 条规则`)
				this.selectedRules = []
				this.fetchRules()
			} catch (error) {
				if (error !== 'cancel') {
					this.$message.error('批量删除失败')
				}
			}
		},
		showImportDialog() {
			// TODO: 实现导入功能
			this.$message.info('导入功能开发中')
		},
		async handleExport() {
			try {
				await this.exportRules()
				this.$message.success('导出成功')
			} catch (error) {
				this.$message.error('导出失败')
			}
		},
		async handleSyncToSentinel() {
			try {
				await this.syncToSentinel()
				this.$message.success('同步到Sentinel成功')
			} catch (error) {
				this.$message.error('同步失败')
			}
		},
		handleSizeChange(val) {
			this.pagination.pageSize = val
			this.pagination.currentPage = 1
		},
		handleCurrentChange(val) {
			this.pagination.currentPage = val
		},
		resetForm() {
			this.ruleForm = {
				id: null,
				resource: '',
				ipAddress: '',
				grade: 1,
				count: 10,
				controlBehavior: 0,
				warmUpPeriodSec: 10,
				maxQueueingTimeMs: 500,
				priority: 10,
				enabled: true
			}
			if (this.$refs.ruleForm) {
				this.$refs.ruleForm.resetFields()
			}
		},
		getPriorityColor(priority) {
			if (priority >= 80) return 'danger'
			if (priority >= 50) return 'warning'
			return 'success'
		},
		getControlBehaviorText(behavior) {
			const map = {
				0: '快速失败',
				1: 'Warm Up',
				2: '排队等待'
			}
			return map[behavior] || '未知'
		},
		getControlBehaviorColor(behavior) {
			const map = {
				0: 'danger',
				1: 'warning',
				2: 'info'
			}
			return map[behavior] || 'default'
		},
		formatTime(time) {
			if (!time) return '-'
			// 统一使用 YYYY-MM-DD HH:mm:ss 格式
			const date = new Date(time)
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const hours = String(date.getHours()).padStart(2, '0')
			const minutes = String(date.getMinutes()).padStart(2, '0')
			const seconds = String(date.getSeconds()).padStart(2, '0')
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
		}
	}
}
</script>

<style scoped>
.ip-flow-rules-tab {
	padding: 20px;
}

.header-actions {
	margin-bottom: 20px;
}

.header-actions .el-button {
	margin-right: 10px;
}

.search-filters {
	margin-bottom: 20px;
	padding: 20px;
	background: #f5f7fa;
	border-radius: 4px;
}

.stats-info {
	display: flex;
	justify-content: space-around;
	align-items: center;
	height: 32px;
}

.stats-info span {
	font-size: 12px;
	color: #606266;
}

.pagination {
	margin-top: 20px;
	text-align: right;
}
</style>