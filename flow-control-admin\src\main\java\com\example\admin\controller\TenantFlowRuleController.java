package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.Result;
import com.example.admin.dto.TenantFlowRuleDTO;
import com.example.admin.service.TenantFlowRuleService;
import com.example.admin.vo.TenantFlowRuleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 租户流量规则控制器
 * 提供租户流量规则的CRUD操作、批量管理、统计分析等功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "租户流量规则管理")
@RestController
@RequestMapping("/api/tenant-flow-rules")
@Validated
public class TenantFlowRuleController {

    private static final Logger log = LoggerFactory.getLogger(TenantFlowRuleController.class);

    @Autowired
    private TenantFlowRuleService tenantFlowRuleService;

    /**
     * 分页查询租户流量规则
     */
    @Operation(summary = "分页查询租户流量规则")
    @GetMapping
    public Result<Page<TenantFlowRuleVO>> pageTenantFlowRules(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "规则名称") @RequestParam(required = false) String ruleName,
            // 租户限流为全局生效，移除资源匹配模式参数
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled,
            @Parameter(description = "流控效果") @RequestParam(required = false) Integer controlBehavior) {
        try {
            Page<TenantFlowRuleVO> pageParam = new Page<>(page, size);
            Page<TenantFlowRuleVO> result = tenantFlowRuleService.selectTenantFlowRulePage(
                pageParam, tenantId, ruleName, enabled, controlBehavior);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询租户流量规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询租户流量规则
     */
    @Operation(summary = "根据ID查询租户流量规则")
    @GetMapping("/{id}")
    public Result<TenantFlowRuleVO> getTenantFlowRuleById(
            @Parameter(description = "规则ID") @PathVariable @NotNull Long id) {
        try {
            TenantFlowRuleVO result = tenantFlowRuleService.getTenantFlowRuleById(id);
            if (result == null) {
                return Result.error("租户流量规则不存在");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询租户流量规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建租户流量规则
     */
    @Operation(summary = "创建租户流量规则")
    @PostMapping
    public Result<String> createTenantFlowRule(
            @Parameter(description = "租户流量规则信息") @RequestBody @Valid TenantFlowRuleDTO tenantFlowRuleDTO) {
        try {
            boolean result = tenantFlowRuleService.createTenantFlowRule(tenantFlowRuleDTO);
            if (result) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建租户流量规则失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新租户流量规则
     */
    @Operation(summary = "更新租户流量规则")
    @PutMapping("/{id}")
    public Result<String> updateTenantFlowRule(
            @Parameter(description = "规则ID") @PathVariable @NotNull Long id,
            @Parameter(description = "租户流量规则信息") @RequestBody @Valid TenantFlowRuleDTO tenantFlowRuleDTO) {
        try {
            boolean result = tenantFlowRuleService.updateTenantFlowRule(id, tenantFlowRuleDTO);
            if (result) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新租户流量规则失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除租户流量规则
     */
    @Operation(summary = "删除租户流量规则")
    @DeleteMapping("/{id}")
    public Result<String> deleteTenantFlowRule(
            @Parameter(description = "规则ID") @PathVariable @NotNull Long id) {
        try {
            boolean result = tenantFlowRuleService.deleteTenantFlowRule(id);
            if (result) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除租户流量规则失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除租户流量规则
     */
    @Operation(summary = "批量删除租户流量规则")
    @DeleteMapping("/batch")
    public Result<String> batchDeleteTenantFlowRules(
            @Parameter(description = "规则ID列表") @RequestBody @NotEmpty List<Long> ids) {
        try {
            boolean result = tenantFlowRuleService.batchDeleteTenantFlowRules(ids);
            if (result) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除租户流量规则失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用租户流量规则
     */
    @Operation(summary = "启用/禁用租户流量规则")
    @PutMapping("/{id}/status")
    public Result<String> updateTenantFlowRuleStatus(
            @Parameter(description = "规则ID") @PathVariable @NotNull Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer enabled) {
        try {
            boolean result;
            if (enabled == 1) {
                result = tenantFlowRuleService.enableTenantFlowRule(id);
            } else {
                result = tenantFlowRuleService.disableTenantFlowRule(id);
            }
            if (result) {
                String message = enabled == 1 ? "启用成功" : "禁用成功";
                return Result.success(message);
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("更新租户流量规则状态失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 批量启用/禁用租户流量规则
     */
    @Operation(summary = "批量启用/禁用租户流量规则")
    @PutMapping("/batch/status")
    public Result<String> batchUpdateTenantFlowRuleStatus(
            @Parameter(description = "规则ID列表") @RequestBody @NotEmpty List<Long> ids,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer enabled) {
        try {
            boolean result = tenantFlowRuleService.batchUpdateEnabled(ids, enabled);
            if (result) {
                String message = enabled == 1 ? "批量启用成功" : "批量禁用成功";
                return Result.success(message);
            } else {
                return Result.error("批量操作失败");
            }
        } catch (Exception e) {
            log.error("批量更新租户流量规则状态失败", e);
            return Result.error("批量操作失败: " + e.getMessage());
        }
    }

    /**
     * 根据租户ID查询租户流量规则
     */
    @Operation(summary = "根据租户ID查询租户流量规则")
    @GetMapping("/tenant/{tenantId}")
    public Result<List<TenantFlowRuleVO>> getTenantFlowRulesByTenantId(
            @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled,
            @Parameter(description = "限制数量") @RequestParam(required = false) Integer limit) {
        try {
            List<TenantFlowRuleVO> result = tenantFlowRuleService.getTenantFlowRulesByTenantId(tenantId, enabled, limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据租户ID查询租户流量规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据资源模式查询租户流量规则
     */
    // 租户限流为全局生效，不再需要根据资源模式查询的API端点
    // @Operation(summary = "根据资源模式查询租户流量规则")
    // @GetMapping("/resource/{resourcePattern}")
    // public Result<List<TenantFlowRuleVO>> getTenantFlowRulesByResourcePattern(...) { ... }

    /**
     * 获取有效流量规则列表
     */
    @Operation(summary = "获取有效流量规则列表")
    @GetMapping("/enabled")
    public Result<List<TenantFlowRuleVO>> getEnabledRules(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            // 租户限流为全局生效，移除资源匹配模式参数
            @Parameter(description = "限制数量") @RequestParam(required = false) Integer limit) {
        try {
            List<TenantFlowRuleVO> result = tenantFlowRuleService.getEnabledRules(tenantId, limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取有效流量规则列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按优先级排序查询流量规则
     */
    @Operation(summary = "按优先级排序查询流量规则")
    @GetMapping("/priority")
    public Result<List<TenantFlowRuleVO>> getRulesByPriorityOrder(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            // 租户限流为全局生效，移除资源匹配模式参数
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled,
            @Parameter(description = "限制数量") @RequestParam(required = false) Integer limit) {
        try {
            List<TenantFlowRuleVO> result = tenantFlowRuleService.getRulesByPriorityOrder(
                tenantId, enabled, limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("按优先级排序查询流量规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建租户流量规则
     */
    @Operation(summary = "批量创建租户流量规则")
    @PostMapping("/batch")
    public Result<String> batchCreateTenantFlowRules(
            @Parameter(description = "租户流量规则列表") @RequestBody @NotEmpty List<TenantFlowRuleDTO> tenantFlowRuleDTOList) {
        try {
            boolean result = tenantFlowRuleService.batchCreateTenantFlowRules(tenantFlowRuleDTOList);
            if (result) {
                return Result.success("批量创建成功，共创建" + tenantFlowRuleDTOList.size() + "条规则");
            } else {
                return Result.error("批量创建失败");
            }
        } catch (Exception e) {
            log.error("批量创建租户流量规则失败", e);
            return Result.error("批量创建失败: " + e.getMessage());
        }
    }

    /**
     * 复制租户流量规则
     */
    @Operation(summary = "复制租户流量规则")
    @PostMapping("/{id}/copy")
    public Result<String> copyTenantFlowRule(
            @Parameter(description = "源规则ID") @PathVariable @NotNull Long id,
            @Parameter(description = "新规则名称") @RequestParam @NotNull String newRuleName,
            @Parameter(description = "目标租户ID") @RequestParam(required = false) String targetTenantId) {
        try {
            boolean result = tenantFlowRuleService.copyTenantFlowRule(id, newRuleName, targetTenantId);
            if (result) {
                return Result.success("复制成功");
            } else {
                return Result.error("复制失败");
            }
        } catch (Exception e) {
            log.error("复制租户流量规则失败", e);
            return Result.error("复制失败: " + e.getMessage());
        }
    }

    /**
     * 批量复制到其他租户
     */
    @Operation(summary = "批量复制到其他租户")
    @PostMapping("/batch/copy")
    public Result<Map<String, Object>> batchCopyToTenant(
            @Parameter(description = "源规则ID列表") @RequestBody @NotEmpty List<Long> sourceIds,
            @Parameter(description = "目标租户ID") @RequestParam @NotNull String targetTenantId,
            @Parameter(description = "名称前缀") @RequestParam(defaultValue = "copy_") String namePrefix) {
        try {
            Map<String, Object> result = tenantFlowRuleService.batchCopyToTenant(sourceIds, targetTenantId, namePrefix);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量复制租户流量规则失败", e);
            return Result.error("批量复制失败: " + e.getMessage());
        }
    }

    /**
     * 导入租户流量规则
     */
    @Operation(summary = "导入租户流量规则")
    @PostMapping("/import")
    public Result<Map<String, Object>> importTenantFlowRules(
            @Parameter(description = "租户流量规则列表") @RequestBody @NotEmpty List<TenantFlowRuleDTO> tenantFlowRuleDTOList,
            @Parameter(description = "是否覆盖已存在的规则") @RequestParam(defaultValue = "false") boolean overwrite) {
        try {
            Map<String, Object> result = tenantFlowRuleService.importTenantFlowRules(tenantFlowRuleDTOList, overwrite);
            return Result.success(result);
        } catch (Exception e) {
            log.error("导入租户流量规则失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出租户流量规则
     */
    @Operation(summary = "导出租户流量规则")
    @GetMapping("/export")
    public Result<List<TenantFlowRuleDTO>> exportTenantFlowRules(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled
            // 租户限流为全局生效，移除资源匹配模式参数
            ) {
        try {
            List<TenantFlowRuleDTO> result = tenantFlowRuleService.exportTenantFlowRules(tenantId, enabled);
            return Result.success(result);
        } catch (Exception e) {
            log.error("导出租户流量规则失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    /**
     * 验证租户流量规则
     */
    @Operation(summary = "验证租户流量规则")
    @PostMapping("/validate")
    public Result<Map<String, Object>> validateTenantFlowRule(
            @Parameter(description = "租户流量规则信息") @RequestBody @Valid TenantFlowRuleDTO tenantFlowRuleDTO) {
        try {
            Map<String, Object> result = tenantFlowRuleService.validateTenantFlowRule(tenantFlowRuleDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("验证租户流量规则失败", e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取启用状态统计
     */
    @Operation(summary = "获取启用状态统计")
    @GetMapping("/statistics/enabled")
    public Result<List<Map<String, Object>>> getEnabledStatistics(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        try {
            List<Map<String, Object>> result = tenantFlowRuleService.getEnabledStatistics(tenantId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取启用状态统计失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户流量规则分布统计
     */
    @Operation(summary = "获取租户流量规则分布统计")
    @GetMapping("/statistics/tenant")
    public Result<List<Map<String, Object>>> getTenantFlowRuleStatistics(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> result = tenantFlowRuleService.getTenantFlowRuleStatistics(limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取租户流量规则分布统计失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取流控行为统计
     */
    @Operation(summary = "获取流控效果统计")
    @GetMapping("/statistics/control-behavior")
    public Result<List<Map<String, Object>>> getControlBehaviorStatistics(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        try {
            List<Map<String, Object>> result = tenantFlowRuleService.getFlowBehaviorStatistics(tenantId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取流控效果统计失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取最大优先级
     */
    @Operation(summary = "获取最大优先级")
    @GetMapping("/max-priority")
    public Result<Integer> getMaxPriority(
            @Parameter(description = "租户ID") @RequestParam @NotNull String tenantId) {
        try {
            Integer result = tenantFlowRuleService.getMaxPriority(tenantId);
            return Result.success(result != null ? result : 0);
        } catch (Exception e) {
            log.error("获取最大优先级失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按优先级范围查询流量规则
     */
    @Operation(summary = "按优先级范围查询流量规则")
    @GetMapping("/priority-range")
    public Result<List<TenantFlowRuleVO>> getRulesByPriorityRange(
            @Parameter(description = "租户ID") @RequestParam @NotNull String tenantId,
            @Parameter(description = "最小优先级") @RequestParam @NotNull Integer minPriority,
            @Parameter(description = "最大优先级") @RequestParam @NotNull Integer maxPriority,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled) {
        try {
            List<TenantFlowRuleVO> result = tenantFlowRuleService.getRulesByPriorityRange(
                tenantId, minPriority, maxPriority, enabled);
            return Result.success(result);
        } catch (Exception e) {
            log.error("按优先级范围查询流量规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取即将过期的流量规则
     */
    @Operation(summary = "获取即将过期的流量规则")
    @GetMapping("/expiring")
    public Result<List<TenantFlowRuleVO>> getExpiringRules(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "小时数") @RequestParam(defaultValue = "24") Integer hours) {
        try {
            List<TenantFlowRuleVO> result = tenantFlowRuleService.getExpiringRules(tenantId, hours);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取即将过期的流量规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 禁用过期流量规则
     */
    @Operation(summary = "禁用过期流量规则")
    @PutMapping("/disable-expired")
    public Result<String> disableExpiredRules() {
        try {
            int count = tenantFlowRuleService.disableExpiredRules();
            return Result.success("成功禁用" + count + "条过期规则");
        } catch (Exception e) {
            log.error("禁用过期流量规则失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前有效流量规则
     */
    @Operation(summary = "获取当前有效流量规则")
    @GetMapping("/valid")
    public Result<List<TenantFlowRuleVO>> getValidRules(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId
            // 租户限流为全局生效，移除资源匹配模式参数
            ) {
        try {
            List<TenantFlowRuleVO> result = tenantFlowRuleService.getValidRules(tenantId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取当前有效流量规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户流量规则汇总
     */
    @Operation(summary = "获取租户流量规则汇总")
    @GetMapping("/summary/{tenantId}")
    public Result<Map<String, Object>> getTenantFlowRuleSummary(
            @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId) {
        try {
            Map<String, Object> result = tenantFlowRuleService.getTenantFlowRuleSummary(tenantId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取租户流量规则汇总失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取全局流量规则汇总
     */
    @Operation(summary = "获取全局流量规则汇总")
    @GetMapping("/summary/global")
    public Result<Map<String, Object>> getGlobalFlowRuleSummary() {
        try {
            Map<String, Object> result = tenantFlowRuleService.getGlobalFlowRuleSummary();
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取全局流量规则汇总失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 同步流量规则到网关
     */
    @Operation(summary = "同步流量规则到网关")
    @PostMapping("/sync/{tenantId}")
    public Result<String> syncFlowRulesToGateway(
            @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId) {
        try {
            // 获取该租户的所有有效规则并同步到网关
            List<TenantFlowRuleVO> rules = tenantFlowRuleService.getValidRules(tenantId);
            // 这里应该调用网关的规则同步接口
            return Result.success("同步成功，共同步" + rules.size() + "条规则");
        } catch (Exception e) {
            log.error("同步流量规则到网关失败", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }
}