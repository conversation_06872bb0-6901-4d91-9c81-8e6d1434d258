package com.example.admin.dto;



import jakarta.validation.constraints.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 统计数据DTO
 */

public class StatisticsDTO {
    
    /**
     * 租户ID
     */
    @Size(max = 50, message = "租户ID长度不能超过50个字符")
    private String tenantId;
    
    /**
     * 资源名称
     */
    @Size(max = 200, message = "资源名称长度不能超过200个字符")
    private String resourceName;
    
    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;
    
    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;
    
    /**
     * 统计粒度：HOURLY-小时，DAILY-日
     */
    @NotBlank(message = "统计粒度不能为空")
    @Pattern(regexp = "^(HOURLY|DAILY)$", message = "统计粒度必须为HOURLY或DAILY")
    private String granularity;
    
    /**
     * 时间范围（分钟）- 用于实时监控
     */
    @Min(value = 1, message = "时间范围必须大于0")
    @Max(value = 1440, message = "时间范围不能超过1440分钟（24小时）")
    private Integer timeRange;
    
    /**
     * 开始时间 - 用于实时监控
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间 - 用于实时监控
     */
    private LocalDateTime endTime;
    
    /**
     * 验证日期范围
     */
    @AssertTrue(message = "结束日期必须大于或等于开始日期")
    public boolean isValidDateRange() {
        if (startDate == null || endDate == null) {
            return true; // 让@NotNull注解处理空值
        }
        return !endDate.isBefore(startDate);
    }
    
    /**
     * 验证时间范围
     */
    @AssertTrue(message = "结束时间必须大于开始时间")
    public boolean isValidTimeRange() {
        if (startTime == null || endTime == null) {
            return true;
        }
        return endTime.isAfter(startTime);
    }
}