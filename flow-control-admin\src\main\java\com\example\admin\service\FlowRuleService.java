package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.FlowRuleDTO;
import com.example.common.entity.FlowRule;
import com.example.admin.vo.FlowRuleVO;

import java.util.List;
import java.util.Map;

/**
 * 流控规则服务接口
 */
public interface FlowRuleService extends IService<FlowRule> {
    
    /**
     * 分页查询流控规则
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @param ruleName 规则名称
     * @param status 状态
     * @param limitMode 限流模式
     * @return 流控规则VO分页结果
     */
    Page<FlowRuleVO> selectFlowRulePage(Page<FlowRuleVO> page, String tenantId, String resourceName, 
                                        String ruleName, Integer status, Integer limitMode);
    
    /**
     * 根据ID查询流控规则详情
     *
     * @param id 规则ID
     * @return 流控规则VO
     */
    FlowRuleVO getFlowRuleById(Long id);
    
    /**
     * 创建流控规则
     *
     * @param flowRuleDTO 流控规则DTO
     * @return 是否成功
     */
    boolean createFlowRule(FlowRuleDTO flowRuleDTO);
    
    /**
     * 更新流控规则
     *
     * @param id 规则ID
     * @param flowRuleDTO 流控规则DTO
     * @return 是否成功
     */
    boolean updateFlowRule(Long id, FlowRuleDTO flowRuleDTO);
    
    /**
     * 删除流控规则
     *
     * @param id 规则ID
     * @return 是否成功
     */
    boolean deleteFlowRule(Long id);
    
    /**
     * 批量删除流控规则
     *
     * @param ids 规则ID列表
     * @return 是否成功
     */
    boolean batchDeleteFlowRules(List<Long> ids);
    
    /**
     * 启用流控规则
     *
     * @param id 规则ID
     * @return 是否成功
     */
    boolean enableFlowRule(Long id);
    
    /**
     * 禁用流控规则
     *
     * @param id 规则ID
     * @return 是否成功
     */
    boolean disableFlowRule(Long id);
    
    /**
     * 批量更新规则状态
     *
     * @param ids 规则ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Long> ids, Integer status);
    
    /**
     * 根据租户ID查询流控规则
     *
     * @param tenantId 租户ID
     * @param status 状态（可选）
     * @param limit 限制数量
     * @return 流控规则列表
     */
    List<FlowRuleVO> getFlowRulesByTenantId(String tenantId, Integer status, Integer limit);
    
    /**
     * 根据资源名称查询流控规则
     *
     * @param resourceName 资源名称
     * @param tenantId 租户ID（可选）
     * @param status 状态（可选）
     * @param limit 限制数量
     * @return 流控规则列表
     */
    List<FlowRuleVO> getFlowRulesByResourceName(String resourceName, String tenantId, Integer status, Integer limit);
    
    /**
     * 检查规则名称是否存在
     *
     * @param ruleName 规则名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByRuleName(String ruleName, Long excludeId);
    
    /**
     * 统计租户规则数量
     *
     * @param tenantId 租户ID
     * @param status 状态（可选）
     * @return 规则数量
     */
    int countByTenantId(String tenantId, Integer status);
    
    /**
     * 查询启用的规则（按优先级排序）
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param limit 限制数量
     * @return 启用的规则列表
     */
    List<FlowRuleVO> getEnabledRules(String tenantId, String resourceName, Integer limit);
    
    /**
     * 查询优先级排序的规则
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param status 状态（可选）
     * @param limit 限制数量
     * @return 优先级排序的规则列表
     */
    List<FlowRuleVO> getRulesByPriorityOrder(String tenantId, String resourceName, Integer status, Integer limit);
    
    /**
     * 统计规则状态分布
     *
     * @param tenantId 租户ID（可选）
     * @return 状态统计结果
     */
    List<Map<String, Object>> getStatusStatistics(String tenantId);
    
    /**
     * 统计租户规则分布
     *
     * @param limit 限制数量
     * @return 租户规则统计结果
     */
    List<Map<String, Object>> getTenantRuleStatistics(Integer limit);
    
    /**
     * 批量创建流控规则
     *
     * @param flowRuleDTOList 流控规则DTO列表
     * @return 是否成功
     */
    boolean batchCreateFlowRules(List<FlowRuleDTO> flowRuleDTOList);
    
    /**
     * 复制流控规则
     *
     * @param id 原规则ID
     * @param newRuleName 新规则名称
     * @return 是否成功
     */
    boolean copyFlowRule(Long id, String newRuleName);
    
    /**
     * 导入流控规则
     *
     * @param flowRuleDTOList 流控规则DTO列表
     * @param overwrite 是否覆盖已存在的规则
     * @return 导入结果
     */
    Map<String, Object> importFlowRules(List<FlowRuleDTO> flowRuleDTOList, boolean overwrite);
    
    /**
     * 导出流控规则
     *
     * @param tenantId 租户ID（可选）
     * @param status 状态（可选）
     * @return 流控规则列表
     */
    List<FlowRuleVO> exportFlowRules(String tenantId, Integer status);
    
    /**
     * 验证流控规则配置
     *
     * @param flowRuleDTO 流控规则DTO
     * @return 验证结果
     */
    Map<String, Object> validateFlowRule(FlowRuleDTO flowRuleDTO);
    
    /**
     * 同步规则到Sentinel
     *
     * @param id 规则ID
     * @return 是否成功
     */
    boolean syncRuleToSentinel(Long id);
    
    /**
     * 批量同步规则到Sentinel
     *
     * @param ids 规则ID列表
     * @return 同步结果
     */
    Map<String, Object> batchSyncRulesToSentinel(List<Long> ids);
    
    /**
     * 从Sentinel同步规则
     *
     * @param tenantId 租户ID
     * @return 同步结果
     */
    Map<String, Object> syncRulesFromSentinel(String tenantId);
}