package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.Result;
import com.example.admin.dto.IPFlowRuleDTO;
import com.example.admin.service.IPFlowRuleService;
import com.example.admin.vo.IPFlowRuleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * IP流量规则控制器
 * 提供IP流量规则的CRUD操作、批量管理、统计分析等功能
 */
@Tag(name = "IP流量规则管理")
@RestController
@RequestMapping("/api/ip-flow-rules")
@Validated
public class IPFlowRuleController {

	private static final Logger log = LoggerFactory.getLogger(IPFlowRuleController.class);

	@Autowired
	private IPFlowRuleService ipFlowRuleService;

	/**
	 * 分页查询IP流量规则
	 */
	@Operation(summary = "分页查询IP流量规则")
	@GetMapping
	public Result<Page<IPFlowRuleVO>> pageIPFlowRules(
			@Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
			@Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
			@Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
			@Parameter(description = "规则类型") @RequestParam(required = false) String ruleType,
			@Parameter(description = "规则状态") @RequestParam(required = false) Integer status) {
		try {
			Page<IPFlowRuleVO> pageParam = new Page<>(page, size);
			Page<IPFlowRuleVO> result = ipFlowRuleService.selectIPFlowRulePage(pageParam, tenantId, null, ruleType, status, null);
			return Result.success(result);
		} catch (Exception e) {
			log.error("分页查询IP流量规则失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 根据ID查询IP流量规则
	 */
	@Operation(summary = "根据ID查询IP流量规则")
	@GetMapping("/{id}")
	public Result<IPFlowRuleVO> getIPFlowRuleById(@Parameter(description = "规则ID") @PathVariable @NotNull Long id) {
		try {
			IPFlowRuleVO result = ipFlowRuleService.getIPFlowRuleById(id);
			if (result == null) {
				return Result.error("IP流量规则不存在");
			}
			return Result.success(result);
		} catch (Exception e) {
			log.error("查询IP流量规则失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 创建IP流量规则
	 */
	@Operation(summary = "创建IP流量规则")
	@PostMapping
	public Result<String> createIPFlowRule(
			@Parameter(description = "IP流量规则信息") @RequestBody @Valid IPFlowRuleDTO ipFlowRuleDTO) {
		try {
			boolean result = ipFlowRuleService.createIPFlowRule(ipFlowRuleDTO);
			if (result) {
				return Result.success("创建成功");
			} else {
				return Result.error("创建失败");
			}
		} catch (Exception e) {
			log.error("创建IP流量规则失败", e);
			return Result.error("创建失败: " + e.getMessage());
		}
	}

	/**
	 * 更新IP流量规则
	 */
	@Operation(summary = "更新IP流量规则")
	@PutMapping("/{id}")
	public Result<String> updateIPFlowRule(@Parameter(description = "规则ID") @PathVariable @NotNull Long id,
			@Parameter(description = "IP流量规则信息") @RequestBody @Valid IPFlowRuleDTO ipFlowRuleDTO) {
		try {
			boolean result = ipFlowRuleService.updateIPFlowRule(id, ipFlowRuleDTO);
			if (result) {
				return Result.success("更新成功");
			} else {
				return Result.error("更新失败");
			}
		} catch (Exception e) {
			log.error("更新IP流量规则失败", e);
			return Result.error("更新失败: " + e.getMessage());
		}
	}

	/**
	 * 删除IP流量规则
	 */
	@Operation(summary = "删除IP流量规则")
	@DeleteMapping("/{id}")
	public Result<String> deleteIPFlowRule(@Parameter(description = "规则ID") @PathVariable @NotNull Long id) {
		try {
			boolean result = ipFlowRuleService.deleteIPFlowRule(id);
			if (result) {
				return Result.success("删除成功");
			} else {
				return Result.error("删除失败");
			}
		} catch (Exception e) {
			log.error("删除IP流量规则失败", e);
			return Result.error("删除失败: " + e.getMessage());
		}
	}

	/**
	 * 批量删除IP流量规则
	 */
	@Operation(summary = "批量删除IP流量规则")
	@DeleteMapping("/batch")
	public Result<String> batchDeleteIPFlowRules(
			@Parameter(description = "规则ID列表") @RequestBody @NotEmpty List<Long> ids) {
		try {
			boolean result = ipFlowRuleService.batchDeleteIPFlowRules(ids);
			if (result) {
				return Result.success("批量删除成功");
			} else {
				return Result.error("批量删除失败");
			}
		} catch (Exception e) {
			log.error("批量删除IP流量规则失败", e);
			return Result.error("批量删除失败: " + e.getMessage());
		}
	}

	/**
	 * 启用/禁用IP流量规则
	 */
	@Operation(summary = "启用/禁用IP流量规则")
	@PutMapping("/{id}/status")
	public Result<String> toggleIPFlowRuleStatus(@Parameter(description = "规则ID") @PathVariable @NotNull Long id,
			@Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer status) {
		try {
			boolean result;
			if (status == 1) {
				result = ipFlowRuleService.enableIPFlowRule(id);
			} else {
				result = ipFlowRuleService.disableIPFlowRule(id);
			}
			if (result) {
				String message = status == 1 ? "启用成功" : "禁用成功";
				return Result.success(message);
			} else {
				return Result.error("操作失败");
			}
		} catch (Exception e) {
			log.error("更新IP流量规则状态失败", e);
			return Result.error("操作失败: " + e.getMessage());
		}
	}

	/**
	 * 批量启用/禁用IP流量规则
	 */
	@Operation(summary = "批量启用/禁用IP流量规则")
	@PutMapping("/batch/status")
	public Result<String> batchToggleIPFlowRuleStatus(
			@Parameter(description = "规则ID列表") @RequestBody @NotEmpty List<Long> ids,
			@Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer status) {
		try {
			boolean result = ipFlowRuleService.batchUpdateStatus(ids, status);
			if (result) {
				String message = status == 1 ? "批量启用成功" : "批量禁用成功";
				return Result.success(message);
			} else {
				return Result.error("批量操作失败");
			}
		} catch (Exception e) {
			log.error("批量更新IP流量规则状态失败", e);
			return Result.error("批量操作失败: " + e.getMessage());
		}
	}

	/**
	 * 根据租户ID查询IP流量规则
	 */
	@Operation(summary = "根据租户ID查询IP流量规则")
	@GetMapping("/tenant/{tenantId}")
	public Result<List<IPFlowRuleVO>> getIPFlowRulesByTenantId(
			@Parameter(description = "租户ID") @PathVariable @NotNull String tenantId,
			@Parameter(description = "状态") @RequestParam(required = false) Integer status,
			@Parameter(description = "限制数量") @RequestParam(defaultValue = "100") Integer limit) {
		try {
			List<IPFlowRuleVO> result = ipFlowRuleService.getIPFlowRulesByTenantId(tenantId, status, limit);
			return Result.success(result);
		} catch (Exception e) {
			log.error("根据租户ID查询IP流量规则失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 同步规则到Sentinel
	 */
	@Operation(summary = "同步规则到Sentinel")
	@PostMapping("/sync/{tenantId}")
	public Result<Map<String, Object>> syncRulesToSentinel(
			@Parameter(description = "租户ID") @PathVariable @NotNull String tenantId) {
		try {
			Map<String, Object> result = ipFlowRuleService.syncRulesFromSentinel(tenantId);
			return Result.success(result);
		} catch (Exception e) {
			log.error("同步IP规则到Sentinel失败", e);
			return Result.error("同步失败: " + e.getMessage());
		}
	}

	/**
	 * 导入IP流量规则
	 */
	@Operation(summary = "导入IP流量规则")
	@PostMapping("/import")
	public Result<Map<String, Object>> importIPFlowRules(
			@Parameter(description = "IP流量规则列表") @RequestBody @NotEmpty List<IPFlowRuleDTO> ipFlowRuleDTOs,
			@Parameter(description = "是否覆盖已存在的规则") @RequestParam(defaultValue = "false") boolean overwrite) {
		try {
			Map<String, Object> result = ipFlowRuleService.importIPFlowRules(ipFlowRuleDTOs, overwrite);
			return Result.success(result);
		} catch (Exception e) {
			log.error("导入IP流量规则失败", e);
			return Result.error("导入失败: " + e.getMessage());
		}
	}

	/**
	 * 导出IP流量规则
	 */
	@Operation(summary = "导出IP流量规则")
	@GetMapping("/export")
	public Result<List<IPFlowRuleVO>> exportIPFlowRules(
			@Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
			@Parameter(description = "状态") @RequestParam(required = false) Integer status) {
		try {
			List<IPFlowRuleVO> result = ipFlowRuleService.exportIPFlowRules(tenantId, status);
			return Result.success(result);
		} catch (Exception e) {
			log.error("导出IP流量规则失败", e);
			return Result.error("导出失败: " + e.getMessage());
		}
	}

	/**
	 * 获取IP流量规则统计
	 */
	@Operation(summary = "获取IP流量规则统计")
	@GetMapping("/statistics")
	public Result<Object> getIPFlowRuleStatistics(
			@Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
		try {
			Object result = ipFlowRuleService.getIPFlowRuleStatistics(tenantId);
			return Result.success(result);
		} catch (Exception e) {
			log.error("获取IP流量规则统计失败", e);
			return Result.error("统计失败: " + e.getMessage());
		}
	}

	/**
	 * 验证IP流量规则
	 */
	@Operation(summary = "验证IP流量规则")
	@PostMapping("/validate")
	public Result<Map<String, Object>> validateIPFlowRule(
			@Parameter(description = "IP流量规则信息") @RequestBody @Valid IPFlowRuleDTO ipFlowRuleDTO) {
		try {
			Map<String, Object> result = ipFlowRuleService.validateIPFlowRule(ipFlowRuleDTO);
			return Result.success(result);
		} catch (Exception e) {
			log.error("验证IP流量规则失败", e);
			return Result.error("验证失败: " + e.getMessage());
		}
	}

	/**
	 * 刷新规则缓存
	 */
	@Operation(summary = "刷新规则缓存")
	@PostMapping("/cache/refresh")
	public Result<String> refreshRuleCache(
			@Parameter(description = "租户ID，为空则刷新所有") @RequestParam(required = false) String tenantId) {
		try {
			ipFlowRuleService.syncRulesFromSentinel(tenantId);
			String message = tenantId != null ? "已刷新租户 " + tenantId + " 的规则缓存" : "已刷新所有规则缓存";
			return Result.success(message);
		} catch (Exception e) {
			log.error("刷新规则缓存失败", e);
			return Result.error("刷新失败: " + e.getMessage());
		}
	}
}