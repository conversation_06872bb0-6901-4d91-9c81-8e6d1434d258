import request from '@/utils/request'

// 接口限流规则API
export const interfaceRuleApi = {
  // 获取接口规则列表
  getInterfaceRuleList(params) {
    return request({
      url: '/api/flow-rules',
      method: 'get',
      params
    })
  },

  // 创建接口规则
  createInterfaceRule(data) {
    return request({
      url: '/api/flow-rules',
      method: 'post',
      data
    })
  },

  // 更新接口规则
  updateInterfaceRule(id, data) {
    return request({
      url: `/api/flow-rules/${id}`,
      method: 'put',
      data
    })
  },

  // 删除接口规则
  deleteInterfaceRule(id) {
    return request({
      url: `/api/flow-rules/${id}`,
      method: 'delete'
    })
  },

  // 更新接口规则状态
  updateInterfaceRuleStatus(id, status) {
    return request({
      url: `/api/flow-rules/${id}/status`,
      method: 'put',
      params: { status }
    })
  },

  // 获取租户列表
  getTenantList() {
    return request({
      url: '/api/tenant-configs',
      method: 'get'
    })
  },

  // 获取接口资源列表
  getInterfaceList(tenantId) {
    return request({
      url: '/api/interfaces',
      method: 'get',
      params: { tenantId }
    })
  }
}