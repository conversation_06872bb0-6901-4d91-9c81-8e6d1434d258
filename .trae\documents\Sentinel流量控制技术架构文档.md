# Sentinel流量控制技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[客户端请求] --> B[Spring Cloud Gateway]
    B --> C[Sentinel Gateway Filter]
    C --> D[多维度规则引擎]
    D --> E[分层限流检查器]
    E --> F[排队等待管理器]
    F --> G[后端微服务]
    
    H[配置管理中心] --> I[Nacos配置中心]
    I --> J[配置热更新推送]
    J --> C
    
    K[监控数据收集] --> L[Sentinel Dashboard]
    C --> K
    
    M[规则持久化] --> N[MySQL数据库]
    D --> M
    
    subgraph "网关层"
        B
        C
    end
    
    subgraph "流量控制核心"
        D
        E
        F
    end
    
    subgraph "配置管理层"
        H
        I
    end
    
    subgraph "监控层"
        K
        L
    end
    
    subgraph "数据持久层"
        M
        N
    end
```

## 2. 技术描述

* **前端**: React\@18 + Ant Design\@5 + TypeScript\@5 + Vite\@4

* **网关**: Spring Cloud Gateway\@3.1 + Spring Boot\@2.7

* **流量控制**: Sentinel\@1.8 + 自定义扩展组件

* **配置中心**: Nacos\@2.2

* **数据库**: MySQL\@8.0 + MyBatis-Plus\@3.5

* **监控**: Sentinel Dashboard + Micrometer + Prometheus

* **缓存**: Redis\@7.0

## 3. 路由定义

| 路由                   | 用途          |
| -------------------- | ----------- |
| /admin/flow-rules    | 流量控制规则管理页面  |
| /admin/monitor       | 实时监控仪表板页面   |
| /admin/config        | 系统配置管理页面    |
| /admin/alerts        | 告警管理页面      |
| /api/flow-rules/\*\* | 流量规则相关API接口 |
| /api/monitor/\*\*    | 监控数据查询API接口 |
| /api/config/\*\*     | 配置管理API接口   |

## 4. API定义

### 4.1 核心API

#### 流量规则管理

```
POST /api/flow-rules
```

创建流量控制规则

Request:

| 参数名               | 参数类型   | 是否必需  | 描述                           |
| ----------------- | ------ | ----- | ---------------------------- |
| tenantId          | string | true  | 租户ID                         |
| resource          | string | true  | 资源标识（接口地址）                   |
| limitApp          | string | false | 来源应用（IP地址等）                  |
| grade             | int    | true  | 限流模式：0-线程数 1-QPS             |
| count             | double | true  | 限流阈值                         |
| strategy          | int    | true  | 流控策略：0-直接 1-关联 2-链路          |
| controlBehavior   | int    | true  | 流控行为：0-快速失败 1-Warm Up 2-排队等待 |
| maxQueueingTimeMs | int    | false | 排队等待超时时间（毫秒）                 |
| warmUpPeriodSec   | int    | false | 预热时间（秒）                      |

Response:

| 参数名     | 参数类型     | 描述      |
| ------- | -------- | ------- |
| success | boolean  | 操作是否成功  |
| data    | FlowRule | 创建的规则对象 |
| message | string   | 响应消息    |

Example:

```json
{
  "tenantId": "tenant_001",
  "resource": "/api/user/info",
  "limitApp": "default",
  "grade": 1,
  "count": 10.0,
  "strategy": 0,
  "controlBehavior": 2,
  "maxQueueingTimeMs": 5000
}
```

```
GET /api/flow-rules
```

查询流量控制规则列表

Request:

| 参数名      | 参数类型   | 是否必需  | 描述       |
| -------- | ------ | ----- | -------- |
| tenantId | string | false | 租户ID过滤   |
| resource | string | false | 资源标识过滤   |
| pageNum  | int    | false | 页码，默认1   |
| pageSize | int    | false | 页大小，默认10 |

Response:

| 参数名     | 参数类型                 | 描述     |
| ------- | -------------------- | ------ |
| success | boolean              | 查询是否成功 |
| data    | PageResult<FlowRule> | 分页查询结果 |
| total   | long                 | 总记录数   |

#### 监控数据查询

```
GET /api/monitor/metrics
```

获取实时监控指标

Request:

| 参数名       | 参数类型   | 是否必需  | 描述    |
| --------- | ------ | ----- | ----- |
| tenantId  | string | false | 租户ID  |
| resource  | string | false | 资源标识  |
| startTime | long   | true  | 开始时间戳 |
| endTime   | long   | true  | 结束时间戳 |

Response:

| 参数名     | 参数类型        | 描述     |
| ------- | ----------- | ------ |
| success | boolean     | 查询是否成功 |
| data    | MetricsData | 监控指标数据 |

### 4.2 配置热更新API

```
POST /api/config/refresh
```

触发配置热更新

Request:

| 参数名        | 参数类型   | 是否必需  | 描述                             |
| ---------- | ------ | ----- | ------------------------------ |
| configType | string | true  | 配置类型：flow-rules, degrade-rules |
| tenantId   | string | false | 指定租户ID，为空则全量更新                 |

Response:

| 参数名           | 参数类型    | 描述       |
| ------------- | ------- | -------- |
| success       | boolean | 更新是否成功   |
| affectedNodes | int     | 影响的网关节点数 |
| message       | string  | 更新结果消息   |

## 5. 服务架构图

```mermaid
graph TD
    A[Gateway Controller] --> B[Flow Rule Service]
    A --> C[Monitor Service]
    A --> D[Config Service]
    
    B --> E[Rule Repository]
    B --> F[Sentinel Rule Manager]
    
    C --> G[Metrics Collector]
    C --> H[Data Aggregator]
    
    D --> I[Nacos Config Client]
    D --> J[Config Validator]
    
    E --> K[(MySQL Database)]
    G --> L[(Redis Cache)]
    I --> M[Nacos Server]
    
    subgraph "Controller Layer"
        A
    end
    
    subgraph "Service Layer"
        B
        C
        D
    end
    
    subgraph "Repository Layer"
        E
        G
        I
    end
    
    subgraph "External Dependencies"
        K
        L
        M
    end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    FLOW_RULE ||--o{ RULE_HISTORY : has
    TENANT ||--o{ FLOW_RULE : owns
    FLOW_RULE ||--o{ MONITOR_METRICS : generates
    
    FLOW_RULE {
        bigint id PK
        string tenant_id
        string resource
        string limit_app
        int grade
        double count
        int strategy
        int control_behavior
        int max_queueing_time_ms
        int warm_up_period_sec
        boolean enabled
        datetime create_time
        datetime update_time
    }
    
    TENANT {
        string tenant_id PK
        string tenant_name
        double total_qps_limit
        boolean enabled
        datetime create_time
        datetime update_time
    }
    
    RULE_HISTORY {
        bigint id PK
        bigint rule_id FK
        string operation_type
        text old_config
        text new_config
        string operator
        datetime create_time
    }
    
    MONITOR_METRICS {
        bigint id PK
        string tenant_id
        string resource
        double qps
        double success_qps
        double block_qps
        double avg_rt
        int queue_length
        datetime timestamp
    }
```

### 6.2 数据定义语言

#### 流量规则表 (flow\_rules)

```sql
-- 创建流量规则表
CREATE TABLE flow_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    resource VARCHAR(256) NOT NULL COMMENT '资源标识',
    limit_app VARCHAR(128) DEFAULT 'default' COMMENT '来源应用',
    grade INT NOT NULL DEFAULT 1 COMMENT '限流模式：0-线程数 1-QPS',
    count DOUBLE NOT NULL COMMENT '限流阈值',
    strategy INT NOT NULL DEFAULT 0 COMMENT '流控策略：0-直接 1-关联 2-链路',
    control_behavior INT NOT NULL DEFAULT 0 COMMENT '流控行为：0-快速失败 1-Warm Up 2-排队等待',
    max_queueing_time_ms INT DEFAULT 0 COMMENT '排队等待超时时间（毫秒）',
    warm_up_period_sec INT DEFAULT 10 COMMENT '预热时间（秒）',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_tenant_resource (tenant_id, resource),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_resource (resource)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流量控制规则表';

-- 创建租户表
CREATE TABLE tenants (
    tenant_id VARCHAR(64) PRIMARY KEY COMMENT '租户ID',
    tenant_name VARCHAR(128) NOT NULL COMMENT '租户名称',
    total_qps_limit DOUBLE DEFAULT 100.0 COMMENT '租户总QPS限制',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户信息表';

-- 创建规则历史表
CREATE TABLE rule_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_id BIGINT NOT NULL COMMENT '规则ID',
    operation_type VARCHAR(32) NOT NULL COMMENT '操作类型：CREATE, UPDATE, DELETE',
    old_config TEXT COMMENT '旧配置JSON',
    new_config TEXT COMMENT '新配置JSON',
    operator VARCHAR(64) COMMENT '操作人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_rule_id (rule_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则变更历史表';

-- 创建监控指标表
CREATE TABLE monitor_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    resource VARCHAR(256) NOT NULL COMMENT '资源标识',
    qps DOUBLE DEFAULT 0 COMMENT '每秒请求数',
    success_qps DOUBLE DEFAULT 0 COMMENT '成功QPS',
    block_qps DOUBLE DEFAULT 0 COMMENT '被限流QPS',
    avg_rt DOUBLE DEFAULT 0 COMMENT '平均响应时间',
    queue_length INT DEFAULT 0 COMMENT '排队长度',
    timestamp DATETIME NOT NULL COMMENT '统计时间点',
    INDEX idx_tenant_resource_time (tenant_id, resource, timestamp),
    INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控指标表';

-- 初始化数据
INSERT INTO tenants (tenant_id, tenant_name, total_qps_limit) VALUES 
('tenant_001', '测试租户1', 100.0),
('tenant_002', '测试租户2', 200.0);

INSERT INTO flow_rules (tenant_id, resource, count, control_behavior, max_queueing_time_ms) VALUES 
('tenant_001', '/api/user/info', 10.0, 2, 5000),
('tenant_001', '/api/order/create', 2.0, 2, 3000),
('tenant_002', '/api/product/list', 20.0, 2, 5000);
```

