package com.example.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.dto.TenantConfigDTO;
import com.example.common.entity.TenantConfig;
import com.example.admin.mapper.TenantConfigMapper;
import com.example.admin.service.TenantConfigService;
import com.example.admin.vo.TenantConfigVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户配置服务实现类
 */
@Service
public class TenantConfigServiceImpl extends ServiceImpl<TenantConfigMapper, TenantConfig> implements TenantConfigService {
    
    @Resource
    private TenantConfigMapper tenantConfigMapper;
    
    @Override
    public Page<TenantConfigVO> selectTenantConfigPage(Page<TenantConfigVO> page, String tenantName, Integer status) {
        Page<TenantConfig> tenantConfigPage = new Page<>(page.getCurrent(), page.getSize());
        Page<TenantConfig> resultPage = tenantConfigMapper.selectTenantConfigPage(tenantConfigPage, tenantName, status);
        
        Page<TenantConfigVO> voPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        List<TenantConfigVO> voList = resultPage.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        voPage.setRecords(voList);
        
        return voPage;
    }
    
    @Override
    public TenantConfigVO getTenantConfigById(Long id) {
        TenantConfig tenantConfig = this.getById(id);
        if (tenantConfig == null) {
            return null;
        }
        return convertToVO(tenantConfig);
    }
    
    @Override
    public TenantConfigVO getTenantConfigByTenantId(String tenantId) {
        TenantConfig tenantConfig = tenantConfigMapper.selectByTenantId(tenantId);
        if (tenantConfig == null) {
            return null;
        }
        return convertToVO(tenantConfig);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createTenantConfig(TenantConfigDTO tenantConfigDTO) {
        // 检查租户ID是否已存在
        if (existsByTenantId(tenantConfigDTO.getTenantId(), null)) {
            throw new RuntimeException("租户ID已存在");
        }
        
        // 检查租户名称是否已存在
        if (existsByTenantName(tenantConfigDTO.getTenantName(), null)) {
            throw new RuntimeException("租户名称已存在");
        }
        
        TenantConfig tenantConfig = new TenantConfig();
        BeanUtils.copyProperties(tenantConfigDTO, tenantConfig);
        tenantConfig.setCreateTime(LocalDateTime.now());
        tenantConfig.setUpdateTime(LocalDateTime.now());
        
        return this.save(tenantConfig);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTenantConfig(Long id, TenantConfigDTO tenantConfigDTO) {
        TenantConfig existingTenant = this.getById(id);
        if (existingTenant == null) {
            throw new RuntimeException("租户配置不存在");
        }
        
        // 检查租户ID是否已存在（排除当前租户）
        if (existsByTenantId(tenantConfigDTO.getTenantId(), id)) {
            throw new RuntimeException("租户ID已存在");
        }
        
        // 检查租户名称是否已存在（排除当前租户）
        if (existsByTenantName(tenantConfigDTO.getTenantName(), id)) {
            throw new RuntimeException("租户名称已存在");
        }
        
        BeanUtils.copyProperties(tenantConfigDTO, existingTenant);
        existingTenant.setId(id);
        existingTenant.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(existingTenant);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTenantConfig(Long id) {
        TenantConfig tenantConfig = this.getById(id);
        if (tenantConfig == null) {
            throw new RuntimeException("租户配置不存在");
        }
        
        // TODO: 检查是否有关联的规则
        // if (hasAssociatedRules(tenantConfig.getTenantId())) {
        //     throw new RuntimeException("该租户下存在关联的规则，无法删除");
        // }
        
        return this.removeById(id);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteTenantConfigs(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        
        // TODO: 检查每个租户是否有关联的规则
        // for (Long id : ids) {
        //     TenantConfig tenantConfig = this.getById(id);
        //     if (tenantConfig != null && hasAssociatedRules(tenantConfig.getTenantId())) {
        //         throw new RuntimeException("租户 " + tenantConfig.getTenantName() + " 下存在关联的规则，无法删除");
        //     }
        // }
        
        return this.removeByIds(ids);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableTenant(Long id) {
        return updateTenantStatus(id, 1);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableTenant(Long id) {
        return updateTenantStatus(id, 0);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> ids, Integer status) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        return tenantConfigMapper.batchUpdateStatus(ids, status, "system") > 0;
    }
    
    @Override
    public boolean existsByTenantId(String tenantId, Long excludeId) {
        return tenantConfigMapper.existsByTenantId(tenantId, excludeId) > 0;
    }
    
    @Override
    public boolean existsByTenantName(String tenantName, Long excludeId) {
        return tenantConfigMapper.existsByTenantName(tenantName, excludeId) > 0;
    }
    
    @Override
    public int getTotalTenantCount() {
        return tenantConfigMapper.getTotalCount();
    }
    
    @Override
    public long getTenantCount() {
        return (long) tenantConfigMapper.getTotalCount();
    }
    
    @Override
    public int getEnabledTenantCount() {
        return tenantConfigMapper.getEnabledCount();
    }
    
    @Override
    public int countByStatus(Integer status) {
        return tenantConfigMapper.countByStatus(status);
    }
    
    @Override
    public long getTenantCountByStatus(Integer status) {
        return (long) tenantConfigMapper.countByStatus(status);
    }
    
    /**
     * 更新租户状态
     */
    private boolean updateTenantStatus(Long id, Integer status) {
        TenantConfig tenantConfig = this.getById(id);
        if (tenantConfig == null) {
            throw new RuntimeException("租户配置不存在");
        }
        
        tenantConfig.setStatus(status);
        tenantConfig.setUpdateTime(LocalDateTime.now());
        return this.updateById(tenantConfig);
    }
    
    /**
     * 将TenantConfig实体转换为TenantConfigVO视图对象
     * @param tenantConfig 租户配置实体
     * @return 租户配置视图对象
     */
    private TenantConfigVO convertToVO(TenantConfig tenantConfig) {
        if (tenantConfig == null) {
            return null;
        }
        TenantConfigVO vo = new TenantConfigVO();
        BeanUtils.copyProperties(tenantConfig, vo);
        return vo;
    }
}