<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.SystemConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.common.entity.SystemConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="config_key" property="configKey" jdbcType="VARCHAR"/>
        <result column="config_value" property="configValue" jdbcType="VARCHAR"/>
        <result column="config_type" property="configType" jdbcType="VARCHAR"/>
        <result column="config_name" property="configName" jdbcType="VARCHAR"/>
        <result column="config_desc" property="configDesc" jdbcType="VARCHAR"/>
        <result column="default_value" property="defaultValue" jdbcType="VARCHAR"/>
        <result column="value_type" property="valueType" jdbcType="VARCHAR"/>
        <result column="value_range" property="valueRange" jdbcType="VARCHAR"/>
        <result column="is_system" property="isSystem" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- SystemConfigVO结果映射 -->
    <resultMap id="VOResultMap" type="com.example.admin.vo.SystemConfigVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="config_key" property="configKey" jdbcType="VARCHAR"/>
        <result column="config_value" property="configValue" jdbcType="VARCHAR"/>
        <result column="config_type" property="configType" jdbcType="VARCHAR"/>
        <result column="config_desc" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="is_system" property="isSystem" jdbcType="INTEGER"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
        <result column="default_value" property="defaultValue" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, config_key, config_value, config_type, config_name, config_desc,
        default_value, value_type, value_range, is_system, status, sort_order,
        remark, create_by, create_time, update_by, update_time, deleted
    </sql>

    <!-- 分页查询系统配置 -->
    <select id="selectConfigPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE deleted = 0
        <if test="configType != null and configType != ''">
            AND config_type = #{configType}
        </if>
        <if test="configKey != null and configKey != ''">
            AND config_key LIKE CONCAT('%', #{configKey}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据配置键查询配置 -->
    <select id="selectByConfigKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE config_key = #{configKey} AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据配置键和租户ID查询配置VO -->
    <select id="selectByConfigKeyAndTenantId" resultMap="VOResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE config_key = #{configKey} AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据配置类型查询配置列表 -->
    <select id="selectByConfigType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE config_type = #{configType} AND deleted = 0
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 查询所有启用的配置 -->
    <select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE status = 1 AND deleted = 0
        ORDER BY config_type, sort_order ASC
    </select>

    <!-- 批量更新配置状态 -->
    <update id="batchUpdateStatus">
        UPDATE system_config
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 根据配置键更新配置值 -->
    <update id="updateValueByKey">
        UPDATE system_config
        SET config_value = #{configValue}, update_by = #{updateBy}, update_time = NOW()
        WHERE config_key = #{configKey} AND deleted = 0
    </update>

    <!-- 检查配置键是否存在 -->
    <select id="existsByConfigKey" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM system_config
        WHERE config_key = #{configKey} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 查询配置类型列表 -->
    <select id="selectConfigTypes" resultType="java.lang.String">
        SELECT DISTINCT config_type
        FROM system_config
        WHERE deleted = 0
        ORDER BY config_type
    </select>

    <!-- 统计各种状态的配置数量 -->
    <select id="selectStatusStatistics" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count,
            CASE status
                WHEN 0 THEN '禁用'
                WHEN 1 THEN '启用'
                ELSE '未知'
            END as status_name
        FROM system_config
        WHERE deleted = 0
        GROUP BY status
        ORDER BY status
    </select>

    <!-- 统计各种类型的配置数量 -->
    <select id="selectTypeStatistics" resultType="java.util.Map">
        SELECT 
            config_type,
            COUNT(*) as count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as enabled_count,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as disabled_count
        FROM system_config
        WHERE deleted = 0
        GROUP BY config_type
        ORDER BY config_type
    </select>

    <!-- 查询最近更新的配置 -->
    <select id="selectRecentlyUpdated" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE deleted = 0
        ORDER BY update_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询系统默认配置 -->
    <select id="selectSystemDefaults" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE is_system = 1 AND deleted = 0
        ORDER BY config_type, sort_order ASC
    </select>

    <!-- 查询租户相关配置 -->
    <select id="selectTenantConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE deleted = 0
        AND (config_type = 'TENANT' OR config_key LIKE 'tenant.%')
        AND status = 1
        ORDER BY sort_order ASC
    </select>

    <!-- 查询流控相关配置 -->
    <select id="selectFlowControlConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE deleted = 0
        AND (config_type = 'FLOW_CONTROL' OR config_key LIKE 'flow.%' OR config_key LIKE 'sentinel.%')
        AND status = 1
        ORDER BY sort_order ASC
    </select>

    <!-- 查询监控相关配置 -->
    <select id="selectMonitorConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE deleted = 0
        AND (config_type = 'MONITOR' OR config_key LIKE 'monitor.%')
        AND status = 1
        ORDER BY sort_order ASC
    </select>

    <!-- 查询告警相关配置 -->
    <select id="selectAlarmConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE deleted = 0
        AND (config_type = 'ALARM' OR config_key LIKE 'alarm.%')
        AND status = 1
        ORDER BY sort_order ASC
    </select>

    <!-- 批量插入或更新配置 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO system_config (
            config_key, config_value, config_type, config_name, config_desc,
            default_value, value_type, value_range, is_system, status, sort_order,
            remark, create_by, create_time, update_by, update_time, deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.configKey}, #{item.configValue}, #{item.configType}, #{item.configName},
                #{item.configDesc}, #{item.defaultValue}, #{item.valueType}, #{item.valueRange},
                #{item.isSystem}, #{item.status}, #{item.sortOrder}, #{item.remark},
                #{item.createBy}, NOW(), #{item.updateBy}, NOW(), 0
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            config_value = VALUES(config_value),
            config_name = VALUES(config_name),
            config_desc = VALUES(config_desc),
            status = VALUES(status),
            update_by = VALUES(update_by),
            update_time = NOW()
    </insert>

    <!-- 重置配置为默认值 -->
    <update id="resetToDefault">
        UPDATE system_config
        SET config_value = default_value, update_by = #{updateBy}, update_time = NOW()
        WHERE config_key IN
        <foreach collection="configKeys" item="configKey" open="(" separator="," close=")">
            #{configKey}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 查询配置变更历史（如果需要的话） -->
    <select id="selectConfigHistory" resultType="java.util.Map">
        SELECT 
            config_key,
            config_value,
            update_by,
            update_time,
            'CURRENT' as change_type
        FROM system_config
        WHERE config_key = #{configKey} AND deleted = 0
        ORDER BY update_time DESC
        LIMIT #{limit}
    </select>

    <!-- 导出配置数据 -->
    <select id="selectForExport" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM system_config
        WHERE deleted = 0
        <if test="configType != null and configType != ''">
            AND config_type = #{configType}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY config_type, sort_order ASC
    </select>

    <!-- 验证配置值格式 -->
    <select id="validateConfigValue" resultType="java.lang.Boolean">
        SELECT 
            CASE 
                WHEN sc.value_type = 'INTEGER' THEN #{configValue} REGEXP '^[0-9]+$'
                WHEN sc.value_type = 'DOUBLE' THEN #{configValue} REGEXP '^[0-9]+(\\.[0-9]+)?$'
                WHEN sc.value_type = 'BOOLEAN' THEN #{configValue} IN ('true', 'false', '1', '0')
                WHEN sc.value_type = 'JSON' THEN JSON_VALID(#{configValue})
                WHEN sc.value_range IS NOT NULL THEN FIND_IN_SET(#{configValue}, sc.value_range) > 0
                ELSE TRUE
            END as is_valid
        FROM system_config sc
        WHERE sc.config_key = #{configKey} AND sc.deleted = 0
        LIMIT 1
    </select>

</mapper>