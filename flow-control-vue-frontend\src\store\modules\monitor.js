const state = {
  realTimeData: {
    qps: 0,
    rt: 0,
    successRate: 0,
    errorRate: 0
  },
  historicalData: [],
  alerts: [],
  loading: false
}

const mutations = {
  SET_REAL_TIME_DATA(state, data) {
    state.realTimeData = { ...state.realTimeData, ...data }
  },
  SET_HISTORICAL_DATA(state, data) {
    state.historicalData = data
  },
  ADD_HISTORICAL_DATA(state, data) {
    state.historicalData.push(data)
    // 保持最近1000条记录
    if (state.historicalData.length > 1000) {
      state.historicalData.shift()
    }
  },
  SET_ALERTS(state, alerts) {
    state.alerts = alerts
  },
  ADD_ALERT(state, alert) {
    state.alerts.unshift(alert)
    // 保持最近100条告警
    if (state.alerts.length > 100) {
      state.alerts.pop()
    }
  },
  CLEAR_ALERTS(state) {
    state.alerts = []
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  }
}

const actions = {
  async fetchRealTimeData({ commit }) {
    try {
      // 调用真实的API获取实时数据
      // const response = await api.monitor.getRealTimeData()
      // const latestData = response.data[response.data.length - 1]
      
      // 临时使用模拟数据，等待后端API完成
      const data = {
        qps: Math.floor(Math.random() * 1000),
        rt: Math.floor(Math.random() * 100),
        successRate: 95 + Math.random() * 5,
        errorRate: Math.random() * 5
      }
      
      commit('SET_REAL_TIME_DATA', data)
      commit('ADD_HISTORICAL_DATA', {
        timestamp: Date.now(),
        ...data
      })
    } catch (error) {
      console.error('获取实时数据失败:', error)
      // 发生错误时使用默认值
      commit('SET_REAL_TIME_DATA', {
        qps: 0,
        rt: 0,
        successRate: 0,
        errorRate: 0
      })
    }
  },
  async fetchHistoricalData({ commit }, { startTime, endTime }) {
    // eslint-disable-next-line no-unused-vars
    commit('SET_LOADING', true)
    try {
      // 调用真实的API获取历史数据
      // const response = await api.monitor.getHistoryData({
      //   startTime,
      //   endTime
      // })
      // commit('SET_HISTORICAL_DATA', response.data)
      
      // 临时使用空数据
      const data = []
      commit('SET_HISTORICAL_DATA', data)
    } catch (error) {
      console.error('获取历史数据失败:', error)
      commit('SET_HISTORICAL_DATA', [])
    } finally {
      commit('SET_LOADING', false)
    }
  },
  async fetchAlerts({ commit }) {
    try {
      // 调用真实的API获取告警信息
      // const response = await api.monitor.getAlerts({
      //   page: 1,
      //   size: 10,
      //   status: 'unread'
      // })
      // commit('SET_ALERTS', response.data.list)
      
      // 临时使用模拟告警数据
      const alerts = [
        {
          id: 1,
          level: '警告',
          message: 'QPS超过阈值1000',
          time: Date.now() - 300000,
          status: 'unread'
        },
        {
          id: 2,
          level: '信息',
          message: '系统运行正常',
          time: Date.now() - 600000,
          status: 'read'
        }
      ]
      commit('SET_ALERTS', alerts)
    } catch (error) {
      console.error('获取告警信息失败:', error)
      commit('SET_ALERTS', [])
    }
  }
}

const getters = {
  realTimeData: state => state.realTimeData,
  historicalData: state => state.historicalData,
  alerts: state => state.alerts,
  isLoading: state => state.loading
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}