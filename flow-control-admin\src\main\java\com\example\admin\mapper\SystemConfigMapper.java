package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.entity.SystemConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统配置Mapper接口
 */
@Mapper
public interface SystemConfigMapper extends BaseMapper<SystemConfig> {
    
    /**
     * 分页查询系统配置
     *
     * @param page 分页参数
     * @param configType 配置类型
     * @param configKey 配置键
     * @param status 状态
     * @return 系统配置列表
     */
    Page<SystemConfig> selectConfigPage(Page<SystemConfig> page,
                                        @Param("configType") String configType,
                                        @Param("configKey") String configKey,
                                        @Param("status") Integer status);
    
    /**
     * 分页查询系统配置VO
     *
     * @param page 分页参数
     * @param configKey 配置键
     * @param configType 配置类型
     * @param tenantId 租户ID
     * @param status 状态
     * @param description 描述
     * @return 系统配置VO分页结果
     */
    Page<com.example.admin.vo.SystemConfigVO> selectSystemConfigPage(Page<com.example.admin.vo.SystemConfigVO> page,
                                                                      @Param("configKey") String configKey,
                                                                      @Param("configType") String configType,
                                                                      @Param("tenantId") String tenantId,
                                                                      @Param("status") Integer status,
                                                                      @Param("description") String description);
    
    /**
     * 根据配置键查询配置
     *
     * @param configKey 配置键
     * @return 系统配置
     */
    SystemConfig selectByConfigKey(@Param("configKey") String configKey);
    
    /**
     * 根据配置键和租户ID查询配置VO
     *
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @return 系统配置VO
     */
    com.example.admin.vo.SystemConfigVO selectByConfigKeyAndTenantId(@Param("configKey") String configKey, @Param("tenantId") String tenantId);
    
    /**
     * 根据配置类型查询配置列表
     *
     * @param configType 配置类型
     * @param tenantId 租户ID
     * @param status 状态（可选）
     * @return 系统配置VO列表
     */
    List<com.example.admin.vo.SystemConfigVO> selectByConfigType(@Param("configType") String configType,
                                                                  @Param("tenantId") String tenantId,
                                                                  @Param("status") Integer status);
    
    /**
     * 查询所有启用的配置
     *
     * @return 启用的配置列表
     */
    List<SystemConfig> selectAllEnabled();
    
    /**
     * 更新配置状态
     *
     * @param id 配置ID
     * @param status 状态
     * @param updateTime 更新时间
     * @return 更新数量
     */
    int updateConfigStatus(@Param("id") Long id,
                           @Param("status") Integer status,
                           @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 批量更新配置状态
     *
     * @param ids 配置ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids,
                          @Param("status") Integer status,
                          @Param("updateBy") String updateBy);
    
    /**
     * 批量更新配置状态（带时间）
     *
     * @param ids 配置ID列表
     * @param status 状态
     * @param updateTime 更新时间
     * @return 更新数量
     */
    int batchUpdateConfigStatus(@Param("ids") List<Long> ids,
                                @Param("status") Integer status,
                                @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 根据配置键更新配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @param updateBy 更新人
     * @return 更新数量
     */
    int updateValueByKey(@Param("configKey") String configKey,
                         @Param("configValue") String configValue,
                         @Param("updateBy") String updateBy);
    
    /**
     * 检查配置键是否存在
     *
     * @param configKey 配置键
     * @param tenantId 租户ID
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    int existsByConfigKey(@Param("configKey") String configKey,
                          @Param("tenantId") String tenantId,
                          @Param("excludeId") Long excludeId);
    
    /**
     * 查询配置类型列表
     *
     * @return 配置类型列表
     */
    List<String> selectConfigTypes();
    
    /**
     * 统计各种状态的配置数量
     *
     * @return 状态统计结果
     */
    List<java.util.Map<String, Object>> selectStatusStatistics();
    
    /**
     * 统计各种类型的配置数量
     *
     * @return 类型统计结果
     */
    List<java.util.Map<String, Object>> selectTypeStatistics();
    
    /**
     * 查询最近更新的配置
     *
     * @param limit 限制数量
     * @return 最近更新的配置列表
     */
    List<SystemConfig> selectRecentlyUpdated(@Param("limit") int limit);
    
    /**
     * 查询系统默认配置
     *
     * @return 系统默认配置列表
     */
    List<SystemConfig> selectSystemDefaults();
    
    /**
     * 查询租户相关配置
     *
     * @param tenantId 租户ID
     * @return 租户相关配置列表
     */
    List<SystemConfig> selectTenantConfigs(@Param("tenantId") String tenantId);
    
    /**
     * 查询流控相关配置
     *
     * @return 流控相关配置列表
     */
    List<SystemConfig> selectFlowControlConfigs();
    
    /**
     * 查询监控相关配置
     *
     * @return 监控相关配置列表
     */
    List<SystemConfig> selectMonitorConfigs();
    
    /**
     * 查询告警相关配置
     *
     * @return 告警相关配置列表
     */
    List<SystemConfig> selectAlarmConfigs();
    
    /**
     * 批量插入或更新配置
     *
     * @param configList 配置列表
     * @return 操作数量
     */
    int batchInsertOrUpdate(@Param("list") List<SystemConfig> configList);
    
    /**
     * 重置配置为默认值
     *
     * @param configKeys 配置键列表
     * @param updateBy 更新人
     * @return 重置数量
     */
    int resetToDefault(@Param("configKeys") List<String> configKeys,
                       @Param("updateBy") String updateBy);
    
    /**
     * 查询配置变更历史（如果需要的话）
     *
     * @param configKey 配置键
     * @param limit 限制数量
     * @return 配置变更历史
     */
    List<java.util.Map<String, Object>> selectConfigHistory(@Param("configKey") String configKey,
                                                             @Param("limit") int limit);
    
    /**
     * 导出配置数据
     *
     * @param configType 配置类型（可选）
     * @param status 状态（可选）
     * @return 配置数据列表
     */
    List<SystemConfig> selectForExport(@Param("configType") String configType,
                                       @Param("status") Integer status);
    
    /**
     * 验证配置值格式
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 是否有效
     */
    boolean validateConfigValue(@Param("configKey") String configKey,
                                @Param("configValue") String configValue);
}