package com.example.admin.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.regex.Pattern;

/**
 * IP工具类 提供IP地址相关的工具方法
 */
public class IPUtils {

	private static final Logger log = LoggerFactory.getLogger(IPUtils.class);

	/** 未知IP */
	private static final String UNKNOWN = "unknown";

	/** 本地IP */
	private static final String LOCALHOST_IP = "127.0.0.1";
	private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

	/** IP地址正则表达式 */
	private static final Pattern IP_PATTERN = Pattern
			.compile("^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$");

	/** 私有IP地址范围 */
	private static final String[] PRIVATE_IP_PREFIXES = { "10.", "192.168.", "172.16.", "172.17.", "172.18.", "172.19.",
			"172.20.", "172.21.", "172.22.", "172.23.", "172.24.", "172.25.", "172.26.", "172.27.", "172.28.",
			"172.29.", "172.30.", "172.31." };

	/**
	 * 获取客户端真实IP地址 考虑代理服务器的情况
	 */
	public static String getClientIP(HttpServletRequest request) {
		if (request == null) {
			return LOCALHOST_IP;
		}

		String ip = null;

		// X-Forwarded-For：Squid 服务代理
		String ipAddresses = request.getHeader("X-Forwarded-For");
		if (ipAddresses == null || ipAddresses.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddresses)) {
			// Proxy-Client-IP：apache 服务代理
			ipAddresses = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddresses == null || ipAddresses.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddresses)) {
			// WL-Proxy-Client-IP：weblogic 服务代理
			ipAddresses = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddresses == null || ipAddresses.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddresses)) {
			// HTTP_CLIENT_IP：有些代理服务器
			ipAddresses = request.getHeader("HTTP_CLIENT_IP");
		}
		if (ipAddresses == null || ipAddresses.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddresses)) {
			// X-Real-IP：nginx服务代理
			ipAddresses = request.getHeader("X-Real-IP");
		}

		// 有些网络通过多层代理，那么获取到的ip就会有多个，一般都是通过逗号（,）分割开来，并且第一个ip为客户端的真实IP
		if (ipAddresses != null && ipAddresses.length() != 0) {
			ip = ipAddresses.split(",")[0];
		}

		// 还是不能获取到，最后再通过request.getRemoteAddr();获取
		if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}

		// 处理IPv6本地地址
		if (LOCALHOST_IPV6.equals(ip)) {
			ip = LOCALHOST_IP;
		}

		return ip;
	}

	/**
	 * 验证IP地址格式是否正确
	 */
	public static boolean isValidIP(String ip) {
		if (!StringUtils.hasText(ip)) {
			return false;
		}
		return IP_PATTERN.matcher(ip).matches();
	}

	/**
	 * 判断是否为内网IP
	 */
	public static boolean isInternalIP(String ip) {
		if (!isValidIP(ip)) {
			return false;
		}

		// 本地回环地址
		if (ip.startsWith("127.") || LOCALHOST_IP.equals(ip)) {
			return true;
		}

		// 检查私有IP地址范围
		for (String prefix : PRIVATE_IP_PREFIXES) {
			if (ip.startsWith(prefix)) {
				return true;
			}
		}

		return false;
	}

	/**
	 * 判断是否为外网IP
	 */
	public static boolean isExternalIP(String ip) {
		return isValidIP(ip) && !isInternalIP(ip);
	}

	/**
	 * IP地址转换为长整型
	 */
	public static long ipToLong(String ip) {
		if (!isValidIP(ip)) {
			throw new IllegalArgumentException("Invalid IP address: " + ip);
		}

		String[] parts = ip.split("\\.");
		long result = 0;
		for (int i = 0; i < 4; i++) {
			result = result * 256 + Integer.parseInt(parts[i]);
		}
		return result;
	}

	/**
	 * 长整型转换为IP地址
	 */
	public static String longToIP(long ip) {
		return ((ip >> 24) & 0xFF) + "." + ((ip >> 16) & 0xFF) + "." + ((ip >> 8) & 0xFF) + "." + (ip & 0xFF);
	}

	/**
	 * 判断IP是否在指定的IP段内 支持CIDR格式，如：***********/24
	 */
	public static boolean isIPInRange(String ip, String cidr) {
		if (!isValidIP(ip) || !StringUtils.hasText(cidr)) {
			return false;
		}

		try {
			String[] parts = cidr.split("/");
			if (parts.length != 2) {
				return false;
			}

			String networkIP = parts[0];
			int prefixLength = Integer.parseInt(parts[1]);

			if (!isValidIP(networkIP) || prefixLength < 0 || prefixLength > 32) {
				return false;
			}

			long ipLong = ipToLong(ip);
			long networkLong = ipToLong(networkIP);
			long mask = (0xFFFFFFFFL << (32 - prefixLength)) & 0xFFFFFFFFL;

			return (ipLong & mask) == (networkLong & mask);
		} catch (Exception e) {
			log.warn("判断IP是否在范围内失败: ip={}, cidr={}", ip, cidr, e);
			return false;
		}
	}

	/**
	 * 判断IP是否在指定的IP段列表内
	 */
	public static boolean isIPInRanges(String ip, String[] cidrs) {
		if (cidrs == null || cidrs.length == 0) {
			return false;
		}

		for (String cidr : cidrs) {
			if (isIPInRange(ip, cidr)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 判断IP是否在指定的IP范围内（支持起始IP-结束IP格式）
	 */
	public static boolean isIPInRange(String ip, String startIP, String endIP) {
		if (!isValidIP(ip) || !isValidIP(startIP) || !isValidIP(endIP)) {
			return false;
		}

		try {
			long ipLong = ipToLong(ip);
			long startLong = ipToLong(startIP);
			long endLong = ipToLong(endIP);

			return ipLong >= startLong && ipLong <= endLong;
		} catch (Exception e) {
			log.warn("判断IP是否在范围内失败: ip={}, start={}, end={}", ip, startIP, endIP, e);
			return false;
		}
	}

	/**
	 * 获取IP地址的网络段 例如：************* -> ***********/24
	 */
	public static String getNetworkSegment(String ip, int prefixLength) {
		if (!isValidIP(ip) || prefixLength < 0 || prefixLength > 32) {
			return null;
		}

		try {
			long ipLong = ipToLong(ip);
			long mask = (0xFFFFFFFFL << (32 - prefixLength)) & 0xFFFFFFFFL;
			long networkLong = ipLong & mask;
			String networkIP = longToIP(networkLong);
			return networkIP + "/" + prefixLength;
		} catch (Exception e) {
			log.warn("获取IP网络段失败: ip={}, prefixLength={}", ip, prefixLength, e);
			return null;
		}
	}

	/**
	 * 获取本机IP地址
	 */
	public static String getLocalIP() {
		try {
			InetAddress address = InetAddress.getLocalHost();
			return address.getHostAddress();
		} catch (UnknownHostException e) {
			log.warn("获取本机IP失败", e);
			return LOCALHOST_IP;
		}
	}

	/**
	 * 获取本机主机名
	 */
	public static String getLocalHostName() {
		try {
			InetAddress address = InetAddress.getLocalHost();
			return address.getHostName();
		} catch (UnknownHostException e) {
			log.warn("获取本机主机名失败", e);
			return "localhost";
		}
	}

	/**
	 * 检查IP地址是否可达
	 */
	public static boolean isReachable(String ip, int timeout) {
		if (!isValidIP(ip)) {
			return false;
		}

		try {
			InetAddress address = InetAddress.getByName(ip);
			return address.isReachable(timeout);
		} catch (Exception e) {
			log.debug("检查IP可达性失败: ip={}", ip, e);
			return false;
		}
	}

	/**
	 * 检查IP地址是否可达（默认超时5秒）
	 */
	public static boolean isReachable(String ip) {
		return isReachable(ip, 5000);
	}

	/**
	 * 获取IP地址的地理位置信息 TODO: 集成IP地理位置数据库或第三方服务
	 */
	public static String getIPLocation(String ip) {
		if (!isValidIP(ip)) {
			return "未知";
		}

		if (isInternalIP(ip)) {
			return "内网IP";
		}

		// TODO: 实现IP地理位置查询
		// 可以集成MaxMind GeoIP2、纯真IP数据库等
		return "未知位置";
	}

	/**
	 * 获取IP地址的ISP信息 TODO: 集成ISP数据库或第三方服务
	 */
	public static String getIPISP(String ip) {
		if (!isValidIP(ip)) {
			return "未知";
		}

		if (isInternalIP(ip)) {
			return "内网";
		}

		// TODO: 实现ISP信息查询
		return "未知ISP";
	}

	/**
	 * 生成IP范围内的所有IP地址 注意：仅适用于小范围IP段，避免内存溢出
	 */
	public static java.util.List<String> generateIPRange(String startIP, String endIP) {
		java.util.List<String> ipList = new java.util.ArrayList<>();

		if (!isValidIP(startIP) || !isValidIP(endIP)) {
			return ipList;
		}

		try {
			long start = ipToLong(startIP);
			long end = ipToLong(endIP);

			if (start > end) {
				long temp = start;
				start = end;
				end = temp;
			}

			// 限制生成的IP数量，避免内存溢出
			if (end - start > 10000) {
				log.warn("IP范围过大，限制生成数量: start={}, end={}", startIP, endIP);
				end = start + 10000;
			}

			for (long i = start; i <= end; i++) {
				ipList.add(longToIP(i));
			}
		} catch (Exception e) {
			log.error("生成IP范围失败: start={}, end={}", startIP, endIP, e);
		}

		return ipList;
	}

	/**
	 * 掩码IP地址（用于日志脱敏） 例如：************* -> 192.168.1.***
	 */
	public static String maskIP(String ip) {
		if (!isValidIP(ip)) {
			return ip;
		}

		String[] parts = ip.split("\\.");
		return parts[0] + "." + parts[1] + "." + parts[2] + ".***";
	}

	/**
	 * 计算两个IP地址之间的距离（IP数量）
	 */
	public static long getIPDistance(String ip1, String ip2) {
		if (!isValidIP(ip1) || !isValidIP(ip2)) {
			return -1;
		}

		try {
			long long1 = ipToLong(ip1);
			long long2 = ipToLong(ip2);
			return Math.abs(long2 - long1);
		} catch (Exception e) {
			log.warn("计算IP距离失败: ip1={}, ip2={}", ip1, ip2, e);
			return -1;
		}
	}
}