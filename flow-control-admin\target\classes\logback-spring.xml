<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<!-- 定义日志文件的存储地址 -->
	<property name="LOG_HOME" value="logs" />

	<!-- 控制台输出 -->
	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<!-- 使用系统默认编码 -->
			<charset>${file.encoding:-UTF-8}</charset>
			<!-- 简化日志格式，避免中文字符 -->
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
		</encoder>
	</appender>

	<!-- 按照每天生成日志文件 -->
	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_HOME}/flow-control-admin.log</file>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<charset>UTF-8</charset>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<!-- 日志文件输出的文件名 -->
			<FileNamePattern>${LOG_HOME}/flow-control-admin.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
			<!-- 日志文件保留天数 -->
			<MaxHistory>30</MaxHistory>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
	</appender>

	<!-- 异步输出 -->
	<appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>512</queueSize>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="FILE"/>
	</appender>

	<!-- 日志输出级别 -->
	<root level="INFO">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ASYNC" />
	</root>

	<!-- 设置特定包的日志级别 -->
	<logger name="com.example.admin" level="DEBUG" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ASYNC" />
	</logger>

	<logger name="com.alibaba.csp.sentinel" level="INFO" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ASYNC" />
	</logger>

	<logger name="org.springframework.cloud.alibaba" level="INFO" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ASYNC" />
	</logger>

	<!-- MyBatis SQL日志 -->
	<logger name="com.example.admin.mapper" level="DEBUG" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ASYNC" />
	</logger>

</configuration>
