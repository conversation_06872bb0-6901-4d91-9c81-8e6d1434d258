import request from '@/utils/request'

// 租户限流规则API
export const tenantRuleApi = {
  // 获取租户规则列表
  getTenantRuleList(params) {
    return request({
      url: '/api/tenant-flow-rules',
      method: 'get',
      params
    })
  },

  // 创建租户规则
  createTenantRule(data) {
    return request({
      url: '/api/tenant-flow-rules',
      method: 'post',
      data
    })
  },

  // 更新租户规则
  updateTenantRule(id, data) {
    return request({
      url: `/api/tenant-flow-rules/${id}`,
      method: 'put',
      data
    })
  },

  // 删除租户规则
  deleteTenantRule(id) {
    return request({
      url: `/api/tenant-flow-rules/${id}`,
      method: 'delete'
    })
  },

  // 更新租户规则状态
  updateTenantRuleStatus(id, status) {
    return request({
      url: `/api/tenant-flow-rules/${id}/status`,
      method: 'put',
      data: { status }
    })
  },

  // 获取租户列表
  getTenantList() {
    return request({
      url: '/api/tenant-configs',
      method: 'get'
    })
  }
}