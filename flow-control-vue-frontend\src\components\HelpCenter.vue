<template>
  <div class="help-center">
    <!-- 帮助中心对话框 -->
    <el-dialog
      :title="$t('help.title')"
      :visible.sync="showHelp"
      width="80%"
      :before-close="closeHelp"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 搜索框 -->
        <div class="help-search">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('help.search')"
            prefix-icon="el-icon-search"
            @input="handleSearch"
            clearable
          >
          </el-input>
        </div>
        
        <div class="help-main">
          <!-- 左侧分类导航 -->
          <div class="help-sidebar">
            <el-menu
              :default-active="activeCategory"
              class="help-menu"
              @select="selectCategory"
            >
              <el-menu-item 
                v-for="category in categories" 
                :key="category.key"
                :index="category.key"
              >
                <i :class="category.icon"></i>
                <span>{{ category.label }}</span>
              </el-menu-item>
            </el-menu>
          </div>
          
          <!-- 右侧内容区域 -->
          <div class="help-body">
            <!-- 搜索结果 -->
            <div v-if="searchQuery && searchResults.length > 0" class="search-results">
              <h3>{{ $t('common.searchResults') }}</h3>
              <div 
                v-for="result in searchResults" 
                :key="result.id"
                class="search-result-item"
                @click="selectArticle(result)"
              >
                <h4>{{ result.title }}</h4>
                <p>{{ result.excerpt }}</p>
              </div>
            </div>
            
            <!-- 分类内容 -->
            <div v-else-if="!selectedArticle" class="category-content">
              <h2>{{ getCurrentCategoryLabel() }}</h2>
              <div class="article-list">
                <div 
                  v-for="article in getCurrentCategoryArticles()" 
                  :key="article.id"
                  class="article-item"
                  @click="selectArticle(article)"
                >
                  <div class="article-header">
                    <h3>{{ article.title }}</h3>
                    <span class="article-meta">{{ article.updateTime }}</span>
                  </div>
                  <p class="article-excerpt">{{ article.excerpt }}</p>
                </div>
              </div>
            </div>
            
            <!-- 文章详情 -->
            <div v-else class="article-detail">
              <div class="article-header">
                <el-button 
                  type="text" 
                  icon="el-icon-arrow-left"
                  @click="backToCategory"
                >
                  {{ $t('common.back') }}
                </el-button>
                <h2>{{ selectedArticle.title }}</h2>
              </div>
              
              <div class="article-content" v-html="selectedArticle.content"></div>
              
              <div class="article-footer">
                <div class="article-actions">
                  <el-button 
                    type="text" 
                    icon="el-icon-thumb"
                    @click="likeArticle"
                  >
                    {{ $t('common.helpful') }} ({{ selectedArticle.likes || 0 }})
                  </el-button>
                  
                  <el-button 
                    type="text" 
                    icon="el-icon-share"
                    @click="shareArticle"
                  >
                    {{ $t('common.share') }}
                  </el-button>
                </div>
                
                <div class="article-meta">
                  <span>{{ $t('common.lastUpdated') }}: {{ selectedArticle.updateTime }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 快速帮助按钮 -->
    <el-dropdown 
      v-if="!showHelp" 
      class="quick-help"
      trigger="click"
      @command="handleQuickHelp"
    >
      <el-button class="help-trigger" type="primary" circle>
        <i class="el-icon-question"></i>
      </el-button>
      
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="guide">
          <i class="el-icon-guide"></i>
          {{ $t('common.userGuide') }}
        </el-dropdown-item>
        <el-dropdown-item command="help">
          <i class="el-icon-document"></i>
          {{ $t('help.title') }}
        </el-dropdown-item>
        <el-dropdown-item command="contact">
          <i class="el-icon-service"></i>
          {{ $t('common.contactSupport') }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: 'HelpCenter',
  data() {
    return {
      showHelp: false,
      searchQuery: '',
      activeCategory: 'getting_started',
      selectedArticle: null,
      searchResults: [],
      
      // 分类配置
      categories: [
        {
          key: 'getting_started',
          label: this.$t('help.categories.getting_started'),
          icon: 'el-icon-star-on'
        },
        {
          key: 'flow_rules',
          label: this.$t('help.categories.flow_rules'),
          icon: 'el-icon-s-order'
        },
        {
          key: 'ip_rules',
          label: this.$t('help.categories.ip_rules'),
          icon: 'el-icon-s-check'
        },
        {
          key: 'monitoring',
          label: this.$t('help.categories.monitoring'),
          icon: 'el-icon-view'
        },
        {
          key: 'configuration',
          label: this.$t('help.categories.configuration'),
          icon: 'el-icon-tools'
        },
        {
          key: 'troubleshooting',
          label: this.$t('help.categories.troubleshooting'),
          icon: 'el-icon-warning'
        }
      ],
      
      // 帮助文章数据
      articles: {
        getting_started: [
          {
            id: 'gs_1',
            title: this.$t('help.getting_started.overview'),
            excerpt: '了解系统的基本概念和主要功能模块',
            content: `
              <h3>系统概述</h3>
              <p>流量控制系统是一个功能强大的网络流量管理平台，主要包含以下功能模块：</p>
              <ul>
                <li><strong>仪表盘</strong>：显示系统状态和关键指标</li>
                <li><strong>流量规则</strong>：配置和管理流量控制规则</li>
                <li><strong>IP规则</strong>：设置IP白名单和黑名单</li>
                <li><strong>监控统计</strong>：实时监控和历史数据分析</li>
                <li><strong>系统配置</strong>：系统参数和用户管理</li>
              </ul>
              <h3>主要特性</h3>
              <ul>
                <li>实时流量监控和控制</li>
                <li>灵活的规则配置</li>
                <li>直观的数据可视化</li>
                <li>多语言支持</li>
                <li>响应式设计，支持移动设备</li>
              </ul>
            `,
            updateTime: '2024-01-15',
            likes: 15
          },
          {
            id: 'gs_2',
            title: this.$t('help.getting_started.first_login'),
            excerpt: '首次登录系统的步骤和注意事项',
            content: `
              <h3>首次登录</h3>
              <p>首次使用系统时，请按照以下步骤进行：</p>
              <ol>
                <li>使用管理员提供的用户名和密码登录</li>
                <li>首次登录后建议立即修改密码</li>
                <li>查看系统引导，了解基本功能</li>
                <li>根据需要配置个人偏好设置</li>
              </ol>
              <h3>安全建议</h3>
              <ul>
                <li>使用强密码，包含字母、数字和特殊字符</li>
                <li>定期更换密码</li>
                <li>不要在公共设备上保存登录信息</li>
              </ul>
            `,
            updateTime: '2024-01-15',
            likes: 8
          }
        ],
        
        flow_rules: [
          {
            id: 'fr_1',
            title: this.$t('help.flow_rules.create_rule'),
            excerpt: '如何创建和配置流量控制规则',
            content: `
              <h3>创建流量规则</h3>
              <p>流量规则用于控制网络流量的行为，您可以按照以下步骤创建规则：</p>
              <ol>
                <li>进入"流量规则"页面</li>
                <li>点击"新增规则"按钮</li>
                <li>填写规则基本信息：
                  <ul>
                    <li>规则名称：便于识别的名称</li>
                    <li>资源名称：要控制的资源标识</li>
                    <li>限流阈值：每秒允许的请求数</li>
                    <li>流控效果：快速失败、Warm Up、排队等待</li>
                  </ul>
                </li>
                <li>设置规则优先级</li>
                <li>保存并启用规则</li>
              </ol>
            `,
            updateTime: '2024-01-14',
            likes: 12
          }
        ],
        
        ip_rules: [
          {
            id: 'ir_1',
            title: this.$t('help.ip_rules.whitelist'),
            excerpt: 'IP白名单的配置和管理方法',
            content: `
              <h3>IP白名单设置</h3>
              <p>IP白名单用于允许特定IP地址访问系统，配置方法如下：</p>
              <ol>
                <li>进入"IP规则"页面</li>
                <li>选择"白名单"标签</li>
                <li>点击"添加IP"按钮</li>
                <li>输入IP地址或IP段</li>
                <li>添加备注说明</li>
                <li>保存配置</li>
              </ol>
              <h3>IP格式说明</h3>
              <ul>
                <li>单个IP：*************</li>
                <li>IP段：***********/24</li>
                <li>IP范围：*************-*************</li>
              </ul>
            `,
            updateTime: '2024-01-13',
            likes: 6
          }
        ],
        
        monitoring: [],
        configuration: [],
        troubleshooting: []
      }
    }
  },
  
  methods: {
    openHelp() {
      this.showHelp = true
      this.searchQuery = ''
      this.selectedArticle = null
    },
    
    closeHelp() {
      this.showHelp = false
      this.searchQuery = ''
      this.selectedArticle = null
    },
    
    selectCategory(categoryKey) {
      this.activeCategory = categoryKey
      this.selectedArticle = null
      this.searchQuery = ''
    },
    
    selectArticle(article) {
      this.selectedArticle = article
    },
    
    backToCategory() {
      this.selectedArticle = null
    },
    
    handleSearch() {
      if (!this.searchQuery.trim()) {
        this.searchResults = []
        return
      }
      
      const query = this.searchQuery.toLowerCase()
      const results = []
      
      // 搜索所有文章
      Object.values(this.articles).forEach(categoryArticles => {
        categoryArticles.forEach(article => {
          if (
            article.title.toLowerCase().includes(query) ||
            article.excerpt.toLowerCase().includes(query) ||
            article.content.toLowerCase().includes(query)
          ) {
            results.push(article)
          }
        })
      })
      
      this.searchResults = results
    },
    
    getCurrentCategoryLabel() {
      const category = this.categories.find(cat => cat.key === this.activeCategory)
      return category ? category.label : ''
    },
    
    getCurrentCategoryArticles() {
      return this.articles[this.activeCategory] || []
    },
    
    likeArticle() {
      if (this.selectedArticle) {
        this.selectedArticle.likes = (this.selectedArticle.likes || 0) + 1
        this.$message.success(this.$t('common.thankYou'))
      }
    },
    
    shareArticle() {
      if (this.selectedArticle) {
        // 复制文章链接到剪贴板
        const url = `${window.location.origin}/help/${this.selectedArticle.id}`
        navigator.clipboard.writeText(url).then(() => {
          this.$message.success(this.$t('common.linkCopied'))
        })
      }
    },
    
    handleQuickHelp(command) {
      switch (command) {
        case 'guide':
          this.$emit('show-guide')
          break
        case 'help':
          this.openHelp()
          break
        case 'contact':
          this.showContactSupport()
          break
      }
    },
    
    showContactSupport() {
      this.$alert(
        '如需技术支持，请联系：\n邮箱：<EMAIL>\n电话：400-123-4567',
        this.$t('common.contactSupport'),
        {
          confirmButtonText: this.$t('common.ok')
        }
      )
    }
  }
}
</script>

<style scoped>
.help-center {
  position: relative;
}

.help-dialog {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__body {
    padding: 0;
  }
}

.help-content {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.help-search {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.help-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.help-sidebar {
  width: 200px;
  border-right: 1px solid #ebeef5;
  background-color: #fafafa;
}

.help-menu {
  border: none;
  background-color: transparent;
}

.help-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.search-results {
  h3 {
    margin-bottom: 20px;
    color: #303133;
  }
}

.search-result-item {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  }
  
  h4 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 16px;
  }
  
  p {
    margin: 0;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
  }
}

.category-content {
  h2 {
    margin-bottom: 20px;
    color: #303133;
    border-bottom: 2px solid #409eff;
    padding-bottom: 10px;
  }
}

.article-list {
  display: grid;
  gap: 15px;
}

.article-item {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
    transform: translateY(-2px);
  }
  
  .article-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    
    h3 {
      margin: 0;
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }
    
    .article-meta {
      color: #909399;
      font-size: 12px;
      white-space: nowrap;
    }
  }
  
  .article-excerpt {
    margin: 0;
    color: #606266;
    font-size: 14px;
    line-height: 1.6;
  }
}

.article-detail {
  .article-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    
    h2 {
      margin: 0 0 0 10px;
      color: #303133;
    }
  }
  
  .article-content {
    line-height: 1.8;
    color: #303133;
    
    h3 {
      color: #409eff;
      margin: 25px 0 15px 0;
    }
    
    ul, ol {
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
      }
    }
    
    p {
      margin-bottom: 15px;
    }
    
    code {
      background-color: #f5f7fa;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }
  }
  
  .article-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .article-actions {
      display: flex;
      gap: 15px;
    }
    
    .article-meta {
      color: #909399;
      font-size: 12px;
    }
  }
}

.quick-help {
  position: fixed;
  bottom: 80px;
  right: 30px;
  z-index: 1000;
}

.help-trigger {
  width: 50px;
  height: 50px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 在大屏幕上隐藏快速帮助按钮 */
@media (min-width: 1200px) {
  .quick-help {
    display: none;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .help-main {
    flex-direction: column;
  }
  
  .help-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #ebeef5;
  }
  
  .help-menu {
    display: flex;
    overflow-x: auto;
    
    .el-menu-item {
      white-space: nowrap;
    }
  }
  
  .quick-help {
    bottom: 70px;
    right: 20px;
  }
  
  .help-trigger {
    width: 45px;
    height: 45px;
  }
}
</style>