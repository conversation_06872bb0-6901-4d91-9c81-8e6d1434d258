package com.example.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.dto.FlowRuleDTO;
import com.example.common.entity.FlowRule;
import com.example.admin.mapper.FlowRuleMapper;
import com.example.admin.service.FlowRuleService;

import com.example.admin.vo.FlowRuleVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流控规则服务实现类
 */
@Service
public class FlowRuleServiceImpl extends ServiceImpl<FlowRuleMapper, FlowRule> implements FlowRuleService {

	private static final Logger log = LoggerFactory.getLogger(FlowRuleServiceImpl.class);

	@Resource
	private FlowRuleMapper flowRuleMapper;

	@Override
	public Page<FlowRuleVO> selectFlowRulePage(Page<FlowRuleVO> page, String tenantId, String resourceName,
			String ruleName, Integer status, Integer limitMode) {
		return flowRuleMapper.selectFlowRuleVOPage(page, tenantId, resourceName, status);
	}

	@Override
	public FlowRuleVO getFlowRuleById(Long id) {
		FlowRule flowRule = this.getById(id);
		if (flowRule == null) {
			return null;
		}
		FlowRuleVO flowRuleVO = new FlowRuleVO();
		BeanUtils.copyProperties(flowRule, flowRuleVO);
		return flowRuleVO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean createFlowRule(FlowRuleDTO flowRuleDTO) {
		// 检查规则名称是否已存在
		if (existsByRuleName(flowRuleDTO.getRuleName(), null)) {
			throw new RuntimeException("规则名称已存在");
		}

		// 检查资源名称是否已存在
		// 资源名称重复检查已移除

		FlowRule flowRule = new FlowRule();
		BeanUtils.copyProperties(flowRuleDTO, flowRule);
		flowRule.setCreateTime(LocalDateTime.now());
		flowRule.setUpdateTime(LocalDateTime.now());

		boolean result = this.save(flowRule);

		// 发布规则更新
		if (result) {
			publishFlowRules();
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateFlowRule(Long id, FlowRuleDTO flowRuleDTO) {
		FlowRule existingRule = this.getById(id);
		if (existingRule == null) {
			throw new RuntimeException("流控规则不存在");
		}

		// 检查规则名称是否已存在（排除当前规则）
		if (existsByRuleName(flowRuleDTO.getRuleName(), id)) {
			throw new RuntimeException("规则名称已存在");
		}

		// 检查资源名称是否已存在（排除当前规则）
		// 资源名称重复检查已移除

		BeanUtils.copyProperties(flowRuleDTO, existingRule);
		existingRule.setId(id);
		existingRule.setUpdateTime(LocalDateTime.now());

		boolean result = this.updateById(existingRule);

		// 发布到Nacos
		if (result) {
			publishFlowRules();
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteFlowRule(Long id) {
		boolean result = this.removeById(id);

		// 发布规则更新
		if (result) {
			publishFlowRules();
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchDeleteFlowRules(List<Long> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return false;
		}
		boolean result = this.removeByIds(ids);

		// 发布规则更新
		if (result) {
			publishFlowRules();
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean enableFlowRule(Long id) {
		return updateRuleStatus(id, 1);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean disableFlowRule(Long id) {
		return updateRuleStatus(id, 0);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchUpdateStatus(List<Long> ids, Integer status) {
		if (CollectionUtils.isEmpty(ids)) {
			return false;
		}
		return flowRuleMapper.batchUpdateStatus(ids, status, null) > 0;
	}

	@Override
	public List<FlowRuleVO> getFlowRulesByTenantId(String tenantId, Integer status, Integer limit) {
		// 转换为FlowRule列表，然后转换为FlowRuleVO
		List<FlowRule> flowRules = flowRuleMapper.selectByTenantId(tenantId);
		return flowRules.stream().map(this::convertToVO).collect(Collectors.toList());
	}

	@Override
	public List<FlowRuleVO> getFlowRulesByResourceName(String resourceName, String tenantId, Integer status,
			Integer limit) {
		List<FlowRule> flowRules = flowRuleMapper.selectByResourceName(resourceName);
		return flowRules.stream().map(this::convertToVO).collect(Collectors.toList());
	}

	@Override
	public boolean existsByRuleName(String ruleName, Long excludeId) {
		FlowRule rule = flowRuleMapper.selectByRuleName(ruleName, excludeId);
		return rule != null;
	}

	@Override
	public int countByTenantId(String tenantId, Integer status) {
		return flowRuleMapper.countByTenantId(tenantId);
	}

	@Override
	public List<FlowRuleVO> getEnabledRules(String tenantId, String resourceName, Integer limit) {
		List<FlowRule> flowRules = flowRuleMapper.selectAllEnabled();
		return flowRules.stream().map(this::convertToVO).collect(Collectors.toList());
	}

	@Override
	public List<FlowRuleVO> getRulesByPriorityOrder(String tenantId, String resourceName, Integer status,
			Integer limit) {
		List<FlowRule> flowRules = flowRuleMapper.selectByPriorityOrder(tenantId, resourceName);
		return flowRules.stream().map(this::convertToVO).collect(Collectors.toList());
	}

	@Override
	public List<Map<String, Object>> getStatusStatistics(String tenantId) {
		return flowRuleMapper.selectStatusStatistics();
	}

	@Override
	public List<Map<String, Object>> getTenantRuleStatistics(Integer limit) {
		return flowRuleMapper.selectTenantRuleStatistics();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchCreateFlowRules(List<FlowRuleDTO> flowRuleDTOList) {
		if (CollectionUtils.isEmpty(flowRuleDTOList)) {
			return false;
		}

		List<FlowRule> flowRules = flowRuleDTOList.stream().map(dto -> {
			FlowRule flowRule = new FlowRule();
			BeanUtils.copyProperties(dto, flowRule);
			flowRule.setCreateTime(LocalDateTime.now());
			flowRule.setUpdateTime(LocalDateTime.now());
			return flowRule;
		}).collect(Collectors.toList());

		return this.saveBatch(flowRules);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean copyFlowRule(Long id, String newRuleName) {
		FlowRule originalRule = this.getById(id);
		if (originalRule == null) {
			throw new RuntimeException("原规则不存在");
		}

		if (existsByRuleName(newRuleName, null)) {
			throw new RuntimeException("新规则名称已存在");
		}

		FlowRule newRule = new FlowRule();
		BeanUtils.copyProperties(originalRule, newRule);
		newRule.setId(null);
		newRule.setRuleName(newRuleName);
		newRule.setCreateTime(LocalDateTime.now());
		newRule.setUpdateTime(LocalDateTime.now());

		return this.save(newRule);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<String, Object> importFlowRules(List<FlowRuleDTO> flowRuleDTOList, boolean overwrite) {
		Map<String, Object> result = new HashMap<>();
		int successCount = 0;
		int failCount = 0;
		List<String> errorMessages = new ArrayList<>();

		for (FlowRuleDTO dto : flowRuleDTOList) {
			try {
				if (overwrite && existsByRuleName(dto.getRuleName(), null)) {
					// 覆盖模式：先删除再创建
					FlowRule existingRule = flowRuleMapper.selectByRuleName(dto.getRuleName(), null);
					if (existingRule != null) {
						this.removeById(existingRule.getId());
					}
				}

				if (createFlowRule(dto)) {
					successCount++;
				} else {
					failCount++;
					errorMessages.add("规则 " + dto.getRuleName() + " 创建失败");
				}
			} catch (Exception e) {
				failCount++;
				errorMessages.add("规则 " + dto.getRuleName() + " 导入失败: " + e.getMessage());
			}
		}

		result.put("successCount", successCount);
		result.put("failCount", failCount);
		result.put("errorMessages", errorMessages);
		return result;
	}

	@Override
	public List<FlowRuleVO> exportFlowRules(String tenantId, Integer status) {
		// 简化实现，使用现有方法
		List<FlowRule> flowRules = flowRuleMapper.selectByTenantId(tenantId);
		return flowRules.stream().map(this::convertToVO).collect(Collectors.toList());
	}

	@Override
	public Map<String, Object> validateFlowRule(FlowRuleDTO flowRuleDTO) {
		Map<String, Object> result = new HashMap<>();
		List<String> errors = new ArrayList<>();

		// 验证必填字段
		if (!StringUtils.hasText(flowRuleDTO.getRuleName())) {
			errors.add("规则名称不能为空");
		}
		if (!StringUtils.hasText(flowRuleDTO.getResourceName())) {
			errors.add("资源名称不能为空");
		}
		if (flowRuleDTO.getThreshold() == null || flowRuleDTO.getThreshold() <= 0) {
			errors.add("阈值必须大于0");
		}

		// 验证枚举值
		if (flowRuleDTO.getLimitMode() == null
				|| (flowRuleDTO.getLimitMode() != 0 && flowRuleDTO.getLimitMode() != 1)) {
			errors.add("限流模式值无效");
		}

		result.put("valid", errors.isEmpty());
		result.put("errors", errors);
		return result;
	}

	@Override
	public boolean syncRuleToSentinel(Long id) {
		// TODO: 实现与Sentinel的同步逻辑
		FlowRule flowRule = this.getById(id);
		if (flowRule == null) {
			return false;
		}

		// 这里应该调用Sentinel的API来同步规则
		// 暂时返回true表示同步成功
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<String, Object> batchSyncRulesToSentinel(List<Long> ids) {
		Map<String, Object> result = new HashMap<>();
		int successCount = 0;
		int failCount = 0;

		for (Long id : ids) {
			if (syncRuleToSentinel(id)) {
				successCount++;
			} else {
				failCount++;
			}
		}

		result.put("successCount", successCount);
		result.put("failCount", failCount);
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<String, Object> syncRulesFromSentinel(String tenantId) {
		// TODO: 实现从Sentinel同步规则的逻辑
		Map<String, Object> result = new HashMap<>();
		result.put("syncCount", 0);
		result.put("message", "暂未实现从Sentinel同步规则功能");
		return result;
	}

	/**
	 * 转换FlowRule为FlowRuleVO
	 */
	private FlowRuleVO convertToVO(FlowRule flowRule) {
		FlowRuleVO vo = new FlowRuleVO();
		BeanUtils.copyProperties(flowRule, vo);

		// 设置限流模式名称
		if (flowRule.getLimitMode() != null) {
			switch (flowRule.getLimitMode()) {
			case 0:
				vo.setLimitModeName("QPS限流");
				break;
			case 1:
				vo.setLimitModeName("并发限流");
				break;
			default:
				vo.setLimitModeName("未知");
				break;
			}
		}

		// 设置其他枚举字段的名称
		if (flowRule.getStrategy() != null) {
			switch (flowRule.getStrategy()) {
			case 0:
				vo.setStrategyName("直接");
				break;
			case 1:
				vo.setStrategyName("关联");
				break;
			case 2:
				vo.setStrategyName("链路");
				break;
			default:
				vo.setStrategyName("未知");
				break;
			}
		}

		if (flowRule.getBehavior() != null) {
			switch (flowRule.getBehavior()) {
			case 0:
				vo.setBehaviorName("快速失败");
				break;
			case 1:
				vo.setBehaviorName("预热");
				break;
			case 2:
				vo.setBehaviorName("排队等待");
				break;
			default:
				vo.setBehaviorName("未知");
				break;
			}
		}

		return vo;
	}

	/**
	 * 更新规则状态
	 */
	private boolean updateRuleStatus(Long id, Integer status) {
		FlowRule flowRule = this.getById(id);
		if (flowRule == null) {
			throw new RuntimeException("流控规则不存在");
		}

		flowRule.setStatus(status);
		flowRule.setUpdateTime(LocalDateTime.now());
		boolean result = this.updateById(flowRule);

		// 发布规则更新
		if (result) {
			publishFlowRules();
		}

		return result;
	}

	/**
	 * 发布流控规则（已移除Nacos依赖）
	 */
	private void publishFlowRules() {
		try {
			List<FlowRule> allRules = this.list();
			log.info("Flow rules updated, total count: {}", allRules.size());
			// 规则变更后，Gateway会通过定时任务自动从数据库加载最新规则
		} catch (Exception e) {
			log.error("Failed to process flow rules update", e);
		}
	}
}