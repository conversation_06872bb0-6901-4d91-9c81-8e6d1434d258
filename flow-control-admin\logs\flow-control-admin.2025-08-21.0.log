2025-08-21 16:43:12.278 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 16:43:12.353 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 29512 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 16:43:12.353 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 16:43:12.354 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 16:43:13.688 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 16:43:13.692 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 16:43:13.737 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-08-21 16:43:14.583 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 16:43:14.597 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 16:43:14.599 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 16:43:14.599 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 16:43:14.709 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 16:43:14.710 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2297 ms
2025-08-21 16:43:14.822 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 16:43:14.944 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 16:43:15.388 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 16:43:15.390 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 16:43:15.391 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 16:43:16.244 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 16:43:16.287 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 16:43:16.290 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 16:43:16.291 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 16:43:16.292 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 16:43:16.494 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 16:43:16.517 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 16:43:16.534 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 16:43:16.658 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 16:43:16.672 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 16:43:16.696 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 16:43:16.709 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 16:43:17.249 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 16:43:17.312 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 16:43:17.348 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-08-21 16:43:17.365 [main] INFO  com.example.admin.AdminApplication - Started AdminApplication in 5.704 seconds (process running for 6.368)
2025-08-21 16:43:32.542 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 16:43:32.542 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-21 16:43:32.544 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-21 16:43:32.560 [http-nio-8081-exec-1] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/api/tenant-flow-rules
2025-08-21 16:43:32.567 [http-nio-8081-exec-1] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/api/tenant-flow-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 16:43:34.073 [http-nio-8081-exec-2] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/api/flow-rules
2025-08-21 16:43:34.074 [http-nio-8081-exec-2] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/api/flow-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 16:43:34.381 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 16:43:34.548 [http-nio-8081-exec-3] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-21 16:43:35.062 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 681ms
2025-08-21 16:43:35.571 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 16:43:35.799 [http-nio-8081-exec-6] ERROR com.example.admin.controller.IPFlowRuleController - 分页查询IP流量规则失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ir.flow_behavior' in 'field list'
### The error may exist in file [D:\java\openplatform\flow-control-admin\target\classes\mapper\IPFlowRuleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             ir.id,             ir.rule_name,             ir.tenant_id,             COALESCE(ti.tenant_name, ir.tenant_id) as tenant_name,             ir.rule_type,             CASE ir.rule_type                 WHEN 'SINGLE_IP' THEN '单个IP'                 WHEN 'IP_RANGE' THEN 'IP范围'                 WHEN 'IP_CIDR' THEN 'IP段'                 WHEN 'WILDCARD' THEN '通配符'                 ELSE '未知'             END as rule_type_name,             ir.ip_value,             ir.qps_limit,             ir.flow_behavior,             ir.priority,             ir.description,             ir.status,             CASE ir.status                 WHEN 0 THEN '禁用'                 WHEN 1 THEN '启用'                 ELSE '未知'             END as status_name,             ir.create_time,             ir.update_time         FROM ip_flow_rule ir         LEFT JOIN tenant_info ti ON ir.tenant_id = ti.tenant_id         WHERE ir.deleted = 0                     ORDER BY ir.priority ASC, ir.create_time DESC LIMIT ?
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ir.flow_behavior' in 'field list'
; bad SQL grammar []
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:246)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy113.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:119)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:84)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy117.selectIPFlowRuleVOPage(Unknown Source)
	at com.example.admin.service.impl.IPFlowRuleServiceImpl.selectIPFlowRulePage(IPFlowRuleServiceImpl.java:51)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)
	at com.example.admin.service.impl.IPFlowRuleServiceImpl$$SpringCGLIB$$0.selectIPFlowRulePage(<generated>)
	at com.example.admin.controller.IPFlowRuleController.pageIPFlowRules(IPFlowRuleController.java:51)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.example.admin.controller.IPFlowRuleController$$SpringCGLIB$$0.pageIPFlowRules(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'ir.flow_behavior' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy3/jdk.proxy3.$Proxy151.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy149.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy148.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 73 common frames omitted
2025-08-21 16:43:35.804 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 233ms
2025-08-21 16:43:37.330 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 16:43:37.342 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 13ms
2025-08-21 16:43:37.644 [http-nio-8081-exec-8] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/api/flow-rules
2025-08-21 16:43:37.646 [http-nio-8081-exec-8] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/api/flow-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 16:43:38.311 [http-nio-8081-exec-5] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/api/tenant-flow-rules
2025-08-21 16:43:38.312 [http-nio-8081-exec-5] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/api/tenant-flow-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 17:35:14.412 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-21 17:35:14.414 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-21 17:35:29.166 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 17:35:29.220 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 29724 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 17:35:29.220 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 17:35:29.221 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 17:35:30.230 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 17:35:30.233 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 17:35:30.265 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-08-21 17:35:30.895 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 17:35:30.906 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 17:35:30.908 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 17:35:30.909 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 17:35:31.031 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 17:35:31.032 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1752 ms
2025-08-21 17:35:31.181 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 17:35:31.341 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 17:35:31.913 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 17:35:31.918 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 17:35:31.919 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 17:35:32.486 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 17:35:32.515 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 17:35:32.517 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 17:35:32.518 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 17:35:32.521 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 17:35:32.646 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 17:35:32.656 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 17:35:32.665 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 17:35:32.759 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 17:35:32.773 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 17:35:32.791 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 17:35:32.801 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 17:35:33.396 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 17:35:33.464 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 17:35:33.504 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-08-21 17:35:33.515 [main] INFO  com.example.admin.AdminApplication - Started AdminApplication in 4.779 seconds (process running for 5.295)
2025-08-21 17:35:42.907 [http-nio-8081-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 17:35:42.907 [http-nio-8081-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-21 17:35:42.910 [http-nio-8081-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-21 17:35:42.926 [http-nio-8081-exec-2] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/api/flow-rules
2025-08-21 17:35:42.926 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 17:35:42.931 [http-nio-8081-exec-2] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/api/flow-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 17:35:43.093 [http-nio-8081-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-21 17:35:43.480 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 554ms
2025-08-21 17:35:52.498 [http-nio-8081-exec-3] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/api/tenant-flow-rules
2025-08-21 17:35:52.499 [http-nio-8081-exec-3] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/api/tenant-flow-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 17:36:02.523 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 17:36:02.523 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/api/flow-rules
2025-08-21 17:36:02.524 [http-nio-8081-exec-6] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/api/flow-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 17:36:02.534 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 11ms
2025-08-21 17:36:12.528 [http-nio-8081-exec-4] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/api/tenant-flow-rules
2025-08-21 17:36:12.528 [http-nio-8081-exec-4] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/api/tenant-flow-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 17:36:20.972 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:36:21.016 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 44ms
2025-08-21 17:38:04.739 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 17:38:04.790 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 13416 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 17:38:04.790 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 17:38:04.791 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 17:38:05.786 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 17:38:05.789 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 17:38:05.836 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-08-21 17:38:06.540 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 17:38:06.545 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 17:38:06.551 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 17:38:06.551 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 17:38:06.635 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 17:38:06.636 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1786 ms
2025-08-21 17:38:06.726 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 17:38:06.825 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 17:38:07.203 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 17:38:07.206 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 17:38:07.207 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 17:38:07.718 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 17:38:07.736 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 17:38:07.738 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 17:38:07.739 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 17:38:07.740 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 17:38:07.835 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 17:38:07.843 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 17:38:07.853 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 17:38:07.943 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 17:38:07.954 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 17:38:07.971 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 17:38:07.977 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 17:38:08.399 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 17:38:08.461 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 17:38:08.477 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-21 17:38:08.495 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-21 17:38:08.621 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-21 17:38:08.652 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-08-21 17:38:21.135 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-21 17:38:21.139 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-21 17:38:32.068 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 17:38:32.113 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 23288 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 17:38:32.113 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 17:38:32.113 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 17:38:33.087 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 17:38:33.090 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 17:38:33.119 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-08-21 17:38:33.633 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 17:38:33.644 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 17:38:33.647 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 17:38:33.647 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 17:38:33.729 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 17:38:33.729 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1573 ms
2025-08-21 17:38:33.815 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 17:38:33.916 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 17:38:34.311 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 17:38:34.318 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 17:38:34.318 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 17:38:34.999 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 17:38:35.026 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 17:38:35.029 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 17:38:35.031 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 17:38:35.031 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 17:38:35.162 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 17:38:35.182 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 17:38:35.194 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 17:38:35.309 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 17:38:35.324 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 17:38:35.346 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 17:38:35.356 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 17:38:35.849 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 17:38:35.912 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 17:38:35.939 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-08-21 17:38:35.950 [main] INFO  com.example.admin.AdminApplication - Started AdminApplication in 4.354 seconds (process running for 4.992)
2025-08-21 17:39:11.929 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 17:39:11.929 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-21 17:39:11.930 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-21 17:39:11.948 [http-nio-8081-exec-1] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/api/tenant-flow-rules
2025-08-21 17:39:11.953 [http-nio-8081-exec-1] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/api/tenant-flow-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 17:41:26.658 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 17:41:26.734 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 35784 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 17:41:26.735 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 17:41:26.736 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 17:41:27.920 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 17:41:27.924 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 17:41:27.957 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-21 17:41:28.600 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 17:41:28.610 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 17:41:28.613 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 17:41:28.614 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 17:41:28.717 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 17:41:28.717 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1904 ms
2025-08-21 17:41:28.824 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 17:41:28.926 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 17:41:29.283 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 17:41:29.287 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 17:41:29.288 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 17:41:29.814 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 17:41:29.835 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 17:41:29.837 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 17:41:29.837 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 17:41:29.837 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 17:41:29.918 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 17:41:29.926 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 17:41:29.934 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 17:41:30.015 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 17:41:30.025 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 17:41:30.041 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 17:41:30.048 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 17:41:30.437 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 17:41:30.504 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 17:41:30.536 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-08-21 17:41:30.548 [main] INFO  com.example.admin.AdminApplication - Started AdminApplication in 4.442 seconds (process running for 5.029)
2025-08-21 17:41:43.043 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 17:41:43.043 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-21 17:41:43.043 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-08-21 17:41:43.073 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: POST, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:41:43.190 [http-nio-8081-exec-1] WARN  c.e.admin.config.WebConfig$GlobalExceptionHandler - 参数验证失败
org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.example.admin.common.Result<java.lang.String> com.example.admin.controller.TenantFlowRuleController.createTenantFlowRule(com.example.admin.dto.TenantFlowRuleDTO) with 2 errors: [Field error in object 'tenantFlowRuleDTO' on field 'enabled': rejected value [null]; codes [NotNull.tenantFlowRuleDTO.enabled,NotNull.enabled,NotNull.java.lang.Integer,NotNull]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [tenantFlowRuleDTO.enabled,enabled]; arguments []; default message [enabled]]; default message [启用状态不能为空]] [Field error in object 'tenantFlowRuleDTO' on field 'count': rejected value [null]; codes [NotNull.tenantFlowRuleDTO.count,NotNull.count,NotNull.java.lang.Double,NotNull]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [tenantFlowRuleDTO.count,count]; arguments []; default message [count]]; default message [限流阈值不能为空]] 
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:143)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:226)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:179)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 17:41:43.213 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: POST, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 140ms
2025-08-21 17:41:57.719 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 17:41:57.763 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 28552 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 17:41:57.764 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 17:41:57.764 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 17:41:58.871 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 17:41:58.874 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 17:41:58.903 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-08-21 17:41:59.506 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 17:41:59.517 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 17:41:59.518 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 17:41:59.519 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 17:41:59.612 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 17:41:59.613 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1804 ms
2025-08-21 17:41:59.707 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 17:41:59.807 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 17:42:00.212 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 17:42:00.217 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 17:42:00.219 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 17:42:00.899 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 17:42:00.926 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 17:42:00.928 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 17:42:00.930 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 17:42:00.931 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 17:42:01.065 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 17:42:01.076 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 17:42:01.087 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 17:42:01.179 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 17:42:01.191 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 17:42:01.207 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 17:42:01.214 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 17:42:01.842 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 17:42:01.916 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 17:42:01.931 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-21 17:42:01.941 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-21 17:42:02.024 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-21 17:42:02.038 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-08-21 17:42:10.361 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-21 17:42:47.292 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 17:42:47.330 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 25812 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 17:42:47.330 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 17:42:47.330 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 17:42:48.185 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 17:42:48.186 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 17:42:48.214 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-08-21 17:42:48.730 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 17:42:48.739 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 17:42:48.741 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 17:42:48.741 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 17:42:48.829 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 17:42:48.830 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1458 ms
2025-08-21 17:42:48.914 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 17:42:49.001 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 17:42:49.354 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 17:42:49.357 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 17:42:49.360 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 17:42:49.905 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 17:42:49.924 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 17:42:49.927 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 17:42:49.928 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 17:42:49.928 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 17:42:50.010 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 17:42:50.018 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 17:42:50.028 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 17:42:50.115 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 17:42:50.125 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 17:42:50.140 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 17:42:50.145 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 17:42:50.578 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 17:42:50.653 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 17:42:50.683 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-08-21 17:42:50.696 [main] INFO  com.example.admin.AdminApplication - Started AdminApplication in 3.79 seconds (process running for 4.407)
2025-08-21 17:43:40.486 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 17:43:40.486 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-21 17:43:40.487 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-21 17:43:40.506 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:43:40.665 [http-nio-8081-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-21 17:43:41.065 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 559ms
2025-08-21 17:43:46.596 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:43:46.643 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 47ms
2025-08-21 17:43:54.744 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:43:54.762 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 17:44:03.605 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 17:44:03.726 [http-nio-8081-exec-4] ERROR c.example.admin.controller.IPWhitelistController - 分页查询IP白名单失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'enabled' in 'where clause'
### The error may exist in com/example/admin/mapper/IPWhitelistMapper.java (best guess)
### The error may involve com.example.admin.mapper.IPWhitelistMapper.selectByTenantIdAndEnabled-Inline
### The error occurred while setting parameters
### SQL: SELECT * FROM ip_whitelist WHERE tenant_id = ? AND enabled = ? AND deleted = 0 ORDER BY priority DESC
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'enabled' in 'where clause'
; bad SQL grammar []
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:246)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy113.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy118.selectByTenantIdAndEnabled(Unknown Source)
	at com.example.admin.service.impl.IPWhitelistServiceImpl.selectIPWhitelistPage(IPWhitelistServiceImpl.java:58)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)
	at com.example.admin.service.impl.IPWhitelistServiceImpl$$SpringCGLIB$$0.selectIPWhitelistPage(<generated>)
	at com.example.admin.controller.IPWhitelistController.pageIPWhitelists(IPWhitelistController.java:62)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.example.admin.controller.IPWhitelistController$$SpringCGLIB$$0.pageIPWhitelists(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'enabled' in 'where clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy3/jdk.proxy3.$Proxy151.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy149.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy148.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 73 common frames omitted
2025-08-21 17:44:03.730 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 125ms
2025-08-21 17:44:18.740 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:44:18.756 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 17:44:40.035 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:44:40.055 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 20ms
2025-08-21 17:44:43.677 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:44:43.701 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 24ms
2025-08-21 17:44:48.244 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 17:44:48.248 [http-nio-8081-exec-8] ERROR c.example.admin.controller.IPWhitelistController - 分页查询IP白名单失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'enabled' in 'where clause'
### The error may exist in com/example/admin/mapper/IPWhitelistMapper.java (best guess)
### The error may involve com.example.admin.mapper.IPWhitelistMapper.selectByTenantIdAndEnabled-Inline
### The error occurred while setting parameters
### SQL: SELECT * FROM ip_whitelist WHERE tenant_id = ? AND enabled = ? AND deleted = 0 ORDER BY priority DESC
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'enabled' in 'where clause'
; bad SQL grammar []
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:246)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy113.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy118.selectByTenantIdAndEnabled(Unknown Source)
	at com.example.admin.service.impl.IPWhitelistServiceImpl.selectIPWhitelistPage(IPWhitelistServiceImpl.java:58)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)
	at com.example.admin.service.impl.IPWhitelistServiceImpl$$SpringCGLIB$$0.selectIPWhitelistPage(<generated>)
	at com.example.admin.controller.IPWhitelistController.pageIPWhitelists(IPWhitelistController.java:62)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.example.admin.controller.IPWhitelistController$$SpringCGLIB$$0.pageIPWhitelists(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'enabled' in 'where clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy3/jdk.proxy3.$Proxy151.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy149.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy148.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 73 common frames omitted
2025-08-21 17:44:48.252 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 8ms
2025-08-21 17:44:48.816 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:44:48.837 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 22ms
2025-08-21 17:44:49.178 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:44:49.202 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 24ms
2025-08-21 17:45:04.152 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 17:45:04.157 [http-nio-8081-exec-1] ERROR c.example.admin.controller.IPWhitelistController - 分页查询IP白名单失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'enabled' in 'where clause'
### The error may exist in com/example/admin/mapper/IPWhitelistMapper.java (best guess)
### The error may involve com.example.admin.mapper.IPWhitelistMapper.selectByTenantIdAndEnabled-Inline
### The error occurred while setting parameters
### SQL: SELECT * FROM ip_whitelist WHERE tenant_id = ? AND enabled = ? AND deleted = 0 ORDER BY priority DESC
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'enabled' in 'where clause'
; bad SQL grammar []
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:246)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy113.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy118.selectByTenantIdAndEnabled(Unknown Source)
	at com.example.admin.service.impl.IPWhitelistServiceImpl.selectIPWhitelistPage(IPWhitelistServiceImpl.java:58)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)
	at com.example.admin.service.impl.IPWhitelistServiceImpl$$SpringCGLIB$$0.selectIPWhitelistPage(<generated>)
	at com.example.admin.controller.IPWhitelistController.pageIPWhitelists(IPWhitelistController.java:62)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.example.admin.controller.IPWhitelistController$$SpringCGLIB$$0.pageIPWhitelists(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'enabled' in 'where clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy3/jdk.proxy3.$Proxy151.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy149.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy148.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 73 common frames omitted
2025-08-21 17:45:04.161 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 9ms
2025-08-21 17:45:04.591 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:04.612 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 21ms
2025-08-21 17:45:04.955 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:04.975 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 20ms
2025-08-21 17:45:15.496 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:15.528 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 32ms
2025-08-21 17:45:34.389 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:34.418 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 29ms
2025-08-21 17:45:34.421 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:34.446 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 25ms
2025-08-21 17:45:34.725 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:34.747 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 23ms
2025-08-21 17:45:34.790 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:34.814 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 25ms
2025-08-21 17:45:58.747 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:58.758 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:58.798 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 40ms
2025-08-21 17:45:58.816 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 69ms
2025-08-21 17:45:59.129 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:59.167 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:45:59.167 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 38ms
2025-08-21 17:45:59.206 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 39ms
2025-08-21 17:46:03.235 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:03.272 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 37ms
2025-08-21 17:46:06.065 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:06.086 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 21ms
2025-08-21 17:46:12.040 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:12.066 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 26ms
2025-08-21 17:46:12.424 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:12.445 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 21ms
2025-08-21 17:46:15.853 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:15.864 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 17:46:21.313 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:21.356 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 43ms
2025-08-21 17:46:41.247 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:41.247 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:41.273 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 26ms
2025-08-21 17:46:41.280 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 33ms
2025-08-21 17:46:41.648 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:41.671 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:41.685 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 37ms
2025-08-21 17:46:41.703 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 32ms
2025-08-21 17:46:41.753 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:41.771 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:41.801 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 48ms
2025-08-21 17:46:41.801 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 30ms
2025-08-21 17:46:53.563 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:46:53.575 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 17:47:03.009 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:47:03.065 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 56ms
2025-08-21 17:47:03.159 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:47:03.196 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 37ms
2025-08-21 17:47:24.619 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:47:24.637 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 17:47:24.684 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:47:24.695 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 17:47:39.547 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:47:39.558 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 17:47:39.735 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:47:39.753 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 17:47:52.807 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:47:52.829 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 22ms
2025-08-21 17:47:52.877 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:47:52.890 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 17:48:05.006 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:48:05.025 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 19ms
2025-08-21 17:48:05.086 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 17:48:05.106 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 20ms
2025-08-21 17:48:44.688 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 17:48:44.737 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 36968 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 17:48:44.737 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 17:48:44.739 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 17:48:45.626 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 17:48:45.628 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 17:48:45.658 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-08-21 17:48:46.246 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 17:48:46.257 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 17:48:46.259 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 17:48:46.260 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 17:48:46.355 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 17:48:46.356 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1573 ms
2025-08-21 17:48:46.450 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 17:48:46.540 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 17:48:47.018 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 17:48:47.022 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 17:48:47.025 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 17:48:47.695 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 17:48:47.717 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 17:48:47.719 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 17:48:47.720 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 17:48:47.721 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 17:48:47.823 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 17:48:47.838 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 17:48:47.853 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 17:48:47.976 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 17:48:47.989 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 17:48:48.011 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 17:48:48.019 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 17:48:48.582 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 17:48:48.660 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 17:48:48.677 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-21 17:48:48.687 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-21 17:48:48.784 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-21 17:48:48.801 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-08-21 17:57:44.528 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-21 17:57:44.533 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-21 17:59:49.553 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 17:59:49.600 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 39968 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 17:59:49.601 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 17:59:49.601 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 17:59:50.493 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 17:59:50.496 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 17:59:50.525 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-08-21 17:59:51.104 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 17:59:51.114 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 17:59:51.117 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 17:59:51.118 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 17:59:51.225 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 17:59:51.226 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1580 ms
2025-08-21 17:59:51.366 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 17:59:51.497 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 17:59:51.998 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 17:59:52.003 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 17:59:52.005 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 17:59:52.643 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 17:59:52.663 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 17:59:52.665 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 17:59:52.666 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 17:59:52.666 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 17:59:52.764 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 17:59:52.777 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 17:59:52.794 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 17:59:52.912 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 17:59:52.927 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 17:59:52.944 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 17:59:52.951 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 17:59:53.602 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 17:59:53.670 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 17:59:53.696 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-08-21 17:59:53.706 [main] INFO  com.example.admin.AdminApplication - Started AdminApplication in 4.54 seconds (process running for 5.024)
2025-08-21 18:00:55.692 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 18:00:55.693 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-21 18:00:55.694 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-21 18:00:55.716 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:00:55.920 [http-nio-8081-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-21 18:00:56.398 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 683ms
2025-08-21 18:00:58.035 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:00:58.088 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 53ms
2025-08-21 18:01:09.768 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:01:09.792 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 24ms
2025-08-21 18:03:40.554 [http-nio-8081-exec-4] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/ip-rules
2025-08-21 18:03:40.558 [http-nio-8081-exec-4] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/ip-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 18:03:40.748 [http-nio-8081-exec-5] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/ip-rules
2025-08-21 18:03:40.749 [http-nio-8081-exec-5] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/ip-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 18:31:25.455 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:31:25.533 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 78ms
2025-08-21 18:31:30.018 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 18:31:30.037 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 19ms
2025-08-21 18:31:30.335 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:31:30.352 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 18:31:32.255 [http-nio-8081-exec-9] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/ip-rules
2025-08-21 18:31:32.256 [http-nio-8081-exec-9] ERROR c.e.admin.config.WebConfig$GlobalExceptionHandler - 系统异常
org.springframework.web.servlet.NoHandlerFoundException: No endpoint GET /api/ip-rules.
	at org.springframework.web.servlet.DispatcherServlet.noHandlerFound(DispatcherServlet.java:1304)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 18:32:01.153 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 18:32:01.153 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:32:01.160 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 18:32:01.171 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 18:32:02.196 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:32:02.211 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 18:32:05.573 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 18:32:05.575 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:32:05.584 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 11ms
2025-08-21 18:32:05.595 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 22ms
2025-08-21 18:32:21.628 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:32:21.679 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 51ms
2025-08-21 18:32:22.049 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:32:22.062 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 18:37:44.152 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 18:37:44.394 [http-nio-8081-exec-7] ERROR c.example.admin.controller.IPWhitelistController - 分页查询IP白名单失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'priority' in 'order clause'
### The error may exist in com/example/admin/mapper/IPWhitelistMapper.java (best guess)
### The error may involve com.example.admin.mapper.IPWhitelistMapper.selectByTenantIdAndEnabled-Inline
### The error occurred while setting parameters
### SQL: SELECT * FROM ip_whitelist WHERE tenant_id = ? AND status = ? AND deleted = 0 ORDER BY priority DESC
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'priority' in 'order clause'
; bad SQL grammar []
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:246)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy113.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy118.selectByTenantIdAndEnabled(Unknown Source)
	at com.example.admin.service.impl.IPWhitelistServiceImpl.selectIPWhitelistPage(IPWhitelistServiceImpl.java:58)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)
	at com.example.admin.service.impl.IPWhitelistServiceImpl$$SpringCGLIB$$0.selectIPWhitelistPage(<generated>)
	at com.example.admin.controller.IPWhitelistController.pageIPWhitelists(IPWhitelistController.java:62)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.example.admin.controller.IPWhitelistController$$SpringCGLIB$$0.pageIPWhitelists(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'priority' in 'order clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy3/jdk.proxy3.$Proxy151.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy149.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy148.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 73 common frames omitted
2025-08-21 18:37:44.401 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 249ms
2025-08-21 18:38:14.366 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 18:38:14.375 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 9ms
2025-08-21 18:38:14.675 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:38:14.686 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 18:38:35.039 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:38:35.049 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 18:38:43.860 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:38:43.872 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 18:39:02.364 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:39:02.377 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 18:39:09.343 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: POST, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:39:09.417 [http-nio-8081-exec-2] DEBUG c.e.a.c.MybatisPlusConfig$AuditMetaObjectHandler - Auto-fill insert fields completed
2025-08-21 18:39:09.464 [http-nio-8081-exec-2] INFO  c.e.admin.service.impl.TenantFlowRuleServiceImpl - 创建租户流控规则成功: tenantId=111, ruleName=1111
2025-08-21 18:39:09.464 [http-nio-8081-exec-2] INFO  c.e.admin.service.impl.TenantFlowRuleServiceImpl - 发布租户流控规则到网关: tenantId=111
2025-08-21 18:39:09.558 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: POST, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 215ms
2025-08-21 18:39:09.879 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:39:09.901 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 22ms
2025-08-21 18:39:13.688 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: DELETE, URI: /api/tenant-flow-rules/6, RemoteAddr: 127.0.0.1
2025-08-21 18:39:13.709 [http-nio-8081-exec-5] DEBUG c.e.a.c.MybatisPlusConfig$AuditMetaObjectHandler - Auto-fill update fields completed
2025-08-21 18:39:13.717 [http-nio-8081-exec-5] INFO  c.e.admin.service.impl.TenantFlowRuleServiceImpl - 删除租户流控规则成功: id=6, tenantId=111
2025-08-21 18:39:13.717 [http-nio-8081-exec-5] INFO  c.e.admin.service.impl.TenantFlowRuleServiceImpl - 发布租户流控规则到网关: tenantId=111
2025-08-21 18:39:13.743 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: DELETE, URI: /api/tenant-flow-rules/6, Status: 200, ExecuteTime: 54ms
2025-08-21 18:39:14.060 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 18:39:14.070 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 18:45:54.181 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 18:45:54.264 [http-nio-8081-exec-7] ERROR c.example.admin.controller.IPWhitelistController - 分页查询IP白名单失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'priority' in 'order clause'
### The error may exist in com/example/admin/mapper/IPWhitelistMapper.java (best guess)
### The error may involve com.example.admin.mapper.IPWhitelistMapper.selectByTenantIdAndEnabled-Inline
### The error occurred while setting parameters
### SQL: SELECT * FROM ip_whitelist WHERE tenant_id = ? AND status = ? AND deleted = 0 ORDER BY priority DESC
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'priority' in 'order clause'
; bad SQL grammar []
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:246)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy113.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy118.selectByTenantIdAndEnabled(Unknown Source)
	at com.example.admin.service.impl.IPWhitelistServiceImpl.selectIPWhitelistPage(IPWhitelistServiceImpl.java:58)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)
	at com.example.admin.service.impl.IPWhitelistServiceImpl$$SpringCGLIB$$0.selectIPWhitelistPage(<generated>)
	at com.example.admin.controller.IPWhitelistController.pageIPWhitelists(IPWhitelistController.java:62)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.example.admin.controller.IPWhitelistController$$SpringCGLIB$$0.pageIPWhitelists(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'priority' in 'order clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy3/jdk.proxy3.$Proxy151.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy149.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy148.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 73 common frames omitted
2025-08-21 18:45:54.269 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 88ms
2025-08-21 18:46:15.884 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-21 18:46:15.886 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-21 19:00:46.663 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 19:00:46.722 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 4860 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 19:00:46.722 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 19:00:46.724 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 19:00:47.605 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 19:00:47.607 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 19:00:47.638 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-08-21 19:00:48.224 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 19:00:48.233 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 19:00:48.235 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 19:00:48.236 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 19:00:48.337 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 19:00:48.337 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1560 ms
2025-08-21 19:00:48.436 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 19:00:48.542 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 19:00:48.934 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 19:00:48.939 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 19:00:48.940 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 19:00:49.674 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 19:00:49.702 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 19:00:49.705 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 19:00:49.706 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 19:00:49.707 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 19:00:49.818 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 19:00:49.831 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 19:00:49.845 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 19:00:49.962 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 19:00:49.977 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 19:00:49.997 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 19:00:50.003 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 19:00:50.511 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 19:00:50.580 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 19:00:50.624 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-08-21 19:00:50.639 [main] INFO  com.example.admin.AdminApplication - Started AdminApplication in 4.472 seconds (process running for 4.983)
2025-08-21 19:01:09.558 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-21 19:01:09.603 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 25412 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-21 19:01:09.603 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-21 19:01:09.604 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 19:01:10.505 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-21 19:01:10.508 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-21 19:01:10.544 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-08-21 19:01:11.114 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-21 19:01:11.122 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 19:01:11.124 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 19:01:11.125 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-21 19:01:11.208 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 19:01:11.209 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1564 ms
2025-08-21 19:01:11.328 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-21 19:01:11.466 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-21 19:01:11.917 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-21 19:01:11.922 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-21 19:01:11.924 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-21 19:01:12.503 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-21 19:01:12.542 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-21 19:01:12.546 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-21 19:01:12.548 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-21 19:01:12.549 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-21 19:01:12.657 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-21 19:01:12.672 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-21 19:01:12.682 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-21 19:01:12.775 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-21 19:01:12.784 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-21 19:01:12.800 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-21 19:01:12.809 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-21 19:01:13.282 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-21 19:01:13.343 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 19:01:13.356 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-21 19:01:13.372 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-08-21 19:01:13.459 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-21 19:01:13.473 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-08-21 19:01:34.538 [http-nio-8081-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 19:01:34.538 [http-nio-8081-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-21 19:01:34.539 [http-nio-8081-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-21 19:01:34.559 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:01:34.559 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:01:34.734 [http-nio-8081-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-21 19:01:35.162 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 603ms
2025-08-21 19:01:35.162 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 603ms
2025-08-21 19:01:45.546 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:01:45.583 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 37ms
2025-08-21 19:01:47.098 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:01:47.117 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 19ms
2025-08-21 19:01:48.277 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 19:01:48.304 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 27ms
2025-08-21 19:02:08.225 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:02:08.242 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:02:11.395 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:02:11.404 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 8ms
2025-08-21 19:02:11.703 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:02:11.720 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 19:02:13.328 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:02:13.343 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 19:02:13.940 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:02:13.950 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 10ms
2025-08-21 19:02:14.241 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:02:14.266 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 25ms
2025-08-21 19:02:14.857 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:02:14.869 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:02:15.767 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:02:15.784 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 19:02:19.884 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:02:19.884 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:02:19.890 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 6ms
2025-08-21 19:02:19.898 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:06:13.912 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:06:13.912 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:06:14.000 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 87ms
2025-08-21 19:06:14.009 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 97ms
2025-08-21 19:06:46.645 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:06:46.656 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:07:03.737 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:07:03.737 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:03.745 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 8ms
2025-08-21 19:07:03.751 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:07:06.041 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:07:06.049 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 8ms
2025-08-21 19:07:06.217 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:06.232 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 19:07:07.719 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:07.735 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:07:09.440 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:09.461 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 22ms
2025-08-21 19:07:10.506 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 19:07:10.512 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 6ms
2025-08-21 19:07:11.279 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:11.291 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:07:12.486 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:12.486 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:07:12.493 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 19:07:12.498 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:07:12.750 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:12.763 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 19:07:13.739 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:13.760 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 21ms
2025-08-21 19:07:15.135 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 19:07:15.141 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 6ms
2025-08-21 19:07:16.925 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:16.938 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 19:07:18.633 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:18.654 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 21ms
2025-08-21 19:07:19.180 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:19.202 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 22ms
2025-08-21 19:07:20.032 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:20.055 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 23ms
2025-08-21 19:07:20.763 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:20.779 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:07:22.091 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:07:22.115 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 24ms
2025-08-21 19:08:08.209 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:08:08.219 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 10ms
2025-08-21 19:08:08.511 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:08:08.524 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 19:08:11.273 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:08:11.284 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:11:45.703 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:11:45.755 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 52ms
2025-08-21 19:11:51.298 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:11:51.306 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 8ms
2025-08-21 19:11:51.606 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:11:51.622 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:12:09.220 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:12:09.232 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:12:11.002 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:12:11.010 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 8ms
2025-08-21 19:12:13.276 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 19:12:13.279 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 3ms
2025-08-21 19:12:13.840 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:12:13.850 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 19:12:14.812 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:12:14.812 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:12:14.819 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 19:12:14.826 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:12:16.198 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:12:16.208 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 19:13:56.190 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:13:56.210 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 20ms
2025-08-21 19:14:05.664 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:14:05.680 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:15:34.857 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:15:34.873 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:16:38.677 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:16:38.694 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 19:16:42.465 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:16:42.472 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 19:16:42.778 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:16:42.794 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:16:43.705 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:16:43.716 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:17:37.950 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:17:37.960 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 10ms
2025-08-21 19:17:38.264 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:38.280 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:17:43.071 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:43.081 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 19:17:46.143 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:46.160 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 19:17:47.247 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:17:47.247 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:47.253 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 6ms
2025-08-21 19:17:47.259 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:17:47.592 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:47.601 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 9ms
2025-08-21 19:17:48.475 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:17:48.475 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:48.485 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 10ms
2025-08-21 19:17:48.511 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 36ms
2025-08-21 19:17:49.455 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:49.478 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 23ms
2025-08-21 19:17:49.959 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:17:49.968 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 9ms
2025-08-21 19:17:50.264 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:50.278 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:17:50.994 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:51.001 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 7ms
2025-08-21 19:17:51.394 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:17:51.406 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 12ms
2025-08-21 19:17:51.692 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:51.703 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:17:52.312 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:52.323 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:17:53.483 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:17:53.491 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 8ms
2025-08-21 19:17:53.789 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:53.800 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:17:54.900 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:54.917 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 19:17:56.299 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:17:56.306 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 19:17:56.604 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:17:56.616 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:18:07.428 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:18:07.436 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 8ms
2025-08-21 19:19:19.099 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:19:19.099 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:19.106 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 19:19:19.114 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 19:19:20.215 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:20.229 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:19:22.498 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 19:19:22.518 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 20ms
2025-08-21 19:19:22.787 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:22.798 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:19:24.838 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:19:24.838 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:24.846 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 8ms
2025-08-21 19:19:24.854 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:19:25.686 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:25.702 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 19:19:29.545 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:29.545 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:19:29.552 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 19:19:29.562 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 19:19:30.111 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:30.120 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 9ms
2025-08-21 19:19:31.642 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 19:19:31.645 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 3ms
2025-08-21 19:19:31.899 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:31.913 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:19:33.971 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:19:33.980 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 9ms
2025-08-21 19:19:34.250 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:34.263 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 19:19:34.822 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:34.833 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:19:42.633 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:42.643 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 19:19:43.925 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:19:43.931 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 6ms
2025-08-21 19:19:44.238 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:44.248 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 19:19:45.839 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:45.848 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 9ms
2025-08-21 19:19:46.407 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:19:46.413 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 6ms
2025-08-21 19:19:46.709 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:46.718 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 9ms
2025-08-21 19:19:54.678 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:19:54.692 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:24:47.524 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:24:47.587 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 63ms
2025-08-21 19:24:49.255 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:24:49.255 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:24:49.261 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 6ms
2025-08-21 19:24:49.266 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:24:50.966 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:24:50.976 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 19:24:51.538 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 19:24:51.543 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 5ms
2025-08-21 19:24:55.066 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:24:55.078 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:25:06.829 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:25:06.840 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:26:45.548 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:26:45.559 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:26:46.809 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:26:46.821 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:26:55.760 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:26:55.760 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:26:55.770 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 10ms
2025-08-21 19:26:55.777 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 19:26:56.998 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:26:57.005 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 7ms
2025-08-21 19:26:58.583 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:26:58.594 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:27:04.757 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:27:04.769 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:30:12.073 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:30:12.134 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 61ms
2025-08-21 19:30:15.287 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:30:15.294 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 6ms
2025-08-21 19:30:15.592 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:30:15.604 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:30:18.278 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:30:18.288 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 19:32:36.567 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:32:36.567 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:32:36.615 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 48ms
2025-08-21 19:32:36.621 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 54ms
2025-08-21 19:32:39.049 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:32:39.060 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:34:06.187 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:34:06.198 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:34:20.605 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:34:20.619 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:35:40.020 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:35:40.039 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:35:40.039 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 19ms
2025-08-21 19:35:40.052 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:35:40.057 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 19:35:40.072 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 20ms
2025-08-21 19:35:40.119 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:35:40.138 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 19ms
2025-08-21 19:35:40.147 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:35:40.165 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 19:35:40.417 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:35:40.430 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 19:36:17.274 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:36:17.287 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 19:36:32.028 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:36:32.043 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 19:36:57.007 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:36:57.007 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:36:57.058 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 51ms
2025-08-21 19:36:57.067 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 60ms
2025-08-21 19:37:00.026 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:37:00.043 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 19:37:02.122 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:37:02.132 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 19:39:07.099 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:39:07.172 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 73ms
2025-08-21 19:39:08.080 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:39:08.102 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 22ms
2025-08-21 19:39:09.115 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:39:09.123 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 8ms
2025-08-21 19:39:09.427 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:39:09.446 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 19ms
2025-08-21 19:39:11.447 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:39:11.464 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 19:39:12.009 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:39:12.022 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 19:39:12.317 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:39:12.332 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 19:39:26.437 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:39:26.442 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:39:26.458 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 21ms
2025-08-21 19:39:26.465 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 23ms
2025-08-21 19:39:26.524 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:39:26.540 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:39:26.783 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:39:26.795 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:41:13.506 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:41:13.506 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:41:13.514 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 8ms
2025-08-21 19:41:13.521 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 19:41:18.619 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:41:18.634 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 19:41:32.669 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:41:32.677 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 8ms
2025-08-21 19:42:54.874 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:42:54.887 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 19:42:55.041 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:42:55.052 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:44:03.230 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:44:03.230 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:44:03.237 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 19:44:03.246 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:44:05.586 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:44:05.600 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:44:07.853 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:44:07.853 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:44:07.860 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 19:44:07.869 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:44:59.561 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:44:59.582 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 21ms
2025-08-21 19:44:59.607 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:44:59.639 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 32ms
2025-08-21 19:44:59.701 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:44:59.735 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 34ms
2025-08-21 19:44:59.810 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:44:59.828 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 19:45:29.588 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:45:29.592 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:45:29.612 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 24ms
2025-08-21 19:45:29.644 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 52ms
2025-08-21 19:45:29.977 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:45:29.987 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 10ms
2025-08-21 19:45:30.290 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:45:30.323 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 33ms
2025-08-21 19:45:49.786 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:45:49.830 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 44ms
2025-08-21 19:47:29.052 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:47:29.063 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:47:30.425 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:47:30.433 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 8ms
2025-08-21 19:47:32.516 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:47:32.528 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:49:09.765 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:49:09.827 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 61ms
2025-08-21 19:49:09.846 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:49:09.895 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 49ms
2025-08-21 19:49:10.090 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:49:10.124 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 34ms
2025-08-21 19:49:10.347 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:49:10.365 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 19:50:29.908 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:50:29.926 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 19:50:29.936 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:50:29.957 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 21ms
2025-08-21 19:50:30.000 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:50:30.029 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 29ms
2025-08-21 19:50:30.378 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:50:30.395 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 19:55:13.912 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:14.009 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 97ms
2025-08-21 19:55:27.643 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:27.643 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:55:27.651 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 8ms
2025-08-21 19:55:27.667 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 24ms
2025-08-21 19:55:29.535 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:29.547 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:55:30.900 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 19:55:30.903 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 3ms
2025-08-21 19:55:32.136 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:55:32.142 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 6ms
2025-08-21 19:55:32.447 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:32.458 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:55:34.346 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:34.357 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:55:46.261 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:55:46.273 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 12ms
2025-08-21 19:55:46.574 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:46.589 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 19:55:47.626 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:47.637 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 11ms
2025-08-21 19:55:48.215 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:55:48.227 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 12ms
2025-08-21 19:55:48.527 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:48.558 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 31ms
2025-08-21 19:55:49.082 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:49.094 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 12ms
2025-08-21 19:55:50.004 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:50.019 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 19:55:50.922 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:55:50.922 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:50.929 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 19:55:50.945 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 23ms
2025-08-21 19:55:51.862 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:51.878 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:55:52.887 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 19:55:52.890 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 3ms
2025-08-21 19:55:53.622 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:53.642 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 20ms
2025-08-21 19:55:54.585 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-21 19:55:54.586 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:54.592 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 7ms
2025-08-21 19:55:54.600 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:55:54.844 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:55:54.858 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 19:55:55.837 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-21 19:55:55.840 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 3ms
2025-08-21 19:56:00.372 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:56:00.388 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:56:02.448 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:56:02.458 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 10ms
2025-08-21 19:57:53.637 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:57:53.653 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 16ms
2025-08-21 19:57:53.811 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 19:57:53.830 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 19ms
2025-08-21 20:03:34.679 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:03:34.790 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 112ms
2025-08-21 20:06:02.714 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:06:02.801 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 87ms
2025-08-21 20:06:02.836 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:06:02.845 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 9ms
2025-08-21 20:06:14.623 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:06:14.640 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 20:06:14.726 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:06:14.743 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 20:06:24.682 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:06:24.710 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 28ms
2025-08-21 20:06:24.853 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:06:24.866 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 13ms
2025-08-21 20:06:43.651 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:06:43.668 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 20:06:43.797 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:06:43.819 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 22ms
2025-08-21 20:06:53.439 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:06:53.471 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 32ms
2025-08-21 20:06:53.615 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:06:53.636 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 21ms
2025-08-21 20:07:13.741 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:07:13.759 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 20:07:15.939 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:07:15.985 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 46ms
2025-08-21 20:07:28.491 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: POST, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:07:28.630 [http-nio-8081-exec-2] WARN  c.e.admin.config.WebConfig$GlobalExceptionHandler - 参数验证失败
org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.example.admin.common.Result<java.lang.String> com.example.admin.controller.IPFlowRuleController.createIPFlowRule(com.example.admin.dto.IPFlowRuleDTO) with 2 errors: [Field error in object 'IPFlowRuleDTO' on field 'ruleType': rejected value [null]; codes [NotBlank.IPFlowRuleDTO.ruleType,NotBlank.ruleType,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [IPFlowRuleDTO.ruleType,ruleType]; arguments []; default message [ruleType]]; default message [规则类型不能为空]] [Field error in object 'IPFlowRuleDTO' on field 'tenantId': rejected value [null]; codes [NotBlank.IPFlowRuleDTO.tenantId,NotBlank.tenantId,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [IPFlowRuleDTO.tenantId,tenantId]; arguments []; default message [tenantId]]; default message [租户ID不能为空]] 
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:143)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:226)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:179)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 20:07:28.643 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: POST, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 152ms
2025-08-21 20:07:41.273 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:07:41.290 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 20:07:41.582 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:07:41.637 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 55ms
2025-08-21 20:10:20.915 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:10:20.995 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 80ms
2025-08-21 20:10:21.004 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:10:21.019 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 20:10:32.733 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:10:32.750 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 17ms
2025-08-21 20:10:32.827 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:10:32.845 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 18ms
2025-08-21 20:10:43.084 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:10:43.099 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 15ms
2025-08-21 20:10:43.187 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-21 20:10:43.201 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 14ms
2025-08-21 20:11:24.626 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-21 20:11:24.630 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
