package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.FlowControlLogDTO;
import com.example.common.entity.FlowControlLog;
import com.example.admin.vo.FlowControlLogVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 流控日志服务接口
 */
public interface FlowControlLogService extends IService<FlowControlLog> {
    
    /**
     * 分页查询流控日志
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @param eventType 事件类型
     * @param clientIp 客户端IP
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 流控日志VO分页结果
     */
    Page<FlowControlLogVO> selectFlowControlLogPage(Page<FlowControlLogVO> page, String tenantId, String resourceName,
                                                    String eventType, String clientIp, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据ID查询流控日志详情
     *
     * @param id 日志ID
     * @return 流控日志VO
     */
    FlowControlLogVO getFlowControlLogById(Long id);
    
    /**
     * 创建流控日志记录
     *
     * @param flowControlLogDTO 流控日志DTO
     * @return 是否成功
     */
    boolean createFlowControlLog(FlowControlLogDTO flowControlLogDTO);
    
    /**
     * 更新流控日志记录
     *
     * @param id 日志ID
     * @param flowControlLogDTO 流控日志DTO
     * @return 是否成功
     */
    boolean updateFlowControlLog(Long id, FlowControlLogDTO flowControlLogDTO);
    
    /**
     * 删除流控日志记录
     *
     * @param id 日志ID
     * @return 是否成功
     */
    boolean deleteFlowControlLog(Long id);
    
    /**
     * 批量删除流控日志记录
     *
     * @param ids 日志ID列表
     * @return 是否成功
     */
    boolean batchDeleteFlowControlLogs(List<Long> ids);
    
    /**
     * 根据时间范围查询流控日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param eventType 事件类型（可选）
     * @param limit 限制数量
     * @return 流控日志列表
     */
    List<FlowControlLogVO> getFlowControlLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime,
                                                          String tenantId, String resourceName, String eventType, Integer limit);
    
    /**
     * 根据租户ID查询流控日志
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param eventType 事件类型（可选）
     * @param limit 限制数量
     * @return 流控日志列表
     */
    List<FlowControlLogVO> getFlowControlLogsByTenantId(String tenantId, LocalDateTime startTime,
                                                         LocalDateTime endTime, String eventType, Integer limit);
    
    /**
     * 根据资源名称查询流控日志
     *
     * @param resourceName 资源名称
     * @param tenantId 租户ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param eventType 事件类型（可选）
     * @param limit 限制数量
     * @return 流控日志列表
     */
    List<FlowControlLogVO> getFlowControlLogsByResourceName(String resourceName, String tenantId,
                                                             LocalDateTime startTime, LocalDateTime endTime, String eventType, Integer limit);
    
    /**
     * 根据事件类型查询流控日志
     *
     * @param eventType 事件类型
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return 流控日志列表
     */
    List<FlowControlLogVO> getFlowControlLogsByEventType(String eventType, String tenantId, String resourceName,
                                                          LocalDateTime startTime, LocalDateTime endTime, Integer limit);
    
    /**
     * 查询最近的流控日志
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param eventType 事件类型（可选）
     * @param minutes 最近多少分钟
     * @param limit 限制数量
     * @return 最近的流控日志列表
     */
    List<FlowControlLogVO> getRecentFlowControlLogs(String tenantId, String resourceName, String eventType,
                                                     Integer minutes, Integer limit);
    
    /**
     * 统计事件类型分布
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 事件类型分布统计
     */
    List<Map<String, Object>> getEventTypeDistribution(String tenantId, String resourceName,
                                                        LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计资源访问情况
     *
     * @param tenantId 租户ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return 资源访问统计
     */
    List<Map<String, Object>> getResourceAccessStatistics(String tenantId, LocalDateTime startTime,
                                                           LocalDateTime endTime, Integer limit);
    
    /**
     * 统计租户日志情况
     *
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return 租户日志统计
     */
    List<Map<String, Object>> getTenantLogStatistics(LocalDateTime startTime, LocalDateTime endTime, Integer limit);
    
    /**
     * 统计IP访问情况
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return IP访问统计
     */
    List<Map<String, Object>> getIpAccessStatistics(String tenantId, String resourceName,
                                                     LocalDateTime startTime, LocalDateTime endTime, Integer limit);
    
    /**
     * 查询异常日志
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return 异常日志列表
     */
    List<FlowControlLogVO> getAbnormalLogs(String tenantId, String resourceName,
                                           LocalDateTime startTime, LocalDateTime endTime, Integer limit);
    
    /**
     * 查询告警日志
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return 告警日志列表
     */
    List<FlowControlLogVO> getAlarmLogs(String tenantId, String resourceName,
                                        LocalDateTime startTime, LocalDateTime endTime, Integer limit);
    
    /**
     * 批量插入流控日志
     *
     * @param flowControlLogList 流控日志列表
     * @return 是否成功
     */
    boolean batchInsertFlowControlLogs(List<FlowControlLog> flowControlLogList);
    
    /**
     * 删除过期的日志数据
     *
     * @param beforeTime 过期时间点
     * @return 删除的记录数
     */
    int deleteExpiredLogs(LocalDateTime beforeTime);
    
    /**
     * 获取日志趋势数据
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param eventType 事件类型（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 时间间隔（分钟）
     * @return 趋势数据列表
     */
    List<Map<String, Object>> getLogTrendData(String tenantId, String resourceName, String eventType,
                                               LocalDateTime startTime, LocalDateTime endTime, Integer interval);
    
    /**
     * 统计日志数量
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param eventType 事件类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 日志数量
     */
    Long getLogCount(String tenantId, String resourceName, String eventType,
                     LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 导出流控日志
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param eventType 事件类型（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 流控日志列表
     */
    List<FlowControlLogVO> exportFlowControlLogs(String tenantId, String resourceName, String eventType,
                                                  LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取日志汇总信息
     *
     * @param tenantId 租户ID（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组字段（hour, day, month）
     * @return 汇总信息列表
     */
    List<Map<String, Object>> getLogSummary(String tenantId, LocalDateTime startTime,
                                             LocalDateTime endTime, String groupBy);
    
    /**
     * 获取热点IP列表
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 热点IP列表
     */
    List<Map<String, Object>> getHotIpList(String tenantId, String resourceName,
                                            LocalDateTime startTime, LocalDateTime endTime, Integer limit);
    
    /**
     * 获取攻击检测结果
     *
     * @param tenantId 租户ID（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param threshold 攻击阈值
     * @param limit 限制数量
     * @return 攻击检测结果
     */
    List<Map<String, Object>> getAttackDetectionResults(String tenantId, LocalDateTime startTime,
                                                         LocalDateTime endTime, Integer threshold, Integer limit);
    
    /**
     * 清理日志数据
     *
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanupLogData(Integer retentionDays);
    
    /**
     * 获取日志存储统计
     *
     * @return 存储统计信息
     */
    Map<String, Object> getLogStorageStatistics();
    
    /**
     * 同步日志到外部系统
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param targetSystem 目标系统
     * @return 是否成功
     */
    boolean syncLogsToExternalSystem(LocalDateTime startTime, LocalDateTime endTime, String targetSystem);
    
    /**
     * 生成日志报告
     *
     * @param tenantId 租户ID（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param reportType 报告类型
     * @return 报告内容
     */
    Map<String, Object> generateLogReport(String tenantId, LocalDateTime startTime,
                                           LocalDateTime endTime, String reportType);
}