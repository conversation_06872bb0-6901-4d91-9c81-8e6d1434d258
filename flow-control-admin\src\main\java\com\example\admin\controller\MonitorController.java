package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.Result;
import com.example.admin.dto.MonitorDataDTO;
import com.example.admin.dto.MonitorStatisticsDTO;
import com.example.admin.dto.StatisticsDTO;
import com.example.common.entity.MonitorStatistics;
import com.example.admin.service.MonitorStatisticsService;
import com.example.admin.vo.DashboardVO;
import com.example.admin.vo.MonitorVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 监控统计控制器
 * 提供实时监控、历史统计、性能分析等功能
 */
@Tag(name = "监控统计管理")
@RestController
@RequestMapping("/api/monitor")
@Validated
public class MonitorController {

    private static final Logger log = LoggerFactory.getLogger(MonitorController.class);

    @Autowired
    private MonitorStatisticsService monitorStatisticsService;

    /**
     * 获取仪表盘数据
     */
    @Operation(summary = "获取仪表盘数据")
    @GetMapping("/dashboard")
    public Result<Object> getDashboardData(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        try {
            Map<String, Object> result = monitorStatisticsService.getDashboardData(tenantId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取仪表盘数据失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时监控数据
     */
    @Operation(summary = "获取实时监控数据")
    @GetMapping("/realtime")
    public Result<Object> getRealtimeData(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "资源名称") @RequestParam(required = false) String resourceName,
            @Parameter(description = "时间范围（分钟）") @RequestParam(defaultValue = "30") int timeRange) {
        try {
            List<MonitorVO> result = monitorStatisticsService.getRealtimeMonitorData(tenantId, resourceName, timeRange, null);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取实时监控数据失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询监控统计数据
     */
    @Operation(summary = "分页查询监控统计数据")
    @GetMapping("/statistics")
    public Result<Page<MonitorVO>> pageMonitorStatistics(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "资源名称") @RequestParam(required = false) String resourceName,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            Page<MonitorVO> pageParam = new Page<>(page, size);
            Page<MonitorVO> result = monitorStatisticsService.selectMonitorStatisticsPage(pageParam, tenantId, resourceName, startTime, endTime);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询监控统计数据失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取趋势数据
     */
    @Operation(summary = "获取趋势数据")
    @GetMapping("/trend")
    public Result<List<StatisticsDTO>> getTrendData(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "资源名称") @RequestParam(required = false) String resourceName,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "粒度：HOURLY, DAILY") @RequestParam(defaultValue = "HOURLY") String granularity) {
        try {
            // 将granularity转换为interval（分钟）
            Integer interval = "HOURLY".equals(granularity) ? 60 : 1440; // 小时=60分钟，日=1440分钟
            List<Map<String, Object>> trendData = monitorStatisticsService.getTrendData(tenantId, resourceName, startTime, endTime, interval);
            // 将Map转换为StatisticsDTO（简化处理）
            List<StatisticsDTO> result = trendData.stream().map(map -> {
                StatisticsDTO dto = new StatisticsDTO();
                // 这里需要根据实际Map结构进行转换
                return dto;
            }).collect(Collectors.toList());
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取趋势数据失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取资源QPS排行
     */
    @Operation(summary = "获取资源QPS排行")
    @GetMapping("/ranking/qps")
    public Result<List<Map<String, Object>>> getQPSRanking(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "排行数量") @RequestParam(defaultValue = "10") int limit,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<Map<String, Object>> result = monitorStatisticsService.getResourceQpsRanking(tenantId, startTime, endTime, limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取资源QPS排行失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取资源响应时间排行
     */
    @Operation(summary = "获取资源响应时间排行")
    @GetMapping("/ranking/rt")
    public Result<List<Map<String, Object>>> getResponseTimeRanking(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "排行数量") @RequestParam(defaultValue = "10") int limit,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<Map<String, Object>> result = monitorStatisticsService.getResourceResponseTimeRanking(tenantId, startTime, endTime, limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取资源响应时间排行失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户统计数据
     */
    @Operation(summary = "获取租户统计数据")
    @GetMapping("/tenant/{tenantId}/statistics")
    public Result<Map<String, Object>> getTenantStatistics(
            @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<Map<String, Object>> tenantStats = monitorStatisticsService.getTenantStatistics(startTime, endTime, null);
            // 将List转换为Map格式返回
            Map<String, Object> result = Map.of("tenantStatistics", tenantStats);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取租户统计数据失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统概览数据
     */
    @Operation(summary = "获取系统概览数据")
    @GetMapping("/overview")
    public Result<Map<String, Object>> getSystemOverview(
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            Map<String, Object> result = monitorStatisticsService.getSystemOverview(startTime, endTime);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取系统概览数据失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 批量插入监控数据
     */
    @Operation(summary = "批量插入监控数据")
    @PostMapping("/batch")
    public Result<String> batchInsertMonitorData(
            @Parameter(description = "监控数据列表") @RequestBody List<MonitorStatisticsDTO> monitorDataList) {
        try {
            // 将DTO转换为实体对象
            List<MonitorStatistics> monitorStatisticsList = monitorDataList.stream()
                .map(dto -> {
                    MonitorStatistics entity = new MonitorStatistics();
                    entity.setTenantId(dto.getTenantId());
                    entity.setResourceName(dto.getResourceName());
                    entity.setTotalRequests(dto.getTotalRequests().longValue());
                    entity.setAvgRt(java.math.BigDecimal.valueOf(dto.getAvgRt()));
                    entity.setPassRequests(dto.getSuccessCount());
                    entity.setBlockRequests(dto.getBlockedCount());
                    entity.setStatTime(LocalDateTime.now());
                    return entity;
                })
                .collect(Collectors.toList());
            boolean result = monitorStatisticsService.batchInsertMonitorStatistics(monitorStatisticsList);
            if (result) {
                return Result.success("批量插入成功");
            } else {
                return Result.error("批量插入失败");
            }
        } catch (Exception e) {
            log.error("批量插入监控数据失败", e);
            return Result.error("批量插入失败: " + e.getMessage());
        }
    }

    /**
     * 删除过期数据
     */
    @Operation(summary = "删除过期数据")
    @DeleteMapping("/expired")
    public Result<String> deleteExpiredData(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "30") int retentionDays) {
        try {
            // 计算过期时间点
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(retentionDays);
            int deletedCount = monitorStatisticsService.deleteExpiredData(beforeTime);
            return Result.success("删除过期数据成功，共删除 " + deletedCount + " 条记录");
        } catch (Exception e) {
            log.error("删除过期数据失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取异常数据
     */
    @Operation(summary = "获取异常数据")
    @GetMapping("/abnormal")
    public Result<List<MonitorVO>> getAbnormalData(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "资源名称") @RequestParam(required = false) String resourceName,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "异常阈值") @RequestParam(defaultValue = "1000.0") Double threshold,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "100") Integer limit) {
        try {
            List<MonitorVO> result = monitorStatisticsService.getAbnormalData(tenantId, resourceName, startTime, endTime, threshold, limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取异常数据失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 导出监控数据
     */
    @Operation(summary = "导出监控数据")
    @GetMapping("/export")
    public Result<List<MonitorVO>> exportMonitorData(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "资源名称") @RequestParam(required = false) String resourceName,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<MonitorVO> result = monitorStatisticsService.exportMonitorStatistics(tenantId, resourceName, startTime, endTime);
            return Result.success(result);
        } catch (Exception e) {
            log.error("导出监控数据失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取热点资源
     */
    @Operation(summary = "获取热点资源")
    @GetMapping("/hotspot")
    public Result<List<Map<String, Object>>> getHotspotResources(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<Map<String, Object>> result = monitorStatisticsService.getHotResources(tenantId, startTime, endTime, limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取热点资源失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取性能指标
     */
    @Operation(summary = "获取性能指标")
    @GetMapping("/performance")
    public Result<Map<String, Object>> getPerformanceMetrics(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "资源名称") @RequestParam(required = false) String resourceName,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            Map<String, Object> result = monitorStatisticsService.getPerformanceMetrics(tenantId, resourceName, startTime, endTime);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取性能指标失败", e);
            return Result.error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 数据清理
     */
    @Operation(summary = "数据清理")
    @PostMapping("/cleanup")
    public Result<String> cleanupData(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "30") Integer retentionDays) {
        try {
            int deletedCount = monitorStatisticsService.cleanupMonitorData(retentionDays);
            return Result.success("数据清理成功，共清理 " + deletedCount + " 条记录");
        } catch (Exception e) {
            log.error("数据清理失败", e);
            return Result.error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 获取存储统计
     */
    @Operation(summary = "获取存储统计")
    @GetMapping("/storage/statistics")
    public Result<Map<String, Object>> getStorageStatistics() {
        try {
            Map<String, Object> result = monitorStatisticsService.getStorageStatistics();
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取存储统计失败", e);
            return Result.error("获取统计失败: " + e.getMessage());
        }
    }
}