<template>
  <div class="interface-flow-rules">
    <layout>
      <div class="page-content">
        <div class="page-header">
          <h1>接口限流规则配置</h1>
          <p>设置按租户下某个接口资源的QPS或并发数量，以及Sentinel支持的限流配置</p>
        </div>

        <!-- 操作栏 -->
        <div class="operation-bar">
          <el-button type="primary" icon="el-icon-plus" @click="showAddDialog">
            新增接口规则
          </el-button>
          
          <div class="filter-group">
            <el-select
              v-model="searchForm.tenantId"
              placeholder="选择租户"
              clearable
              style="width: 200px; margin-right: 10px"
              @change="handleSearch"
            >
              <el-option
                v-for="tenant in tenantList"
                :key="tenant.tenantId"
                :label="tenant.tenantName"
                :value="tenant.tenantId"
              />
            </el-select>
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入规则名称或资源名称"
              clearable
              style="width: 250px"
              @keyup.enter.native="handleSearch"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>
        </div>

        <!-- 数据表格 -->
        <el-table
          :data="tableData"
          :loading="loading"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column prop="ruleName" label="规则名称" min-width="150" show-overflow-tooltip />
          <el-table-column prop="tenantId" label="租户ID" width="120" show-overflow-tooltip />
          <el-table-column prop="tenantName" label="租户名称" min-width="150" show-overflow-tooltip />
          <el-table-column prop="resourceName" label="资源名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="limitMode" label="限制模式" width="110">
            <template slot-scope="scope">
              <el-tag :type="scope.row.limitMode === 0 ? 'primary' : 'success'">
                {{ scope.row.limitMode === 0 ? 'QPS' : '并发数' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="threshold" label="阈值" width="80" />
          <el-table-column prop="behavior" label="控制行为" width="110">
            <template slot-scope="scope">
              <el-tag :type="getBehaviorTagType(scope.row.behavior)">
                {{ getBehaviorText(scope.row.behavior) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="warmUpPeriod" label="预热时长(s)" width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.behavior === 1">{{ scope.row.warmUpPeriod }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="queueTimeout" label="排队超时(ms)" width="130">
            <template slot-scope="scope">
              <span v-if="scope.row.behavior === 2">{{ scope.row.queueTimeout }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" @click="editRule(scope.row)">编辑</el-button>
              <el-button
                size="mini"
                :type="scope.row.status === 1 ? 'warning' : 'success'"
                @click="toggleStatus(scope.row)"
              >
                {{ scope.row.status === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button size="mini" type="danger" @click="deleteRule(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          >
          </el-pagination>
        </div>

        <!-- 新增/编辑对话框 -->
        <el-dialog
          :title="isEdit ? '编辑接口规则' : '新增接口规则'"
          :visible.sync="dialogVisible"
          width="600px"
          :close-on-click-modal="false"
        >
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            :rules="computedFormRules"
            label-width="120px"
          >
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称" />
            </el-form-item>
            <el-form-item label="租户" prop="tenantId">
              <el-select v-model="ruleForm.tenantId" placeholder="请选择租户" style="width: 100%">
                <el-option
                  v-for="tenant in tenantList"
                  :key="tenant.tenantId"
                  :label="tenant.tenantName"
                  :value="tenant.tenantId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="资源名称" prop="resourceName">
              <el-input v-model="ruleForm.resourceName" placeholder="请输入资源名称，如：/api/user/list" />
            </el-form-item>
            <el-form-item label="限制模式" prop="limitMode">
              <el-radio-group v-model="ruleForm.limitMode">
                <el-radio :label="0">QPS限制</el-radio>
                <el-radio :label="1">并发数限制</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="阈值" prop="threshold">
              <el-input-number
                v-model="ruleForm.threshold"
                :min="1"
                :max="999999"
                style="width: 100%"
                placeholder="请输入限制阈值"
              />
            </el-form-item>
            <el-form-item label="控制行为" prop="behavior">
              <el-select v-model="ruleForm.behavior" style="width: 100%">
                <el-option label="快速失败" :value="0" />
                <el-option label="预热" :value="1" />
                <el-option label="排队等待" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="ruleForm.behavior === 1"
              label="预热时长(秒)"
              prop="warmUpPeriod"
            >
              <el-input-number
                v-model="ruleForm.warmUpPeriod"
                :min="1"
                :max="3600"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item
              v-if="ruleForm.behavior === 2"
              label="排队超时(毫秒)"
              prop="queueTimeout"
            >
              <el-input-number
                v-model="ruleForm.queueTimeout"
                :min="1"
                :max="60000"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-switch
                v-model="ruleForm.status"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" :loading="submitting" @click="submitForm">
              {{ isEdit ? '更新' : '创建' }}
            </el-button>
          </div>
        </el-dialog>
      </div>
    </layout>
  </div>
</template>

<script>
import Layout from '@/components/Layout.vue'
import { interfaceRuleApi } from '@/api/interfaceRule'
import validationRules from '@/utils/validation'

export default {
  name: 'InterfaceFlowRules',
  components: {
    Layout
  },
  beforeCreate() {
    // 注册验证规则到Vue实例
    this.$validation = validationRules;
  },
  data() {
    return {
      loading: false,
      submitting: false,
      dialogVisible: false,
      isEdit: false,
      tableData: [],
      tenantList: [],
      searchForm: {
        tenantId: '',
        keyword: ''
      },
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },
      ruleForm: {
        ruleName: '',
        tenantId: '',
        resourceName: '',
        limitMode: 0,
        threshold: null,
        behavior: 0,
        warmUpPeriod: null,
        queueTimeout: null,
        status: 1
      },
      formRules: {}
    }
  },
  computed: {
    // 动态计算表单验证规则
    computedFormRules() {
      const rules = {
        tenantId: this.$validation.interfaceFlowRuleRules.tenantId,
        ruleName: this.$validation.interfaceFlowRuleRules.ruleName,
        resourceName: this.$validation.interfaceFlowRuleRules.resourceName,
        limitMode: this.$validation.interfaceFlowRuleRules.limitMode,
        threshold: this.$validation.interfaceFlowRuleRules.threshold,
        behavior: this.$validation.interfaceFlowRuleRules.behavior,
        warmUpPeriod: this.$validation.interfaceFlowRuleRules.warmUpPeriod,
        queueTimeout: this.$validation.interfaceFlowRuleRules.queueTimeout
      };
      
      // 根据限流模式动态调整验证规则
      if (this.ruleForm.limitMode === 0 || this.ruleForm.limitMode === 1) {
        rules.threshold = [
          { required: true, message: '请输入限流阈值', trigger: 'blur' },
          ...this.$validation.interfaceFlowRuleRules.threshold
        ];
      }
      
      // 根据流控效果动态调整验证规则
      if (this.ruleForm.behavior === 1) {
        rules.warmUpPeriod = [
          { required: true, message: '请输入预热时长', trigger: 'blur' },
          ...this.$validation.interfaceFlowRuleRules.warmUpPeriod
        ];
      }
      
      if (this.ruleForm.behavior === 2) {
        rules.queueTimeout = [
          { required: true, message: '请输入排队超时时间', trigger: 'blur' },
          ...this.$validation.interfaceFlowRuleRules.queueTimeout
        ];
      }
      
      return rules;
    }
  },
  mounted() {
    this.loadTenantList()
    this.loadData()
  },
  methods: {
    async loadTenantList() {
      try {
        const response = await interfaceRuleApi.getTenantList({ current: 1, size: 1000 })
        this.tenantList = response.data.records || []
      } catch (error) {
        console.error('加载租户列表失败：', error)
      }
    },
    async loadData() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          tenantId: this.searchForm.tenantId,
          keyword: this.searchForm.keyword
        }
        const response = await interfaceRuleApi.getInterfaceRuleList(params)
        this.tableData = response.data.records || []
        this.pagination.total = response.data.total || 0
      } catch (error) {
        this.$message.error('加载数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      this.pagination.current = 1
      this.loadData()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadData()
    },
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadData()
    },
    showAddDialog() {
      this.isEdit = false
      this.resetForm()
      this.dialogVisible = true
    },
    editRule(row) {
      this.isEdit = true
      this.ruleForm = { ...row }
      this.dialogVisible = true
    },
    async toggleStatus(row) {
      try {
        const newStatus = row.status === 1 ? 0 : 1
        await interfaceRuleApi.updateInterfaceRuleStatus(row.id, newStatus)
        this.$message.success('状态更新成功')
        this.loadData()
      } catch (error) {
        this.$message.error('状态更新失败：' + error.message)
      }
    },
    async deleteRule(row) {
      try {
        await this.$confirm('确定要删除这条接口规则吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await interfaceRuleApi.deleteInterfaceRule(row.id)
        this.$message.success('删除成功')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败：' + error.message)
        }
      }
    },
    async submitForm() {
      try {
        await this.$refs.ruleForm.validate()
        this.submitting = true
        
        if (this.isEdit) {
          await interfaceRuleApi.updateInterfaceRule(this.ruleForm.id, this.ruleForm)
          this.$message.success('更新成功')
        } else {
          await interfaceRuleApi.createInterfaceRule(this.ruleForm)
          this.$message.success('创建成功')
        }
        
        this.dialogVisible = false
        this.loadData()
      } catch (error) {
        if (error.message) {
          this.$message.error('操作失败：' + error.message)
        }
      } finally {
        this.submitting = false
      }
    },
    resetForm() {
      this.ruleForm = {
        ruleName: '',
        tenantId: '',
        resourceName: '',
        limitMode: 0,
        threshold: null,
        behavior: 0,
        warmUpPeriod: null,
        queueTimeout: null,
        status: 1
      }
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate()
      }
    },
    getBehaviorText(behavior) {
      const map = {
        0: '快速失败',
        1: '预热',
        2: '排队等待'
      }
      return map[behavior] || '未知'
    },
    getBehaviorTagType(behavior) {
      const map = {
        0: 'danger',
        1: 'warning',
        2: 'info'
      }
      return map[behavior] || ''
    },
    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style scoped>
.interface-flow-rules {
  height: 100%;
}

.page-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  width: 100%;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.text-muted {
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 768px) {
  .operation-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    flex-direction: column;
    width: 100%;
  }
  
  .filter-group .el-select,
  .filter-group .el-input {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 10px;
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>