package com.example.gateway.filter;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.example.common.constant.FlowControlConstants;
import com.example.gateway.service.DatabaseRuleService;
import com.example.gateway.util.RequestContextExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 多层限流过滤器 实现租户级限流 → 租户接口限流的两层检查顺序 租户级限流：控制整个租户的流量，支持QPS或线程数限流（同一租户只能选一种）
 * 租户接口限流：在租户限流通过后，对租户+接口进行限流
 */
@Slf4j
@Component
public class MultiDimensionFlowFilter implements GlobalFilter, Ordered {

	@Autowired
	private DatabaseRuleService databaseRuleService;

	@Autowired
	private RequestContextExtractor contextExtractor;

	/**
	 * 构造函数 - 用于验证Bean是否被正确创建
	 */
	public MultiDimensionFlowFilter() {
		log.info("=== MultiDimensionFlowFilter Bean 已创建 ===");
	}

	@Override
	public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
		// 强制输出INFO级别日志，确认过滤器被调用
		log.info("=== MultiDimensionFlowFilter.filter() 被调用 ===");
		log.info("Request URI: {}", exchange.getRequest().getURI());
		log.info("Request Path: {}", exchange.getRequest().getPath().value());

		// 调试：打印所有请求头
		log.debug("All request headers: {}", exchange.getRequest().getHeaders());

		// 获取所有Sentinel规则
		List<com.alibaba.csp.sentinel.slots.block.flow.FlowRule> allRules = com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager
				.getRules();

		// 提取请求上下文信息
		String tenantId = contextExtractor.extractTenantId(exchange);
		String resource = contextExtractor.extractResource(exchange);
		String requestId = contextExtractor.extractRequestId(exchange);

		// 调试：打印提取到的上下文信息
		log.debug("Extracted context: tenantId={}, resource={}, requestId={}", tenantId, resource, requestId);

		// 在请求属性中保存上下文信息，供后续使用
		exchange.getAttributes().put("tenantId", tenantId);
		exchange.getAttributes().put("resource", resource);
		exchange.getAttributes().put("requestId", requestId);

		log.debug("Processing request: requestId={}, tenant={}, resource={}", requestId, tenantId, resource);

		// 构建两层限流资源标识
		String tenantResource = FlowControlConstants.ResourcePrefix.TENANT + tenantId; // 租户级别限流
		String tenantInterfaceResource = FlowControlConstants.ResourcePrefix.TENANT + tenantId + ":" + resource; // 租户接口限流

		log.debug("Built flow control resources: tenantResource={}, tenantInterfaceResource={}", tenantResource,
				tenantInterfaceResource);

		// 检查是否需要加载默认租户规则
		databaseRuleService.ensureDefaultTenantRule(tenantId);

		// 使用AtomicReference来存储Entry，以便在lambda表达式中使用
		final AtomicReference<Entry> tenantEntryRef = new AtomicReference<>();
		final AtomicReference<Entry> tenantInterfaceEntryRef = new AtomicReference<>();

		try {
			// 第一层：租户级别限流检查（支持线程数和QPS限流，同一租户只能选一种）
			log.debug("=== Tenant Level Flow Control Check ===");
			log.debug("Resource: {}, TenantId: {}", tenantResource, tenantId);

			// 查找匹配的租户规则
			com.alibaba.csp.sentinel.slots.block.flow.FlowRule tenantMatchedRule = null;
			for (com.alibaba.csp.sentinel.slots.block.flow.FlowRule rule : allRules) {
				if (tenantResource.equals(rule.getResource())) {
					tenantMatchedRule = rule;
					break;
				}
			}

			if (tenantMatchedRule != null) {
				log.debug("Found matching tenant rule for {}: count={}, grade={} (0=thread,1=qps)", tenantResource,
						tenantMatchedRule.getCount(), tenantMatchedRule.getGrade());
			} else {
				log.debug("No matching tenant rule found for resource: {}, will use default rule if available",
						tenantResource);
			}

			// 进行Sentinel检查，让Sentinel根据已加载的规则进行限流
			log.debug("Attempting tenant level flow control entry...");
			Entry tenantEntry = SphU.entry(tenantResource);
			tenantEntryRef.set(tenantEntry);
			log.debug("Tenant level flow control passed: resource={}, tenantId={}", tenantResource, tenantId);

			// 第二层：租户接口流量限流检查
			log.debug("=== Tenant Interface Flow Control Check ===");
			log.debug("Resource: {}, TenantId: {}, Interface: {}", tenantInterfaceResource, tenantId, resource);

			// 查找匹配的接口规则
			com.alibaba.csp.sentinel.slots.block.flow.FlowRule interfaceMatchedRule = null;
			for (com.alibaba.csp.sentinel.slots.block.flow.FlowRule rule : allRules) {
				if (tenantInterfaceResource.equals(rule.getResource())) {
					interfaceMatchedRule = rule;
					break;
				}
			}

			if (interfaceMatchedRule != null) {
				log.debug("Found matching interface rule for {}: count={}, grade={} (0=thread,1=qps)",
						tenantInterfaceResource, interfaceMatchedRule.getCount(), interfaceMatchedRule.getGrade());
			} else {
				log.debug("No matching interface rule found for resource: {}", tenantInterfaceResource);
			}

			// 进行Sentinel检查，让Sentinel根据已加载的规则进行限流
			log.debug("Attempting tenant interface flow control entry...");
			Entry tenantInterfaceEntry = SphU.entry(tenantInterfaceResource);
			tenantInterfaceEntryRef.set(tenantInterfaceEntry);
			log.debug("Tenant interface flow control passed: resource={}, tenantId={}, interface={}",
					tenantInterfaceResource, tenantId, resource);

			// 记录请求统计信息
			recordMetrics(tenantId, resource, "", true, requestId);

			// 继续处理请求，在请求完成后释放资源
			return chain.filter(exchange).doOnSuccess(result -> {
				log.debug("Request completed successfully: requestId={}", requestId);
			}).doOnError(error -> {
				log.error("Request failed: requestId={}, error={}", requestId, error.getMessage());
			}).doFinally(signalType -> {
				// 在请求真正完成后释放资源（按获取的逆序释放）
				log.debug("Releasing Sentinel entries for requestId={}, signalType={}", requestId, signalType);
				if (tenantInterfaceEntryRef.get() != null) {
					tenantInterfaceEntryRef.get().exit();
					log.debug("Released tenantInterfaceEntry for requestId={}", requestId);
				}
				if (tenantEntryRef.get() != null) {
					tenantEntryRef.get().exit();
					log.debug("Released tenantEntry for requestId={}", requestId);
				}
			});

		} catch (BlockException e) {
			// 确定限流类型
			String limitType = determineLimitType(e, null, tenantEntryRef.get(), null, tenantInterfaceEntryRef.get());

			log.warn("Request blocked: requestId={}, tenant={}, resource={}, reason={}, limitType={}", requestId,
					tenantId, resource, e.getClass().getSimpleName(), limitType);

			// 记录限流统计信息
			recordMetrics(tenantId, resource, "", false, requestId);

			// 返回限流响应
			return handleBlockException(exchange, e, requestId, limitType);

		} catch (Exception e) {
			// 处理其他异常
			log.error("Unexpected error in flow control filter: requestId={}", requestId, e);

			// 发生异常时立即释放资源
			if (tenantInterfaceEntryRef.get() != null) {
				tenantInterfaceEntryRef.get().exit();
			}
			if (tenantEntryRef.get() != null) {
				tenantEntryRef.get().exit();
			}

			// 重新抛出异常
			throw new RuntimeException("Flow control filter error", e);
		}
	}

	/**
	 * 处理限流异常
	 */
	private Mono<Void> handleBlockException(ServerWebExchange exchange, BlockException e, String requestId,
			String limitType) {
		// 设置响应状态码
		if (e instanceof FlowException) {
			exchange.getResponse().setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
		} else {
			exchange.getResponse().setStatusCode(HttpStatus.SERVICE_UNAVAILABLE);
		}

		// 设置响应头
		exchange.getResponse().getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
		exchange.getResponse().getHeaders().add("X-Request-Id", requestId);
		exchange.getResponse().getHeaders().add("X-RateLimit-Type", limitType);

		// 构建响应体
		String responseBody = buildErrorResponse(e, requestId, limitType);

		// 写入响应
		return exchange.getResponse().writeWith(
				Mono.just(exchange.getResponse().bufferFactory().wrap(responseBody.getBytes(StandardCharsets.UTF_8))));
	}

	/**
	 * 构建错误响应
	 */
	private String buildErrorResponse(BlockException e, String requestId, String limitType) {
		int code;
		String message;

		if (e instanceof FlowException) {
			code = FlowControlConstants.ResponseCode.TOO_MANY_REQUESTS;
			message = getLimitTypeMessage(limitType);
		} else {
			code = FlowControlConstants.ResponseCode.SERVICE_UNAVAILABLE;
			message = "服务暂时不可用，请稍后重试";
		}

		return String.format(
				"{\"code\":%d,\"message\":\"%s\",\"limitType\":\"%s\",\"data\":null,\"requestId\":\"%s\",\"timestamp\":%d}",
				code, message, limitType, requestId, System.currentTimeMillis());
	}

	/**
	 * 记录监控指标
	 */
	private void recordMetrics(String tenantId, String resource, String clientIp, boolean success, String requestId) {
		// 异步记录监控指标，避免影响请求性能
		// 这里可以发送到消息队列或直接写入数据库
		try {
			// TODO: 实现监控指标记录逻辑
			// 可以使用Micrometer、自定义指标收集器等
			log.debug("Recording metrics: tenant={}, resource={}, ip={}, success={}, requestId={}", tenantId, resource,
					clientIp, success, requestId);
		} catch (Exception ex) {
			log.error("Failed to record metrics: requestId={}", requestId, ex);
		}
	}

	/**
	 * 确定限流类型
	 */
	private String determineLimitType(BlockException e, Entry ipEntry, Entry tenantEntry, Entry tenantTotalEntry,
			Entry tenantInterfaceEntry) {
		// 根据哪个Entry为null来判断在哪一层被限流
		if (tenantEntry == null) {
			return "TENANT_THREAD_LIMIT";
		} else if (tenantInterfaceEntry == null) {
			return "TENANT_INTERFACE_QPS_LIMIT";
		} else {
			return "UNKNOWN_LIMIT";
		}
	}

	/**
	 * 获取限流类型对应的消息
	 */
	private String getLimitTypeMessage(String limitType) {
		switch (limitType) {
		case "TENANT_THREAD_LIMIT":
			return "租户线程数超限";
		case "TENANT_INTERFACE_QPS_LIMIT":
			return "租户接口QPS超限";
		default:
			return "未知限流类型";
		}
	}

	@Override
	public int getOrder() {
		// 设置最高优先级，确保在所有其他过滤器之前执行
		// 使用HIGHEST_PRECEDENCE确保最高优先级
		return Ordered.HIGHEST_PRECEDENCE;
	}
}