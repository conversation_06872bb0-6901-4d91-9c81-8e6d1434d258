#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程限流测试脚本
根据用户提供的SQL数据，测试所有租户的线程限流功能
所有租户规则：grade=0（线程限流），count=5（最大并发线程数）
"""

import requests
import threading
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse
from datetime import datetime

class ThreadLimitTester:
    def __init__(self, base_url="http://localhost:8088", sleep_duration=3):
        self.base_url = base_url
        self.sleep_duration = sleep_duration
        self.results = []
        self.lock = threading.Lock()
        
        # 根据用户SQL数据定义的租户列表
        self.tenants = [
            {"id": "tenant1", "name": "租户1限流", "thread_limit": 5},
            {"id": "tenant2", "name": "租户2限流", "thread_limit": 5},
            {"id": "tenant3", "name": "租户3限流", "thread_limit": 5},
            {"id": "tenant4", "name": "租户4限流", "thread_limit": 5},
            {"id": "tenant5", "name": "租户5限流", "thread_limit": 5}
        ]
    
    def send_request(self, tenant_id, request_id, endpoint="/api/test"):
        """发送单个请求"""
        url = f"{self.base_url}{endpoint}"
        headers = {"X-Tenant-ID": tenant_id}
        params = {"sleep": self.sleep_duration}
        
        start_time = time.time()
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            end_time = time.time()
            
            result = {
                "tenant_id": tenant_id,
                "request_id": request_id,
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response_time": end_time - start_time,
                "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                "thread_name": threading.current_thread().name,
                "blocked": False,
                "error": None
            }
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    result["response_data"] = data
                except:
                    result["response_data"] = response.text
            else:
                result["blocked"] = True
                result["error"] = f"HTTP {response.status_code}: {response.text}"
                
        except requests.exceptions.Timeout:
            result = {
                "tenant_id": tenant_id,
                "request_id": request_id,
                "status_code": 0,
                "success": False,
                "response_time": time.time() - start_time,
                "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                "thread_name": threading.current_thread().name,
                "blocked": True,
                "error": "Request timeout"
            }
        except Exception as e:
            result = {
                "tenant_id": tenant_id,
                "request_id": request_id,
                "status_code": 0,
                "success": False,
                "response_time": time.time() - start_time,
                "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                "thread_name": threading.current_thread().name,
                "blocked": True,
                "error": str(e)
            }
        
        with self.lock:
            self.results.append(result)
            print(f"[{result['timestamp']}] {tenant_id} 请求{request_id}: {'成功' if result['success'] else '失败/被限流'} - {result.get('error', '正常')}")
        
        return result
    
    def test_single_tenant_thread_limit(self, tenant_id, concurrent_requests=8):
        """测试单个租户的线程限流"""
        print(f"\n=== 测试租户 {tenant_id} 的线程限流 ===")
        print(f"发送 {concurrent_requests} 个并发请求，每个请求持续 {self.sleep_duration} 秒")
        print(f"预期：最多5个请求成功，其余被限流")
        
        tenant_results = []
        
        with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
            futures = []
            
            # 同时发送多个请求
            for i in range(concurrent_requests):
                future = executor.submit(self.send_request, tenant_id, i+1)
                futures.append(future)
            
            # 等待所有请求完成
            for future in as_completed(futures):
                result = future.result()
                tenant_results.append(result)
        
        return tenant_results
    
    def test_all_tenants(self, concurrent_per_tenant=8):
        """测试所有租户的线程限流"""
        print("\n" + "="*60)
        print("开始测试所有租户的线程限流功能")
        print(f"每个租户发送 {concurrent_per_tenant} 个并发请求")
        print(f"根据SQL数据，每个租户的线程限制为5")
        print("="*60)
        
        all_results = {}
        
        for tenant in self.tenants:
            tenant_id = tenant["id"]
            results = self.test_single_tenant_thread_limit(tenant_id, concurrent_per_tenant)
            all_results[tenant_id] = results
            
            # 等待一下再测试下一个租户
            time.sleep(1)
        
        return all_results
    
    def generate_report(self, all_results):
        """生成测试报告"""
        print("\n" + "="*60)
        print("测试报告")
        print("="*60)
        
        for tenant_id, results in all_results.items():
            tenant_info = next(t for t in self.tenants if t["id"] == tenant_id)
            
            successful_requests = [r for r in results if r["success"]]
            blocked_requests = [r for r in results if r["blocked"]]
            
            print(f"\n租户: {tenant_id} ({tenant_info['name']})")
            print(f"  线程限制: {tenant_info['thread_limit']}")
            print(f"  总请求数: {len(results)}")
            print(f"  成功请求: {len(successful_requests)}")
            print(f"  被限流请求: {len(blocked_requests)}")
            print(f"  限流是否生效: {'是' if len(blocked_requests) > 0 else '否'}")
            
            if len(successful_requests) > tenant_info['thread_limit']:
                print(f"  ⚠️  警告: 成功请求数({len(successful_requests)})超过了线程限制({tenant_info['thread_limit']})")
            else:
                print(f"  ✅ 线程限流正常工作")
        
        # 总体统计
        total_requests = sum(len(results) for results in all_results.values())
        total_successful = sum(len([r for r in results if r["success"]]) for results in all_results.values())
        total_blocked = sum(len([r for r in results if r["blocked"]]) for results in all_results.values())
        
        print(f"\n总体统计:")
        print(f"  总请求数: {total_requests}")
        print(f"  总成功数: {total_successful}")
        print(f"  总限流数: {total_blocked}")
        print(f"  限流率: {(total_blocked/total_requests*100):.1f}%")
        
        # 检查是否有任何租户的限流失效
        failed_tenants = []
        for tenant_id, results in all_results.items():
            tenant_info = next(t for t in self.tenants if t["id"] == tenant_id)
            successful_count = len([r for r in results if r["success"]])
            if successful_count > tenant_info['thread_limit']:
                failed_tenants.append(tenant_id)
        
        if failed_tenants:
            print(f"\n❌ 限流功能异常的租户: {', '.join(failed_tenants)}")
        else:
            print(f"\n✅ 所有租户的线程限流功能正常")

def main():
    parser = argparse.ArgumentParser(description='线程限流测试脚本')
    parser.add_argument('--url', default='http://localhost:8088', help='网关服务URL')
    parser.add_argument('--concurrent', type=int, default=8, help='每个租户的并发请求数')
    parser.add_argument('--sleep', type=int, default=3, help='每个请求的持续时间（秒）')
    
    args = parser.parse_args()
    
    tester = ThreadLimitTester(base_url=args.url, sleep_duration=args.sleep)
    
    try:
        # 测试所有租户
        all_results = tester.test_all_tenants(concurrent_per_tenant=args.concurrent)
        
        # 生成报告
        tester.generate_report(all_results)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()