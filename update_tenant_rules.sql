-- 更新租户限流规则
USE flow_control;

-- 清空现有的租户限流规则
DELETE FROM tenant_flow_rules;

-- 插入新的租户限流规则
-- tenant1: 线程数限流，count=5
INSERT INTO tenant_flow_rules (id, tenant_id, rule_name, grade, count, control_behavior, warm_up_period_sec, max_queueing_time_ms, cluster_mode, ref_resource, limit_app, cluster_threshold_type, priority, enabled, start_time, end_time, description, create_time, update_time, create_by, update_by, deleted) 
VALUES (1, 'tenant1', '租户1线程数限流', 0, 5, 0, NULL, NULL, 0, NULL, 0, NULL, 1, 1, NULL, NULL, '租户1的线程数限流规则', '2025-08-31 15:15:00', '2025-08-31 15:15:00', 'system', NULL, 0);

-- tenant2: QPS限流，count=20
INSERT INTO tenant_flow_rules (id, tenant_id, rule_name, grade, count, control_behavior, warm_up_period_sec, max_queueing_time_ms, cluster_mode, ref_resource, limit_app, cluster_threshold_type, priority, enabled, start_time, end_time, description, create_time, update_time, create_by, update_by, deleted) 
VALUES (2, 'tenant2', '租户2QPS限流', 1, 20, 0, NULL, NULL, 0, NULL, 0, NULL, 1, 1, NULL, NULL, '租户2的QPS限流规则', '2025-08-31 15:15:00', '2025-08-31 15:15:00', 'system', NULL, 0);

-- tenant3: 线程数限流，count=3
INSERT INTO tenant_flow_rules (id, tenant_id, rule_name, grade, count, control_behavior, warm_up_period_sec, max_queueing_time_ms, cluster_mode, ref_resource, limit_app, cluster_threshold_type, priority, enabled, start_time, end_time, description, create_time, update_time, create_by, update_by, deleted) 
VALUES (3, 'tenant3', '租户3线程数限流', 0, 3, 0, NULL, NULL, 0, NULL, 0, NULL, 1, 1, NULL, NULL, '租户3的线程数限流规则', '2025-08-31 15:15:00', '2025-08-31 15:15:00', 'system', NULL, 0);

-- 租户+接口级别的限流规则
INSERT INTO tenant_flow_rules (id, tenant_id, rule_name, grade, count, control_behavior, warm_up_period_sec, max_queueing_time_ms, cluster_mode, ref_resource, limit_app, cluster_threshold_type, priority, enabled, start_time, end_time, description, create_time, update_time, create_by, update_by, deleted) 
VALUES (4, 'tenant1', '租户1接口限流', 1, 10, 0, NULL, NULL, 0, 'userInfo', 0, NULL, 2, 1, NULL, NULL, '租户1的接口QPS限流规则', '2025-08-31 15:15:00', '2025-08-31 15:15:00', 'system', NULL, 0);

INSERT INTO tenant_flow_rules (id, tenant_id, rule_name, grade, count, control_behavior, warm_up_period_sec, max_queueing_time_ms, cluster_mode, ref_resource, limit_app, cluster_threshold_type, priority, enabled, start_time, end_time, description, create_time, update_time, create_by, update_by, deleted) 
VALUES (5, 'tenant2', '租户2接口限流', 1, 15, 0, NULL, NULL, 0, 'orderInfo', 0, NULL, 2, 1, NULL, NULL, '租户2的接口QPS限流规则', '2025-08-31 15:15:00', '2025-08-31 15:15:00', 'system', NULL, 0);

INSERT INTO tenant_flow_rules (id, tenant_id, rule_name, grade, count, control_behavior, warm_up_period_sec, max_queueing_time_ms, cluster_mode, ref_resource, limit_app, cluster_threshold_type, priority, enabled, start_time, end_time, description, create_time, update_time, create_by, update_by, deleted) 
VALUES (6, 'tenant3', '租户3接口限流', 0, 2, 0, NULL, NULL, 0, 'productInfo', 0, NULL, 2, 1, NULL, NULL, '租户3的接口线程数限流规则', '2025-08-31 15:15:00', '2025-08-31 15:15:00', 'system', NULL, 0);

SELECT 'Database updated successfully!' as result;