package com.example.admin;

import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;
import org.w3c.dom.Document;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.io.InputStream;

public class XmlValidationTest {

    @Test
    public void testXmlValidation() {
        String[] xmlFiles = {
            "mapper/FlowControlLogMapper.xml",
            "mapper/SystemConfigMapper.xml",
            "mapper/FlowRuleMapper.xml",
            "mapper/IPFlowRuleMapper.xml",
            "mapper/TenantInfoMapper.xml",
            "mapper/MonitorStatisticsMapper.xml"
        };

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setValidating(false);
        factory.setNamespaceAware(true);

        for (String xmlFile : xmlFiles) {
            try {
                System.out.println("Validating: " + xmlFile);
                DocumentBuilder builder = factory.newDocumentBuilder();
                
                ClassPathResource resource = new ClassPathResource(xmlFile);
                if (resource.exists()) {
                    try (InputStream inputStream = resource.getInputStream()) {
                        Document document = builder.parse(inputStream);
                        System.out.println("✓ " + xmlFile + " is valid");
                    }
                } else {
                    System.out.println("✗ " + xmlFile + " not found");
                }
            } catch (Exception e) {
                System.out.println("✗ " + xmlFile + " validation failed: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
}