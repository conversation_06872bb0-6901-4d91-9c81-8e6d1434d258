// 主题切换样式文件

// 明亮主题变量
:root {
  // 主色调
  --primary-color: #409EFF;
  --primary-light: #66b1ff;
  --primary-dark: #337ecc;
  
  // 成功色
  --success-color: #67C23A;
  --success-light: #85ce61;
  --success-dark: #529b2e;
  
  // 警告色
  --warning-color: #E6A23C;
  --warning-light: #ebb563;
  --warning-dark: #b88230;
  
  // 危险色
  --danger-color: #F56C6C;
  --danger-light: #f78989;
  --danger-dark: #c45656;
  
  // 信息色
  --info-color: #909399;
  --info-light: #a6a9ad;
  --info-dark: #73767a;
  
  // 文本颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #C0C4CC;
  
  // 边框颜色
  --border-base: #DCDFE6;
  --border-light: #E4E7ED;
  --border-lighter: #EBEEF5;
  --border-extra-light: #F2F6FC;
  
  // 背景颜色
  --background-base: #F5F7FA;
  --background-light: #FAFAFA;
  --background-white: #FFFFFF;
  --background-page: #F5F5F5;
  
  // 侧边栏颜色
  --sidebar-bg: #304156;
  --sidebar-logo-bg: #2b3a4b;
  --sidebar-text: #bfcbd9;
  --sidebar-active-text: #409EFF;
  
  // 头部颜色
  --header-bg: #FFFFFF;
  --header-border: #E6E6E6;
  
  // 卡片阴影
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 暗黑主题变量
[data-theme="dark"] {
  // 主色调保持不变
  --primary-color: #409EFF;
  --primary-light: #66b1ff;
  --primary-dark: #337ecc;
  
  // 成功色
  --success-color: #67C23A;
  --success-light: #85ce61;
  --success-dark: #529b2e;
  
  // 警告色
  --warning-color: #E6A23C;
  --warning-light: #ebb563;
  --warning-dark: #b88230;
  
  // 危险色
  --danger-color: #F56C6C;
  --danger-light: #f78989;
  --danger-dark: #c45656;
  
  // 信息色
  --info-color: #909399;
  --info-light: #a6a9ad;
  --info-dark: #73767a;
  
  // 文本颜色（暗黑主题）
  --text-primary: #E4E7ED;
  --text-regular: #CFD3DC;
  --text-secondary: #A4A7B0;
  --text-placeholder: #6C6E72;
  
  // 边框颜色（暗黑主题）
  --border-base: #4C4D4F;
  --border-light: #414243;
  --border-lighter: #363637;
  --border-extra-light: #2B2B2C;
  
  // 背景颜色（暗黑主题）
  --background-base: #1D1E1F;
  --background-light: #25262A;
  --background-white: #2B2F36;
  --background-page: #1A1A1A;
  
  // 侧边栏颜色（暗黑主题）
  --sidebar-bg: #1F2937;
  --sidebar-logo-bg: #111827;
  --sidebar-text: #9CA3AF;
  --sidebar-active-text: #60A5FA;
  
  // 头部颜色（暗黑主题）
  --header-bg: #2B2F36;
  --header-border: #4C4D4F;
  
  // 卡片阴影（暗黑主题）
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.24), 0 0 6px rgba(0, 0, 0, 0.08);
  --box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.24), 0 0 6px rgba(0, 0, 0, 0.24);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

// 主题切换过渡动画
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

// 应用主题变量到现有样式
body {
  background-color: var(--background-page);
  color: var(--text-primary);
}

// 卡片样式主题适配
.card {
  background-color: var(--background-white) !important;
  border-color: var(--border-light) !important;
  box-shadow: var(--box-shadow-base) !important;
  color: var(--text-primary) !important;
}

// Element UI 组件主题适配
.el-table {
  background-color: var(--background-white) !important;
  color: var(--text-primary) !important;
  
  th {
    background-color: var(--background-base) !important;
    color: var(--text-regular) !important;
    border-color: var(--border-light) !important;
  }
  
  td {
    border-color: var(--border-lighter) !important;
  }
  
  tr:hover > td {
    background-color: var(--background-light) !important;
  }
}

.el-input {
  .el-input__inner {
    background-color: var(--background-white) !important;
    border-color: var(--border-base) !important;
    color: var(--text-primary) !important;
    
    &::placeholder {
      color: var(--text-placeholder) !important;
    }
    
    &:focus {
      border-color: var(--primary-color) !important;
    }
  }
}

.el-select {
  .el-input__inner {
    background-color: var(--background-white) !important;
    border-color: var(--border-base) !important;
    color: var(--text-primary) !important;
  }
}

.el-form-item__label {
  color: var(--text-regular) !important;
}

.el-dialog {
  background-color: var(--background-white) !important;
  
  .el-dialog__header {
    border-color: var(--border-lighter) !important;
  }
  
  .el-dialog__title {
    color: var(--text-primary) !important;
  }
}

.el-message-box {
  background-color: var(--background-white) !important;
  border-color: var(--border-light) !important;
  
  .el-message-box__title {
    color: var(--text-primary) !important;
  }
  
  .el-message-box__content {
    color: var(--text-regular) !important;
  }
}

// 页面容器主题适配
.page-container {
  background-color: var(--background-page) !important;
}

// 状态标签主题适配
.status-tag {
  &.status-success {
    background-color: var(--success-color) !important;
  }
  
  &.status-warning {
    background-color: var(--warning-color) !important;
  }
  
  &.status-danger {
    background-color: var(--danger-color) !important;
  }
  
  &.status-info {
    background-color: var(--info-color) !important;
  }
}

// 主题切换按钮样式
.theme-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: var(--background-light);
  }
  
  .theme-icon {
    font-size: 16px;
    margin-right: 6px;
    color: var(--text-regular);
  }
  
  .theme-text {
    font-size: 14px;
    color: var(--text-regular);
  }
}

// 暗黑主题下的特殊处理
[data-theme="dark"] {
  // 滚动条样式
  ::-webkit-scrollbar {
    background-color: var(--background-base);
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: var(--border-base);
    
    &:hover {
      background-color: var(--border-light);
    }
  }
  
  // 选中文本颜色
  ::selection {
    background-color: var(--primary-color);
    color: var(--background-white);
  }
}