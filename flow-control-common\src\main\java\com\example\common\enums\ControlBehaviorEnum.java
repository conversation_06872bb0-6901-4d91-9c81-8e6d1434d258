package com.example.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流控行为枚举
 */
@Getter
@AllArgsConstructor
public enum ControlBehaviorEnum {
    
    /**
     * 快速失败
     */
    REJECT(0, "快速失败", "直接拒绝超出阈值的请求"),
    
    /**
     * Warm Up
     */
    WARM_UP(1, "Warm Up", "预热模式，逐渐增加流量"),
    
    /**
     * 排队等待
     */
    QUEUE(2, "排队等待", "请求排队等待，避免直接拒绝");
    
    private final int code;
    private final String name;
    private final String description;
    
    /**
     * 根据代码获取枚举
     */
    public static ControlBehaviorEnum getByCode(int code) {
        for (ControlBehaviorEnum behavior : values()) {
            if (behavior.getCode() == code) {
                return behavior;
            }
        }
        throw new IllegalArgumentException("Unknown control behavior code: " + code);
    }
    
    /**
     * 是否为排队等待模式
     */
    public boolean isQueue() {
        return this == QUEUE;
    }
    
    /**
     * 是否为预热模式
     */
    public boolean isWarmUp() {
        return this == WARM_UP;
    }
    
    /**
     * 是否为快速失败模式
     */
    public boolean isReject() {
        return this == REJECT;
    }
}