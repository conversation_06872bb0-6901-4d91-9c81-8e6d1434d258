package com.example.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.dto.FlowControlLogDTO;
import com.example.common.entity.FlowControlLog;
import com.example.admin.mapper.FlowControlLogMapper;
import com.example.admin.service.FlowControlLogService;
import com.example.admin.vo.FlowControlLogVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流控日志服务实现类
 */
@Service
public class FlowControlLogServiceImpl extends ServiceImpl<FlowControlLogMapper, FlowControlLog> implements FlowControlLogService {
    
    @Resource
    private FlowControlLogMapper flowControlLogMapper;
    
    @Override
    public Page<FlowControlLogVO> selectFlowControlLogPage(Page<FlowControlLogVO> page, String tenantId, String resourceName,
                                                           String eventType, String clientIp, LocalDateTime startTime, LocalDateTime endTime) {
        // 创建FlowControlLog的分页对象
        Page<FlowControlLog> logPage = new Page<>(page.getCurrent(), page.getSize());
        
        // 调用mapper的selectLogPage方法
        Page<FlowControlLog> resultPage = flowControlLogMapper.selectLogPage(logPage, tenantId, resourceName, eventType, startTime, endTime);
        
        // 转换为VO对象
        Page<FlowControlLogVO> voPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        List<FlowControlLogVO> voList = resultPage.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        voPage.setRecords(voList);
        
        return voPage;
    }
    
    @Override
    public FlowControlLogVO getFlowControlLogById(Long id) {
        FlowControlLog flowControlLog = this.getById(id);
        if (flowControlLog == null) {
            return null;
        }
        FlowControlLogVO flowControlLogVO = new FlowControlLogVO();
        BeanUtils.copyProperties(flowControlLog, flowControlLogVO);
        return flowControlLogVO;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createFlowControlLog(FlowControlLogDTO flowControlLogDTO) {
        FlowControlLog flowControlLog = new FlowControlLog();
        BeanUtils.copyProperties(flowControlLogDTO, flowControlLog);
        flowControlLog.setCreateTime(LocalDateTime.now());
        
        return this.save(flowControlLog);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFlowControlLog(Long id, FlowControlLogDTO flowControlLogDTO) {
        FlowControlLog existingLog = this.getById(id);
        if (existingLog == null) {
            throw new RuntimeException("流控日志记录不存在");
        }
        
        BeanUtils.copyProperties(flowControlLogDTO, existingLog);
        existingLog.setId(id);
        
        return this.updateById(existingLog);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFlowControlLog(Long id) {
        return this.removeById(id);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteFlowControlLogs(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        return this.removeByIds(ids);
    }
    
    @Override
    public List<FlowControlLogVO> getFlowControlLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime,
                                                                 String tenantId, String resourceName, String eventType, Integer limit) {
        List<FlowControlLog> logs = flowControlLogMapper.selectByTimeRange(startTime, endTime, tenantId, resourceName);
        return logs.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<FlowControlLogVO> getFlowControlLogsByTenantId(String tenantId, LocalDateTime startTime,
                                                                LocalDateTime endTime, String eventType, Integer limit) {
        List<FlowControlLog> logs = flowControlLogMapper.selectByTenantId(tenantId, eventType, limit);
        return logs.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<FlowControlLogVO> getFlowControlLogsByResourceName(String resourceName, String tenantId,
                                                                    LocalDateTime startTime, LocalDateTime endTime, String eventType, Integer limit) {
        List<FlowControlLog> logs = flowControlLogMapper.selectByResourceName(resourceName, eventType, limit);
        return logs.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<FlowControlLogVO> getFlowControlLogsByEventType(String eventType, String tenantId, String resourceName,
                                                                 LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        List<FlowControlLog> logs = flowControlLogMapper.selectByEventType(eventType, startTime, endTime, limit);
        return logs.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<FlowControlLogVO> getRecentFlowControlLogs(String tenantId, String resourceName, String eventType,
                                                            Integer minutes, Integer limit) {
        List<FlowControlLog> logs = flowControlLogMapper.selectRecentLogs(minutes, tenantId, resourceName);
        return logs.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<Map<String, Object>> getEventTypeDistribution(String tenantId, String resourceName,
                                                               LocalDateTime startTime, LocalDateTime endTime) {
        return flowControlLogMapper.selectEventTypeStatistics(startTime, endTime, tenantId);
    }
    
    @Override
    public List<Map<String, Object>> getResourceAccessStatistics(String tenantId, LocalDateTime startTime,
                                                                  LocalDateTime endTime, Integer limit) {
        return flowControlLogMapper.selectResourceAccessStatistics(startTime, endTime, tenantId, limit);
    }
    
    @Override
    public List<Map<String, Object>> getTenantLogStatistics(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        return flowControlLogMapper.selectTenantLogStatistics(startTime, endTime);
    }
    
    @Override
    public List<Map<String, Object>> getIpAccessStatistics(String tenantId, String resourceName,
                                                            LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        return flowControlLogMapper.selectIpAccessStatistics(startTime, endTime, tenantId, limit);
    }
    
    @Override
    public List<FlowControlLogVO> getAbnormalLogs(String tenantId, String resourceName,
                                                   LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        List<FlowControlLog> logs = flowControlLogMapper.selectAbnormalLogs(startTime, endTime, tenantId, limit);
        return logs.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<FlowControlLogVO> getAlarmLogs(String tenantId, String resourceName,
                                                LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        List<FlowControlLog> logs = flowControlLogMapper.selectAlarmLogs(startTime, endTime, tenantId, limit);
        return logs.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsertFlowControlLogs(List<FlowControlLog> flowControlLogList) {
        if (CollectionUtils.isEmpty(flowControlLogList)) {
            return false;
        }
        
        // 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        flowControlLogList.forEach(log -> {
            if (log.getCreateTime() == null) {
                log.setCreateTime(now);
            }
        });
        
        return flowControlLogMapper.batchInsert(flowControlLogList) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteExpiredLogs(LocalDateTime beforeTime) {
        return flowControlLogMapper.deleteExpiredLogs(beforeTime, null);
    }
    
    /**
     * 将FlowControlLog实体转换为FlowControlLogVO视图对象
     * @param log 流控日志实体
     * @return 流控日志视图对象
     */
    private FlowControlLogVO convertToVO(FlowControlLog log) {
        if (log == null) {
            return null;
        }
        FlowControlLogVO vo = new FlowControlLogVO();
        BeanUtils.copyProperties(log, vo);
        return vo;
    }
    
    @Override
    public List<Map<String, Object>> getLogTrendData(String tenantId, String resourceName, String eventType,
                                                      LocalDateTime startTime, LocalDateTime endTime, Integer interval) {
        String granularity = "hour";
        if (interval != null && interval >= 24) {
            granularity = "day";
        }
        return flowControlLogMapper.selectLogTrendData(startTime, endTime, tenantId, eventType, granularity);
    }
    
    @Override
    public Long getLogCount(String tenantId, String resourceName, String eventType,
                            LocalDateTime startTime, LocalDateTime endTime) {
        return flowControlLogMapper.countLogs(startTime, endTime, tenantId);
    }
    
    @Override
    public List<FlowControlLogVO> exportFlowControlLogs(String tenantId, String resourceName, String eventType,
                                                         LocalDateTime startTime, LocalDateTime endTime) {
        List<FlowControlLog> logs = flowControlLogMapper.selectByTimeRange(startTime, endTime, tenantId, resourceName);
        return logs.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<Map<String, Object>> getLogSummary(String tenantId, LocalDateTime startTime,
                                                    LocalDateTime endTime, String groupBy) {
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", tenantId);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("groupBy", groupBy);
        
        // 根据分组类型获取汇总数据
        switch (groupBy.toLowerCase()) {
            case "hour":
                return getHourlyLogSummary(tenantId, startTime, endTime);
            case "day":
                return getDailyLogSummary(tenantId, startTime, endTime);
            case "month":
                return getMonthlyLogSummary(tenantId, startTime, endTime);
            default:
                return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getHotIpList(String tenantId, String resourceName,
                                                   LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        return flowControlLogMapper.selectHotIpList(tenantId, resourceName, startTime, endTime, limit);
    }
    
    @Override
    public List<Map<String, Object>> getAttackDetectionResults(String tenantId, LocalDateTime startTime,
                                                                LocalDateTime endTime, Integer threshold, Integer limit) {
        return flowControlLogMapper.selectAttackDetectionResults(tenantId, startTime, endTime, threshold, limit);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupLogData(Integer retentionDays) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);
        return deleteExpiredLogs(cutoffTime);
    }
    
    @Override
    public Map<String, Object> getLogStorageStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取总记录数
        long totalCount = this.count();
        statistics.put("totalRecords", totalCount);
        
        // 获取最早和最新记录时间
        FlowControlLog earliest = this.lambdaQuery()
                .orderByAsc(FlowControlLog::getCreateTime)
                .last("LIMIT 1")
                .one();
        FlowControlLog latest = this.lambdaQuery()
                .orderByDesc(FlowControlLog::getCreateTime)
                .last("LIMIT 1")
                .one();
        
        if (earliest != null) {
            statistics.put("earliestRecord", earliest.getCreateTime());
        }
        if (latest != null) {
            statistics.put("latestRecord", latest.getCreateTime());
        }
        
        // 计算数据跨度（天数）
        if (earliest != null && latest != null) {
            long days = java.time.Duration.between(earliest.getCreateTime(), latest.getCreateTime()).toDays();
            statistics.put("dataSpanDays", days);
        }
        
        // 按事件类型统计
        List<Map<String, Object>> eventTypeStats = getEventTypeDistribution(null, null, null, null);
        statistics.put("eventTypeDistribution", eventTypeStats);
        
        return statistics;
    }
    
    @Override
    public boolean syncLogsToExternalSystem(LocalDateTime startTime, LocalDateTime endTime, String targetSystem) {
        // TODO: 实现同步日志到外部系统的逻辑
        List<FlowControlLog> logsToSync = this.lambdaQuery()
                .between(FlowControlLog::getCreateTime, startTime, endTime)
                .list();
        
        switch (targetSystem.toLowerCase()) {
            case "elasticsearch":
                return syncToElasticsearch(logsToSync);
            case "kafka":
                return syncToKafka(logsToSync);
            case "syslog":
                return syncToSyslog(logsToSync);
            default:
                return false;
        }
    }
    
    @Override
    public Map<String, Object> generateLogReport(String tenantId, LocalDateTime startTime,
                                                  LocalDateTime endTime, String reportType) {
        Map<String, Object> report = new HashMap<>();
        report.put("reportType", reportType);
        report.put("tenantId", tenantId);
        report.put("startTime", startTime);
        report.put("endTime", endTime);
        report.put("generateTime", LocalDateTime.now());
        
        switch (reportType.toLowerCase()) {
            case "summary":
                return generateSummaryReport(tenantId, startTime, endTime);
            case "security":
                return generateSecurityReport(tenantId, startTime, endTime);
            case "performance":
                return generatePerformanceReport(tenantId, startTime, endTime);
            default:
                report.put("error", "不支持的报告类型: " + reportType);
                return report;
        }
    }
    
    /**
     * 获取小时级日志汇总
     */
    private List<Map<String, Object>> getHourlyLogSummary(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        // TODO: 实现小时级日志汇总逻辑
        return new ArrayList<>();
    }
    
    /**
     * 获取日级日志汇总
     */
    private List<Map<String, Object>> getDailyLogSummary(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        // TODO: 实现日级日志汇总逻辑
        return new ArrayList<>();
    }
    
    /**
     * 获取月级日志汇总
     */
    private List<Map<String, Object>> getMonthlyLogSummary(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        // TODO: 实现月级日志汇总逻辑
        return new ArrayList<>();
    }
    
    /**
     * 同步到Elasticsearch
     */
    private boolean syncToElasticsearch(List<FlowControlLog> logs) {
        // TODO: 实现同步到Elasticsearch的逻辑
        return true;
    }
    
    /**
     * 同步到Kafka
     */
    private boolean syncToKafka(List<FlowControlLog> logs) {
        // TODO: 实现同步到Kafka的逻辑
        return true;
    }
    
    /**
     * 同步到Syslog
     */
    private boolean syncToSyslog(List<FlowControlLog> logs) {
        // TODO: 实现同步到Syslog的逻辑
        return true;
    }
    
    /**
     * 生成汇总报告
     */
    private Map<String, Object> generateSummaryReport(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> report = new HashMap<>();
        
        // 总日志数
        Long totalLogs = getLogCount(tenantId, null, null, startTime, endTime);
        report.put("totalLogs", totalLogs);
        
        // 事件类型分布
        List<Map<String, Object>> eventTypeDistribution = getEventTypeDistribution(tenantId, null, startTime, endTime);
        report.put("eventTypeDistribution", eventTypeDistribution);
        
        // 资源访问统计
        List<Map<String, Object>> resourceStats = getResourceAccessStatistics(tenantId, startTime, endTime, 10);
        report.put("topResources", resourceStats);
        
        return report;
    }
    
    /**
     * 生成安全报告
     */
    private Map<String, Object> generateSecurityReport(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> report = new HashMap<>();
        
        // 异常日志
        List<FlowControlLogVO> abnormalLogs = getAbnormalLogs(tenantId, null, startTime, endTime, 100);
        report.put("abnormalLogs", abnormalLogs);
        
        // 热点IP
        List<Map<String, Object>> hotIps = getHotIpList(tenantId, null, startTime, endTime, 20);
        report.put("hotIps", hotIps);
        
        // 攻击检测结果
        List<Map<String, Object>> attacks = getAttackDetectionResults(tenantId, startTime, endTime, 100, 50);
        report.put("potentialAttacks", attacks);
        
        return report;
    }
    
    /**
     * 生成性能报告
     */
    private Map<String, Object> generatePerformanceReport(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> report = new HashMap<>();
        
        // 资源访问统计
        List<Map<String, Object>> resourceStats = getResourceAccessStatistics(tenantId, startTime, endTime, 20);
        report.put("resourcePerformance", resourceStats);
        
        // 趋势数据
        List<Map<String, Object>> trendData = getLogTrendData(tenantId, null, null, startTime, endTime, 60);
        report.put("trendData", trendData);
        
        return report;
    }
}