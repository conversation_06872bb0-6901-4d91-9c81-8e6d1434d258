import java.sql.*;
import java.io.*;
import java.nio.file.*;

public class DatabaseRebuild {
    private static final String URL = "*****************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "123456";
    
    public static void main(String[] args) {
        try {
            System.out.println("开始重建数据库...");
            
            // 读取SQL文件
            String sqlContent = Files.readString(Paths.get("rebuild_database.sql"));
            
            // 连接数据库
            Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            
            // 分割SQL语句并执行
            String[] sqlStatements = sqlContent.split(";");
            
            for (String sql : sqlStatements) {
                sql = sql.trim();
                if (!sql.isEmpty() && !sql.startsWith("--") && !sql.startsWith("/*")) {
                    try {
                        Statement stmt = conn.createStatement();
                        stmt.execute(sql);
                        stmt.close();
                    } catch (SQLException e) {
                        // 忽略一些预期的错误（如表不存在等）
                        if (!e.getMessage().contains("doesn't exist") && 
                            !e.getMessage().contains("Unknown table")) {
                            System.out.println("执行SQL时出错: " + sql);
                            System.out.println("错误信息: " + e.getMessage());
                        }
                    }
                }
            }
            
            conn.close();
            System.out.println("数据库重建完成！");
            
        } catch (Exception e) {
            System.out.println("重建数据库失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}