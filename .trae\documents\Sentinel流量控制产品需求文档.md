# Sentinel在Spring Gateway中的流量控制产品需求文档

## 1. 产品概述

本项目旨在基于Sentinel框架在Spring Cloud Gateway中实现多维度、分层级的智能流量控制系统。系统支持基于租户、接口、IP等多个维度的精细化流量管控，提供排队等待机制替代直接拒绝，并具备配置热更新能力。

- 解决微服务网关层面的流量控制和服务保护问题
- 为多租户SaaS平台提供差异化的流量管控能力
- 通过智能排队机制提升用户体验，避免直接拒绝请求

## 2. 核心功能

### 2.1 用户角色

| 角色 | 权限说明 | 核心功能 |
|------|----------|----------|
| 系统管理员 | 全局配置管理 | 可配置所有租户的流量控制规则、查看监控数据 |
| 租户管理员 | 租户级配置 | 可查看和调整自己租户的流量配置、监控数据 |
| 普通用户 | 接口调用 | 通过网关调用各种API接口，受流量控制影响 |

### 2.2 功能模块

系统主要包含以下核心页面：
1. **流量控制配置页面**：多维度规则配置、分层QPS设置、排队参数配置
2. **监控仪表板页面**：实时流量监控、QPS统计、排队状态展示
3. **规则管理页面**：规则的增删改查、批量导入导出、版本管理
4. **告警管理页面**：阈值告警配置、通知设置、告警历史查看

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 流量控制配置页面 | 多维度规则配置 | 配置基于租户ID、接口地址、IP地址的流量控制规则，支持组合条件 |
| 流量控制配置页面 | 分层QPS设置 | 设置租户总QPS限制和特定接口QPS限制，支持优先级配置 |
| 流量控制配置页面 | 排队参数配置 | 配置排队等待时间、队列长度、超时处理策略 |
| 监控仪表板页面 | 实时流量监控 | 展示各维度的实时QPS、成功率、响应时间等指标 |
| 监控仪表板页面 | 排队状态展示 | 显示当前排队请求数量、平均等待时间、排队成功率 |
| 规则管理页面 | 规则CRUD操作 | 创建、查询、更新、删除流量控制规则，支持批量操作 |
| 规则管理页面 | 配置热更新 | 实时推送配置变更到网关节点，无需重启服务 |
| 告警管理页面 | 阈值告警配置 | 设置QPS、响应时间、错误率等指标的告警阈值 |
| 告警管理页面 | 通知设置 | 配置邮件、短信、钉钉等多种告警通知方式 |

## 3. 核心流程

### 3.1 流量控制主流程

用户请求通过Spring Gateway时，系统按以下流程进行流量控制：
1. 请求进入网关，提取租户ID、接口地址、IP地址等维度信息
2. 查询对应的流量控制规则，按优先级进行匹配
3. 执行分层限流检查：先检查租户总QPS，再检查接口级QPS
4. 如果超出限制，进入排队等待机制而非直接拒绝
5. 排队成功后继续处理请求，排队超时则返回限流响应
6. 记录流量统计数据，用于监控和告警

### 3.2 配置管理流程

管理员配置流量控制规则的流程：
1. 登录管理后台，进入流量控制配置页面
2. 选择配置维度（租户、接口、IP），设置限流参数
3. 配置排队等待参数，包括队列长度和超时时间
4. 保存配置后，系统自动推送到所有网关节点
5. 配置立即生效，无需重启服务

```mermaid
graph TD
    A[用户请求] --> B[Spring Gateway]
    B --> C[提取维度信息]
    C --> D[查询流量控制规则]
    D --> E[租户级QPS检查]
    E --> F{是否超限?}
    F -->|否| G[接口级QPS检查]
    F -->|是| H[进入排队等待]
    G --> I{是否超限?}
    I -->|否| J[正常处理请求]
    I -->|是| H
    H --> K{排队是否成功?}
    K -->|是| J
    K -->|否| L[返回限流响应]
    J --> M[记录统计数据]
    L --> M
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**：蓝色系（#1890ff）作为主色，灰色系（#f0f2f5）作为背景色
- **按钮样式**：圆角按钮，支持悬停和点击状态变化
- **字体**：中文使用微软雅黑，英文使用Roboto，主要字号14px
- **布局风格**：卡片式布局，左侧导航栏 + 右侧内容区域
- **图标风格**：使用Ant Design图标库，简洁现代风格

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 流量控制配置页面 | 规则配置表单 | 多级联动选择器、数值输入框、开关组件，采用步骤条引导配置流程 |
| 监控仪表板页面 | 数据可视化 | 实时图表（折线图、柱状图）、数值卡片、进度条，深色主题可选 |
| 规则管理页面 | 数据表格 | 可编辑表格、搜索过滤器、批量操作按钮，支持拖拽排序 |
| 告警管理页面 | 告警配置 | 阈值滑块、通知方式多选框、告警历史时间轴展示 |

### 4.3 响应式设计

系统采用桌面优先的响应式设计，支持1920px、1366px、1024px等主流分辨率。移动端进行适配优化，支持触摸操作，关键功能在移动设备上保持可用性。