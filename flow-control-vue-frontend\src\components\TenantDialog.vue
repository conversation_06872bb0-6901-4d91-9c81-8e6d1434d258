<template>
	<el-dialog
		:title="isEdit ? '编辑租户' : '新增租户'"
		:visible.sync="dialogVisible"
		width="600px"
		@close="handleClose"
	>
		<el-form
			:model="formData"
			:rules="rules"
			ref="tenantForm"
			label-width="120px"
		>
			<el-form-item label="租户ID" prop="tenantId">
				<el-input
					v-model="formData.tenantId"
					placeholder="请输入租户ID，只能包含字母、数字、下划线"
					:disabled="isEdit"
				/>
				<div class="form-tip">租户ID只能包含字母、数字、下划线，创建后不可修改</div>
			</el-form-item>

			<el-form-item label="租户名称" prop="tenantName">
				<el-input
					v-model="formData.tenantName"
					placeholder="请输入租户名称"
				/>
			</el-form-item>

			<el-form-item label="描述" prop="description">
				<el-input
					v-model="formData.description"
					type="textarea"
					:rows="3"
					placeholder="请输入租户描述"
				/>
			</el-form-item>

			<el-form-item label="联系人" prop="contactPerson">
				<el-input
					v-model="formData.contactPerson"
					placeholder="请输入联系人姓名"
				/>
			</el-form-item>

			<el-form-item label="联系邮箱" prop="contactEmail">
				<el-input
					v-model="formData.contactEmail"
					placeholder="请输入联系邮箱"
				/>
			</el-form-item>

			<el-form-item label="联系电话" prop="contactPhone">
				<el-input
					v-model="formData.contactPhone"
					placeholder="请输入联系电话"
				/>
			</el-form-item>

			<el-form-item label="状态" prop="status">
				<el-radio-group v-model="formData.status">
					<el-radio :label="1">启用</el-radio>
					<el-radio :label="0">禁用</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>

		<div slot="footer" class="dialog-footer">
			<el-button @click="handleClose">取消</el-button>
			<el-button
				type="primary"
				@click="handleSubmit"
				:loading="submitting"
			>
				{{ isEdit ? '更新' : '创建' }}
			</el-button>
		</div>
	</el-dialog>
</template>

<script>
export default {
	name: 'TenantDialog',
	props: {
		visible: {
			type: Boolean,
			default: false,
		},
		form: {
			type: Object,
			default: () => ({}),
		},
		isEdit: {
			type: Boolean,
			default: false,
		},
		submitting: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			formData: {
				tenantId: '',
				tenantName: '',
				description: '',
				contactPerson: '',
				contactEmail: '',
				contactPhone: '',
				status: 1,
			},
			rules: {
				tenantId: [
					{ required: true, message: '请输入租户ID', trigger: 'blur' },
					{
						pattern: /^[a-zA-Z0-9_]+$/,
						message: '租户ID只能包含字母、数字、下划线',
						trigger: 'blur',
					},
					{ min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
				],
				tenantName: [
					{ required: true, message: '请输入租户名称', trigger: 'blur' },
					{ min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' },
				],
				description: [
					{ max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' },
				],
				contactPerson: [
					{ max: 50, message: '联系人姓名不能超过 50 个字符', trigger: 'blur' },
				],
				contactEmail: [
					{
						type: 'email',
						message: '请输入正确的邮箱地址',
						trigger: ['blur', 'change'],
					},
				],
				contactPhone: [
					{
						pattern: /^[0-9\-+\s()]+$/,
						message: '请输入正确的电话号码',
						trigger: 'blur',
					},
				],


			},
		};
	},
	computed: {
		dialogVisible: {
			get() {
				return this.visible;
			},
			set(value) {
				if (!value) {
					this.$emit('close');
				}
			},
		},
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.formData = { ...this.form };
				this.$nextTick(() => {
					if (this.$refs.tenantForm) {
						this.$refs.tenantForm.clearValidate();
					}
				});
			}
		},
	},
	methods: {
		handleClose() {
			this.$emit('close');
		},

		handleSubmit() {
			this.$refs.tenantForm.validate((valid) => {
				if (valid) {
					this.$emit('submit', this.formData);
				} else {
					return false;
				}
			});
		},
	},
};
</script>

<style scoped>
.form-tip {
	color: #909399;
	font-size: 12px;
	margin-top: 5px;
	line-height: 1.4;
}

.dialog-footer {
	text-align: right;
}

.el-divider {
	margin: 20px 0;
}
</style>