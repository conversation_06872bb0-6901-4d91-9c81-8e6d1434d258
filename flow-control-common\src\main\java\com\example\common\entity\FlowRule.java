package com.example.common.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

/**
 * 流量控制规则实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@TableName("flow_rule")
public class FlowRule {

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 规则名称
	 */
	@TableField("rule_name")
	private String ruleName;

	/**
	 * 资源名称
	 */
	@TableField("resource_name")
	private String resourceName;

	/**
	 * 租户ID
	 */
	@TableField("tenant_id")
	private String tenantId;

	/**
	 * 限流模式：0-QPS限流，1-并发限流（线程数） 注意：数据库存储值与Sentinel标准相反，转换时需要处理
	 */
	@TableField("limit_mode")
	private Integer limitMode;

	/**
	 * 阈值
	 */
	@TableField("threshold")
	private Integer threshold;

	/**
	 * 流控策略：0-直接，1-关联，2-链路
	 */
	@TableField("strategy")
	private Integer strategy;

	/**
	 * 关联资源
	 */
	@TableField("related_resource")
	private String relatedResource;

	/**
	 * 流控行为：0-快速失败，1-预热，2-排队等待
	 */
	@TableField("behavior")
	private Integer behavior;

	/**
	 * 预热时长（秒）
	 */
	@TableField("warm_up_period")
	private Integer warmUpPeriod;

	/**
	 * 排队超时时间（毫秒）
	 */
	@TableField("queue_timeout")
	private Integer queueTimeout;

	/**
	 * 是否集群模式：0-否，1-是
	 */
	@TableField("cluster_mode")
	private Integer clusterMode;

	/**
	 * 规则状态：0-禁用，1-启用
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 优先级
	 */
	@TableField("priority")
	private Integer priority;

	/**
	 * 规则描述
	 */
	@TableField("description")
	private String description;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 删除标志：0-未删除，1-已删除
	 */
	@TableField("deleted")
	@TableLogic
	private Integer deleted;

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public void setUpdateTime(LocalDateTime updateTime) {
		this.updateTime = updateTime;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getId() {
		return id;
	}

	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public String getResourceName() {
		return resourceName;
	}

	public void setResourceName(String resourceName) {
		this.resourceName = resourceName;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Integer getLimitMode() {
		return limitMode;
	}

	public void setLimitMode(Integer limitMode) {
		this.limitMode = limitMode;
	}

	public Integer getThreshold() {
		return threshold;
	}

	public void setThreshold(Integer threshold) {
		this.threshold = threshold;
	}

	public Integer getStrategy() {
		return strategy;
	}

	public void setStrategy(Integer strategy) {
		this.strategy = strategy;
	}

	public String getRelatedResource() {
		return relatedResource;
	}

	public void setRelatedResource(String relatedResource) {
		this.relatedResource = relatedResource;
	}

	public Integer getBehavior() {
		return behavior;
	}

	public void setBehavior(Integer behavior) {
		this.behavior = behavior;
	}

	public Integer getWarmUpPeriod() {
		return warmUpPeriod;
	}

	public void setWarmUpPeriod(Integer warmUpPeriod) {
		this.warmUpPeriod = warmUpPeriod;
	}

	public Integer getQueueTimeout() {
		return queueTimeout;
	}

	public void setQueueTimeout(Integer queueTimeout) {
		this.queueTimeout = queueTimeout;
	}

	public Integer getClusterMode() {
		return clusterMode;
	}

	public void setClusterMode(Integer clusterMode) {
		this.clusterMode = clusterMode;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public LocalDateTime getUpdateTime() {
		return updateTime;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

	public Integer getDeleted() {
		return deleted;
	}

	public void setDeleted(Integer deleted) {
		this.deleted = deleted;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		FlowRule flowRule = (FlowRule) o;
		return java.util.Objects.equals(id, flowRule.id) && java.util.Objects.equals(ruleName, flowRule.ruleName)
				&& java.util.Objects.equals(resourceName, flowRule.resourceName)
				&& java.util.Objects.equals(tenantId, flowRule.tenantId)
				&& java.util.Objects.equals(limitMode, flowRule.limitMode)
				&& java.util.Objects.equals(threshold, flowRule.threshold)
				&& java.util.Objects.equals(strategy, flowRule.strategy)
				&& java.util.Objects.equals(relatedResource, flowRule.relatedResource)
				&& java.util.Objects.equals(behavior, flowRule.behavior)
				&& java.util.Objects.equals(warmUpPeriod, flowRule.warmUpPeriod)
				&& java.util.Objects.equals(queueTimeout, flowRule.queueTimeout)
				&& java.util.Objects.equals(clusterMode, flowRule.clusterMode)
				&& java.util.Objects.equals(status, flowRule.status)
				&& java.util.Objects.equals(priority, flowRule.priority)
				&& java.util.Objects.equals(description, flowRule.description)
				&& java.util.Objects.equals(createTime, flowRule.createTime)
				&& java.util.Objects.equals(updateTime, flowRule.updateTime)
				&& java.util.Objects.equals(createBy, flowRule.createBy)
				&& java.util.Objects.equals(updateBy, flowRule.updateBy)
				&& java.util.Objects.equals(deleted, flowRule.deleted);
	}

	@Override
	public int hashCode() {
		return java.util.Objects.hash(id, ruleName, resourceName, tenantId, limitMode, threshold, strategy,
				relatedResource, behavior, warmUpPeriod, queueTimeout, clusterMode, status, priority, description,
				createTime, updateTime, createBy, updateBy, deleted);
	}

	@Override
	public String toString() {
		return "FlowRule{" + "id=" + id + ", ruleName='" + ruleName + '\'' + ", resourceName='" + resourceName + '\''
				+ ", tenantId='" + tenantId + '\'' + ", limitMode=" + limitMode + ", threshold=" + threshold
				+ ", strategy=" + strategy + ", relatedResource='" + relatedResource + '\'' + ", behavior=" + behavior
				+ ", warmUpPeriod=" + warmUpPeriod + ", queueTimeout=" + queueTimeout + ", clusterMode=" + clusterMode
				+ ", status=" + status + ", priority=" + priority + ", description='" + description + '\''
				+ ", createTime=" + createTime + ", updateTime=" + updateTime + ", createBy='" + createBy + '\''
				+ ", updateBy='" + updateBy + '\'' + ", deleted=" + deleted + '}';
	}
}