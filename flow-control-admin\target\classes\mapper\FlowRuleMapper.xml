<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.FlowRuleMapper">

	<!-- 结果映射 -->
	<resultMap id="BaseResultMap" type="com.example.common.entity.FlowRule">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
		<result column="resource_name" property="resourceName" jdbcType="VARCHAR"/>
		<result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
		<result column="limit_mode" property="limitMode" jdbcType="INTEGER"/>
		<result column="threshold" property="threshold" jdbcType="DOUBLE"/>
		<result column="strategy" property="strategy" jdbcType="INTEGER"/>
		<result column="related_resource" property="relatedResource" jdbcType="VARCHAR"/>
		<result column="behavior" property="behavior" jdbcType="INTEGER"/>
		<result column="warm_up_period" property="warmUpPeriod" jdbcType="INTEGER"/>
		<result column="queue_timeout" property="queueTimeout" jdbcType="INTEGER"/>
		<result column="cluster_mode" property="clusterMode" jdbcType="INTEGER"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="priority" property="priority" jdbcType="INTEGER"/>
		<result column="description" property="description" jdbcType="VARCHAR"/>
		<result column="create_by" property="createBy" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="deleted" property="deleted" jdbcType="INTEGER"/>
	</resultMap>

	<!-- 基础字段 -->
	<sql id="Base_Column_List">
        id, rule_name, resource_name, tenant_id, limit_mode, threshold, strategy,
        related_resource, behavior, warm_up_period, queue_timeout, cluster_mode,
        status, priority, description, create_by, create_time, update_by, update_time, deleted
	</sql>

	<!-- FlowRuleVO结果映射 -->
	<resultMap id="FlowRuleVOResultMap" type="com.example.admin.vo.FlowRuleVO">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
		<result column="resource_name" property="resourceName" jdbcType="VARCHAR"/>
		<result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
		<result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
		<result column="limit_mode" property="limitMode" jdbcType="INTEGER"/>
		<result column="limit_mode_name" property="limitModeName" jdbcType="VARCHAR"/>
		<result column="threshold" property="threshold" jdbcType="INTEGER"/>
		<result column="strategy" property="strategy" jdbcType="INTEGER"/>
		<result column="strategy_name" property="strategyName" jdbcType="VARCHAR"/>
		<result column="related_resource" property="relatedResource" jdbcType="VARCHAR"/>
		<result column="behavior" property="behavior" jdbcType="INTEGER"/>
		<result column="behavior_name" property="behaviorName" jdbcType="VARCHAR"/>
		<result column="warm_up_period" property="warmUpPeriod" jdbcType="INTEGER"/>
		<result column="queue_timeout" property="queueTimeout" jdbcType="INTEGER"/>
		<result column="cluster_mode" property="clusterMode" jdbcType="INTEGER"/>
		<result column="cluster_mode_name" property="clusterModeName" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="status_name" property="statusName" jdbcType="VARCHAR"/>
		<result column="priority" property="priority" jdbcType="INTEGER"/>
		<result column="description" property="description" jdbcType="VARCHAR"/>
		<result column="create_by" property="createBy" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>

	<!-- 分页查询流控规则VO（带租户名称和枚举值名称） -->
	<select id="selectFlowRuleVOPage" resultMap="FlowRuleVOResultMap">
        SELECT
            fr.id,
            fr.rule_name,
            fr.resource_name,
            fr.tenant_id,
            COALESCE(ti.tenant_name, fr.tenant_id) as tenant_name,
            fr.limit_mode,
            CASE fr.limit_mode
                WHEN 0 THEN 'QPS'
                WHEN 1 THEN '线程数'
                ELSE '未知'
            END as limit_mode_name,
            fr.threshold,
            fr.strategy,
            CASE fr.strategy
                WHEN 0 THEN '直接'
                WHEN 1 THEN '关联'
                WHEN 2 THEN '链路'
                ELSE '未知'
            END as strategy_name,
            fr.related_resource,
            fr.behavior,
            CASE fr.behavior
                WHEN 0 THEN '快速失败'
                WHEN 1 THEN '预热'
                WHEN 2 THEN '排队等待'
                ELSE '未知'
            END as behavior_name,
            fr.warm_up_period,
            fr.queue_timeout,
            fr.cluster_mode,
            CASE fr.cluster_mode
                WHEN 0 THEN '单机'
                WHEN 1 THEN '集群'
                ELSE '未知'
            END as cluster_mode_name,
            fr.status,
            CASE fr.status
                WHEN 0 THEN '禁用'
                WHEN 1 THEN '启用'
                ELSE '未知'
            END as status_name,
            fr.priority,
            fr.description,
            fr.create_by,
            fr.create_time,
            fr.update_by,
            fr.update_time
        FROM flow_rule fr
        LEFT JOIN tenant_info ti ON fr.tenant_id = ti.tenant_id
        WHERE fr.deleted = 0
		<if test="tenantId != null and tenantId != ''">
            AND fr.tenant_id = #{tenantId}
		</if>
		<if test="resourceName != null and resourceName != ''">
            AND fr.resource_name LIKE CONCAT('%', #{resourceName}, '%')
		</if>
		<if test="status != null">
            AND fr.status = #{status}
		</if>
        ORDER BY fr.priority ASC, fr.create_time DESC
	</select>

	<!-- 分页查询流控规则 -->
	<select id="selectFlowRulePage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_rule
        WHERE deleted = 0
	<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
	</if>
	<if test="resourceName != null and resourceName != ''">
            AND resource_name LIKE CONCAT('%', #{resourceName}, '%')
	</if>
	<if test="ruleName != null and ruleName != ''">
            AND rule_name LIKE CONCAT('%', #{ruleName}, '%')
	</if>
	<if test="status != null">
            AND status = #{status}
	</if>
	<if test="limitMode != null">
            AND limit_mode = #{limitMode}
	</if>
        ORDER BY priority ASC, create_time DESC
</select>

<!-- 根据租户ID查询流控规则 -->
<select id="selectByTenantId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_rule
        WHERE tenant_id = #{tenantId} AND deleted = 0
<if test="status != null">
            AND status = #{status}
</if>
        ORDER BY priority ASC, create_time DESC
<if test="limit != null">
            LIMIT #{limit}
</if>
</select>

<!-- 根据资源名称查询流控规则 -->
<select id="selectByResourceName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_rule
        WHERE resource_name = #{resourceName} AND deleted = 0
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
<if test="status != null">
            AND status = #{status}
</if>
        ORDER BY priority ASC, create_time DESC
<if test="limit != null">
            LIMIT #{limit}
</if>
</select>

<!-- 根据规则名称查询（用于重名检查） -->
<select id="selectByRuleName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_rule
        WHERE rule_name = #{ruleName} AND deleted = 0
<if test="excludeId != null">
            AND id != #{excludeId}
</if>
        LIMIT 1
</select>

<!-- 批量更新规则状态 -->
<update id="batchUpdateStatus">
        UPDATE flow_rule
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE id IN
<foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
</foreach>
        AND deleted = 0
</update>

<!-- 统计租户规则数量 -->
<select id="countByTenantId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM flow_rule
        WHERE tenant_id = #{tenantId} AND deleted = 0
<if test="status != null">
            AND status = #{status}
</if>
</select>

<!-- 查询启用的规则（按优先级排序） -->
<select id="selectEnabledRules" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_rule
        WHERE status = 1 AND deleted = 0
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
<if test="resourceName != null and resourceName != ''">
            AND resource_name = #{resourceName}
</if>
        ORDER BY priority ASC, create_time DESC
<if test="limit != null">
            LIMIT #{limit}
</if>
</select>

<!-- 查询优先级排序的规则 -->
<select id="selectByPriorityOrder" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_rule
        WHERE deleted = 0
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
<if test="resourceName != null and resourceName != ''">
            AND resource_name = #{resourceName}
</if>
<if test="status != null">
            AND status = #{status}
</if>
        ORDER BY priority ASC, create_time DESC
<if test="limit != null">
            LIMIT #{limit}
</if>
</select>

<!-- 查询即将过期的规则 -->
<select id="selectExpiringRules" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_rule
        WHERE deleted = 0 AND status = 1
        AND create_time &lt; #{beforeTime}
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
        ORDER BY create_time ASC
<if test="limit != null">
            LIMIT #{limit}
</if>
</select>

<!-- 统计规则状态分布 -->
<select id="selectStatusStatistics" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count,
            CASE status
                WHEN 0 THEN '禁用'
                WHEN 1 THEN '启用'
                ELSE '未知'
            END as status_name
        FROM flow_rule
        WHERE deleted = 0
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
        GROUP BY status
        ORDER BY status
</select>

<!-- 统计租户规则分布 -->
<select id="selectTenantRuleStatistics" resultType="java.util.Map">
        SELECT 
            fr.tenant_id,
            ti.tenant_name,
            COUNT(*) as rule_count,
            SUM(CASE WHEN fr.status = 1 THEN 1 ELSE 0 END) as enabled_count,
            SUM(CASE WHEN fr.status = 0 THEN 1 ELSE 0 END) as disabled_count
        FROM flow_rule fr
        LEFT JOIN tenant_info ti ON fr.tenant_id = ti.tenant_id
        WHERE fr.deleted = 0
        GROUP BY fr.tenant_id, ti.tenant_name
        ORDER BY rule_count DESC
<if test="limit != null">
            LIMIT #{limit}
</if>
</select>

<!-- 批量插入规则 -->
<insert id="batchInsert">
        INSERT INTO flow_rule (
            rule_name, resource_name, tenant_id, limit_mode, threshold, strategy,
            related_resource, behavior, warm_up_period, queue_timeout, cluster_mode,
            status, priority, description, create_by, create_time, update_by, update_time, deleted
        ) VALUES
<foreach collection="list" item="item" separator=",">
            (
                #{item.ruleName}, #{item.resourceName}, #{item.tenantId}, #{item.limitMode},
                #{item.threshold}, #{item.strategy}, #{item.relatedResource}, #{item.behavior},
                #{item.warmUpPeriod}, #{item.queueTimeout}, #{item.clusterMode}, #{item.status},
                #{item.priority}, #{item.description}, #{item.createBy}, NOW(), #{item.updateBy}, NOW(), 0
            )
</foreach>
</insert>

<!-- 检查规则名称是否存在 -->
<select id="existsByRuleName" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM flow_rule
        WHERE rule_name = #{ruleName} AND deleted = 0
<if test="excludeId != null">
            AND id != #{excludeId}
</if>
</select>

<!-- 检查资源是否有规则 -->
<select id="existsByResourceName" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM flow_rule
        WHERE resource_name = #{resourceName} AND deleted = 0
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
<if test="status != null">
            AND status = #{status}
</if>
</select>

</mapper>