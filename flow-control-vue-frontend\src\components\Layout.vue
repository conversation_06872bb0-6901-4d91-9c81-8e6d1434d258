<template>
	<div class="layout">
		<el-container>
			<!-- 移动端遮罩层 -->
			<div
				v-if="isMobile && !isCollapsed"
				class="mobile-overlay"
				@click="toggleSidebar"
			></div>

			<!-- 侧边栏 -->
			<el-aside
				:width="sidebarWidth"
				class="sidebar"
				:class="{
					'sidebar-collapsed': isCollapsed,
					'sidebar-mobile': isMobile,
				}"
			>
				<div class="logo">
					<h3 v-if="!isCollapsed || isMobile">Sentinel</h3>
					<h3 v-else class="logo-collapsed">S</h3>
				</div>
				<el-menu
					:default-active="$route.path"
					class="sidebar-menu"
					background-color="#304156"
					text-color="#bfcbd9"
					active-text-color="#409EFF"
					:collapse="isCollapsed && !isMobile"
					router
				>
					<el-menu-item index="/dashboard">
						<i class="el-icon-s-home"></i>
						<span>{{ $t('menu.dashboard') }}</span>
					</el-menu-item>
					<el-menu-item index="/tenant-flow-rules">
						<i class="el-icon-user"></i>
						<span>{{ $t('menu.tenantFlowRules') }}</span>
					</el-menu-item>
					<el-menu-item index="/interface-flow-rules">
						<i class="el-icon-s-order"></i>
						<span>{{ $t('menu.interfaceFlowRules') }}</span>
					</el-menu-item>
					<el-menu-item index="/ip-flow-rules">
						<i class="el-icon-connection"></i>
						<span>{{ $t('menu.ipFlowRules') }}</span>
					</el-menu-item>
					<el-menu-item index="/ip-blackwhite-list">
						<i class="el-icon-s-check"></i>
						<span>{{ $t('menu.ipBlackWhiteList') }}</span>
					</el-menu-item>
					<el-menu-item index="/statistics">
						<i class="el-icon-data-analysis"></i>
						<span>{{ $t('menu.statistics') }}</span>
					</el-menu-item>
					<el-menu-item index="/config">
						<i class="el-icon-tools"></i>
						<span>{{ $t('menu.config') }}</span>
					</el-menu-item>
				</el-menu>
			</el-aside>

			<!-- 主内容区 -->
			<el-container>
				<!-- 顶部导航 -->
				<el-header class="header">
					<div class="header-left">
						<!-- 汉堡菜单按钮 -->
						<el-button
							type="text"
							class="sidebar-toggle"
							@click="toggleSidebar"
						>
							<i class="el-icon-s-fold" v-if="!isCollapsed"></i>
							<i class="el-icon-s-unfold" v-else></i>
						</el-button>

						<el-breadcrumb separator="/" class="breadcrumb">
							<el-breadcrumb-item :to="{ path: '/dashboard' }">{{
								$t('menu.dashboard')
							}}</el-breadcrumb-item>
							<el-breadcrumb-item>{{
								getCurrentPageName()
							}}</el-breadcrumb-item>
						</el-breadcrumb>
					</div>
					<div class="header-right">
						<!-- 语言切换按钮 -->
						<div class="language-toggle" @click="toggleLanguage">
							<span class="language-flag">{{
								currentLanguageFlag
							}}</span>
							<span class="language-text" v-if="!isMobile">{{
								currentLanguageLabel
							}}</span>
						</div>

						<!-- 主题切换按钮 -->
						<div class="theme-toggle" @click="toggleTheme">
							<i :class="themeIcon" class="theme-icon"></i>
							<span class="theme-text" v-if="!isMobile">{{
								themeText
							}}</span>
						</div>

						<el-dropdown @command="handleCommand">
							<span class="user-info">
								<i class="el-icon-user-solid"></i>
								{{
									currentUser ? currentUser.username : '用户'
								}}
								<i class="el-icon-arrow-down"></i>
							</span>
							<el-dropdown-menu slot="dropdown">
								<el-dropdown-item command="language-settings">
									<i class="el-icon-s-opportunity"></i>
									{{ $t('user.languageSettings') }}
								</el-dropdown-item>
								<el-dropdown-item command="theme-settings">
									<i class="el-icon-setting"></i>
									{{ $t('user.themeSettings') }}
								</el-dropdown-item>
								<el-dropdown-item divided command="logout">
									<i class="el-icon-switch-button"></i>
									{{ $t('user.logout') }}
								</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
					</div>
				</el-header>

				<!-- 主内容 -->
				<el-main class="main-content">
					<slot></slot>

					<!-- 用户引导组件 -->
					<UserGuide
						ref="userGuide"
						:auto-start="shouldShowGuide"
						@complete="onGuideComplete"
					/>

					<!-- 帮助中心组件 -->
					<HelpCenter ref="helpCenter" @show-guide="showUserGuide" />
				</el-main>
			</el-container>
		</el-container>
	</div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import UserGuide from './UserGuide.vue';
import HelpCenter from './HelpCenter.vue';

export default {
	name: 'Layout',
	components: {
		UserGuide,
		HelpCenter,
	},
	data() {
		return {
			isCollapsed: false,
			isMobile: false,
			shouldShowGuide: false,
		};
	},
	computed: {
		...mapGetters('auth', ['currentUser']),
		...mapGetters('theme', [
			'currentTheme',
			'isDarkTheme',
			'themeIcon',
			'themeText',
		]),
		...mapGetters('language', [
			'currentLanguage',
			'currentLanguageLabel',
			'currentLanguageFlag',
		]),
		sidebarWidth() {
			if (this.isMobile) {
				return this.isCollapsed ? '0px' : '200px';
			}
			return this.isCollapsed ? '64px' : '200px';
		},
	},
	mounted() {
		this.checkMobile();
		window.addEventListener('resize', this.checkMobile);

		// 移动端默认折叠侧边栏
		if (this.isMobile) {
			this.isCollapsed = true;
		}

		// 初始化主题
		this.$store.dispatch('theme/initTheme');
		// 初始化语言
		this.$store.dispatch('language/initLanguage');

		// 检查是否需要显示用户引导
		this.checkUserGuide();
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.checkMobile);
	},
	methods: {
		...mapActions('auth', ['logout']),
		...mapActions('theme', ['toggleTheme', 'setThemeManually']),
		...mapActions('language', ['toggleLanguage', 'setLanguage']),
		checkMobile() {
			this.isMobile = window.innerWidth <= 768;
			if (this.isMobile && !this.isCollapsed) {
				this.isCollapsed = true;
			}
		},
		toggleSidebar() {
			this.isCollapsed = !this.isCollapsed;
		},
		toggleTheme() {
			this.$store.dispatch('theme/toggleTheme');
			this.$message.success(
				`已切换到${this.isDarkTheme ? '明亮' : '暗黑'}模式`
			);
		},
		getCurrentPageName() {
			const routeNames = {
				'/dashboard': this.$t('menu.dashboard'),
				'/tenant-flow-rules': this.$t('menu.tenantFlowRules'),
				'/interface-flow-rules': this.$t('menu.interfaceFlowRules'),
				'/ip-flow-rules': this.$t('menu.ipFlowRules'),
				'/ip-blackwhite-list': this.$t('menu.ipBlackWhiteList'),
				'/statistics': this.$t('menu.statistics'),
				'/config': this.$t('menu.config'),
			};
			return (
				routeNames[this.$route.path] || this.$t('common.unknownPage')
			);
		},
		async handleCommand(command) {
			if (command === 'logout') {
				try {
					await this.logout();
					this.$message.success('退出登录成功');
					this.$router.push('/login');
				} catch (error) {
					this.$message.error('退出登录失败');
				}
			} else if (command === 'theme-settings') {
				this.showThemeSettings();
			} else if (command === 'language-settings') {
				this.showLanguageSettings();
			}
		},
		showThemeSettings() {
			this.$msgbox({
				title: this.$t('user.themeSettings'),
				message: this.$createElement(
					'div',
					{
						style: 'text-align: center; padding: 20px;',
					},
					[
						this.$createElement(
							'p',
							{ style: 'margin-bottom: 20px; color: #606266;' },
							this.$t('theme.selectTheme')
						),
						this.$createElement(
							'div',
							{
								style: 'display: flex; justify-content: center; gap: 20px;',
							},
							[
								this.$createElement(
									'el-button',
									{
										props: {
											type:
												this.currentTheme === 'light'
													? 'primary'
													: 'default',
											icon: 'el-icon-sunny',
										},
										on: {
											click: () => {
												this.$store.dispatch(
													'theme/setThemeManually',
													'light'
												);
												this.$message.success(
													this.$t(
														'theme.switchToLight'
													)
												);
											},
										},
									},
									this.$t('theme.light')
								),
								this.$createElement(
									'el-button',
									{
										props: {
											type:
												this.currentTheme === 'dark'
													? 'primary'
													: 'default',
											icon: 'el-icon-moon',
										},
										on: {
											click: () => {
												this.$store.dispatch(
													'theme/setThemeManually',
													'dark'
												);
												this.$message.success(
													this.$t(
														'theme.switchToDark'
													)
												);
											},
										},
									},
									this.$t('theme.dark')
								),
							]
						),
					]
				),
				showCancelButton: false,
				confirmButtonText: this.$t('common.close'),
				customClass: 'theme-settings-dialog',
			});
		},

		showLanguageSettings() {
			this.$msgbox({
				title: this.$t('user.languageSettings'),
				message: this.$createElement(
					'div',
					{
						style: 'text-align: center; padding: 20px;',
					},
					[
						this.$createElement(
							'p',
							{ style: 'margin-bottom: 20px; color: #606266;' },
							this.$t('language.selectLanguage')
						),
						this.$createElement(
							'div',
							{
								style: 'display: flex; justify-content: center; gap: 20px;',
							},
							[
								this.$createElement(
									'el-button',
									{
										props: {
											type:
												this.currentLanguage === 'zh-CN'
													? 'primary'
													: 'default',
											icon: 'el-icon-chat-dot-round',
										},
										on: {
											click: () => {
												this.$store.dispatch(
													'language/setLanguage',
													'zh-CN'
												);
												this.$message.success(
													this.$t(
														'language.switchToChinese'
													)
												);
											},
										},
									},
									this.$t('language.chinese')
								),
								this.$createElement(
									'el-button',
									{
										props: {
											type:
												this.currentLanguage === 'en-US'
													? 'primary'
													: 'default',
											icon: 'el-icon-chat-dot-round',
										},
										on: {
											click: () => {
												this.$store.dispatch(
													'language/setLanguage',
													'en-US'
												);
												this.$message.success(
													this.$t(
														'language.switchToEnglish'
													)
												);
											},
										},
									},
									this.$t('language.english')
								),
							]
						),
					]
				),
				showCancelButton: false,
				confirmButtonText: this.$t('common.close'),
				customClass: 'language-settings-dialog',
			});
		},

		// 检查是否需要显示用户引导
		checkUserGuide() {
			const completed = localStorage.getItem('user_guide_completed');
			const isFirstVisit = localStorage.getItem('first_visit');

			if (completed !== 'true' || isFirstVisit === null) {
				// 延迟显示引导，确保页面完全加载
				setTimeout(() => {
					this.shouldShowGuide = true;
				}, 1000);

				localStorage.setItem('first_visit', 'false');
			}
		},

		// 显示用户引导
		showUserGuide() {
			if (this.$refs.userGuide) {
				this.$refs.userGuide.startGuide();
			}
		},

		// 引导完成回调
		onGuideComplete() {
			this.$message.success(this.$t('guide.completed'));
		},
	},
};
</script>

<style scoped>
.layout {
	height: 100vh;
	position: relative;
}

/* 移动端遮罩层 */
.mobile-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	transition: opacity 0.3s;
}

/* 侧边栏样式 */
.sidebar {
	background-color: var(--sidebar-bg);
	overflow: hidden;
	transition: width 0.3s;
	position: relative;
	z-index: 1001;
}

.sidebar-mobile {
	position: fixed;
	top: 0;
	left: 0;
	height: 100vh;
	z-index: 1001;
}

.sidebar-collapsed.sidebar-mobile {
	transform: translateX(-100%);
}

.logo {
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: var(--sidebar-logo-bg);
	color: white;
	margin-bottom: 0;
	transition: all 0.3s;
}

.logo h3 {
	margin: 0;
	font-weight: 600;
	font-size: 18px;
	transition: all 0.3s;
}

.logo-collapsed {
	font-size: 20px !important;
}

.sidebar-menu {
	border: none;
	height: calc(100vh - 60px);
}

.sidebar-menu .el-menu-item {
	height: 50px;
	line-height: 50px;
}

.sidebar-menu .el-menu-item i {
	margin-right: 8px;
}

/* 顶部导航样式 */
.header {
	background-color: var(--header-bg);
	border-bottom: 1px solid var(--header-border);
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20px;
	height: 60px !important;
	position: relative;
	z-index: 999;
	transition: all 0.3s ease;
}

.header-left {
	display: flex;
	align-items: center;
}

.sidebar-toggle {
	margin-right: 15px;
	font-size: 18px;
	color: var(--text-regular);
	transition: color 0.3s ease;
}

.sidebar-toggle:hover {
	color: var(--primary-color);
}

.breadcrumb {
	font-size: 14px;

	.el-breadcrumb__inner {
		color: var(--text-regular) !important;

		&.is-link:hover {
			color: var(--primary-color) !important;
		}
	}
}

.header-right {
	display: flex;
	align-items: center;
	gap: 15px;
}

/* 主题切换按钮样式 */
.language-toggle {
	display: flex;
	align-items: center;
	cursor: pointer;
	padding: 8px 12px;
	border-radius: 4px;
	transition: all 0.3s ease;
	color: var(--text-regular);

	&:hover {
		background-color: var(--background-light);
		color: var(--primary-color);
	}

	.language-flag {
		font-size: 16px;
		margin-right: 6px;
	}

	.language-text {
		font-size: 14px;
		white-space: nowrap;
	}
}

.theme-toggle {
	display: flex;
	align-items: center;
	cursor: pointer;
	padding: 8px 12px;
	border-radius: 4px;
	transition: all 0.3s ease;
	color: var(--text-regular);

	&:hover {
		background-color: var(--background-light);
		color: var(--primary-color);
	}

	.theme-icon {
		font-size: 16px;
		margin-right: 6px;
	}

	.theme-text {
		font-size: 14px;
		white-space: nowrap;
	}
}

.user-info {
	cursor: pointer;
	color: var(--text-regular);
	font-size: 14px;
	display: flex;
	align-items: center;
	transition: color 0.3s ease;

	&:hover {
		color: var(--primary-color);
	}
}

.user-info i {
	margin: 0 4px;
}

.main-content {
	background-color: var(--background-page);
	padding: 0;
	overflow-y: auto;
	transition: margin-left 0.3s, background-color 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.header {
		padding: 0 15px;
	}

	.breadcrumb {
		display: none;
	}

	.sidebar-toggle {
		margin-right: 10px;
	}

	.main-content {
		margin-left: 0 !important;
	}
}

@media (max-width: 480px) {
	.header {
		padding: 0 10px;
	}

	.user-info span {
		display: none;
	}

	.user-info {
		font-size: 16px;
	}
}

/* 平板适配 */
@media (min-width: 769px) and (max-width: 1024px) {
	.sidebar {
		width: 180px;
	}

	.header {
		padding: 0 15px;
	}
}
</style>
