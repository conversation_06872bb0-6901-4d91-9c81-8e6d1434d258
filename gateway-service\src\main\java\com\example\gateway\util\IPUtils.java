package com.example.gateway.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * IP工具类
 * 提供IP地址验证、范围检查等功能
 */
public class IPUtils {
    private static final Logger log = LoggerFactory.getLogger(IPUtils.class);

    /**
     * 验证IP地址是否有效
     */
    public static boolean isValidIP(String ip) {
        if (!StringUtils.hasText(ip)) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * IP地址转换为长整型
     */
    public static long ipToLong(String ip) {
        if (!isValidIP(ip)) {
            throw new IllegalArgumentException("Invalid IP address: " + ip);
        }

        String[] parts = ip.split("\\.");
        long result = 0;
        for (int i = 0; i < 4; i++) {
            result = result * 256 + Integer.parseInt(parts[i]);
        }
        return result;
    }

    /**
     * 判断IP是否在指定的IP段内 支持CIDR格式，如：***********/24
     */
    public static boolean isIPInRange(String ip, String cidr) {
        if (!isValidIP(ip) || !StringUtils.hasText(cidr)) {
            return false;
        }

        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return false;
            }

            String networkIP = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);

            if (!isValidIP(networkIP) || prefixLength < 0 || prefixLength > 32) {
                return false;
            }

            long ipLong = ipToLong(ip);
            long networkLong = ipToLong(networkIP);
            long mask = (0xFFFFFFFFL << (32 - prefixLength)) & 0xFFFFFFFFL;

            return (ipLong & mask) == (networkLong & mask);
        } catch (Exception e) {
            log.warn("判断IP是否在范围内失败: ip={}, cidr={}", ip, cidr, e);
            return false;
        }
    }

    /**
     * 判断IP是否在指定的IP范围内（支持起始IP-结束IP格式）
     */
    public static boolean isIPInRange(String ip, String startIP, String endIP) {
        if (!isValidIP(ip) || !isValidIP(startIP) || !isValidIP(endIP)) {
            return false;
        }

        try {
            long ipLong = ipToLong(ip);
            long startLong = ipToLong(startIP);
            long endLong = ipToLong(endIP);

            return ipLong >= startLong && ipLong <= endLong;
        } catch (Exception e) {
            log.warn("判断IP是否在范围内失败: ip={}, start={}, end={}", ip, startIP, endIP, e);
            return false;
        }
    }

    /**
     * 检查IP是否匹配通配符模式
     */
    public static boolean isIPInWildcard(String ipAddress, String wildcardPattern) {
        try {
            if (!StringUtils.hasText(ipAddress) || !StringUtils.hasText(wildcardPattern)) {
                return false;
            }

            String[] ipParts = ipAddress.split("\\.");
            String[] patternParts = wildcardPattern.split("\\.");

            if (ipParts.length != 4 || patternParts.length != 4) {
                return false;
            }

            for (int i = 0; i < 4; i++) {
                if (!"*".equals(patternParts[i]) && !ipParts[i].equals(patternParts[i])) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("通配符匹配检查失败: ip={}, pattern={}", ipAddress, wildcardPattern, e);
            return false;
        }
    }

    /**
     * 检查IP是否在范围内（支持多种格式）
     * 支持格式：
     * 1. 单个IP: ***********
     * 2. IP范围: ***********-***********00
     * 3. CIDR: ***********/24
     * 4. 通配符: 192.168.1.*
     */
    public static boolean isIPMatched(String ipAddress, String range) {
        try {
            if (!StringUtils.hasText(ipAddress) || !StringUtils.hasText(range)) {
                return false;
            }

            // 如果包含/，按CIDR处理
            if (range.contains("/")) {
                return isIPInRange(ipAddress, range);
            }

            // 如果包含-，按范围处理
            if (range.contains("-")) {
                String[] parts = range.split("-");
                if (parts.length == 2) {
                    String startIP = parts[0].trim();
                    String endIP = parts[1].trim();
                    return isIPInRange(ipAddress, startIP, endIP);
                }
            }

            // 如果包含*，按通配符处理
            if (range.contains("*")) {
                return isIPInWildcard(ipAddress, range);
            }

            // 否则按单个IP处理
            return ipAddress.equals(range);
        } catch (Exception e) {
            log.error("IP范围检查失败: ip={}, range={}", ipAddress, range, e);
            return false;
        }
    }
}