// 主题管理 Vuex 模块

const state = {
  // 当前主题：'light' 或 'dark'
  currentTheme: localStorage.getItem('theme') || 'light',
  // 是否支持系统主题
  systemThemeSupported: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches !== undefined
}

const mutations = {
  SET_THEME(state, theme) {
    state.currentTheme = theme
    // 保存到本地存储
    localStorage.setItem('theme', theme)
    // 应用主题到DOM
    document.documentElement.setAttribute('data-theme', theme)
  },
  
  TOGGLE_THEME(state) {
    const newTheme = state.currentTheme === 'light' ? 'dark' : 'light'
    state.currentTheme = newTheme
    localStorage.setItem('theme', newTheme)
    document.documentElement.setAttribute('data-theme', newTheme)
  }
}

const actions = {
  // 设置主题
  setTheme({ commit }, theme) {
    if (theme === 'light' || theme === 'dark') {
      commit('SET_THEME', theme)
    }
  },
  
  // 切换主题
  toggleTheme({ commit }) {
    commit('TOGGLE_THEME')
  },
  
  // 初始化主题
  initTheme({ commit, state }) {
    // 检查本地存储中的主题设置
    const savedTheme = localStorage.getItem('theme')
    
    if (savedTheme) {
      commit('SET_THEME', savedTheme)
    } else if (state.systemThemeSupported) {
      // 如果没有保存的主题，使用系统主题
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      commit('SET_THEME', systemTheme)
    } else {
      // 默认使用明亮主题
      commit('SET_THEME', 'light')
    }
    
    // 监听系统主题变化
    if (state.systemThemeSupported) {
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        // 只有在用户没有手动设置主题时才跟随系统
        if (!localStorage.getItem('theme-manual')) {
          const systemTheme = e.matches ? 'dark' : 'light'
          commit('SET_THEME', systemTheme)
        }
      })
    }
  },
  
  // 手动设置主题（标记为用户手动设置）
  setThemeManually({ commit }, theme) {
    localStorage.setItem('theme-manual', 'true')
    commit('SET_THEME', theme)
  },
  
  // 重置为系统主题
  resetToSystemTheme({ commit, state }) {
    localStorage.removeItem('theme-manual')
    if (state.systemThemeSupported) {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      commit('SET_THEME', systemTheme)
    } else {
      commit('SET_THEME', 'light')
    }
  }
}

const getters = {
  // 获取当前主题
  currentTheme: state => state.currentTheme,
  
  // 是否为暗黑主题
  isDarkTheme: state => state.currentTheme === 'dark',
  
  // 是否为明亮主题
  isLightTheme: state => state.currentTheme === 'light',
  
  // 是否支持系统主题
  systemThemeSupported: state => state.systemThemeSupported,
  
  // 获取主题图标
  themeIcon: state => {
    return state.currentTheme === 'dark' ? 'el-icon-sunny' : 'el-icon-moon'
  },
  
  // 获取主题文本
  themeText: state => {
    return state.currentTheme === 'dark' ? '明亮模式' : '暗黑模式'
  },
  
  // 是否为用户手动设置的主题
  isManualTheme: () => {
    return localStorage.getItem('theme-manual') === 'true'
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}