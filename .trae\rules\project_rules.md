## 通用礼节 (General Etiquette)
- 优先保证代码简洁易懂。
- 别搞过度设计，简单实用就好。
- 写代码时，要注意圈复杂度，函数尽量小，尽量可以复用，尽量不写重复代码。
- 写代码时，注意模块设计，尽量使用设计模式。
- 给我解释代码的时候，说人话，别拽专业术语。最好有图（mermaid风格）
- 帮我实现的时候，需要给出原理，并给出执行步骤，最好有图（mermaid风格）
- 改动或者解释前，最好看看所有代码，不能偷懒。
- 改动前，要做最小化修改，尽量不修改到其他模块的代码
- 改动后，假定10条case 输入，并给出预期结果
- 给出的mermaid图，必须自检语法，可以被渲染，在暗黑主题上清晰可见
- 给出的mermaid图，必须要可以被暗黑主题渲染清晰
 
# 实验性规则 (Experimental Rule)
当你被要求修复一个 Bug 时，请遵循以下步骤：
1.  **理解问题 (Understand):** 仔细阅读 Bug 描述和相关代码，复述你对问题的理解。
2.  **分析原因 (Analyze):** 提出至少两种可能的根本原因。
3.  **制定计划 (Plan):** 描述你打算如何验证这些原因，并给出修复方案。
4.  **请求确认 (Confirm):** 在动手修改前，向我确认你的计划。
5.  **执行修复 (Execute):** 实施修复。
6.  **审查 (Review):** 查看自己的修改有没有问题。
7.  **解释说明 (Explain):** 解释你做了哪些修改以及为什么。
 
# MCP Interactive Feedback 规则
1. 在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 MCP mcp-feedback-enhanced。
2. 每当收到用户反馈，若反馈内容非空，必须再次调用 MCP mcp-feedback-enhanced，并根据反馈内容调整行为。
3. 仅当用户明确表示「结束」或「不再需要交互」时，才可停止调用 MCP mcp-feedback-enhanced，流程才算结束。
4. 除非收到结束指令，否则所有步骤都必须重复调用 MCP mcp-feedback-enhanced。
5. 完成任务前，必须使用 MCP mcp-feedback-enhanced 工具向用户询问反馈。
 
Always respond in 中文