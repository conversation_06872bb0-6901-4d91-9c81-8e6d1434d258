#!/bin/bash
# Gateway Service 启动脚本 - 解决中文乱码问题
# 设置字符编码环境变量

echo "正在启动 Gateway Service..."
echo "设置字符编码参数以解决中文乱码问题"

# 设置环境变量
export LANG=C.UTF-8
export LC_ALL=C.UTF-8
export LC_CTYPE=C.UTF-8

# 设置JVM字符编码参数
export JAVA_OPTS="-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -Dconsole.encoding=UTF-8"

# 启动应用
java $JAVA_OPTS -jar target/gateway-service-1.0-SNAPSHOT.jar

echo "Gateway Service 已启动"