package com.example.admin.dto;

import jakarta.validation.constraints.*;
import java.util.Objects;

/**
 * 流量规则DTO
 */
public class FlowRuleDTO {

	/**
	 * 规则名称
	 */
	@NotBlank(message = "规则名称不能为空")
	@Size(max = 100, message = "规则名称长度不能超过100个字符")
	private String ruleName;

	/**
	 * 资源名称
	 */
	@NotBlank(message = "资源名称不能为空")
	@Size(max = 200, message = "资源名称长度不能超过200个字符")
	private String resourceName;

	/**
	 * 租户ID
	 */
	@NotBlank(message = "租户ID不能为空")
	@Size(max = 50, message = "租户ID长度不能超过50个字符")
	private String tenantId;

	/**
	 * 限流模式：0-QPS限流，1-并发限流（线程数）
	 */
	@NotNull(message = "限流模式不能为空")
	@Min(value = 0, message = "限流模式值必须为0或1")
	@Max(value = 1, message = "限流模式值必须为0或1")
	private Integer limitMode;

	/**
	 * 阈值
	 */
	@NotNull(message = "阈值不能为空")
	@Min(value = 1, message = "阈值必须大于0")
	@Max(value = 1000000, message = "阈值不能超过1000000")
	private Integer threshold;

	/**
	 * 流控策略：0-直接，1-关联，2-链路
	 */
	@NotNull(message = "流控策略不能为空")
	@Min(value = 0, message = "流控策略值必须为0、1或2")
	@Max(value = 2, message = "流控策略值必须为0、1或2")
	private Integer strategy;

	/**
	 * 关联资源
	 */
	@Size(max = 200, message = "关联资源长度不能超过200个字符")
	private String relatedResource;

	/**
	 * 流控行为：0-快速失败，1-预热，2-排队等待
	 */
	@NotNull(message = "流控行为不能为空")
	@Min(value = 0, message = "流控行为值必须为0、1或2")
	@Max(value = 2, message = "流控行为值必须为0、1或2")
	private Integer behavior;

	/**
	 * 预热时长（秒）
	 */
	@Min(value = 1, message = "预热时长必须大于0")
	@Max(value = 3600, message = "预热时长不能超过3600秒")
	private Integer warmUpPeriod;

	/**
	 * 排队超时时间（毫秒）
	 */
	@Min(value = 1, message = "排队超时时间必须大于0")
	@Max(value = 60000, message = "排队超时时间不能超过60000毫秒")
	private Integer queueTimeout;

	/**
	 * 是否集群模式：0-否，1-是
	 */
	@Min(value = 0, message = "集群模式值必须为0或1")
	@Max(value = 1, message = "集群模式值必须为0或1")
	private Integer clusterMode = 0;

	/**
	 * 规则状态：0-禁用，1-启用
	 */
	@Min(value = 0, message = "规则状态值必须为0或1")
	@Max(value = 1, message = "规则状态值必须为0或1")
	private Integer status = 1;

	/**
	 * 优先级
	 */
	@Min(value = 1, message = "优先级必须大于0")
	@Max(value = 1000, message = "优先级不能超过1000")
	private Integer priority = 1;

	/**
	 * 规则描述
	 */
	@Size(max = 500, message = "规则描述长度不能超过500个字符")
	private String description;

	/**
	 * 创建人
	 */
	@Size(max = 50, message = "创建人长度不能超过50个字符")
	private String createBy;

	/**
	 * 更新人
	 */
	@Size(max = 50, message = "更新人长度不能超过50个字符")
	private String updateBy;

	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public String getResourceName() {
		return resourceName;
	}

	public void setResourceName(String resourceName) {
		this.resourceName = resourceName;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Integer getLimitMode() {
		return limitMode;
	}

	public void setLimitMode(Integer limitMode) {
		this.limitMode = limitMode;
	}

	public Integer getThreshold() {
		return threshold;
	}

	public void setThreshold(Integer threshold) {
		this.threshold = threshold;
	}

	public Integer getStrategy() {
		return strategy;
	}

	public void setStrategy(Integer strategy) {
		this.strategy = strategy;
	}

	public String getRelatedResource() {
		return relatedResource;
	}

	public void setRelatedResource(String relatedResource) {
		this.relatedResource = relatedResource;
	}

	public Integer getBehavior() {
		return behavior;
	}

	public void setBehavior(Integer behavior) {
		this.behavior = behavior;
	}

	public Integer getWarmUpPeriod() {
		return warmUpPeriod;
	}

	public void setWarmUpPeriod(Integer warmUpPeriod) {
		this.warmUpPeriod = warmUpPeriod;
	}

	public Integer getQueueTimeout() {
		return queueTimeout;
	}

	public void setQueueTimeout(Integer queueTimeout) {
		this.queueTimeout = queueTimeout;
	}

	public Integer getClusterMode() {
		return clusterMode;
	}

	public void setClusterMode(Integer clusterMode) {
		this.clusterMode = clusterMode;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		FlowRuleDTO that = (FlowRuleDTO) o;
		return Objects.equals(ruleName, that.ruleName) && Objects.equals(resourceName, that.resourceName)
				&& Objects.equals(tenantId, that.tenantId) && Objects.equals(limitMode, that.limitMode)
				&& Objects.equals(threshold, that.threshold) && Objects.equals(strategy, that.strategy)
				&& Objects.equals(relatedResource, that.relatedResource) && Objects.equals(behavior, that.behavior)
				&& Objects.equals(warmUpPeriod, that.warmUpPeriod) && Objects.equals(queueTimeout, that.queueTimeout)
				&& Objects.equals(clusterMode, that.clusterMode) && Objects.equals(status, that.status)
				&& Objects.equals(priority, that.priority) && Objects.equals(description, that.description)
				&& Objects.equals(createBy, that.createBy) && Objects.equals(updateBy, that.updateBy);
	}

	@Override
	public int hashCode() {
		return Objects.hash(ruleName, resourceName, tenantId, limitMode, threshold, strategy, relatedResource, behavior,
				warmUpPeriod, queueTimeout, clusterMode, status, priority, description, createBy, updateBy);
	}

	@Override
	public String toString() {
		return "FlowRuleDTO{" + "ruleName='" + ruleName + '\'' + ", resourceName='" + resourceName + '\''
				+ ", tenantId='" + tenantId + '\'' + ", limitMode=" + limitMode + ", threshold=" + threshold
				+ ", strategy=" + strategy + ", relatedResource='" + relatedResource + '\'' + ", behavior=" + behavior
				+ ", warmUpPeriod=" + warmUpPeriod + ", queueTimeout=" + queueTimeout + ", clusterMode=" + clusterMode
				+ ", status=" + status + ", priority=" + priority + ", description='" + description + '\''
				+ ", createBy='" + createBy + '\'' + ", updateBy='" + updateBy + '\'' + '}';
	}
}