#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程限流测试脚本 - 最终版本
根据用户提供的SQL数据，所有租户都使用线程限流（grade=0），count值都为5
测试每个租户的线程限流功能，验证并发请求数超过限制时是否被正确阻止
"""

import requests
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8088/api/test"
TENANTS = ["tenant1", "tenant2", "tenant3", "tenant4", "tenant5"]
THREAD_LIMIT = 5  # 根据SQL数据，所有租户的线程限制都是5
CONCURRENT_REQUESTS = 8  # 每个租户发送8个并发请求
REQUEST_DURATION = 3  # 每个请求持续3秒

class ThreadLimitTester:
    def __init__(self):
        self.results = {}
        self.lock = threading.Lock()
    
    def send_request(self, tenant_id, request_id):
        """发送单个请求"""
        headers = {"X-Tenant-ID": tenant_id}
        params = {"sleep": REQUEST_DURATION}
        
        start_time = time.time()
        try:
            response = requests.get(BASE_URL, headers=headers, params=params, timeout=10)
            end_time = time.time()
            
            result = {
                "tenant_id": tenant_id,
                "request_id": request_id,
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response_time": end_time - start_time,
                "blocked": response.status_code == 429,  # 限流状态码
                "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3]
            }
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    result["processing_time"] = data.get("processingTime", 0)
                except:
                    pass
            elif response.status_code == 429:
                result["error_message"] = "请求被限流"
            else:
                result["error_message"] = f"HTTP {response.status_code}"
                
        except requests.exceptions.Timeout:
            result = {
                "tenant_id": tenant_id,
                "request_id": request_id,
                "status_code": 0,
                "success": False,
                "response_time": time.time() - start_time,
                "blocked": False,
                "error_message": "请求超时",
                "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3]
            }
        except Exception as e:
            result = {
                "tenant_id": tenant_id,
                "request_id": request_id,
                "status_code": 0,
                "success": False,
                "response_time": time.time() - start_time,
                "blocked": False,
                "error_message": str(e),
                "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3]
            }
        
        with self.lock:
            if tenant_id not in self.results:
                self.results[tenant_id] = []
            self.results[tenant_id].append(result)
        
        return result
    
    def test_tenant_thread_limit(self, tenant_id):
        """测试单个租户的线程限流"""
        print(f"\n=== 测试租户 {tenant_id} 的线程限流 ===")
        print(f"线程限制: {THREAD_LIMIT}, 并发请求数: {CONCURRENT_REQUESTS}, 请求持续时间: {REQUEST_DURATION}秒")
        
        # 使用线程池发送并发请求
        with ThreadPoolExecutor(max_workers=CONCURRENT_REQUESTS) as executor:
            futures = []
            
            # 同时提交所有请求
            for i in range(CONCURRENT_REQUESTS):
                future = executor.submit(self.send_request, tenant_id, i + 1)
                futures.append(future)
            
            # 等待所有请求完成
            for future in as_completed(futures):
                result = future.result()
                status = "成功" if result["success"] else ("限流" if result["blocked"] else "失败")
                print(f"  请求{result['request_id']}: {status} ({result['timestamp']})")
    
    def analyze_results(self):
        """分析测试结果"""
        print("\n" + "=" * 80)
        print("测试结果分析")
        print("=" * 80)
        
        total_success = 0
        total_blocked = 0
        total_failed = 0
        
        for tenant_id in TENANTS:
            if tenant_id not in self.results:
                continue
                
            results = self.results[tenant_id]
            success_count = sum(1 for r in results if r["success"])
            blocked_count = sum(1 for r in results if r["blocked"])
            failed_count = len(results) - success_count - blocked_count
            
            total_success += success_count
            total_blocked += blocked_count
            total_failed += failed_count
            
            print(f"\n租户 {tenant_id}:")
            print(f"  总请求数: {len(results)}")
            print(f"  成功请求: {success_count}")
            print(f"  被限流: {blocked_count}")
            print(f"  失败请求: {failed_count}")
            
            # 分析限流效果
            if success_count > THREAD_LIMIT:
                print(f"  ⚠️  警告: 成功请求数({success_count})超过线程限制({THREAD_LIMIT})")
            elif blocked_count > 0:
                print(f"  ✅ 限流正常: {blocked_count}个请求被正确限流")
            else:
                print(f"  ❓ 可能异常: 没有请求被限流，但成功请求数为{success_count}")
        
        print(f"\n总体统计:")
        print(f"  总请求数: {len(TENANTS) * CONCURRENT_REQUESTS}")
        print(f"  成功请求: {total_success}")
        print(f"  被限流: {total_blocked}")
        print(f"  失败请求: {total_failed}")
        
        # 判断限流是否正常工作
        expected_max_success = len(TENANTS) * THREAD_LIMIT
        if total_success <= expected_max_success and total_blocked > 0:
            print(f"\n✅ 线程限流功能正常工作")
        elif total_success > expected_max_success:
            print(f"\n❌ 线程限流功能异常: 成功请求数({total_success})超过预期最大值({expected_max_success})")
        else:
            print(f"\n❓ 线程限流状态不明确: 需要进一步检查")
    
    def run_test(self):
        """运行完整测试"""
        print("线程限流测试开始")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试URL: {BASE_URL}")
        print(f"测试租户: {', '.join(TENANTS)}")
        print(f"每租户线程限制: {THREAD_LIMIT}")
        print(f"每租户并发请求数: {CONCURRENT_REQUESTS}")
        print(f"请求持续时间: {REQUEST_DURATION}秒")
        
        # 测试每个租户
        for tenant_id in TENANTS:
            self.test_tenant_thread_limit(tenant_id)
            time.sleep(1)  # 租户间稍作间隔
        
        # 分析结果
        self.analyze_results()
        
        print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    tester = ThreadLimitTester()
    tester.run_test()

if __name__ == "__main__":
    main()