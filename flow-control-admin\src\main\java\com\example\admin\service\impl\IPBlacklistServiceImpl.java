package com.example.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.util.StringUtils;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import com.example.admin.dto.IPBlacklistDTO;
import com.example.common.entity.IPBlacklist;
import com.example.admin.mapper.IPBlacklistMapper;
import com.example.admin.service.IPBlacklistService;
import com.example.admin.vo.IPBlacklistVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.annotation.Resource;
import java.io.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * IP黑名单服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class IPBlacklistServiceImpl extends ServiceImpl<IPBlacklistMapper, IPBlacklist> implements IPBlacklistService {

    private static final Logger log = LoggerFactory.getLogger(IPBlacklistServiceImpl.class);

    @Resource
    private IPBlacklistMapper ipBlacklistMapper;

    @Override
    public Page<IPBlacklistVO> selectIPBlacklistPage(Page<IPBlacklistVO> page, String tenantId, 
                                                        String listName, String ipType, Integer enabled) {
        // 创建实体分页对象
        Page<IPBlacklist> entityPage = new Page<>(page.getCurrent(), page.getSize());
        
        // 查询实体分页数据
        List<IPBlacklist> allLists = ipBlacklistMapper.selectByTenantIdAndEnabled(tenantId, enabled);
        
        // 根据listName过滤
        if (StringUtils.hasText(listName)) {
            allLists = allLists.stream()
                .filter(list -> list.getListName().contains(listName))
                .collect(Collectors.toList());
        }

        // 根据ipType过滤
        if (StringUtils.hasText(ipType)) {
            allLists = allLists.stream()
                .filter(list -> list.getIpType().equals(ipType))
                .collect(Collectors.toList());
        }
        
        // 手动分页
        long total = allLists.size();
        int start = (int) ((entityPage.getCurrent() - 1) * entityPage.getSize());
        int end = Math.min(start + (int) entityPage.getSize(), (int) total);
        List<IPBlacklist> pageData = start < total ? allLists.subList(start, end) : new ArrayList<>();
        
        // 创建分页结果
        IPage<IPBlacklist> entityResult = new Page<>(entityPage.getCurrent(), entityPage.getSize(), total);
        entityResult.setRecords(pageData);
        
        // 转换为VO分页对象
        Page<IPBlacklistVO> voPage = new Page<>(entityResult.getCurrent(), entityResult.getSize(), entityResult.getTotal());
        List<IPBlacklistVO> voList = entityResult.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public IPBlacklistVO getIPBlacklistById(Long id) {
        IPBlacklist ipList = this.getById(id);
        if (ipList == null) {
            return null;
        }
        return convertToVO(ipList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createIPBlacklist(IPBlacklistDTO ipListDTO) {
        // 验证IP黑名单配置
        Map<String, Object> validationResult = validateIPBlacklist(ipListDTO);
        if (!(Boolean) validationResult.get("valid")) {
            throw new RuntimeException((String) validationResult.get("message"));
        }

        // 检查名单名称是否已存在
        if (existsByListName(ipListDTO.getTenantId(), ipListDTO.getListName(), null)) {
            throw new RuntimeException("名单名称在该租户下已存在");
        }

        IPBlacklist ipList = new IPBlacklist();
        BeanUtils.copyProperties(ipListDTO, ipList);
        ipList.setCreateTime(LocalDateTime.now());
        ipList.setUpdateTime(LocalDateTime.now());
        ipList.setDeleted(0);

        boolean result = this.save(ipList);

        if (result) {
            log.info("创建IP黑名单成功: tenantId={}, listName={}", 
                ipListDTO.getTenantId(), ipListDTO.getListName());
            publishIPBlacklists(ipListDTO.getTenantId());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateIPBlacklist(Long id, IPBlacklistDTO ipListDTO) {
        IPBlacklist existingList = this.getById(id);
        if (existingList == null) {
            throw new RuntimeException("IP黑名单不存在");
        }

        // 验证IP黑名单配置
        Map<String, Object> validationResult = validateIPBlacklist(ipListDTO);
        if (!(Boolean) validationResult.get("valid")) {
            throw new RuntimeException((String) validationResult.get("message"));
        }

        // 检查名单名称是否已存在（排除当前名单）
        if (existsByListName(ipListDTO.getTenantId(), ipListDTO.getListName(), id)) {
            throw new RuntimeException("名单名称在该租户下已存在");
        }

        BeanUtils.copyProperties(ipListDTO, existingList);
        existingList.setId(id);
        existingList.setUpdateTime(LocalDateTime.now());

        boolean result = this.updateById(existingList);

        if (result) {
            log.info("更新IP黑名单成功: id={}, tenantId={}, listName={}", 
                id, ipListDTO.getTenantId(), ipListDTO.getListName());
            publishIPBlacklists(ipListDTO.getTenantId());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIPBlacklist(Long id) {
        IPBlacklist ipList = this.getById(id);
        if (ipList == null) {
            return false;
        }

        boolean result = this.removeById(id);

        if (result) {
            log.info("删除IP黑名单成功: id={}, tenantId={}", id, ipList.getTenantId());
            publishIPBlacklists(ipList.getTenantId());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteIPBlacklists(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }

        // 获取要删除的名单信息，用于后续发布
        List<IPBlacklist> lists = this.listByIds(ids);
        Set<String> tenantIds = lists.stream().map(IPBlacklist::getTenantId).collect(Collectors.toSet());

        boolean result = this.removeByIds(ids);

        if (result) {
            log.info("批量删除IP黑名单成功: ids={}", ids);
            tenantIds.forEach(this::publishIPBlacklists);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableIPBlacklist(Long id) {
        return updateListEnabled(id, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableIPBlacklist(Long id) {
        return updateListEnabled(id, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateEnabled(List<Long> ids, Integer enabled) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }

        // 获取名单信息，用于后续发布
        List<IPBlacklist> lists = this.listByIds(ids);
        Set<String> tenantIds = lists.stream().map(IPBlacklist::getTenantId).collect(Collectors.toSet());

        boolean result = ipBlacklistMapper.batchUpdateEnabled(ids, enabled) > 0;

        if (result) {
            log.info("批量更新IP黑名单状态成功: ids={}, enabled={}", ids, enabled);
            tenantIds.forEach(this::publishIPBlacklists);
        }

        return result;
    }

    @Override
    public List<IPBlacklistVO> getIPBlacklistsByTenantId(String tenantId, Integer enabled, Integer limit) {
        List<IPBlacklist> lists = ipBlacklistMapper.selectByTenantIdAndEnabled(tenantId, enabled);
        if (limit != null && limit > 0 && lists.size() > limit) {
            lists = lists.subList(0, limit);
        }
        return lists.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public boolean existsByListName(String tenantId, String listName, Long excludeId) {
        return ipBlacklistMapper.countByTenantIdAndListName(tenantId) > 0;
    }

    @Override
    public int countByTenantId(String tenantId, Integer enabled) {
        List<IPBlacklist> lists = ipBlacklistMapper.selectByTenantIdAndEnabled(tenantId, enabled);
        return lists.size();
    }

    @Override
    public List<IPBlacklistVO> getEnabledBlacklists(String tenantId, Integer limit) {
        List<IPBlacklist> lists = ipBlacklistMapper.selectByTenantIdAndEnabled(tenantId, 1);
        if (limit != null && limit > 0 && lists.size() > limit) {
            lists = lists.subList(0, limit);
        }
        return lists.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<IPBlacklistVO> getBlacklistsByPriorityOrder(String tenantId, Integer enabled, Integer limit) {
        List<IPBlacklist> lists = ipBlacklistMapper.selectByTenantIdAndEnabled(tenantId, enabled);
        if (limit != null && limit > 0 && lists.size() > limit) {
            lists = lists.subList(0, limit);
        }
        return lists.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getEnabledStatistics(String tenantId) {
        return ipBlacklistMapper.getIPListStats(tenantId);
    }

    public List<Map<String, Object>> getIPTypeStatistics(String tenantId) {
        return ipBlacklistMapper.getIPListStats(tenantId);
    }

    public List<Map<String, Object>> getTenantIPStatistics(Integer limit) {
        List<Map<String, Object>> stats = ipBlacklistMapper.getAllIPListStats();
        if (limit != null && limit > 0 && stats.size() > limit) {
            stats = stats.subList(0, limit);
        }
        return stats;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateIPBlacklists(List<IPBlacklistDTO> ipListDTOList) {
        if (CollectionUtils.isEmpty(ipListDTOList)) {
            return false;
        }

        List<IPBlacklist> lists = new ArrayList<>();
        Set<String> tenantIds = new HashSet<>();
        LocalDateTime now = LocalDateTime.now();

        for (IPBlacklistDTO dto : ipListDTOList) {
            // 验证每个名单
            Map<String, Object> validationResult = validateIPBlacklist(dto);
            if (!(Boolean) validationResult.get("valid")) {
                throw new RuntimeException("名单验证失败: " + validationResult.get("message"));
            }

            IPBlacklist list = new IPBlacklist();
            BeanUtils.copyProperties(dto, list);
            list.setCreateTime(now);
            list.setUpdateTime(now);
            list.setDeleted(0);
            lists.add(list);
            tenantIds.add(dto.getTenantId());
        }

        boolean result = this.saveBatch(lists);

        if (result) {
            log.info("批量创建IP黑名单成功: count={}", lists.size());
            tenantIds.forEach(this::publishIPBlacklists);
        }

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean copyIPBlacklist(Long id, String newListName, String targetTenantId) {
        IPBlacklist sourceList = this.getById(id);
        if (sourceList == null) {
            throw new RuntimeException("源名单不存在");
        }

        String finalTenantId = StringUtils.hasText(targetTenantId) ? targetTenantId : sourceList.getTenantId();

        // 检查新名单名称是否已存在
        if (existsByListName(finalTenantId, newListName, null)) {
            throw new RuntimeException("名单名称在该租户下已存在");
        }

        IPBlacklist newList = new IPBlacklist();
        BeanUtils.copyProperties(sourceList, newList);
        newList.setId(null);
        newList.setListName(newListName);
        newList.setTenantId(finalTenantId);
        newList.setCreateTime(LocalDateTime.now());
        newList.setUpdateTime(LocalDateTime.now());
        newList.setDeleted(0);

        boolean result = this.save(newList);

        if (result) {
            log.info("复制IP黑名单成功: sourceId={}, newListName={}, targetTenantId={}", id, newListName, finalTenantId);
            publishIPBlacklists(finalTenantId);
        }

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importIPBlacklists(List<IPBlacklistDTO> ipListDTOList, boolean overwrite) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();
        Set<String> tenantIds = new HashSet<>();

        for (IPBlacklistDTO dto : ipListDTOList) {
            try {
                // 验证名单
                Map<String, Object> validationResult = validateIPBlacklist(dto);
                if (!(Boolean) validationResult.get("valid")) {
                    errors.add("名单 " + dto.getListName() + " 验证失败: " + validationResult.get("message"));
                    failCount++;
                    continue;
                }

                boolean exists = existsByListName(dto.getTenantId(), dto.getListName(), null);
                if (exists && !overwrite) {
                    errors.add("名单 " + dto.getListName() + " 已存在，跳过导入");
                    failCount++;
                    continue;
                }

                if (exists && overwrite) {
                    // 更新现有名单
                    IPBlacklist existingList = ipBlacklistMapper.selectByTenantId(dto.getTenantId()).stream()
                        .filter(list -> dto.getListName().equals(list.getListName()))
                        .findFirst().orElse(null);
                    if (existingList != null && updateIPBlacklist(existingList.getId(), dto)) {
                        successCount++;
                        tenantIds.add(dto.getTenantId());
                    } else {
                        failCount++;
                        errors.add("更新名单 " + dto.getListName() + " 失败");
                    }
                } else {
                    // 创建新名单
                    if (createIPBlacklist(dto)) {
                        successCount++;
                        tenantIds.add(dto.getTenantId());
                    } else {
                        failCount++;
                        errors.add("创建名单 " + dto.getListName() + " 失败");
                    }
                }
            } catch (Exception e) {
                failCount++;
                errors.add("处理名单 " + dto.getListName() + " 时发生异常: " + e.getMessage());
                log.error("导入IP黑名单异常", e);
            }
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        result.put("totalCount", ipListDTOList.size());

        log.info("导入IP黑名单完成: 成功={}, 失败={}, 总数={}", successCount, failCount, ipListDTOList.size());

        return result;
    }

    public List<IPBlacklistDTO> exportIPBlacklists(String tenantId, Integer enabled) {
        List<IPBlacklist> lists = ipBlacklistMapper.selectByTenantIdAndEnabled(tenantId, enabled);
        return lists.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public Map<String, Object> validateIPBlacklist(IPBlacklistDTO ipListDTO) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();

        // 基本字段验证
        if (!StringUtils.hasText(ipListDTO.getTenantId())) {
            errors.add("租户ID不能为空");
        }
        if (!StringUtils.hasText(ipListDTO.getListName())) {
            errors.add("名单名称不能为空");
        }
        if (!StringUtils.hasText(ipListDTO.getIpType()) || 
            (!"SINGLE".equals(ipListDTO.getIpType()) && !"RANGE".equals(ipListDTO.getIpType()) && !"CIDR".equals(ipListDTO.getIpType()))) {
            errors.add("IP类型必须为SINGLE、RANGE或CIDR");
        }

        // IP配置验证
        if ("SINGLE".equals(ipListDTO.getIpType())) {
            if (!StringUtils.hasText(ipListDTO.getSingleIp())) {
                errors.add("单个IP地址不能为空");
            }
        } else if ("RANGE".equals(ipListDTO.getIpType())) {
            if (!StringUtils.hasText(ipListDTO.getStartIp()) || !StringUtils.hasText(ipListDTO.getEndIp())) {
                errors.add("IP范围起始和结束地址不能为空");
            }
        } else if ("CIDR".equals(ipListDTO.getIpType())) {
            if (!StringUtils.hasText(ipListDTO.getCidrIp())) {
                errors.add("CIDR格式IP不能为空");
            }
        }

        // 时间范围验证
        if (ipListDTO.getStartTime() != null && ipListDTO.getEndTime() != null &&
            ipListDTO.getStartTime().isAfter(ipListDTO.getEndTime())) {
            errors.add("开始时间不能晚于结束时间");
        }

        result.put("valid", errors.isEmpty());
        result.put("message", errors.isEmpty() ? "验证通过" : String.join(", ", errors));
        result.put("errors", errors);

        return result;
    }

    @Override
    public Integer getMaxPriority(String tenantId) {
        return ipBlacklistMapper.getMaxPriorityByTenantId(tenantId);
    }

    @Override
    public List<IPBlacklistVO> getBlacklistsByPriorityRange(String tenantId, Integer minPriority, Integer maxPriority, 
                                                               Integer enabled) {
        List<IPBlacklist> lists = ipBlacklistMapper.selectByPriorityRange(tenantId);
        return lists.stream()
            .filter(list -> list.getEnabled() == enabled || enabled == null)
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    public List<IPBlacklistVO> getExpiringBlacklists(String tenantId, Integer hours) {
        List<IPBlacklist> lists = ipBlacklistMapper.selectExpiringRules(tenantId);
        return lists.stream()
            .filter(list -> list.getEndTime() != null && list.getEndTime().isBefore(LocalDateTime.now().plusHours(hours)))
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int disableExpiredBlacklists() {
        return ipBlacklistMapper.disableExpiredRules();
    }

    @Override
    public List<IPBlacklistVO> getValidBlacklists(String tenantId) {
        List<IPBlacklist> lists = ipBlacklistMapper.selectByTenantIdAndEnabled(tenantId, 1);
        return lists.stream()
            .filter(list -> list.isCurrentlyValid())
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getTenantIPSummary(String tenantId) {
        Map<String, Object> summary = new HashMap<>();
        
        int totalCount = countByTenantId(tenantId, null);
        int enabledCount = countByTenantId(tenantId, 1);
        
        summary.put("totalCount", totalCount);
        summary.put("enabledCount", enabledCount);
        summary.put("disabledCount", totalCount - enabledCount);
        
        return summary;
    }

    @Override
    public Map<String, Object> getGlobalIPSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        List<IPBlacklist> allLists = this.list();
        long totalCount = allLists.size();
        long enabledCount = allLists.stream().filter(list -> list.getEnabled() == 1).count();
        
        summary.put("totalCount", totalCount);
        summary.put("enabledCount", enabledCount);
        summary.put("disabledCount", totalCount - enabledCount);
        
        return summary;
    }

    @Override
    public Map<String, Object> checkIPMatch(String ipAddress, String tenantId) {
        Map<String, Object> result = new HashMap<>();
        result.put("matched", false);
        result.put("matchedLists", new ArrayList<>());
        
        List<IPBlacklist> enabledLists = ipBlacklistMapper.selectByTenantIdAndEnabled(tenantId, 1);
        List<Map<String, Object>> matchedLists = new ArrayList<>();
        
        for (IPBlacklist list : enabledLists) {
            if (list.matchesIP(ipAddress)) {
                Map<String, Object> matchInfo = new HashMap<>();
                matchInfo.put("listId", list.getId());
                matchInfo.put("listName", list.getListName());
                matchInfo.put("priority", list.getPriority());
                matchedLists.add(matchInfo);
                log.debug("IP黑名单匹配成功: ip={}, listName={}", ipAddress, list.getListName());
            }
        }
        
        result.put("matched", !matchedLists.isEmpty());
        result.put("matchedLists", matchedLists);
        return result;
    }

    @Override
    public List<Map<String, Object>> batchCheckIPMatch(String tenantId, List<String> ipAddresses) {
        List<Map<String, Object>> results = new ArrayList<>();
        for (String ip : ipAddresses) {
            Map<String, Object> result = checkIPMatch(ip, tenantId);
            result.put("ipAddress", ip);
            results.add(result);
        }
        return results;
    }

    @Override
    public Map<String, Object> importIPsFromFile(String tenantId, String listName, String fileContent, String fileType) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        try (BufferedReader reader = new BufferedReader(new StringReader(fileContent))) {
            String line;
            int lineNumber = 0;
            
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                line = line.trim();
                
                if (line.isEmpty() || line.startsWith("#")) {
                    continue; // 跳过空行和注释行
                }

                try {
                    IPBlacklistDTO dto = new IPBlacklistDTO();
                    dto.setTenantId(tenantId);
                    dto.setListName(listName + "_" + lineNumber);
                    dto.setEnabled(1);
                    dto.setDescription("从文件导入");

                    if (line.contains("-")) {
                        String[] parts = line.split("-");
                        if (parts.length == 2 && StringUtils.hasText(parts[0].trim()) && StringUtils.hasText(parts[1].trim())) {
                            dto.setIpType("RANGE");
                            dto.setStartIp(parts[0].trim());
                            dto.setEndIp(parts[1].trim());
                        } else {
                            errors.add("第" + lineNumber + "行: 无效的IP范围格式 - " + line);
                            failCount++;
                            continue;
                        }
                    } else if (line.contains("/")) {
                        dto.setIpType("CIDR");
                        dto.setCidrIp(line);
                    } else {
                        dto.setIpType("SINGLE");
                        dto.setSingleIp(line);
                    }

                    if (createIPBlacklist(dto)) {
                        successCount++;
                    } else {
                        failCount++;
                        errors.add("第" + lineNumber + "行: 创建IP黑名单失败 - " + line);
                    }
                } catch (Exception e) {
                    failCount++;
                    errors.add("第" + lineNumber + "行: 处理异常 - " + e.getMessage());
                }
            }
        } catch (IOException e) {
            errors.add("读取文件失败: " + e.getMessage());
            log.error("读取IP文件失败", e);
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        result.put("totalCount", (long)(successCount + failCount));

        return result;
    }

    @Override
    public Map<String, Object> importIPsFromFile(MultipartFile file, String tenantId, String listName, 
                                         String ipType, Integer priority) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            String line;
            int lineNumber = 0;
            
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                line = line.trim();
                
                if (line.isEmpty() || line.startsWith("#")) {
                    continue; // 跳过空行和注释行
                }

                try {
                    IPBlacklistDTO dto = new IPBlacklistDTO();
                    dto.setTenantId(tenantId);
                    dto.setListName(listName + "_" + lineNumber);
                    dto.setIpType(ipType);
                    dto.setPriority(priority);
                    dto.setEnabled(1);
                    dto.setDescription("从文件导入: " + file.getOriginalFilename());

                    // 根据IP类型设置相应字段
                    if ("SINGLE".equals(ipType)) {
                        dto.setSingleIp(line);
                    } else if ("CIDR".equals(ipType)) {
                        dto.setCidrIp(line);
                    } else if ("RANGE".equals(ipType)) {
                        String[] parts = line.split("-");
                        if (parts.length == 2 && StringUtils.hasText(parts[0].trim()) && StringUtils.hasText(parts[1].trim())) {
                            dto.setStartIp(parts[0].trim());
                            dto.setEndIp(parts[1].trim());
                        } else {
                            errors.add("第" + lineNumber + "行: 无效的IP范围格式 - " + line);
                            failCount++;
                            continue;
                        }
                    }

                    if (createIPBlacklist(dto)) {
                        successCount++;
                    } else {
                        failCount++;
                        errors.add("第" + lineNumber + "行: 创建IP黑名单失败 - " + line);
                    }
                } catch (Exception e) {
                    failCount++;
                    errors.add("第" + lineNumber + "行: 处理异常 - " + e.getMessage());
                }
            }
        } catch (IOException e) {
            errors.add("读取文件失败: " + e.getMessage());
            log.error("读取IP文件失败", e);
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        result.put("totalCount", successCount + failCount);

        return result;
    }

    @Override
    public String exportIPsToFile(String tenantId, Integer enabled, String fileType) {
        List<IPBlacklist> lists = ipBlacklistMapper.selectByTenantIdAndEnabled(tenantId, enabled);
        
        StringBuilder content = new StringBuilder();
        content.append("# IP黑名单导出文件\n");
        content.append("# 导出时间: ").append(LocalDateTime.now()).append("\n");
        content.append("# 租户ID: ").append(tenantId).append("\n");
        content.append("# 名单类型: 黑名单\n\n");

        for (IPBlacklist list : lists) {
            content.append("# 名单: ").append(list.getListName()).append("\n");
            
            if ("SINGLE".equals(list.getIpType()) && StringUtils.hasText(list.getIpAddress())) {
                content.append(list.getIpAddress()).append("\n");
            } else if ("RANGE".equals(list.getIpType()) && 
                      StringUtils.hasText(list.getIpStart()) && StringUtils.hasText(list.getIpEnd())) {
                content.append(list.getIpStart()).append("-").append(list.getIpEnd()).append("\n");
            } else if ("CIDR".equals(list.getIpType()) && StringUtils.hasText(list.getIpCidr())) {
                content.append(list.getIpCidr()).append("\n");
            }
        }

        return content.toString();
    }

    // 私有辅助方法

    /**
     * 更新名单启用状态
     */
    private boolean updateListEnabled(Long id, Integer enabled) {
        IPBlacklist list = this.getById(id);
        if (list == null) {
            return false;
        }

        list.setEnabled(enabled);
        list.setUpdateTime(LocalDateTime.now());
        boolean result = this.updateById(list);

        if (result) {
            log.info("更新IP黑名单状态成功: id={}, enabled={}", id, enabled);
            publishIPBlacklists(list.getTenantId());
        }

        return result;
    }

    /**
     * 发布IP黑名单到网关
     */
    private void publishIPBlacklists(String tenantId) {
        try {
            // 这里实现名单发布到网关的逻辑
            log.info("发布IP黑名单到网关: tenantId={}", tenantId);
        } catch (Exception e) {
            log.error("发布IP黑名单到网关失败: tenantId={}", tenantId, e);
        }
    }

    /**
     * 转换为VO对象
     */
    private IPBlacklistVO convertToVO(IPBlacklist ipBlacklist) {
        IPBlacklistVO vo = new IPBlacklistVO();
        BeanUtils.copyProperties(ipBlacklist, vo);
        
        // 设置计算字段
        vo.setIsValid(ipBlacklist.isCurrentlyValid());
        vo.setIsExpiring(ipBlacklist.isExpiringWithin(24));
        vo.setRemainingHours(ipBlacklist.calculateRemainingHours());
        vo.setIpCount(Long.valueOf(ipBlacklist.calculateIpCount()));
        
        return vo;
    }

    /**
     * 转换为DTO对象
     */
    private IPBlacklistDTO convertToDTO(IPBlacklist ipBlacklist) {
        IPBlacklistDTO dto = new IPBlacklistDTO();
        BeanUtils.copyProperties(ipBlacklist, dto);
        return dto;
    }

    @Override
    public Map<String, Object> batchCopyToTenant(List<Long> sourceIds, String targetTenantId, String namePrefix) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;
        
        try {
            List<IPBlacklist> sourceBlacklists = ipBlacklistMapper.selectBatchIds(sourceIds);
            List<IPBlacklist> targetBlacklists = new ArrayList<>();
            
            for (IPBlacklist source : sourceBlacklists) {
                try {
                    IPBlacklist target = new IPBlacklist();
                    BeanUtils.copyProperties(source, target);
                    target.setId(null); // 清空ID，让数据库自动生成
                    target.setTenantId(targetTenantId);
                    target.setListName(namePrefix + "_" + source.getListName());
                    target.setCreateTime(LocalDateTime.now());
                    target.setUpdateTime(LocalDateTime.now());
                    targetBlacklists.add(target);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errors.add("复制黑名单失败: " + source.getListName() + " - " + e.getMessage());
                }
            }
            
            if (!targetBlacklists.isEmpty()) {
                boolean batchResult = this.saveBatch(targetBlacklists);
                if (batchResult) {
                    log.info("批量复制IP黑名单成功: targetTenantId={}, count={}", 
                            targetTenantId, targetBlacklists.size());
                    publishIPBlacklists(targetTenantId);
                } else {
                    errors.add("批量保存失败");
                }
            }
        } catch (Exception e) {
            log.error("批量复制IP黑名单失败: targetTenantId={}", targetTenantId, e);
            errors.add("批量复制异常: " + e.getMessage());
        }
        
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        result.put("totalCount", successCount + failCount);
        
        return result;
    }
    
}