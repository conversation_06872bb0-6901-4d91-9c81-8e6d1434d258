package com.example.admin.vo;


import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 仪表盘数据VO
 */

public class DashboardVO {
    
    /**
     * 系统概览数据
     */
    private SystemOverview systemOverview;
    
    /**
     * 租户概览数据
     */
    private List<TenantOverview> tenantOverviews;
    
    /**
     * 实时监控数据
     */
    private RealtimeMonitor realtimeMonitor;
    
    /**
     * 告警信息
     */
    private List<AlarmInfo> alarmInfos;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 系统概览数据
     */
    public static class SystemOverview {
        /**
         * 总租户数
         */
        private Integer totalTenants;
        
        /**
         * 活跃租户数
         */
        private Integer activeTenants;
        
        /**
         * 总规则数
         */
        private Integer totalRules;
        
        /**
         * 启用规则数
         */
        private Integer enabledRules;
        
        /**
         * 总IP规则数
         */
        private Integer totalIpRules;
        
        /**
         * 启用IP规则数
         */
        private Integer enabledIpRules;
        
        /**
         * 系统健康状态：HEALTHY-健康，WARNING-警告，ERROR-错误
         */
        private String healthStatus;
        
        /**
         * 系统健康状态名称
         */
        private String healthStatusName;
        
        /**
         * 今日总请求数
         */
        private Long todayTotalRequests;
        
        /**
         * 今日阻塞请求数
         */
        private Long todayBlockRequests;
        
        /**
         * 今日通过率
         */
        private Double todayPassRate;
        
        /**
         * 平均响应时间
         */
        private Double avgResponseTime;
    }
    
    /**
     * 租户概览数据
     */
    public static class TenantOverview {
        /**
         * 租户ID
         */
        private String tenantId;
        
        /**
         * 租户名称
         */
        private String tenantName;
        
        /**
         * 规则数量
         */
        private Integer ruleCount;
        
        /**
         * IP规则数量
         */
        private Integer ipRuleCount;
        
        /**
         * 今日请求数
         */
        private Long todayRequests;
        
        /**
         * 今日阻塞数
         */
        private Long todayBlocks;
        
        /**
         * 通过率
         */
        private Double passRate;
        
        /**
         * 状态：0-禁用，1-启用
         */
        private Integer status;
        
        /**
         * 状态名称
         */
        private String statusName;
        
        /**
         * 告警数量
         */
        private Integer alarmCount;
    }
    
    /**
     * 实时监控数据
     */
    public static class RealtimeMonitor {
        /**
         * 当前QPS
         */
        private Double currentQps;
        
        /**
         * 峰值QPS
         */
        private Double peakQps;
        
        /**
         * 当前响应时间
         */
        private Double currentRt;
        
        /**
         * 当前通过率
         */
        private Double currentPassRate;
        
        /**
         * 当前阻塞率
         */
        private Double currentBlockRate;
        
        /**
         * QPS趋势数据（最近30分钟）
         */
        private List<TrendPoint> qpsTrend;
        
        /**
         * 响应时间趋势数据（最近30分钟）
         */
        private List<TrendPoint> rtTrend;
        
        /**
         * 通过率趋势数据（最近30分钟）
         */
        private List<TrendPoint> passRateTrend;
        
        /**
         * 资源排行榜（按QPS）
         */
        private List<ResourceRank> resourceRanks;
    }
    
    /**
     * 趋势数据点
     */
    public static class TrendPoint {
        /**
         * 时间戳
         */
        private LocalDateTime timestamp;
        
        /**
         * 数值
         */
        private Double value;
    }
    
    /**
     * 资源排行
     */
    public static class ResourceRank {
        /**
         * 资源名称
         */
        private String resourceName;
        
        /**
         * QPS值
         */
        private Double qps;
        
        /**
         * 响应时间
         */
        private Double rt;
        
        /**
         * 通过率
         */
        private Double passRate;
        
        /**
         * 租户名称
         */
        private String tenantName;
    }
    
    /**
     * 告警信息
     */
    public static class AlarmInfo {
        /**
         * 告警ID
         */
        private String alarmId;
        
        /**
         * 告警类型：QPS_HIGH-QPS过高，RT_HIGH-响应时间过高，BLOCK_RATE_HIGH-阻塞率过高
         */
        private String alarmType;
        
        /**
         * 告警类型名称
         */
        private String alarmTypeName;
        
        /**
         * 告警级别：INFO-信息，WARNING-警告，ERROR-错误，CRITICAL-严重
         */
        private String alarmLevel;
        
        /**
         * 告警级别名称
         */
        private String alarmLevelName;
        
        /**
         * 告警内容
         */
        private String alarmContent;
        
        /**
         * 资源名称
         */
        private String resourceName;
        
        /**
         * 租户名称
         */
        private String tenantName;
        
        /**
         * 告警时间
         */
        private LocalDateTime alarmTime;
        
        /**
         * 是否已处理
         */
        private Boolean handled;
    }
    
    /**
     * 统计数据汇总
     */
    public static class StatisticsSummary {
        /**
         * 按小时统计的数据
         */
        private Map<String, Long> hourlyStats;
        
        /**
         * 按天统计的数据
         */
        private Map<String, Long> dailyStats;
        
        /**
         * 按租户统计的数据
         */
        private Map<String, Long> tenantStats;
        
        /**
         * 按资源统计的数据
         */
        private Map<String, Long> resourceStats;
    }
}