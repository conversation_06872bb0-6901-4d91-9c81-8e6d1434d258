import java.sql.*;

public class UpdateTenantRule {
    private static final String URL = "*********************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "123456";
    
    public static void main(String[] args) {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            
            System.out.println("=== 更新租户111的限流规则为线程数限流 ===");
            
            // 更新租户111的规则，将grade从1（QPS）改为0（线程数）
            String updateSql = "UPDATE tenant_flow_rules SET grade = 0, rule_name = '租户111线程数限流' WHERE tenant_id = '111'";
            PreparedStatement updateStmt = conn.prepareStatement(updateSql);
            int rowsUpdated = updateStmt.executeUpdate();
            
            System.out.println("更新了 " + rowsUpdated + " 条记录");
            
            // 查询更新后的结果
            String selectSql = "SELECT rule_name, tenant_id, count, grade FROM tenant_flow_rules WHERE tenant_id = '111'";
            PreparedStatement selectStmt = conn.prepareStatement(selectSql);
            ResultSet rs = selectStmt.executeQuery();
            
            System.out.println("\n=== 更新后的规则 ===");
            System.out.println("RuleName\t\tTenantID\tCount\tGrade");
            System.out.println("================================================");
            
            while (rs.next()) {
                System.out.printf("%-20s\t%s\t%.1f\t%d\n", 
                    rs.getString("rule_name"),
                    rs.getString("tenant_id"),
                    rs.getDouble("count"),
                    rs.getInt("grade")
                );
            }
            
            rs.close();
            selectStmt.close();
            updateStmt.close();
            conn.close();
            
            System.out.println("\n规则更新完成！grade=0表示线程数限流，count=1.0表示最多1个并发线程");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}