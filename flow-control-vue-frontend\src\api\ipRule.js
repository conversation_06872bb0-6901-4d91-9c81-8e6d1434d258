import request from '@/utils/request'

// IP限流规则API
export const ipRuleApi = {
	// 获取IP规则列表
	getIpRuleList (params) {
		return request({
			url: '/api/ip-flow-rules',
			method: 'get',
			params
		})
	},

	// 创建IP规则
	createIpRule (data) {
		return request({
			url: '/api/ip-flow-rules',
			method: 'post',
			data
		})
	},

	// 更新IP规则
	updateIpRule (id, data) {
		return request({
			url: `/api/ip-flow-rules/${id}`,
			method: 'put',
			data
		})
	},

	// 删除IP规则
	deleteIpRule (id) {
		return request({
			url: `/api/ip-flow-rules/${id}`,
			method: 'delete'
		})
	},

	// 更新IP规则状态
	updateIpRuleStatus (id, status) {
		return request({
			url: `/api/ip-flow-rules/${id}/status`,
			method: 'put',
			data: { status }
		})
	}
}