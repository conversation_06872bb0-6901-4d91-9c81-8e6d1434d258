#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
过滤器测试脚本
用于验证MultiDimensionFlowFilter是否被正确调用
"""

import requests
import time
import sys

def test_filter_execution():
    """
    测试过滤器是否被执行
    """
    print("=== 测试MultiDimensionFlowFilter是否被调用 ===")
    
    # 测试URL
    url = "http://localhost:8088/api/test"
    
    # 发送一个简单的请求
    headers = {
        "X-Tenant-ID": "tenant1",
        "User-Agent": "FilterTest/1.0"
    }
    
    try:
        print(f"发送请求到: {url}")
        print(f"请求头: {headers}")
        
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        # 检查是否有过滤器相关的响应头
        if 'X-Request-Id' in response.headers:
            print("✓ 发现X-Request-Id响应头，过滤器可能被调用")
        else:
            print("✗ 未发现X-Request-Id响应头，过滤器可能未被调用")
            
        return response.status_code == 200
        
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return False

def test_multiple_requests():
    """
    测试多个请求，看是否有限流效果
    """
    print("\n=== 测试多个请求的限流效果 ===")
    
    url = "http://localhost:8088/api/test?sleep=1"
    headers = {"X-Tenant-ID": "tenant1"}
    
    success_count = 0
    blocked_count = 0
    
    # 快速发送10个请求
    for i in range(10):
        try:
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                success_count += 1
                print(f"请求 {i+1}: 成功 (200)")
            elif response.status_code == 429:
                blocked_count += 1
                print(f"请求 {i+1}: 被限流 (429)")
            else:
                print(f"请求 {i+1}: 其他状态码 ({response.status_code})")
        except Exception as e:
            print(f"请求 {i+1}: 异常 - {e}")
        
        # 短暂延迟
        time.sleep(0.1)
    
    print(f"\n结果统计:")
    print(f"成功请求: {success_count}")
    print(f"被限流请求: {blocked_count}")
    print(f"其他: {10 - success_count - blocked_count}")
    
    if blocked_count > 0:
        print("✓ 检测到限流效果，过滤器正在工作")
        return True
    else:
        print("✗ 未检测到限流效果，过滤器可能未工作")
        return False

def main():
    print("MultiDimensionFlowFilter 测试工具")
    print("=" * 50)
    
    # 测试1: 基本请求测试
    basic_test_passed = test_filter_execution()
    
    # 测试2: 限流效果测试
    limit_test_passed = test_multiple_requests()
    
    print("\n=== 测试总结 ===")
    print(f"基本请求测试: {'通过' if basic_test_passed else '失败'}")
    print(f"限流效果测试: {'通过' if limit_test_passed else '失败'}")
    
    if not basic_test_passed:
        print("\n建议检查:")
        print("1. 网关服务是否正常运行在8088端口")
        print("2. MultiDimensionFlowFilter是否被正确加载")
        print("3. 查看网关服务日志中是否有过滤器的调试信息")
    
    if basic_test_passed and not limit_test_passed:
        print("\n建议检查:")
        print("1. Sentinel规则是否正确加载")
        print("2. 限流配置是否正确")
        print("3. 过滤器中的限流逻辑是否正确执行")

if __name__ == "__main__":
    main()