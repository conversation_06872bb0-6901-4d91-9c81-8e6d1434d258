package com.example.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 监控统计实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@TableName("monitor_statistics")
public class MonitorStatistics {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 统计时间
     */
    @TableField("stat_time")
    private LocalDateTime statTime;
    
    /**
     * 资源名称
     */
    @TableField("resource_name")
    private String resourceName;
    
    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
    
    /**
     * 总请求数
     */
    @TableField("total_requests")
    private Long totalRequests;
    
    /**
     * 通过请求数
     */
    @TableField("pass_requests")
    private Long passRequests;
    
    /**
     * 阻塞请求数
     */
    @TableField("block_requests")
    private Long blockRequests;
    
    /**
     * 排队请求数
     */
    @TableField("queue_requests")
    private Long queueRequests;
    
    /**
     * 总响应时间
     */
    @TableField("total_rt")
    private Long totalRt;
    
    /**
     * 平均响应时间
     */
    @TableField("avg_rt")
    private BigDecimal avgRt;
    
    /**
     * 最大响应时间
     */
    @TableField("max_rt")
    private Integer maxRt;
    
    /**
     * 最小响应时间
     */
    @TableField("min_rt")
    private Integer minRt;
    
    /**
     * 统计类型：HOURLY-小时统计，DAILY-日统计
     */
    @TableField("stat_type")
    private String statType;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getStatTime() {
        return statTime;
    }

    public void setStatTime(LocalDateTime statTime) {
        this.statTime = statTime;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getTotalRequests() {
        return totalRequests;
    }

    public void setTotalRequests(Long totalRequests) {
        this.totalRequests = totalRequests;
    }

    public Long getPassRequests() {
        return passRequests;
    }

    public void setPassRequests(Long passRequests) {
        this.passRequests = passRequests;
    }

    public Long getBlockRequests() {
        return blockRequests;
    }

    public void setBlockRequests(Long blockRequests) {
        this.blockRequests = blockRequests;
    }

    public Long getQueueRequests() {
        return queueRequests;
    }

    public void setQueueRequests(Long queueRequests) {
        this.queueRequests = queueRequests;
    }

    public Long getTotalRt() {
        return totalRt;
    }

    public void setTotalRt(Long totalRt) {
        this.totalRt = totalRt;
    }

    public BigDecimal getAvgRt() {
        return avgRt;
    }

    public void setAvgRt(BigDecimal avgRt) {
        this.avgRt = avgRt;
    }

    public Integer getMaxRt() {
        return maxRt;
    }

    public void setMaxRt(Integer maxRt) {
        this.maxRt = maxRt;
    }

    public Integer getMinRt() {
        return minRt;
    }

    public void setMinRt(Integer minRt) {
        this.minRt = minRt;
    }

    public String getStatType() {
        return statType;
    }

    public void setStatType(String statType) {
        this.statType = statType;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MonitorStatistics that = (MonitorStatistics) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(statTime, that.statTime) &&
                Objects.equals(resourceName, that.resourceName) &&
                Objects.equals(tenantId, that.tenantId) &&
                Objects.equals(totalRequests, that.totalRequests) &&
                Objects.equals(passRequests, that.passRequests) &&
                Objects.equals(blockRequests, that.blockRequests) &&
                Objects.equals(queueRequests, that.queueRequests) &&
                Objects.equals(totalRt, that.totalRt) &&
                Objects.equals(avgRt, that.avgRt) &&
                Objects.equals(maxRt, that.maxRt) &&
                Objects.equals(minRt, that.minRt) &&
                Objects.equals(statType, that.statType) &&
                Objects.equals(createTime, that.createTime) &&
                Objects.equals(updateTime, that.updateTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, statTime, resourceName, tenantId, totalRequests, passRequests, blockRequests, queueRequests, totalRt, avgRt, maxRt, minRt, statType, createTime, updateTime);
    }

    @Override
    public String toString() {
        return "MonitorStatistics{" +
                "id=" + id +
                ", statTime=" + statTime +
                ", resourceName='" + resourceName + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", totalRequests=" + totalRequests +
                ", passRequests=" + passRequests +
                ", blockRequests=" + blockRequests +
                ", queueRequests=" + queueRequests +
                ", totalRt=" + totalRt +
                ", avgRt=" + avgRt +
                ", maxRt=" + maxRt +
                ", minRt=" + minRt +
                ", statType='" + statType + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}