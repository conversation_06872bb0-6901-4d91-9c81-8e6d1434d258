# Sentinel流量控制系统

基于Spring Cloud Gateway和React的分布式流量控制系统，提供多维度流量控制、实时监控和规则管理功能。

## 🚀 项目特性

- **多维度流量控制**: 支持QPS、并发数、响应时间等多种限流策略
- **智能排队机制**: 基于令牌桶和漏桶算法的流量整形
- **实时监控**: 提供实时流量监控和历史数据分析
- **规则热更新**: 支持动态配置规则，无需重启服务
- **可视化管理**: React前端提供友好的管理界面
- **高可用架构**: 基于Spring Cloud生态，支持集群部署
- **容器化部署**: 提供完整的Docker部署方案

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   管理后台      │    │   网关服务      │
│  (React)        │    │ (Spring Boot)   │    │(Spring Gateway) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MySQL       │    │     Redis       │    │     Nacos       │
│   (数据存储)    │    │   (缓存/队列)   │    │  (配置中心)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📦 项目结构

```
openplatform/
├── gateway-service/          # 网关服务模块
│   ├── src/main/java/
│   │   └── com/openplatform/gateway/
│   │       ├── filter/       # 流量控制过滤器
│   │       ├── rule/         # 规则管理器
│   │       ├── queue/        # 排队机制
│   │       └── monitor/      # 监控组件
│   └── Dockerfile
├── flow-control-admin/       # 管理后台模块
│   ├── src/main/java/
│   │   └── com/openplatform/admin/
│   │       ├── controller/   # REST API控制器
│   │       ├── service/      # 业务逻辑层
│   │       ├── entity/       # 数据实体
│   │       └── config/       # 配置类
│   └── Dockerfile
├── flow-control-frontend/    # 前端应用
│   ├── src/
│   │   ├── components/       # React组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务
│   │   └── utils/           # 工具函数
│   ├── Dockerfile
│   └── nginx.conf
├── config/                   # 配置文件
│   ├── nginx.conf           # Nginx配置
│   ├── redis.conf           # Redis配置
│   └── prometheus.yml       # Prometheus配置
├── sql/                     # 数据库脚本
│   └── init.sql            # 初始化脚本
├── docker-compose.yml       # Docker编排文件
├── start.sh                # 启动脚本
├── stop.sh                 # 停止脚本
└── README.md               # 项目文档
```

## 🛠️ 技术栈

### 后端技术
- **Spring Boot 2.7+**: 应用框架
- **Spring Cloud Gateway**: API网关
- **Spring Data JPA**: 数据访问层
- **MySQL 8.0**: 关系型数据库
- **Redis 7.0**: 缓存和消息队列
- **Nacos**: 配置中心和服务发现
- **Micrometer**: 监控指标

### 前端技术
- **React 18**: 前端框架
- **Ant Design**: UI组件库
- **Recharts**: 图表库
- **Axios**: HTTP客户端
- **React Router**: 路由管理

### 基础设施
- **Docker**: 容器化
- **Nginx**: 反向代理
- **Prometheus**: 监控系统
- **Grafana**: 可视化面板

## 🚀 快速开始

### 环境要求

- **生产环境**: Docker 20.0+, Docker Compose 2.0+
- **开发环境**: Java 17+, Node.js 18+, Maven 3.8+, Redis, Nacos, Sentinel

### 方式一：Docker一键启动（推荐生产环境）

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd openplatform
   ```

2. **启动服务**
   ```bash
   chmod +x start.sh
   ./start.sh
   ```

3. **访问应用**
   - 前端应用: http://localhost
   - 管理后台API: http://localhost/api
   - Nacos控制台: http://localhost:8848/nacos
   - Prometheus: http://localhost:9090
   - Grafana: http://localhost:3001

### 方式二：本地开发环境启动

#### 1. 安装依赖服务

**详细安装步骤请参考**: [Sentinel和Nacos安装文档.md](./Sentinel和Nacos安装文档.md)

**快速安装摘要**:
1. 下载并解压 `nacos-server-2.2.4.zip` 到 `D:\nacos`
2. 下载 `sentinel-dashboard-1.8.6.jar` 到 `D:\sentinel`
3. 安装Redis（可选择Windows版本或便携版）

#### 2. 启动服务

**使用自动化脚本（推荐）**:
```cmd
# 启动所有服务（Redis + Nacos + Sentinel + Gateway）
start-all-services.bat

# 或分步启动
start-redis.bat                # 启动Redis
start-nacos-sentinel.bat       # 启动Nacos和Sentinel
cd gateway-service && mvn spring-boot:run  # 启动Gateway
```

**检查服务状态**:
```cmd
check-services-status.bat
```

**停止所有服务**:
```cmd
stop-all-services.bat
```

#### 3. 访问应用
- Gateway服务: http://localhost:8080
- Nacos控制台: http://localhost:8848/nacos (nacos/nacos)
- Sentinel控制台: http://localhost:8858 (sentinel/sentinel)
- Redis: localhost:6379

### 手动部署

1. **构建Java项目**
   ```bash
   mvn clean package -DskipTests
   ```

2. **构建前端项目**
   ```bash
   cd flow-control-frontend
   npm install
   npm run build
   cd ..
   ```

3. **启动Docker服务**
   ```bash
   docker-compose up -d
   ```

## 📊 功能模块

### 1. 流量控制
- **QPS限流**: 基于每秒请求数的流量控制
- **并发限流**: 基于并发请求数的流量控制
- **响应时间控制**: 基于平均响应时间的流量控制
- **自定义规则**: 支持复杂的流量控制规则

### 2. 排队机制
- **令牌桶算法**: 平滑突发流量
- **漏桶算法**: 恒定速率处理请求
- **优先级队列**: 支持请求优先级
- **超时处理**: 自动处理排队超时

### 3. 监控告警
- **实时监控**: 实时流量数据展示
- **历史分析**: 历史数据统计和分析
- **性能指标**: 系统性能监控
- **告警通知**: 异常情况告警

### 4. 规则管理
- **动态配置**: 实时更新流量控制规则
- **规则模板**: 预定义规则模板
- **批量操作**: 支持批量导入导出
- **版本管理**: 规则版本控制

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `MYSQL_HOST` | MySQL主机地址 | `mysql` |
| `MYSQL_PORT` | MySQL端口 | `3306` |
| `MYSQL_DATABASE` | 数据库名称 | `sentinel_flow_control` |
| `REDIS_HOST` | Redis主机地址 | `redis` |
| `REDIS_PORT` | Redis端口 | `6379` |
| `NACOS_SERVER_ADDR` | Nacos服务地址 | `nacos:8848` |

### 应用配置

主要配置文件位于各模块的 `src/main/resources/application.yml`

## 📈 监控指标

### 业务指标
- 请求总数 (Total Requests)
- 通过请求数 (Passed Requests)
- 阻塞请求数 (Blocked Requests)
- 排队请求数 (Queued Requests)
- 平均响应时间 (Average Response Time)
- 通过率 (Pass Rate)

### 系统指标
- CPU使用率
- 内存使用率
- JVM堆内存
- 垃圾回收
- 数据库连接池
- Redis连接数

## 🔍 故障排查

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看服务日志
   docker-compose logs -f [service-name]
   
   # 检查服务状态
   docker-compose ps
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL服务
   docker-compose logs mysql
   
   # 验证数据库连接
   docker-compose exec mysql mysql -u root -p
   ```

3. **前端页面无法访问**
   ```bash
   # 检查Nginx配置
   docker-compose logs nginx
   
   # 检查前端构建
   docker-compose logs frontend
   ```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f gateway-service
docker-compose logs -f admin-service
docker-compose logs -f frontend
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]

## 🙏 致谢

感谢以下开源项目的支持：
- [Spring Cloud Gateway](https://spring.io/projects/spring-cloud-gateway)
- [React](https://reactjs.org/)
- [Ant Design](https://ant.design/)
- [Prometheus](https://prometheus.io/)
- [Grafana](https://grafana.com/)