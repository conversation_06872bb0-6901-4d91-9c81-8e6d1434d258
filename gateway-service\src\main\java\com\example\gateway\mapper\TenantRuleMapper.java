package com.example.gateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.common.entity.TenantRuleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 租户规则Mapper接口
 * 提供租户规则的数据访问方法
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface TenantRuleMapper extends BaseMapper<TenantRuleEntity> {

    /**
     * 查询所有启用的租户规则
     * 
     * @return 启用的租户规则列表
     */
    @Select("SELECT * FROM tenant_flow_rules WHERE enabled = 1 AND deleted = 0 " +
            "AND (start_time IS NULL OR start_time <= NOW()) " +
            "AND (end_time IS NULL OR end_time >= NOW()) " +
            "ORDER BY priority ASC, create_time ASC")
    List<TenantRuleEntity> selectEnabledRules();

    /**
     * 根据租户ID查询启用的规则
     * 
     * @param tenantId 租户ID
     * @return 该租户的启用规则列表
     */
    @Select("SELECT * FROM tenant_flow_rules WHERE tenant_id = #{tenantId} AND enabled = 1 AND deleted = 0 " +
            "AND (start_time IS NULL OR start_time <= NOW()) " +
            "AND (end_time IS NULL OR end_time >= NOW()) " +
            "ORDER BY priority ASC, create_time ASC")
    List<TenantRuleEntity> selectEnabledRulesByTenant(@Param("tenantId") String tenantId);

    /**
     * 根据关联资源查询启用的规则
     * 
     * @param refResource 关联资源
     * @return 匹配的启用规则列表
     */
    @Select("SELECT * FROM tenant_flow_rules WHERE ref_resource = #{refResource} AND enabled = 1 AND deleted = 0 " +
            "AND (start_time IS NULL OR start_time <= NOW()) " +
            "AND (end_time IS NULL OR end_time >= NOW()) " +
            "ORDER BY priority ASC, create_time ASC")
    List<TenantRuleEntity> selectEnabledRulesByResource(@Param("refResource") String refResource);

    /**
     * 根据租户ID和关联资源查询启用的规则
     * 
     * @param tenantId 租户ID
     * @param refResource 关联资源
     * @return 匹配的启用规则列表
     */
    @Select("SELECT * FROM tenant_flow_rules WHERE tenant_id = #{tenantId} AND ref_resource = #{refResource} AND enabled = 1 AND deleted = 0 " +
            "AND (start_time IS NULL OR start_time <= NOW()) " +
            "AND (end_time IS NULL OR end_time >= NOW()) " +
            "ORDER BY priority ASC, create_time ASC")
    List<TenantRuleEntity> selectEnabledRulesByTenantAndResource(@Param("tenantId") String tenantId, 
                                                                 @Param("refResource") String refResource);

    /**
     * 根据优先级范围查询启用的规则
     * 
     * @param tenantId 租户ID
     * @param minPriority 最小优先级
     * @param maxPriority 最大优先级
     * @return 匹配的启用规则列表
     */
    @Select("SELECT * FROM tenant_flow_rules WHERE tenant_id = #{tenantId} AND enabled = 1 AND deleted = 0 " +
            "AND priority >= #{minPriority} AND priority <= #{maxPriority} " +
            "AND (start_time IS NULL OR start_time <= NOW()) " +
            "AND (end_time IS NULL OR end_time >= NOW()) " +
            "ORDER BY priority ASC, create_time ASC")
    List<TenantRuleEntity> selectEnabledRulesByPriorityRange(@Param("tenantId") String tenantId,
                                                              @Param("minPriority") Integer minPriority,
                                                              @Param("maxPriority") Integer maxPriority);

    /**
     * 统计租户规则数量
     * 
     * @param tenantId 租户ID
     * @param enabled 启用状态（可选）
     * @return 规则数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM tenant_flow_rules WHERE tenant_id = #{tenantId} " +
            "<if test='enabled != null'>AND enabled = #{enabled}</if>" +
            "</script>")
    Integer countRulesByTenant(@Param("tenantId") String tenantId, @Param("enabled") Integer enabled);

    /**
     * 获取租户的最大优先级
     * 
     * @param tenantId 租户ID
     * @return 最大优先级值
     */
    @Select("SELECT COALESCE(MAX(priority), 0) FROM tenant_flow_rules WHERE tenant_id = #{tenantId}")
    Integer selectMaxPriorityByTenant(@Param("tenantId") String tenantId);

    /**
     * 查询即将过期的规则
     * 
     * @param hours 小时数
     * @return 即将过期的规则列表
     */
    @Select("SELECT * FROM tenant_flow_rules WHERE enabled = 1 AND deleted = 0 " +
            "AND end_time IS NOT NULL " +
            "AND end_time > NOW() " +
            "AND end_time <= DATE_ADD(NOW(), INTERVAL #{hours} HOUR) " +
            "ORDER BY end_time ASC")
    List<TenantRuleEntity> selectExpiringRules(@Param("hours") Integer hours);

    /**
     * 查询已过期但仍启用的规则
     * 
     * @return 已过期的启用规则列表
     */
    @Select("SELECT * FROM tenant_flow_rules WHERE enabled = 1 AND deleted = 0 " +
            "AND end_time IS NOT NULL " +
            "AND end_time < NOW()")
    List<TenantRuleEntity> selectExpiredEnabledRules();

    /**
     * 禁用已过期的规则
     * 
     * @return 更新的记录数
     */
    @Select("UPDATE tenant_flow_rules SET enabled = 0, update_time = NOW() " +
            "WHERE enabled = 1 AND deleted = 0 AND end_time IS NOT NULL AND end_time < NOW()")
    Integer disableExpiredRules();
}