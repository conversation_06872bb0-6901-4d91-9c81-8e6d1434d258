<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.example</groupId>
		<artifactId>sentinel-gateway-flow-control</artifactId>
		<version>1.0.0</version>
	</parent>
	<groupId>com.example</groupId>
	<artifactId>flow-control-admin</artifactId>
	<version>1.0.0</version>
	<packaging>jar</packaging>
	<name>Flow Control Admin</name>
	<description>Sentinel流量控制系统后端管理服务</description>
	<dependencies>
		<!-- Spring Boot Starter -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.example</groupId>
			<artifactId>flow-control-common</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<!-- Jakarta Annotation API -->
		<dependency>
			<groupId>jakarta.annotation</groupId>
			<artifactId>jakarta.annotation-api</artifactId>
		</dependency>

		<!-- MyBatis Plus for Spring Boot 3 -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
		</dependency>

		<!-- Apache Commons Net for CIDR support -->
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.9.0</version>
		</dependency>

		<!-- MySQL Driver -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
		</dependency>

		<!-- Druid Connection Pool -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<!-- Hutool -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
		</dependency>

		<!-- Jackson -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
		</dependency>

		<!-- Swagger/OpenAPI 3 -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.2.0</version>
		</dependency>

		<!-- FastJSON2 -->
		<dependency>
			<groupId>com.alibaba.fastjson2</groupId>
			<artifactId>fastjson2</artifactId>
			<version>2.0.43</version>
		</dependency>

		<!-- Test Dependencies -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.example.admin.AdminApplication</mainClass>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<!-- 代码的编码方式，使用 UTF-8 -->
					<encoding>UTF-8</encoding>
					<!-- 编译器参数，-parameters 可在运行时保留方法的参数名称 -->
					<compilerArgs>
						<arg>-parameters</arg>
						<arg>--add-opens</arg>
						<arg>jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>
					</compilerArgs>
					<release>21</release>

				</configuration>
			</plugin>
		</plugins>
	</build>
	<!-- 添加仓库配置 -->
	<repositories>
		<repository>
			<id>central</id>
			<name>Maven Central</name>
			<url>https://repo1.maven.org/maven2</url>
		</repository>
		<repository>
			<id>aliyun</id>
			<name>Aliyun Maven Repository</name>
			<url>https://maven.aliyun.com/repository/public</url>
		</repository>
	</repositories>
</project>