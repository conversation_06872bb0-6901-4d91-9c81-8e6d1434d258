[{"D:\\java\\openplatform\\flow-control-vue-frontend\\src\\main.js": "1", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\App.vue": "2", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\index.js": "3", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\router\\index.js": "4", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\index.js": "5", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\i18n\\index.js": "6", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\Login.vue": "7", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\MonitorDashboard.vue": "8", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\Dashboard.vue": "9", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\FlowControlManagement.vue": "10", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\IpBlackWhiteListManagement.vue": "11", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\Config.vue": "12", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\Statistics.vue": "13", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\auth.js": "14", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\theme.js": "15", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\monitor.js": "16", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\language.js": "17", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\i18n\\locales\\zh-CN.js": "18", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\config.js": "19", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\i18n\\locales\\en-US.js": "20", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\TenantManagementTab.vue": "21", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\Layout.vue": "22", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\IpRulesTab.vue": "23", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\TenantDialog.vue": "24", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\InterfaceRulesTab.vue": "25", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\InterfaceRuleDialog.vue": "26", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\IpRuleDialog.vue": "27", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\utils\\export.js": "28", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\HelpCenter.vue": "29", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\UserGuide.vue": "30", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\tenantFlowRules.js": "31", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\ipWhitelist.js": "32", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\ipBlacklist.js": "33", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\ipFlowRules.js": "34", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\IpManagement.vue": "35", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\IpWhitelistTab.vue": "36", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\IpBlacklistTab.vue": "37", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\IpFlowRulesTab.vue": "38", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\TenantFlowRules.vue": "39", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\InterfaceFlowRules.vue": "40", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\IpFlowRules.vue": "41", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\IpBlackWhiteList.vue": "42", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\interfaceRule.js": "43", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\ipRule.js": "44", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\ipBlackWhiteList.js": "45", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\utils\\request.js": "46", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\tenantRule.js": "47", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\tenantFlowRule.js": "48", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\utils\\validation.js": "49"}, {"size": 1177, "mtime": 1754745031969, "results": "50", "hashOfConfig": "51"}, {"size": 395, "mtime": 1754619035619, "results": "52", "hashOfConfig": "51"}, {"size": 30487, "mtime": 1755756892980, "results": "53", "hashOfConfig": "51"}, {"size": 1716, "mtime": 1755151371347, "results": "54", "hashOfConfig": "51"}, {"size": 1133, "mtime": 1755137546463, "results": "55", "hashOfConfig": "51"}, {"size": 1365, "mtime": 1755148147906, "results": "56", "hashOfConfig": "51"}, {"size": 3706, "mtime": 1754620086235, "results": "57", "hashOfConfig": "51"}, {"size": 14733, "mtime": 1755082935292, "results": "58", "hashOfConfig": "51"}, {"size": 9067, "mtime": 1755756395875, "results": "59", "hashOfConfig": "51"}, {"size": 13335, "mtime": 1755138505808, "results": "60", "hashOfConfig": "51"}, {"size": 20312, "mtime": 1755138534870, "results": "61", "hashOfConfig": "51"}, {"size": 42195, "mtime": 1755757970142, "results": "62", "hashOfConfig": "51"}, {"size": 19053, "mtime": 1755756870047, "results": "63", "hashOfConfig": "51"}, {"size": 2160, "mtime": 1754619706816, "results": "64", "hashOfConfig": "51"}, {"size": 3374, "mtime": 1754623553618, "results": "65", "hashOfConfig": "51"}, {"size": 3563, "mtime": 1754619942701, "results": "66", "hashOfConfig": "51"}, {"size": 2307, "mtime": 1754623916552, "results": "67", "hashOfConfig": "51"}, {"size": 8695, "mtime": 1755148606343, "results": "68", "hashOfConfig": "51"}, {"size": 11745, "mtime": 1754623083133, "results": "69", "hashOfConfig": "51"}, {"size": 8917, "mtime": 1755138903089, "results": "70", "hashOfConfig": "51"}, {"size": 3754, "mtime": 1755065135554, "results": "71", "hashOfConfig": "51"}, {"size": 15009, "mtime": 1755776909332, "results": "72", "hashOfConfig": "51"}, {"size": 3920, "mtime": 1755065241153, "results": "73", "hashOfConfig": "51"}, {"size": 7344, "mtime": 1755066230411, "results": "74", "hashOfConfig": "51"}, {"size": 4140, "mtime": 1755065154983, "results": "75", "hashOfConfig": "51"}, {"size": 7063, "mtime": 1755065268853, "results": "76", "hashOfConfig": "51"}, {"size": 6600, "mtime": 1755065354515, "results": "77", "hashOfConfig": "51"}, {"size": 9536, "mtime": 1755138929109, "results": "78", "hashOfConfig": "51"}, {"size": 16901, "mtime": 1755774375912, "results": "79", "hashOfConfig": "51"}, {"size": 11043, "mtime": 1755774321943, "results": "80", "hashOfConfig": "51"}, {"size": 7170, "mtime": 1755137410445, "results": "81", "hashOfConfig": "51"}, {"size": 7823, "mtime": 1755137510278, "results": "82", "hashOfConfig": "51"}, {"size": 7823, "mtime": 1755137487643, "results": "83", "hashOfConfig": "51"}, {"size": 8687, "mtime": 1755137531947, "results": "84", "hashOfConfig": "51"}, {"size": 1499, "mtime": 1755138167620, "results": "85", "hashOfConfig": "51"}, {"size": 10572, "mtime": 1755138306613, "results": "86", "hashOfConfig": "51"}, {"size": 10572, "mtime": 1755138318199, "results": "87", "hashOfConfig": "51"}, {"size": 14013, "mtime": 1755138477854, "results": "88", "hashOfConfig": "51"}, {"size": 19887, "mtime": 1755777025777, "results": "89", "hashOfConfig": "51"}, {"size": 16353, "mtime": 1755777042114, "results": "90", "hashOfConfig": "51"}, {"size": 20999, "mtime": 1755778241613, "results": "91", "hashOfConfig": "51"}, {"size": 25084, "mtime": 1755777463000, "results": "92", "hashOfConfig": "51"}, {"size": 1214, "mtime": 1755770678900, "results": "93", "hashOfConfig": "51"}, {"size": 784, "mtime": 1755772340410, "results": "94", "hashOfConfig": "51"}, {"size": 2027, "mtime": 1755148590358, "results": "95", "hashOfConfig": "51"}, {"size": 3044, "mtime": 1755769235128, "results": "96", "hashOfConfig": "51"}, {"size": 1007, "mtime": 1755149042330, "results": "97", "hashOfConfig": "51"}, {"size": 4449, "mtime": 1755755925662, "results": "98", "hashOfConfig": "51"}, {"size": 12561, "mtime": 1755757002565, "results": "99", "hashOfConfig": "51"}, {"filePath": "100", "messages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, "wlw8or", {"filePath": "103", "messages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "105"}, {"filePath": "106", "messages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "108", "messages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "110", "messages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "112", "messages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "114", "messages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "105"}, {"filePath": "116", "messages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "118"}, {"filePath": "119", "messages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "105"}, {"filePath": "121", "messages": "122", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "123", "usedDeprecatedRules": "124"}, {"filePath": "125", "messages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "129", "usedDeprecatedRules": "105"}, {"filePath": "130", "messages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "105"}, {"filePath": "132", "messages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "134", "messages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "136", "messages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "138", "usedDeprecatedRules": "102"}, {"filePath": "139", "messages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "141", "messages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "143", "messages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "145", "usedDeprecatedRules": "102"}, {"filePath": "146", "messages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "148", "messages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "105"}, {"filePath": "152", "messages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "164", "messages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "105"}, {"filePath": "166", "messages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "105"}, {"filePath": "168", "messages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "170", "usedDeprecatedRules": "102"}, {"filePath": "171", "messages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "173", "usedDeprecatedRules": "102"}, {"filePath": "174", "messages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "176", "usedDeprecatedRules": "102"}, {"filePath": "177", "messages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "179", "usedDeprecatedRules": "102"}, {"filePath": "180", "messages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "124"}, {"filePath": "188", "messages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "105"}, {"filePath": "190", "messages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "105"}, {"filePath": "192", "messages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "105"}, {"filePath": "196", "messages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "198", "messages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "200", "messages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "202", "messages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "204", "messages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "206"}, {"filePath": "207", "messages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, {"filePath": "209", "messages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "102"}, "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\main.js", [], [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\App.vue", [], [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\index.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\router\\index.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\index.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\i18n\\index.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\Login.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\MonitorDashboard.vue", [], [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\Dashboard.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\FlowControlManagement.vue", ["211"], "<template>\n\t<div class=\"flow-control-management\">\n\t\t<layout>\n\t\t\t<div class=\"flow-control-content\">\n\t\t\t\t<div class=\"page-header\">\n\t\t\t\t\t<h1>流量控制管理</h1>\n\t\t\t\t\t<p>管理租户、接口、IP地址三维度的流量控制规则</p>\n\t\t\t\t</div>\n\n\t\t\t\t<!-- 标签页 -->\n\t\t\t\t<el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n\t\t\t\t\t<!-- 租户管理 -->\n\t\t\t\t\t<el-tab-pane label=\"租户管理\" name=\"tenant\">\n\t\t\t\t\t\t<tenant-management-tab\n\t\t\t\t\t\t\t:tenants=\"tenants\"\n\t\t\t\t\t\t\t:loading=\"loading\"\n\t\t\t\t\t\t\t:pagination=\"pagination\"\n\t\t\t\t\t\t\t@load-tenants=\"loadTenants\"\n\t\t\t\t\t\t\t@add-tenant=\"showAddTenantDialog\"\n\t\t\t\t\t\t\t@edit-tenant=\"editTenant\"\n\t\t\t\t\t\t\t@delete-tenant=\"deleteTenant\"\n\t\t\t\t\t\t\t@size-change=\"handleSizeChange\"\n\t\t\t\t\t\t\t@current-change=\"handleCurrentChange\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</el-tab-pane>\n\n\t\t\t\t\t<!-- 接口规则管理 -->\n\t\t\t\t\t<el-tab-pane label=\"接口规则\" name=\"interface\">\n\t\t\t\t\t\t<interface-rules-tab\n\t\t\t\t\t\t\t:rules=\"interfaceRules\"\n\t\t\t\t\t\t\t:loading=\"loading\"\n\t\t\t\t\t\t\t:pagination=\"pagination\"\n\t\t\t\t\t\t\t@load-rules=\"loadInterfaceRules\"\n\t\t\t\t\t\t\t@add-rule=\"showAddInterfaceRuleDialog\"\n\t\t\t\t\t\t\t@edit-rule=\"editInterfaceRule\"\n\t\t\t\t\t\t\t@delete-rule=\"deleteInterfaceRule\"\n\t\t\t\t\t\t\t@size-change=\"handleSizeChange\"\n\t\t\t\t\t\t\t@current-change=\"handleCurrentChange\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</el-tab-pane>\n\n\t\t\t\t\t<!-- IP规则管理 -->\n\t\t\t\t\t<el-tab-pane label=\"IP规则\" name=\"ip\">\n\t\t\t\t\t\t<ip-rules-tab\n\t\t\t\t\t\t\t:rules=\"ipRules\"\n\t\t\t\t\t\t\t:loading=\"loading\"\n\t\t\t\t\t\t\t:pagination=\"pagination\"\n\t\t\t\t\t\t\t@load-rules=\"loadIpRules\"\n\t\t\t\t\t\t\t@add-rule=\"showAddIpRuleDialog\"\n\t\t\t\t\t\t\t@edit-rule=\"editIpRule\"\n\t\t\t\t\t\t\t@delete-rule=\"deleteIpRule\"\n\t\t\t\t\t\t\t@size-change=\"handleSizeChange\"\n\t\t\t\t\t\t\t@current-change=\"handleCurrentChange\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</el-tab-pane>\n\t\t\t\t</el-tabs>\n\n\t\t\t\t<!-- 租户对话框 -->\n\t\t\t\t<tenant-dialog\n\t\t\t\t\t:visible=\"tenantDialogVisible\"\n\t\t\t\t\t:form=\"tenantForm\"\n\t\t\t\t\t:is-edit=\"isTenantEdit\"\n\t\t\t\t\t:submitting=\"submitting\"\n\t\t\t\t\t@close=\"closeTenantDialog\"\n\t\t\t\t\t@submit=\"submitTenantForm\"\n\t\t\t\t/>\n\n\t\t\t\t<!-- 接口规则对话框 -->\n\t\t\t\t<interface-rule-dialog\n\t\t\t\t\t:visible=\"interfaceRuleDialogVisible\"\n\t\t\t\t\t:form=\"interfaceRuleForm\"\n\t\t\t\t\t:is-edit=\"isInterfaceRuleEdit\"\n\t\t\t\t\t:submitting=\"submitting\"\n\t\t\t\t\t:tenants=\"tenants\"\n\t\t\t\t\t@close=\"closeInterfaceRuleDialog\"\n\t\t\t\t\t@submit=\"submitInterfaceRuleForm\"\n\t\t\t\t/>\n\n\t\t\t\t<!-- IP规则对话框 -->\n\t\t\t\t<ip-rule-dialog\n\t\t\t\t\t:visible=\"ipRuleDialogVisible\"\n\t\t\t\t\t:form=\"ipRuleForm\"\n\t\t\t\t\t:is-edit=\"isIpRuleEdit\"\n\t\t\t\t\t:submitting=\"submitting\"\n\t\t\t\t\t@close=\"closeIpRuleDialog\"\n\t\t\t\t\t@submit=\"submitIpRuleForm\"\n\t\t\t\t/>\n\t\t\t</div>\n\t\t</layout>\n\t</div>\n</template>\n\n<script>\nimport Layout from '../components/Layout.vue';\nimport TenantManagementTab from '../components/TenantManagementTab.vue';\nimport InterfaceRulesTab from '../components/InterfaceRulesTab.vue';\nimport IpRulesTab from '../components/IpRulesTab.vue';\nimport TenantDialog from '../components/TenantDialog.vue';\nimport InterfaceRuleDialog from '../components/InterfaceRuleDialog.vue';\nimport IpRuleDialog from '../components/IpRuleDialog.vue';\n\nexport default {\n\tname: 'FlowControlManagement',\n\tcomponents: {\n\t\tLayout,\n\t\tTenantManagementTab,\n\t\tInterfaceRulesTab,\n\t\tIpRulesTab,\n\t\tTenantDialog,\n\t\tInterfaceRuleDialog,\n\t\tIpRuleDialog,\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tactiveTab: 'tenant',\n\t\t\tloading: false,\n\t\t\tsubmitting: false,\n\n\t\t\t// 分页\n\t\t\tpagination: {\n\t\t\t\tcurrent: 1,\n\t\t\t\tsize: 20,\n\t\t\t\ttotal: 0,\n\t\t\t},\n\n\t\t\t// 租户数据\n\t\t\ttenants: [],\n\t\t\ttenantDialogVisible: false,\n\t\t\tisTenantEdit: false,\n\t\t\ttenantForm: {\n\t\t\t\ttenantId: '',\n\t\t\t\ttenantName: '',\n\t\t\t\tdescription: '',\n\t\t\t\tcontactPerson: '',\n\t\t\t\tcontactEmail: '',\n\t\t\t\tcontactPhone: '',\n\t\t\t\ttotalQpsLimit: null,\n\t\t\t\ttotalConcurrentLimit: null,\n\t\t\t\tcontrolBehavior: '0',\n\t\t\t\tmaxQueueingTimeMs: 500,\n\t\t\t\twarmUpPeriodSec: 10,\n\t\t\t\tstatus: 1,\n\t\t\t},\n\n\t\t\t// 接口规则数据\n\t\t\tinterfaceRules: [],\n\t\t\tinterfaceRuleDialogVisible: false,\n\t\t\tisInterfaceRuleEdit: false,\n\t\t\tinterfaceRuleForm: {\n\t\t\t\truleName: '',\n\t\t\t\ttenantId: '',\n\t\t\t\tresourceName: '',\n\t\t\t\tlimitMode: 0, // 0-QPS，1-并发数\n\t\t\t\tthreshold: null,\n\t\t\t\tbehavior: 0, // 0-快速失败，1-预热，2-排队等待\n\t\t\t\twarmUpPeriod: null,\n\t\t\t\tqueueTimeout: null,\n\t\t\t\tstatus: 1,\n\t\t\t},\n\n\t\t\t// IP规则数据\n\t\t\tipRules: [],\n\t\t\tipRuleDialogVisible: false,\n\t\t\tisIpRuleEdit: false,\n\t\t\tipRuleForm: {\n\t\t\t\truleName: '',\n\t\t\t\tipValue: '',\n\t\t\t\tlistType: 'LIMIT', // LIMIT-限制，ALLOW-允许\n\t\t\t\tlimitMode: 0, // 0-QPS，1-并发数\n\t\t\t\tlimitCount: null,\n\t\t\t\tstatus: 1,\n\t\t\t},\n\t\t};\n\t},\n\n\tmounted() {\n\t\tthis.loadCurrentTabData();\n\t},\n\n\tmethods: {\n\t\t// 处理标签页切换\n\t\thandleTabClick(tab) {\n\t\t\tthis.activeTab = tab.name;\n\t\t\tthis.resetPagination();\n\t\t\tthis.loadCurrentTabData();\n\t\t},\n\n\t\t// 加载当前标签页数据\n\t\tloadCurrentTabData() {\n\t\t\tswitch (this.activeTab) {\n\t\t\t\tcase 'tenant':\n\t\t\t\t\tthis.loadTenants();\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'interface':\n\t\t\t\t\tthis.loadInterfaceRules();\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'ip':\n\t\t\t\t\tthis.loadIpRules();\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\n\t\t// 重置分页\n\t\tresetPagination() {\n\t\t\tthis.pagination = {\n\t\t\t\tcurrent: 1,\n\t\t\t\tsize: 20,\n\t\t\t\ttotal: 0,\n\t\t\t};\n\t\t},\n\n\t\t// 租户管理方法\n\t\tasync loadTenants() {\n\t\t\tthis.loading = true;\n\t\t\ttry {\n\t\t\t\tconst response = await this.$api.tenants.getList({\n\t\t\t\t\tcurrent: this.pagination.current,\n\t\t\t\t\tsize: this.pagination.size,\n\t\t\t\t});\n\n\t\t\t\tif (response.data.success) {\n\t\t\t\t\tthis.tenants = response.data.data.records || [];\n\t\t\t\t\tthis.pagination.total = response.data.data.total || 0;\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error('获取租户列表失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Error loading tenants:', error);\n\t\t\t\tthis.$message.error('获取租户列表失败');\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\n\t\tshowAddTenantDialog() {\n\t\t\tthis.isTenantEdit = false;\n\t\t\tthis.tenantDialogVisible = true;\n\t\t},\n\n\t\teditTenant(tenant) {\n\t\t\tthis.isTenantEdit = true;\n\t\t\tthis.tenantForm = { ...tenant };\n\t\t\tthis.tenantDialogVisible = true;\n\t\t},\n\n\t\tasync deleteTenant(tenant) {\n\t\t\ttry {\n\t\t\t\tawait this.$confirm(\n\t\t\t\t\t`确定要删除租户 \"${tenant.tenantName}\" 吗？`,\n\t\t\t\t\t'确认删除',\n\t\t\t\t\t{\n\t\t\t\t\t\tconfirmButtonText: '确定',\n\t\t\t\t\t\tcancelButtonText: '取消',\n\t\t\t\t\t\ttype: 'warning',\n\t\t\t\t\t}\n\t\t\t\t);\n\n\t\t\t\tconst response = await this.$api.tenants.delete(tenant.id);\n\t\t\t\tif (response.data.success) {\n\t\t\t\t\tthis.$message.success('删除成功');\n\t\t\t\t\tthis.loadTenants();\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error('删除失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (error !== 'cancel') {\n\t\t\t\t\tconsole.error('Error deleting tenant:', error);\n\t\t\t\t\tthis.$message.error('删除失败');\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tcloseTenantDialog() {\n\t\t\tthis.tenantDialogVisible = false;\n\t\t\tthis.resetTenantForm();\n\t\t},\n\n\t\tasync submitTenantForm(formData) {\n\t\t\tthis.submitting = true;\n\t\t\ttry {\n\t\t\t\tlet response;\n\t\t\t\tif (this.isTenantEdit) {\n\t\t\t\t\tresponse = await this.$api.tenants.update(formData.id, formData);\n\t\t\t\t} else {\n\t\t\t\t\tresponse = await this.$api.tenants.create(formData);\n\t\t\t\t}\n\n\t\t\t\tif (response.data.success) {\n\t\t\t\t\tthis.$message.success(this.isTenantEdit ? '更新成功' : '创建成功');\n\t\t\t\t\tthis.tenantDialogVisible = false;\n\t\t\t\t\tthis.loadTenants();\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error(response.data.message || '操作失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Error saving tenant:', error);\n\t\t\t\tthis.$message.error('操作失败');\n\t\t\t} finally {\n\t\t\t\tthis.submitting = false;\n\t\t\t}\n\t\t},\n\n\t\tresetTenantForm() {\n\t\t\tthis.tenantForm = {\n\t\t\t\ttenantId: '',\n\t\t\t\ttenantName: '',\n\t\t\t\tdescription: '',\n\t\t\t\tcontactPerson: '',\n\t\t\t\tcontactEmail: '',\n\t\t\t\tcontactPhone: '',\n\t\t\t\ttotalQpsLimit: null,\n\t\t\t\ttotalConcurrentLimit: null,\n\t\t\t\tstatus: 1,\n\t\t\t};\n\t\t},\n\n\t\t// 接口规则管理方法\n\t\tasync loadInterfaceRules() {\n\t\t\tthis.loading = true;\n\t\t\ttry {\n\t\t\t\tconst response = await this.$api.tenantFlowRules.getList({\n\t\t\t\t\tcurrent: this.pagination.current,\n\t\t\t\t\tsize: this.pagination.size,\n\t\t\t\t});\n\n\t\t\t\tif (response.data.success) {\n\t\t\t\t\tthis.interfaceRules = response.data.data.records || [];\n\t\t\t\t\tthis.pagination.total = response.data.data.total || 0;\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error('获取接口规则失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Error loading interface rules:', error);\n\t\t\t\tthis.$message.error('获取接口规则失败');\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\n\t\tshowAddInterfaceRuleDialog() {\n\t\t\tthis.isInterfaceRuleEdit = false;\n\t\t\tthis.interfaceRuleDialogVisible = true;\n\t\t},\n\n\t\teditInterfaceRule(rule) {\n\t\t\tthis.isInterfaceRuleEdit = true;\n\t\t\tthis.interfaceRuleForm = { ...rule };\n\t\t\tthis.interfaceRuleDialogVisible = true;\n\t\t},\n\n\t\tasync deleteInterfaceRule(rule) {\n\t\t\ttry {\n\t\t\t\tawait this.$confirm(\n\t\t\t\t\t`确定要删除规则 \"${rule.ruleName}\" 吗？`,\n\t\t\t\t\t'确认删除',\n\t\t\t\t\t{\n\t\t\t\t\t\tconfirmButtonText: '确定',\n\t\t\t\t\t\tcancelButtonText: '取消',\n\t\t\t\t\t\ttype: 'warning',\n\t\t\t\t\t}\n\t\t\t\t);\n\n\t\t\t\tconst response = await this.$api.tenantFlowRules.delete(rule.id);\n\t\t\t\tif (response.data.success) {\n\t\t\t\t\tthis.$message.success('删除成功');\n\t\t\t\t\tthis.loadInterfaceRules();\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error('删除失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (error !== 'cancel') {\n\t\t\t\t\tconsole.error('Error deleting interface rule:', error);\n\t\t\t\t\tthis.$message.error('删除失败');\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tcloseInterfaceRuleDialog() {\n\t\t\tthis.interfaceRuleDialogVisible = false;\n\t\t\tthis.resetInterfaceRuleForm();\n\t\t},\n\n\t\tasync submitInterfaceRuleForm(formData) {\n\t\t\tthis.submitting = true;\n\t\t\ttry {\n\t\t\t\tlet response;\n\t\t\t\tif (this.isInterfaceRuleEdit) {\n\t\t\t\t\tresponse = await this.$api.tenantFlowRules.update(formData.id, formData);\n\t\t\t\t} else {\n\t\t\t\t\tresponse = await this.$api.tenantFlowRules.create(formData);\n\t\t\t\t}\n\n\t\t\t\tif (response.data.success) {\n\t\t\t\t\tthis.$message.success(this.isInterfaceRuleEdit ? '更新成功' : '创建成功');\n\t\t\t\t\tthis.interfaceRuleDialogVisible = false;\n\t\t\t\t\tthis.loadInterfaceRules();\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error(response.data.message || '操作失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Error saving interface rule:', error);\n\t\t\t\tthis.$message.error('操作失败');\n\t\t\t} finally {\n\t\t\t\tthis.submitting = false;\n\t\t\t}\n\t\t},\n\n\t\tresetInterfaceRuleForm() {\n\t\t\tthis.interfaceRuleForm = {\n\t\t\t\truleName: '',\n\t\t\t\ttenantId: '',\n\t\t\t\tresourceName: '',\n\t\t\t\tlimitMode: 0,\n\t\t\t\tthreshold: null,\n\t\t\t\tbehavior: 0,\n\t\t\t\twarmUpPeriod: null,\n\t\t\t\tqueueTimeout: null,\n\t\t\t\tstatus: 1,\n\t\t\t};\n\t\t},\n\n\t\t// IP规则管理方法\n\t\tasync loadIpRules() {\n\t\t\tthis.loading = true;\n\t\t\ttry {\n\t\t\t\tconst response = await this.$api.ipFlowRules.getList({\n\t\t\t\t\tcurrent: this.pagination.current,\n\t\t\t\t\tsize: this.pagination.size,\n\t\t\t\t});\n\n\t\t\t\tif (response.data.success) {\n\t\t\t\t\tthis.ipRules = response.data.data.records || [];\n\t\t\t\t\tthis.pagination.total = response.data.data.total || 0;\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error('获取IP规则失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Error loading IP rules:', error);\n\t\t\t\tthis.$message.error('获取IP规则失败');\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\n\t\tshowAddIpRuleDialog() {\n\t\t\tthis.isIpRuleEdit = false;\n\t\t\tthis.ipRuleDialogVisible = true;\n\t\t},\n\n\t\teditIpRule(rule) {\n\t\t\tthis.isIpRuleEdit = true;\n\t\t\tthis.ipRuleForm = { ...rule };\n\t\t\tthis.ipRuleDialogVisible = true;\n\t\t},\n\n\t\tasync deleteIpRule(rule) {\n\t\t\ttry {\n\t\t\t\tawait this.$confirm(\n\t\t\t\t\t`确定要删除IP规则 \"${rule.ruleName}\" 吗？`,\n\t\t\t\t\t'确认删除',\n\t\t\t\t\t{\n\t\t\t\t\t\tconfirmButtonText: '确定',\n\t\t\t\t\t\tcancelButtonText: '取消',\n\t\t\t\t\t\ttype: 'warning',\n\t\t\t\t\t}\n\t\t\t\t);\n\n\t\t\t\tconst response = await this.$api.ipFlowRules.delete(rule.id);\n\t\t\t\tif (response.data.success) {\n\t\t\t\t\tthis.$message.success('删除成功');\n\t\t\t\t\tthis.loadIpRules();\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error('删除失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (error !== 'cancel') {\n\t\t\t\t\tconsole.error('Error deleting IP rule:', error);\n\t\t\t\t\tthis.$message.error('删除失败');\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tcloseIpRuleDialog() {\n\t\t\tthis.ipRuleDialogVisible = false;\n\t\t\tthis.resetIpRuleForm();\n\t\t},\n\n\t\tasync submitIpRuleForm(formData) {\n\t\t\tthis.submitting = true;\n\t\t\ttry {\n\t\t\t\tlet response;\n\t\t\t\tif (this.isIpRuleEdit) {\n\t\t\t\t\tresponse = await this.$api.ipFlowRules.update(formData.id, formData);\n\t\t\t\t} else {\n\t\t\t\t\tresponse = await this.$api.ipFlowRules.create(formData);}],\"thought\":\"更新FlowControlManagement.vue文件中的API调用\"}}}\n\t\t\t\t}\n\n\t\t\t\tif (response.data.success) {\n\t\t\t\t\tthis.$message.success(this.isIpRuleEdit ? '更新成功' : '创建成功');\n\t\t\t\t\tthis.ipRuleDialogVisible = false;\n\t\t\t\t\tthis.loadIpRules();\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error(response.data.message || '操作失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Error saving IP rule:', error);\n\t\t\t\tthis.$message.error('操作失败');\n\t\t\t} finally {\n\t\t\t\tthis.submitting = false;\n\t\t\t}\n\t\t},\n\n\t\tresetIpRuleForm() {\n\t\t\tthis.ipRuleForm = {\n\t\t\t\truleName: '',\n\t\t\t\tipValue: '',\n\t\t\t\tlistType: 'LIMIT',\n\t\t\t\tlimitMode: 0,\n\t\t\t\tlimitCount: null,\n\t\t\t\tstatus: 1,\n\t\t\t};\n\t\t},\n\n\t\t// 分页处理\n\t\thandleSizeChange(size) {\n\t\t\tthis.pagination.size = size;\n\t\t\tthis.pagination.current = 1;\n\t\t\tthis.loadCurrentTabData();\n\t\t},\n\n\t\thandleCurrentChange(current) {\n\t\t\tthis.pagination.current = current;\n\t\t\tthis.loadCurrentTabData();\n\t\t},\n\t},\n};\n</script>\n\n<style scoped>\n.flow-control-content {\n\tpadding: 20px;\n}\n\n.page-header {\n\tmargin-bottom: 20px;\n}\n\n.page-header h1 {\n\tmargin: 0 0 8px 0;\n\tfont-size: 24px;\n\tcolor: #303133;\n}\n\n.page-header p {\n\tmargin: 0;\n\tcolor: #909399;\n\tfont-size: 14px;\n}\n</style>", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\IpBlackWhiteListManagement.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\Config.vue", ["212", "213"], "<template>\n\t<div class=\"config\">\n\t\t<layout>\n\t\t\t<div class=\"config-content\">\n\t\t\t\t<div class=\"page-header\">\n\t\t\t\t\t<h1>系统配置</h1>\n\t\t\t\t\t<p>配置系统全局参数和告警设置</p>\n\t\t\t\t</div>\n\n\t\t\t\t<el-tabs v-model=\"activeTab\" type=\"card\">\n\t\t\t\t\t<!-- 全局参数配置 -->\n\t\t\t\t\t<el-tab-pane label=\"全局参数\" name=\"global\">\n\t\t\t\t\t\t<div class=\"config-section\">\n\t\t\t\t\t\t\t<h3>流量控制参数</h3>\n\t\t\t\t\t\t\t<el-form\n\t\t\t\t\t\t\t\t:model=\"localGlobalConfig\"\n\t\t\t\t\t\t\t\t:rules=\"computedGlobalRules\"\n\t\t\t\t\t\t\t\tref=\"globalForm\"\n\t\t\t\t\t\t\t\tlabel-width=\"150px\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<el-row :gutter=\"20\">\n\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"默认QPS阈值\"\n\t\t\t\t\t\t\t\t\t\t\tprop=\"defaultQpsThreshold\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalGlobalConfig.defaultQpsThreshold\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"1\"\n\t\t\t\t\t\t\t\t\t\t\t\t:max=\"10000\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入默认QPS阈值\"\n\t\t\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"默认响应时间阈值\"\n\t\t\t\t\t\t\t\t\t\t\tprop=\"defaultRtThreshold\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalGlobalConfig.defaultRtThreshold\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"1\"\n\t\t\t\t\t\t\t\t\t\t\t\t:max=\"5000\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入响应时间阈值(ms)\"\n\t\t\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t</el-row>\n\n\t\t\t\t\t\t\t\t<el-row :gutter=\"20\">\n\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"统计窗口时间\"\n\t\t\t\t\t\t\t\t\t\t\tprop=\"statisticIntervalMs\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-select\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalGlobalConfig.statisticIntervalMs\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请选择统计窗口时间\"\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"1秒\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:value=\"1000\"\n\t\t\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"5秒\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:value=\"5000\"\n\t\t\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"10秒\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:value=\"10000\"\n\t\t\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"30秒\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:value=\"30000\"\n\t\t\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"1分钟\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:value=\"60000\"\n\t\t\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t\t</el-select>\n\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"熔断恢复时间\"\n\t\t\t\t\t\t\t\t\t\t\tprop=\"timeoutMs\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalGlobalConfig.timeoutMs\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"1000\"\n\t\t\t\t\t\t\t\t\t\t\t\t:max=\"300000\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入熔断恢复时间(ms)\"\n\t\t\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t</el-row>\n\n\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\tlabel=\"启用预热模式\"\n\t\t\t\t\t\t\t\t\tprop=\"warmUpEnabled\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-switch\n\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\tlocalGlobalConfig.warmUpEnabled\n\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t></el-switch>\n\t\t\t\t\t\t\t\t\t<span class=\"form-tip\"\n\t\t\t\t\t\t\t\t\t\t>启用后系统将在启动时进行预热</span\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\tlabel=\"预热时间\"\n\t\t\t\t\t\t\t\t\tprop=\"warmUpPeriodSec\"\n\t\t\t\t\t\t\t\t\tv-if=\"localGlobalConfig.warmUpEnabled\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\tlocalGlobalConfig.warmUpPeriodSec\n\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t:min=\"10\"\n\t\t\t\t\t\t\t\t\t\t:max=\"3600\"\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入预热时间(秒)\"\n\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t<el-form-item>\n\t\t\t\t\t\t\t\t\t<el-button\n\t\t\t\t\t\t\t\t\t\ttype=\"primary\"\n\t\t\t\t\t\t\t\t\t\t@click=\"handleSaveGlobalConfig\"\n\t\t\t\t\t\t\t\t\t\t:loading=\"loading.saving\"\n\t\t\t\t\t\t\t\t\t\t>保存配置</el-button\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-button @click=\"resetGlobalConfig\"\n\t\t\t\t\t\t\t\t\t\t>重置</el-button\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t</el-form>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-tab-pane>\n\n\t\t\t\t\t<!-- 告警阈值设置 -->\n\t\t\t\t\t<el-tab-pane label=\"告警设置\" name=\"alert\">\n\t\t\t\t\t\t<div class=\"config-section\">\n\t\t\t\t\t\t\t<h3>告警阈值配置</h3>\n\t\t\t\t\t\t\t<el-form\n\t\t\t\t\t\t\t\t:model=\"localAlertConfig\"\n\t\t\t\t\t\t\t\t:rules=\"computedAlertRules\"\n\t\t\t\t\t\t\t\tref=\"alertForm\"\n\t\t\t\t\t\t\t\tlabel-width=\"150px\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<el-row :gutter=\"20\">\n\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"QPS告警阈值\"\n\t\t\t\t\t\t\t\t\t\t\tprop=\"qpsWarningThreshold\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalAlertConfig.qpsWarningThreshold\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"1\"\n\t\t\t\t\t\t\t\t\t\t\t\t:max=\"10000\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"QPS警告阈值\"\n\t\t\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"QPS严重告警阈值\"\n\t\t\t\t\t\t\t\t\t\t\tprop=\"qpsCriticalThreshold\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalAlertConfig.qpsCriticalThreshold\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"1\"\n\t\t\t\t\t\t\t\t\t\t\t\t:max=\"10000\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"QPS严重告警阈值\"\n\t\t\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t</el-row>\n\n\t\t\t\t\t\t\t\t<el-row :gutter=\"20\">\n\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"响应时间告警阈值\"\n\t\t\t\t\t\t\t\t\t\t\tprop=\"rtWarningThreshold\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalAlertConfig.rtWarningThreshold\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"1\"\n\t\t\t\t\t\t\t\t\t\t\t\t:max=\"5000\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"响应时间警告阈值(ms)\"\n\t\t\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"响应时间严重告警阈值\"\n\t\t\t\t\t\t\t\t\t\t\tprop=\"rtCriticalThreshold\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalAlertConfig.rtCriticalThreshold\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"1\"\n\t\t\t\t\t\t\t\t\t\t\t\t:max=\"5000\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"响应时间严重告警阈值(ms)\"\n\t\t\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t</el-row>\n\n\t\t\t\t\t\t\t\t<el-row :gutter=\"20\">\n\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"错误率告警阈值\"\n\t\t\t\t\t\t\t\t\t\t\tprop=\"errorRateWarningThreshold\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalAlertConfig.errorRateWarningThreshold\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"0\"\n\t\t\t\t\t\t\t\t\t\t\t\t:max=\"100\"\n\t\t\t\t\t\t\t\t\t\t\t\t:precision=\"2\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"错误率警告阈值(%)\"\n\t\t\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"错误率严重告警阈值\"\n\t\t\t\t\t\t\t\t\t\t\tprop=\"errorRateCriticalThreshold\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tlocalAlertConfig.errorRateCriticalThreshold\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t:min=\"0\"\n\t\t\t\t\t\t\t\t\t\t\t\t:max=\"100\"\n\t\t\t\t\t\t\t\t\t\t\t\t:precision=\"2\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"错误率严重告警阈值(%)\"\n\t\t\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t</el-row>\n\n\t\t\t\t\t\t\t\t<el-form-item>\n\t\t\t\t\t\t\t\t\t<el-button\n\t\t\t\t\t\t\t\t\t\ttype=\"primary\"\n\t\t\t\t\t\t\t\t\t\t@click=\"handleSaveAlertConfig\"\n\t\t\t\t\t\t\t\t\t\t:loading=\"loading.saving\"\n\t\t\t\t\t\t\t\t\t\t>保存配置</el-button\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-button @click=\"resetAlertConfig\"\n\t\t\t\t\t\t\t\t\t\t>重置</el-button\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t</el-form>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-tab-pane>\n\n\t\t\t\t\t<!-- 通知方式配置 -->\n\t\t\t\t\t<el-tab-pane label=\"通知配置\" name=\"notification\">\n\t\t\t\t\t\t<div class=\"config-section\">\n\t\t\t\t\t\t\t<h3>通知方式配置</h3>\n\t\t\t\t\t\t\t<el-form\n\t\t\t\t\t\t\t\t:model=\"localNotificationConfig\"\n\t\t\t\t\t\t\t\tref=\"notificationForm\"\n\t\t\t\t\t\t\t\tlabel-width=\"150px\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<el-form-item label=\"启用邮件通知\">\n\t\t\t\t\t\t\t\t\t<el-switch\n\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.emailEnabled\n\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t></el-switch>\n\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tv-if=\"localNotificationConfig.emailEnabled\"\n\t\t\t\t\t\t\t\t\tclass=\"notification-detail\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\tlabel=\"SMTP服务器\"\n\t\t\t\t\t\t\t\t\t\tprop=\"smtpHost\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<el-input\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.smtpHost\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入SMTP服务器地址\"\n\t\t\t\t\t\t\t\t\t\t></el-input>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t\t<el-row :gutter=\"20\">\n\t\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"SMTP端口\"\n\t\t\t\t\t\t\t\t\t\t\t\tprop=\"smtpPort\"\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<el-input-number\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.smtpPort\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:min=\"1\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:max=\"65535\"\n\t\t\t\t\t\t\t\t\t\t\t\t></el-input-number>\n\t\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t\t<el-form-item label=\"启用SSL\">\n\t\t\t\t\t\t\t\t\t\t\t\t<el-switch\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.smtpSsl\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t></el-switch>\n\t\t\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t</el-row>\n\n\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\tlabel=\"发送邮箱\"\n\t\t\t\t\t\t\t\t\t\tprop=\"smtpUsername\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<el-input\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.smtpUsername\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入发送邮箱\"\n\t\t\t\t\t\t\t\t\t\t></el-input>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\tlabel=\"邮箱密码\"\n\t\t\t\t\t\t\t\t\t\tprop=\"smtpPassword\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<el-input\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.smtpPassword\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\ttype=\"password\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入邮箱密码\"\n\t\t\t\t\t\t\t\t\t\t></el-input>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t\t<el-form-item\n\t\t\t\t\t\t\t\t\t\tlabel=\"接收邮箱\"\n\t\t\t\t\t\t\t\t\t\tprop=\"recipients\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<el-input\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.recipients\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"多个邮箱用逗号分隔\"\n\t\t\t\t\t\t\t\t\t\t></el-input>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t<el-form-item label=\"启用短信通知\">\n\t\t\t\t\t\t\t\t\t<el-switch\n\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.smsEnabled\n\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t></el-switch>\n\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tv-if=\"localNotificationConfig.smsEnabled\"\n\t\t\t\t\t\t\t\t\tclass=\"notification-detail\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"短信服务商\">\n\t\t\t\t\t\t\t\t\t\t<el-select\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.smsProvider\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请选择短信服务商\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"阿里云\"\n\t\t\t\t\t\t\t\t\t\t\t\tvalue=\"aliyun\"\n\t\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"腾讯云\"\n\t\t\t\t\t\t\t\t\t\t\t\tvalue=\"tencent\"\n\t\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"华为云\"\n\t\t\t\t\t\t\t\t\t\t\t\tvalue=\"huawei\"\n\t\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t</el-select>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"AccessKey\">\n\t\t\t\t\t\t\t\t\t\t<el-input\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.smsAccessKey\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入AccessKey\"\n\t\t\t\t\t\t\t\t\t\t></el-input>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"SecretKey\">\n\t\t\t\t\t\t\t\t\t\t<el-input\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.smsSecretKey\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\ttype=\"password\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入SecretKey\"\n\t\t\t\t\t\t\t\t\t\t></el-input>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"短信模板ID\">\n\t\t\t\t\t\t\t\t\t\t<el-input\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.smsTemplateId\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入短信模板ID\"\n\t\t\t\t\t\t\t\t\t\t></el-input>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"接收手机号\">\n\t\t\t\t\t\t\t\t\t\t<el-input\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.phoneNumbers\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"多个手机号用逗号分隔\"\n\t\t\t\t\t\t\t\t\t\t></el-input>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t<el-form-item label=\"启用Webhook通知\">\n\t\t\t\t\t\t\t\t\t<el-switch\n\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.webhookEnabled\n\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t></el-switch>\n\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tv-if=\"\n\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.webhookEnabled\n\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\tclass=\"notification-detail\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"Webhook URL\">\n\t\t\t\t\t\t\t\t\t\t<el-input\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.webhookUrl\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入Webhook URL\"\n\t\t\t\t\t\t\t\t\t\t></el-input>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"请求方法\">\n\t\t\t\t\t\t\t\t\t\t<el-select\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.webhookMethod\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"POST\"\n\t\t\t\t\t\t\t\t\t\t\t\tvalue=\"POST\"\n\t\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\t\tlabel=\"PUT\"\n\t\t\t\t\t\t\t\t\t\t\t\tvalue=\"PUT\"\n\t\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t</el-select>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\n\t\t\t\t\t\t\t\t\t<el-form-item label=\"请求头\">\n\t\t\t\t\t\t\t\t\t\t<el-input\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"\n\t\t\t\t\t\t\t\t\t\t\t\tlocalNotificationConfig.webhookHeaders\n\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\ttype=\"textarea\"\n\t\t\t\t\t\t\t\t\t\t\t:rows=\"3\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder='JSON格式，如：{\"Content-Type\": \"application/json\"}'\n\t\t\t\t\t\t\t\t\t\t></el-input>\n\t\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t<el-form-item>\n\t\t\t\t\t\t\t\t\t<el-button\n\t\t\t\t\t\t\t\t\t\ttype=\"primary\"\n\t\t\t\t\t\t\t\t\t\t@click=\"handleSaveNotificationConfig\"\n\t\t\t\t\t\t\t\t\t\t:loading=\"loading.saving\"\n\t\t\t\t\t\t\t\t\t\t>保存配置</el-button\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-button @click=\"testNotification\"\n\t\t\t\t\t\t\t\t\t\t>测试通知</el-button\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-button @click=\"resetNotificationConfig\"\n\t\t\t\t\t\t\t\t\t\t>重置</el-button\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</el-form-item>\n\t\t\t\t\t\t\t</el-form>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-tab-pane>\n\n\t\t\t\t\t<!-- 系统日志 -->\n\t\t\t\t\t<el-tab-pane label=\"系统日志\" name=\"logs\">\n\t\t\t\t\t\t<div class=\"config-section\">\n\t\t\t\t\t\t\t<div class=\"logs-header\">\n\t\t\t\t\t\t\t\t<h3>系统日志查看</h3>\n\t\t\t\t\t\t\t\t<div class=\"logs-controls\">\n\t\t\t\t\t\t\t\t\t<el-select\n\t\t\t\t\t\t\t\t\t\tv-model=\"logLevel\"\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"日志级别\"\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\t\tstyle=\"width: 120px; margin-right: 10px\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"全部\"\n\t\t\t\t\t\t\t\t\t\t\tvalue=\"\"\n\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"DEBUG\"\n\t\t\t\t\t\t\t\t\t\t\tvalue=\"DEBUG\"\n\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"INFO\"\n\t\t\t\t\t\t\t\t\t\t\tvalue=\"INFO\"\n\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"WARN\"\n\t\t\t\t\t\t\t\t\t\t\tvalue=\"WARN\"\n\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t\t<el-option\n\t\t\t\t\t\t\t\t\t\t\tlabel=\"ERROR\"\n\t\t\t\t\t\t\t\t\t\t\tvalue=\"ERROR\"\n\t\t\t\t\t\t\t\t\t\t></el-option>\n\t\t\t\t\t\t\t\t\t</el-select>\n\n\t\t\t\t\t\t\t\t\t<el-date-picker\n\t\t\t\t\t\t\t\t\t\tv-model=\"logDateRange\"\n\t\t\t\t\t\t\t\t\t\ttype=\"datetimerange\"\n\t\t\t\t\t\t\t\t\t\trange-separator=\"至\"\n\t\t\t\t\t\t\t\t\t\tstart-placeholder=\"开始日期\"\n\t\t\t\t\t\t\t\t\t\tend-placeholder=\"结束日期\"\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\t\tstyle=\"margin-right: 10px\"\n\t\t\t\t\t\t\t\t\t></el-date-picker>\n\n\t\t\t\t\t\t\t\t\t<el-button size=\"small\" @click=\"searchLogs\"\n\t\t\t\t\t\t\t\t\t\t>查询</el-button\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-button\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\t\t@click=\"handleClearLogs\"\n\t\t\t\t\t\t\t\t\t\t>清空</el-button\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-button size=\"small\" @click=\"exportLogs\"\n\t\t\t\t\t\t\t\t\t\t>导出</el-button\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<div class=\"logs-content\">\n\t\t\t\t\t\t\t\t<el-table\n\t\t\t\t\t\t\t\t\t:data=\"systemLogs\"\n\t\t\t\t\t\t\t\t\tstyle=\"width: 100%\"\n\t\t\t\t\t\t\t\t\tmax-height=\"400\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<el-table-column\n\t\t\t\t\t\t\t\t\t\tprop=\"timestamp\"\n\t\t\t\t\t\t\t\t\t\tlabel=\"时间\"\n\t\t\t\t\t\t\t\t\t\twidth=\"180\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t\t\t\t\t{{\n\t\t\t\t\t\t\t\t\t\t\t\tformatTime(scope.row.timestamp)\n\t\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t</el-table-column>\n\t\t\t\t\t\t\t\t\t<el-table-column\n\t\t\t\t\t\t\t\t\t\tprop=\"level\"\n\t\t\t\t\t\t\t\t\t\tlabel=\"级别\"\n\t\t\t\t\t\t\t\t\t\twidth=\"80\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t\t\t\t\t<el-tag\n\t\t\t\t\t\t\t\t\t\t\t\t:type=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tgetLogLevelType(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tscope.row.level\n\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t{{ scope.row.level }}\n\t\t\t\t\t\t\t\t\t\t\t</el-tag>\n\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t</el-table-column>\n\t\t\t\t\t\t\t\t\t<el-table-column\n\t\t\t\t\t\t\t\t\t\tprop=\"logger\"\n\t\t\t\t\t\t\t\t\t\tlabel=\"模块\"\n\t\t\t\t\t\t\t\t\t\twidth=\"150\"\n\t\t\t\t\t\t\t\t\t></el-table-column>\n\t\t\t\t\t\t\t\t\t<el-table-column\n\t\t\t\t\t\t\t\t\t\tprop=\"message\"\n\t\t\t\t\t\t\t\t\t\tlabel=\"消息\"\n\t\t\t\t\t\t\t\t\t\tshow-overflow-tooltip\n\t\t\t\t\t\t\t\t\t></el-table-column>\n\t\t\t\t\t\t\t\t</el-table>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-tab-pane>\n\n\t\t\t\t\t<!-- Nacos配置管理 -->\n\t\t\t\t\t<el-tab-pane label=\"Nacos配置\" name=\"nacos\">\n\t\t\t\t\t\t<div class=\"config-section\">\n\t\t\t\t\t\t\t<div class=\"nacos-header\">\n\t\t\t\t\t\t\t\t<h3>Nacos配置中心管理</h3>\n\t\t\t\t\t\t\t\t<div class=\"nacos-controls\">\n\t\t\t\t\t\t\t\t\t<el-button\n\t\t\t\t\t\t\t\t\t\ttype=\"primary\"\n\t\t\t\t\t\t\t\t\t\ticon=\"el-icon-connection\"\n\t\t\t\t\t\t\t\t\t\t@click=\"testNacosConnection\"\n\t\t\t\t\t\t\t\t\t\t:loading=\"connectionTesting\"\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t测试连接\n\t\t\t\t\t\t\t\t\t</el-button>\n\t\t\t\t\t\t\t\t\t<el-button\n\t\t\t\t\t\t\t\t\t\ttype=\"success\"\n\t\t\t\t\t\t\t\t\t\ticon=\"el-icon-upload2\"\n\t\t\t\t\t\t\t\t\t\t@click=\"publishAllRulesToNacos\"\n\t\t\t\t\t\t\t\t\t\t:loading=\"publishLoading\"\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t发布所有规则\n\t\t\t\t\t\t\t\t\t</el-button>\n\t\t\t\t\t\t\t\t\t<el-button\n\t\t\t\t\t\t\t\t\t\ttype=\"info\"\n\t\t\t\t\t\t\t\t\t\ticon=\"el-icon-refresh\"\n\t\t\t\t\t\t\t\t\t\t@click=\"refreshConfigStatus\"\n\t\t\t\t\t\t\t\t\t\t:loading=\"statusLoading\"\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t刷新状态\n\t\t\t\t\t\t\t\t\t</el-button>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<!-- 连接状态 -->\n\t\t\t\t\t\t\t<el-card class=\"status-card\" shadow=\"never\">\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"card-header\">\n\t\t\t\t\t\t\t\t\t<span>连接状态</span>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class=\"status-info\">\n\t\t\t\t\t\t\t\t\t<el-row :gutter=\"20\">\n\t\t\t\t\t\t\t\t\t\t<el-col :span=\"8\">\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"status-item\">\n\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"status-label\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t>连接状态:</span\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<el-tag\n\t\t\t\t\t\t\t\t\t\t\t\t\t:type=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnacosStatus.connected\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? 'success'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: 'danger'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnacosStatus.connected\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? '已连接'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: '未连接'\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t\t<el-col :span=\"8\">\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"status-item\">\n\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"status-label\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t>服务地址:</span\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"status-value\">{{\n\t\t\t\t\t\t\t\t\t\t\t\t\tnacosStatus.serverAddr ||\n\t\t\t\t\t\t\t\t\t\t\t\t\t'-'\n\t\t\t\t\t\t\t\t\t\t\t\t}}</span>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t\t<el-col :span=\"8\">\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"status-item\">\n\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"status-label\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t>最后测试:</span\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"status-value\">{{\n\t\t\t\t\t\t\t\t\t\t\t\t\tformatTime(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tnacosStatus.testTime\n\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t}}</span>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t</el-row>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</el-card>\n\n\t\t\t\t\t\t\t<!-- 配置状态 -->\n\t\t\t\t\t\t\t<el-card class=\"config-card\" shadow=\"never\">\n\t\t\t\t\t\t\t\t<div slot=\"header\" class=\"card-header\">\n\t\t\t\t\t\t\t\t\t<span>配置状态</span>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class=\"config-info\">\n\t\t\t\t\t\t\t\t\t<el-row :gutter=\"20\">\n\t\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"config-item\">\n\t\t\t\t\t\t\t\t\t\t\t\t<h4>流量规则配置</h4>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"config-details\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"detail-label\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>规则数量:</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"detail-value\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>{{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconfigStatus.tenantFlowRulesCount ||\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t0\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}条</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"detail-label\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>最后发布:</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"detail-value\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>{{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tformatTime(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconfigStatus.tenantFlowRulesLastPublish\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"detail-label\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>发布状态:</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<el-tag\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:type=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconfigStatus.tenantFlowRulesSuccess\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? 'success'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: 'warning'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"mini\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconfigStatus.tenantFlowRulesSuccess\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? '成功'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: '待发布'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"config-actions\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<el-button\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tviewTenantFlowRulesConfig\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:loading=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tviewConfigLoading.flow\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t查看配置\n\t\t\t\t\t\t\t\t\t\t\t\t\t</el-button>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t\t<el-col :span=\"12\">\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"config-item\">\n\t\t\t\t\t\t\t\t\t\t\t\t<h4>IP规则配置</h4>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"config-details\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"detail-label\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>规则数量:</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"detail-value\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>{{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconfigStatus.ipFlowRulesCount ||\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t0\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}条</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"detail-label\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>最后发布:</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"detail-value\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>{{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tformatTime(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconfigStatus.ipFlowRulesLastPublish\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"detail-label\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>发布状态:</span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<el-tag\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:type=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconfigStatus.ipFlowRulesSuccess\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? 'success'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: 'warning'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"mini\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconfigStatus.ipFlowRulesSuccess\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? '成功'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: '待发布'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</el-tag>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"config-actions\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<el-button\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tviewIpFlowRulesConfig\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:loading=\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tviewConfigLoading.ip\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t查看配置\n\t\t\t\t\t\t\t\t\t\t\t\t\t</el-button>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</el-col>\n\t\t\t\t\t\t\t\t\t</el-row>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</el-card>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</el-tab-pane>\n\t\t\t\t</el-tabs>\n\t\t\t</div>\n\t\t</layout>\n\t</div>\n</template>\n\n<script>\nimport Layout from '../components/Layout.vue';\nimport { mapState, mapActions, mapGetters } from 'vuex';\nimport { exportSystemLogsToExcel, exportToCSV } from '../utils/export';\nimport validationRules from '../utils/validation';\n\nexport default {\n\tname: 'Config',\n\tcomponents: {\n\t\tLayout,\n\t},\n\tbeforeCreate() {\n\t\t// 注册验证规则到Vue实例\n\t\tthis.$validation = validationRules;\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tactiveTab: 'global',\n\n\t\t\t// 本地表单数据（用于编辑）\n\t\t\tlocalGlobalConfig: {},\n\t\t\tlocalAlertConfig: {},\n\t\t\tlocalNotificationConfig: {},\n\n\t\t\t// 系统日志过滤器\n\t\t\tlogLevel: '',\n\t\t\tlogDateRange: [],\n\n\t\t\t// Nacos配置相关\n\t\t\tconnectionTesting: false,\n\t\t\tpublishLoading: false,\n\t\t\tstatusLoading: false,\n\t\t\tviewConfigLoading: {\n\t\t\t\tflow: false,\n\t\t\t\tip: false,\n\t\t\t},\n\t\t\tnacosStatus: {\n\t\t\t\tconnected: false,\n\t\t\t\tserverAddr: '',\n\t\t\t\ttestTime: null,\n\t\t\t},\n\t\t\tconfigStatus: {\n\t\t\t\ttenantFlowRulesCount: 0,\n\t\t\ttenantFlowRulesSuccess: false,\n\t\t\ttenantFlowRulesLastPublish: null,\n\t\t\tipFlowRulesCount: 0,\n\t\t\tipFlowRulesSuccess: false,\n\t\t\tipFlowRulesLastPublish: null,\n\t\t\t},\n\n\t\t\t// 表单验证规则\n\t\t\tglobalRules: {},\n\t\t\talertRules: {},\n\t\t\tnotificationRules: {},\n\t\t};\n\t},\n\n\tcomputed: {\n\t\t...mapState('config', [\n\t\t\t'globalConfig',\n\t\t\t'alertThresholds',\n\t\t\t'notificationConfig',\n\t\t\t'systemLogs',\n\t\t\t'loading',\n\t\t]),\n\n\t\t...mapGetters('config', ['filteredSystemLogs']),\n\n\t\t// 动态计算全局配置验证规则\n\t\tcomputedGlobalRules() {\n\t\t\treturn {\n\t\t\t\tdefaultQpsThreshold: this.$validation.alertConfigRules.qpsWarningThreshold,\n\t\t\t\tdefaultRtThreshold: this.$validation.alertConfigRules.rtWarningThreshold,\n\t\t\t\tdefaultConcurrentThreshold: this.$validation.alertConfigRules.concurrentWarningThreshold,\n\t\t\t\twarmUpPeriodSec: this.$validation.alertConfigRules.warmUpPeriod,\n\t\t\t\tmaxQueueingTimeMs: this.$validation.alertConfigRules.queueTimeout\n\t\t\t};\n\t\t},\n\n\t\t// 动态计算告警配置验证规则\n\t\tcomputedAlertRules() {\n\t\t\treturn {\n\t\t\t\tqpsWarningThreshold: this.$validation.alertConfigRules.qpsWarningThreshold,\n\t\t\t\tqpsCriticalThreshold: this.$validation.alertConfigRules.qpsCriticalThreshold,\n\t\t\t\trtWarningThreshold: this.$validation.alertConfigRules.rtWarningThreshold,\n\t\t\t\trtCriticalThreshold: this.$validation.alertConfigRules.rtCriticalThreshold,\n\t\t\t\terrorRateWarningThreshold: this.$validation.alertConfigRules.errorRateWarningThreshold,\n\t\t\t\terrorRateCriticalThreshold: this.$validation.alertConfigRules.errorRateCriticalThreshold,\n\t\t\t\tconcurrentWarningThreshold: this.$validation.alertConfigRules.concurrentWarningThreshold,\n\t\t\t\tconcurrentCriticalThreshold: this.$validation.alertConfigRules.concurrentCriticalThreshold\n\t\t\t};\n\t\t},\n\n\t\t// 动态计算通知配置验证规则\n\t\tcomputedNotificationRules() {\n\t\t\treturn {\n\t\t\t\temailEnabled: this.$validation.notificationConfigRules.emailEnabled,\n\t\t\t\temailRecipients: this.$validation.notificationConfigRules.emailRecipients,\n\t\t\t\tsmsEnabled: this.$validation.notificationConfigRules.smsEnabled,\n\t\t\t\tsmsRecipients: this.$validation.notificationConfigRules.smsRecipients,\n\t\t\t\twebhookEnabled: this.$validation.notificationConfigRules.webhookEnabled,\n\t\t\t\twebhookUrl: this.$validation.notificationConfigRules.webhookUrl,\n\t\t\t\talertInterval: this.$validation.notificationConfigRules.alertInterval,\n\t\t\t\tmaxAlertsPerHour: this.$validation.notificationConfigRules.maxAlertsPerHour\n\t\t\t};\n\t\t},\n\n\t\t// 日志过滤器\n\t\tlogFilters() {\n\t\t\treturn {\n\t\t\t\tlevel: this.logLevel,\n\t\t\t\tstartDate:\n\t\t\t\t\tthis.logDateRange && this.logDateRange[0]\n\t\t\t\t\t\t? this.logDateRange[0]\n\t\t\t\t\t\t: null,\n\t\t\t\tendDate:\n\t\t\t\t\tthis.logDateRange && this.logDateRange[1]\n\t\t\t\t\t\t? this.logDateRange[1]\n\t\t\t\t\t\t: null,\n\t\t\t\tkeyword: '',\n\t\t\t};\n\t\t},\n\n\t\t// 过滤后的系统日志\n\t\tfilteredLogs() {\n\t\t\treturn this.filteredSystemLogs(this.logFilters);\n\t\t},\n\t},\n\n\tasync mounted() {\n\t\tawait this.loadConfig();\n\t\tawait this.loadSystemLogs();\n\n\t\t// 初始化本地表单数据\n\t\tthis.localGlobalConfig = { ...this.globalConfig };\n\t\tthis.localAlertConfig = { ...this.alertThresholds };\n\t\tthis.localNotificationConfig = JSON.parse(\n\t\t\tJSON.stringify(this.notificationConfig)\n\t\t);\n\n\t\t// 如果当前标签是Nacos，则初始化Nacos状态\n\t\tif (this.activeTab === 'nacos') {\n\t\t\tawait this.initNacosStatus();\n\t\t}\n\t},\n\n\tmethods: {\n\t\t...mapActions('config', [\n\t\t\t'loadConfig',\n\t\t\t'saveGlobalConfig',\n\t\t\t'saveAlertThresholds',\n\t\t\t'saveNotificationConfig',\n\t\t\t'testEmailNotification',\n\t\t\t'testSmsNotification',\n\t\t\t'testWebhookNotification',\n\t\t\t'loadSystemLogs',\n\t\t\t'clearSystemLogs',\n\t\t]),\n\t\t// 加载配置\n\t\tloadConfigs() {\n\t\t\t// 这里应该调用API加载配置\n\t\t\t// 暂时使用本地存储\n\t\t\tconst savedGlobalConfig = localStorage.getItem('globalConfig');\n\t\t\tif (savedGlobalConfig) {\n\t\t\t\tthis.globalConfig = {\n\t\t\t\t\t...this.globalConfig,\n\t\t\t\t\t...JSON.parse(savedGlobalConfig),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tconst savedAlertConfig = localStorage.getItem('alertConfig');\n\t\t\tif (savedAlertConfig) {\n\t\t\t\tthis.alertConfig = {\n\t\t\t\t\t...this.alertConfig,\n\t\t\t\t\t...JSON.parse(savedAlertConfig),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tconst savedNotificationConfig =\n\t\t\t\tlocalStorage.getItem('notificationConfig');\n\t\t\tif (savedNotificationConfig) {\n\t\t\t\tthis.notificationConfig = {\n\t\t\t\t\t...this.notificationConfig,\n\t\t\t\t\t...JSON.parse(savedNotificationConfig),\n\t\t\t\t};\n\t\t\t}\n\t\t},\n\n\t\t// 保存全局配置\n\t\tasync handleSaveGlobalConfig() {\n\t\t\tthis.$refs.globalForm.validate(async (valid) => {\n\t\t\t\tif (valid) {\n\t\t\t\t\tconst result = await this.saveGlobalConfig(\n\t\t\t\t\t\tthis.localGlobalConfig\n\t\t\t\t\t);\n\t\t\t\t\tif (result.success) {\n\t\t\t\t\t\tthis.$message.success(result.message);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$message.error(result.message);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 重置全局配置\n\t\tresetGlobalConfig() {\n\t\t\tthis.localGlobalConfig = {\n\t\t\t\tdefaultQpsThreshold: 1000,\n\t\t\t\tdefaultRtThreshold: 3000,\n\t\t\t\tstatisticIntervalMs: 1000,\n\t\t\t\ttimeoutMs: 5000,\n\t\t\t\twarmUpEnabled: false,\n\t\t\t\twarmUpPeriodSec: 10,\n\t\t\t};\n\t\t\tthis.$message.info('全局配置已重置');\n\t\t},\n\n\t\t// 保存告警配置\n\t\tasync handleSaveAlertConfig() {\n\t\t\tthis.$refs.alertForm.validate(async (valid) => {\n\t\t\t\tif (valid) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 调用API保存告警阈值配置\n\t\t\t\t\t\tawait this.$api.alertRules.create(this.localAlertConfig);\n\t\t\t\t\t\t// 同时调用Vuex action\n\t\t\t\t\t\tconst result = await this.saveAlertThresholds(\n\t\t\t\t\t\t\tthis.localAlertConfig\n\t\t\t\t\t\t);\n\t\t\t\t\t\tif (result.success) {\n\t\t\t\t\t\t\tthis.$message.success(result.message);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.$message.error(result.message);\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('保存告警配置失败:', error);\n\t\t\t\t\t\tthis.$message.error('保存告警配置失败: ' + (error.message || '未知错误'));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 重置告警配置\n\t\tresetAlertConfig() {\n\t\t\tthis.localAlertConfig = {\n\t\t\t\tqpsWarningThreshold: 800,\n\t\t\t\tqpsCriticalThreshold: 1000,\n\t\t\t\trtWarningThreshold: 2000,\n\t\t\t\trtCriticalThreshold: 3000,\n\t\t\t\terrorRateWarningThreshold: 5.0,\n\t\t\t\terrorRateCriticalThreshold: 10.0,\n\t\t\t};\n\t\t\tthis.$message.info('告警配置已重置');\n\t\t},\n\n\t\t// 保存通知配置\n\t\tasync handleSaveNotificationConfig() {\n\t\t\ttry {\n\t\t\t\t// 调用API保存通知配置\n\t\t\t\tawait this.$api.alertNotifications.create(this.localNotificationConfig);\n\t\t\t\t// 同时调用Vuex action\n\t\t\t\tconst result = await this.saveNotificationConfig(\n\t\t\t\t\tthis.localNotificationConfig\n\t\t\t\t);\n\t\t\t\tif (result.success) {\n\t\t\t\t\tthis.$message.success(result.message);\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error(result.message);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('保存通知配置失败:', error);\n\t\t\t\tthis.$message.error('保存通知配置失败: ' + (error.message || '未知错误'));\n\t\t\t}\n\t\t},\n\n\t\t// 测试通知\n\t\tasync testNotification() {\n\t\t\tthis.$message.info('正在发送测试通知...');\n\n\t\t\ttry {\n\t\t\t\tconst promises = [];\n\t\t\t\tif (this.localNotificationConfig.emailEnabled) {\n\t\t\t\t\t// 调用API测试邮件通知\n\t\t\t\t\tpromises.push(this.$api.alertNotifications.testEmail(this.localNotificationConfig.email));\n\t\t\t\t\t// 同时调用Vuex action\n\t\t\t\t\tpromises.push(this.testEmailNotification());\n\t\t\t\t}\n\t\t\t\tif (this.localNotificationConfig.smsEnabled) {\n\t\t\t\t\t// 调用API测试短信通知\n\t\t\t\t\tpromises.push(this.$api.alertNotifications.testSms(this.localNotificationConfig.sms));\n\t\t\t\t\t// 同时调用Vuex action\n\t\t\t\t\tpromises.push(this.testSmsNotification());\n\t\t\t\t}\n\t\t\t\tif (this.localNotificationConfig.webhookEnabled) {\n\t\t\t\t\t// 调用API测试Webhook通知\n\t\t\t\t\tpromises.push(this.$api.alertNotifications.testWebhook(this.localNotificationConfig.webhook));\n\t\t\t\t\t// 同时调用Vuex action\n\t\t\t\t\tpromises.push(this.testWebhookNotification());\n\t\t\t\t}\n\n\t\t\t\tawait Promise.all(promises);\n\t\t\t\tthis.$message.success('测试通知发送成功');\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('测试通知失败:', error);\n\t\t\t\tthis.$message.error('测试通知发送失败: ' + (error.message || '未知错误'));\n\t\t\t}\n\t\t},\n\n\t\t// 重置通知配置\n\t\tresetNotificationConfig() {\n\t\t\tthis.localNotificationConfig = {\n\t\t\t\temailEnabled: false,\n\t\t\t\tsmtpHost: '',\n\t\t\t\tsmtpPort: 587,\n\t\t\t\tsmtpSsl: true,\n\t\t\t\tsmtpUsername: '',\n\t\t\t\tsmtpPassword: '',\n\t\t\t\trecipients: '',\n\t\t\t\tsmsEnabled: false,\n\t\t\t\tsmsProvider: 'aliyun',\n\t\t\t\tsmsAccessKey: '',\n\t\t\t\tsmsSecretKey: '',\n\t\t\t\tsmsTemplateId: '',\n\t\t\t\tphoneNumbers: '',\n\t\t\t\twebhookEnabled: false,\n\t\t\t\twebhookUrl: '',\n\t\t\t\twebhookMethod: 'POST',\n\t\t\t\twebhookHeaders: '',\n\t\t\t};\n\t\t\tthis.$message.info('通知配置已重置');\n\t\t},\n\n\t\t// 搜索日志\n\t\tasync searchLogs() {\n\t\t\ttry {\n\t\t\t\t// 调用API搜索系统日志\n\t\t\t\tconst response = await this.$api.systemLogs.list(this.logFilters);\n\t\t\t\t// 同时调用Vuex action\n\t\t\t\tawait this.loadSystemLogs(this.logFilters);\n\t\t\t\tthis.$message.success('日志搜索完成');\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('搜索日志失败:', error);\n\t\t\t\tthis.$message.error('日志搜索失败: ' + (error.message || '未知错误'));\n\t\t\t}\n\t\t},\n\n\t\t// 清空日志\n\t\tasync handleClearLogs() {\n\t\t\tthis.$confirm('确定要清空所有日志吗？', '提示', {\n\t\t\t\tconfirmButtonText: '确定',\n\t\t\t\tcancelButtonText: '取消',\n\t\t\t\ttype: 'warning',\n\t\t\t}).then(async () => {\n\t\t\t\ttry {\n\t\t\t\t\t// 调用API清空系统日志\n\t\t\t\t\tawait this.$api.systemLogs.clear();\n\t\t\t\t\t// 同时调用Vuex action\n\t\t\t\t\tconst result = await this.clearSystemLogs();\n\t\t\t\t\tif (result.success) {\n\t\t\t\t\t\tthis.$message.success(result.message);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$message.error(result.message);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('清空日志失败:', error);\n\t\t\t\t\tthis.$message.error('清空日志失败: ' + (error.message || '未知错误'));\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 导出日志\n\t\tasync exportLogs() {\n\t\t\tif (this.filteredLogs.length === 0) {\n\t\t\t\tthis.$message.warning('暂无日志数据可导出');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\t// 调用API导出系统日志\n\t\t\t\tconst response = await this.$api.systemLogs.export(this.logFilters);\n\t\t\t\t// 处理文件下载\n\t\t\t\tconst blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\n\t\t\t\tconst url = window.URL.createObjectURL(blob);\n\t\t\t\tconst link = document.createElement('a');\n\t\t\t\tlink.href = url;\n\t\t\t\tlink.download = `系统日志_${new Date().toISOString().slice(0, 10)}.xlsx`;\n\t\t\t\tlink.click();\n\t\t\t\twindow.URL.revokeObjectURL(url);\n\t\t\t\tthis.$message.success('日志导出成功');\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('导出日志失败:', error);\n\t\t\t\t// 降级到本地导出\n\t\t\t\tconst success = exportSystemLogsToExcel(\n\t\t\t\t\tthis.filteredLogs,\n\t\t\t\t\t'系统日志'\n\t\t\t\t);\n\t\t\t\tif (success) {\n\t\t\t\t\tthis.$message.success('日志导出成功（本地导出）');\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error('日志导出失败');\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// 格式化时间\n\t\tformatTime(timestamp) {\n\t\t\tif (!timestamp) return '-';\n\t\t\t// 统一使用 YYYY-MM-DD HH:mm:ss 格式\n\t\t\tconst date = new Date(timestamp);\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\n\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\n\t\t\tconst seconds = String(date.getSeconds()).padStart(2, '0');\n\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n\t\t},\n\n\t\t// 获取日志级别类型\n\t\tgetLogLevelType(level) {\n\t\t\tconst types = {\n\t\t\t\tDEBUG: '',\n\t\t\t\tINFO: 'success',\n\t\t\t\tWARN: 'warning',\n\t\t\t\tERROR: 'danger',\n\t\t\t};\n\t\t\treturn types[level] || '';\n\t\t},\n\n\t\t// 添加邮件接收者\n\t\taddEmailRecipient() {\n\t\t\tthis.localNotificationConfig.email.recipients.push('');\n\t\t},\n\n\t\t// 删除邮件接收者\n\t\tremoveEmailRecipient(index) {\n\t\t\tthis.localNotificationConfig.email.recipients.splice(index, 1);\n\t\t},\n\n\t\t// 添加短信号码\n\t\taddPhoneNumber() {\n\t\t\tthis.localNotificationConfig.sms.phoneNumbers.push('');\n\t\t},\n\n\t\t// 删除短信号码\n\t\tremovePhoneNumber(index) {\n\t\t\tthis.localNotificationConfig.sms.phoneNumbers.splice(index, 1);\n\t\t},\n\n\t\t// Nacos配置相关方法\n\t\tasync testNacosConnection() {\n\t\t\ttry {\n\t\t\t\tthis.connectionTesting = true;\n\t\t\t\tconst result = await this.$api.nacosConfig.testConnection();\n\n\t\t\t\tthis.nacosStatus = {\n\t\t\t\t\tconnected: result.data.connected,\n\t\t\t\t\tserverAddr: result.data.serverAddr,\n\t\t\t\t\ttestTime: result.data.testTime,\n\t\t\t\t};\n\n\t\t\t\tif (result.data.connected) {\n\t\t\t\t\tthis.$message.success('Nacos连接测试成功');\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.error('Nacos连接测试失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('测试Nacos连接失败:', error);\n\t\t\t\tthis.$message.error(\n\t\t\t\t\t'测试Nacos连接失败: ' + (error.message || '未知错误')\n\t\t\t\t);\n\t\t\t\tthis.nacosStatus.connected = false;\n\t\t\t} finally {\n\t\t\t\tthis.connectionTesting = false;\n\t\t\t}\n\t\t},\n\n\t\tasync publishAllRulesToNacos() {\n\t\t\ttry {\n\t\t\t\tthis.publishLoading = true;\n\n\t\t\t\t// 先测试连接\n\t\t\t\tconst connectionResult =\n\t\t\t\t\tawait this.$api.nacosConfig.testConnection();\n\t\t\t\tif (!connectionResult.data.connected) {\n\t\t\t\t\tthis.$message.error('Nacos连接失败，请检查配置');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 发布规则\n\t\t\t\tconst result = await this.$api.nacosConfig.publishAllRules();\n\n\t\t\t\t// 更新配置状态\n\t\t\t\tthis.configStatus = {\n\t\t\t\t\ttenantFlowRulesCount: result.data.tenantFlowRulesCount,\n\t\t\t\ttenantFlowRulesSuccess: result.data.tenantFlowRulesSuccess,\n\t\t\t\ttenantFlowRulesLastPublish: new Date().toISOString(),\n\t\t\t\tipFlowRulesCount: result.data.ipFlowRulesCount,\n\t\t\t\tipFlowRulesSuccess: result.data.ipFlowRulesSuccess,\n\t\t\t\tipFlowRulesLastPublish: new Date().toISOString(),\n\t\t\t\t};\n\n\t\t\t\tif (result.data.allSuccess) {\n\t\t\t\t\tthis.$message.success(\n\t\t\t\t\t\t`发布成功！租户流量规则: ${result.data.tenantFlowRulesCount}条，IP流量规则: ${result.data.ipFlowRulesCount}条`\n\t\t\t\t\t);\n\t\t\t\t} else {\n\t\t\t\t\tthis.$message.warning('部分规则发布失败，请查看详细信息');\n\t\t\t\t}\n\n\t\t\t\t// 显示详细结果\n\t\t\t\tthis.$notify({\n\t\t\t\t\ttitle: 'Nacos发布结果',\n\t\t\t\t\tmessage: `\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<p>租户流量规则: ${result.data.tenantFlowRulesCount}条 ${\n\t\t\t\t\t\tresult.data.tenantFlowRulesSuccess ? '✓' : '✗'\n\t\t\t\t\t}</p>\n\t\t\t\t\t<p>IP流量规则: ${result.data.ipFlowRulesCount}条 ${\n\t\t\t\t\t\tresult.data.ipFlowRulesSuccess ? '✓' : '✗'\n\t\t\t\t\t}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t`,\n\t\t\t\t\tdangerouslyUseHTMLString: true,\n\t\t\t\t\ttype: result.data.allSuccess ? 'success' : 'warning',\n\t\t\t\t\tduration: 5000,\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('发布到Nacos失败:', error);\n\t\t\t\tthis.$message.error(\n\t\t\t\t\t'发布到Nacos失败: ' + (error.message || '未知错误')\n\t\t\t\t);\n\t\t\t} finally {\n\t\t\t\tthis.publishLoading = false;\n\t\t\t}\n\t\t},\n\n\t\tasync refreshConfigStatus() {\n\t\t\ttry {\n\t\t\t\tthis.statusLoading = true;\n\t\t\t\tconst result = await this.$api.nacosConfig.getConfigStatus();\n\n\t\t\t\tthis.configStatus = {\n\t\t\t\t\t...this.configStatus,\n\t\t\t\t\t...result.data,\n\t\t\t\t};\n\n\t\t\t\tthis.$message.success('配置状态已刷新');\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('刷新配置状态失败:', error);\n\t\t\t\tthis.$message.error(\n\t\t\t\t\t'刷新配置状态失败: ' + (error.message || '未知错误')\n\t\t\t\t);\n\t\t\t} finally {\n\t\t\t\tthis.statusLoading = false;\n\t\t\t}\n\t\t},\n\n\t\tasync viewTenantFlowRulesConfig() {\n\t\t\ttry {\n\t\t\t\tthis.viewConfigLoading.flow = true;\n\t\t\t\tconst result = await this.$api.nacosConfig.getTenantFlowRulesConfig();\n\n\t\t\t\t// 显示配置内容对话框\n\t\t\t\tthis.$alert(result.data, '流量规则配置', {\n\t\t\t\t\tconfirmButtonText: '确定',\n\t\t\t\t\ttype: 'info',\n\t\t\t\t\tcustomClass: 'config-dialog',\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取流量规则配置失败:', error);\n\t\t\t\tthis.$message.error(\n\t\t\t\t\t'获取流量规则配置失败: ' + (error.message || '未知错误')\n\t\t\t\t);\n\t\t\t} finally {\n\t\t\t\tthis.viewConfigLoading.flow = false;\n\t\t\t}\n\t\t},\n\n\t\tasync viewIpFlowRulesConfig() {\n\t\t\ttry {\n\t\t\t\tthis.viewConfigLoading.ip = true;\n\t\t\t\tconst result = await this.$api.nacosConfig.getIpFlowRulesConfig();\n\n\t\t\t\t// 显示配置内容对话框\n\t\t\t\tthis.$alert(result.data, 'IP规则配置', {\n\t\t\t\t\tconfirmButtonText: '确定',\n\t\t\t\t\ttype: 'info',\n\t\t\t\t\tcustomClass: 'config-dialog',\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取IP规则配置失败:', error);\n\t\t\t\tthis.$message.error(\n\t\t\t\t\t'获取IP规则配置失败: ' + (error.message || '未知错误')\n\t\t\t\t);\n\t\t\t} finally {\n\t\t\t\tthis.viewConfigLoading.ip = false;\n\t\t\t}\n\t\t},\n\n\t\tasync initNacosStatus() {\n\t\t\t// 初始化时自动测试连接和获取状态\n\t\t\tawait this.testNacosConnection();\n\t\t\tawait this.refreshConfigStatus();\n\t\t},\n\t},\n\n\twatch: {\n\t\tactiveTab(newTab) {\n\t\t\tif (newTab === 'nacos') {\n\t\t\t\tthis.initNacosStatus();\n\t\t\t}\n\t\t},\n\t},\n};\n</script>\n\n<style scoped>\n.config-content {\n\tpadding: 20px;\n}\n\n.page-header {\n\tmargin-bottom: 30px;\n}\n\n.page-header h1 {\n\tcolor: #333;\n\tmargin-bottom: 5px;\n}\n\n.page-header p {\n\tcolor: #666;\n\tfont-size: 14px;\n}\n\n.config-section {\n\tbackground: white;\n\tpadding: 20px;\n\tborder-radius: 8px;\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.config-section h3 {\n\tcolor: #333;\n\tmargin-bottom: 20px;\n\tfont-size: 16px;\n}\n\n.form-tip {\n\tcolor: #666;\n\tfont-size: 12px;\n\tmargin-left: 10px;\n}\n\n.notification-detail {\n\tbackground: #f8f9fa;\n\tpadding: 15px;\n\tborder-radius: 4px;\n\tmargin: 15px 0;\n}\n\n.logs-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20px;\n}\n\n.logs-header h3 {\n\tcolor: #333;\n\tmargin: 0;\n\tfont-size: 16px;\n}\n\n.logs-controls {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.logs-content {\n\tbackground: #f8f9fa;\n\tborder-radius: 4px;\n\tpadding: 10px;\n}\n\n/* Nacos配置相关样式 */\n.nacos-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20px;\n}\n\n.nacos-controls {\n\tdisplay: flex;\n\tgap: 10px;\n}\n\n.status-card,\n.config-card {\n\tmargin-bottom: 20px;\n}\n\n.card-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.status-info,\n.config-info {\n\tpadding: 10px 0;\n}\n\n.status-item {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 10px;\n}\n\n.status-label {\n\tfont-weight: 500;\n\tmargin-right: 8px;\n\tcolor: #666;\n}\n\n.status-value {\n\tcolor: #333;\n}\n\n.config-item {\n\tborder: 1px solid #e6e6e6;\n\tborder-radius: 6px;\n\tpadding: 16px;\n\tbackground: #fafafa;\n}\n\n.config-item h4 {\n\tmargin: 0 0 12px 0;\n\tcolor: #333;\n\tfont-size: 16px;\n}\n\n.config-details p {\n\tmargin: 8px 0;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.detail-label {\n\tfont-weight: 500;\n\tmargin-right: 8px;\n\tcolor: #666;\n\tmin-width: 80px;\n}\n\n.detail-value {\n\tcolor: #333;\n}\n\n.config-actions {\n\tmargin-top: 12px;\n\ttext-align: right;\n}\n\n/* 配置对话框样式 */\n.config-dialog .el-message-box__content {\n\tmax-height: 400px;\n\toverflow-y: auto;\n\twhite-space: pre-wrap;\n\tfont-family: 'Courier New', monospace;\n\tfont-size: 12px;\n\tbackground: #f5f5f5;\n\tpadding: 15px;\n\tborder-radius: 4px;\n}\n</style>\n", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\Statistics.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\auth.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\theme.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\monitor.js", ["214", "215"], "const state = {\n  realTimeData: {\n    qps: 0,\n    rt: 0,\n    successRate: 0,\n    errorRate: 0\n  },\n  historicalData: [],\n  alerts: [],\n  loading: false\n}\n\nconst mutations = {\n  SET_REAL_TIME_DATA(state, data) {\n    state.realTimeData = { ...state.realTimeData, ...data }\n  },\n  SET_HISTORICAL_DATA(state, data) {\n    state.historicalData = data\n  },\n  ADD_HISTORICAL_DATA(state, data) {\n    state.historicalData.push(data)\n    // 保持最近1000条记录\n    if (state.historicalData.length > 1000) {\n      state.historicalData.shift()\n    }\n  },\n  SET_ALERTS(state, alerts) {\n    state.alerts = alerts\n  },\n  ADD_ALERT(state, alert) {\n    state.alerts.unshift(alert)\n    // 保持最近100条告警\n    if (state.alerts.length > 100) {\n      state.alerts.pop()\n    }\n  },\n  CLEAR_ALERTS(state) {\n    state.alerts = []\n  },\n  SET_LOADING(state, loading) {\n    state.loading = loading\n  }\n}\n\nconst actions = {\n  async fetchRealTimeData({ commit }) {\n    try {\n      // 调用真实的API获取实时数据\n      // const response = await api.monitor.getRealTimeData()\n      // const latestData = response.data[response.data.length - 1]\n      \n      // 临时使用模拟数据，等待后端API完成\n      const data = {\n        qps: Math.floor(Math.random() * 1000),\n        rt: Math.floor(Math.random() * 100),\n        successRate: 95 + Math.random() * 5,\n        errorRate: Math.random() * 5\n      }\n      \n      commit('SET_REAL_TIME_DATA', data)\n      commit('ADD_HISTORICAL_DATA', {\n        timestamp: Date.now(),\n        ...data\n      })\n    } catch (error) {\n      console.error('获取实时数据失败:', error)\n      // 发生错误时使用默认值\n      commit('SET_REAL_TIME_DATA', {\n        qps: 0,\n        rt: 0,\n        successRate: 0,\n        errorRate: 0\n      })\n    }\n  },\n  async fetchHistoricalData({ commit }, { startTime, endTime }) {\n    // eslint-disable-next-line no-unused-vars\n    commit('SET_LOADING', true)\n    try {\n      // 调用真实的API获取历史数据\n      // const response = await api.monitor.getHistoryData({\n      //   startTime,\n      //   endTime\n      // })\n      // commit('SET_HISTORICAL_DATA', response.data)\n      \n      // 临时使用空数据\n      const data = []\n      commit('SET_HISTORICAL_DATA', data)\n    } catch (error) {\n      console.error('获取历史数据失败:', error)\n      commit('SET_HISTORICAL_DATA', [])\n    } finally {\n      commit('SET_LOADING', false)\n    }\n  },\n  async fetchAlerts({ commit }) {\n    try {\n      // 调用真实的API获取告警信息\n      // const response = await api.monitor.getAlerts({\n      //   page: 1,\n      //   size: 10,\n      //   status: 'unread'\n      // })\n      // commit('SET_ALERTS', response.data.list)\n      \n      // 临时使用模拟告警数据\n      const alerts = [\n        {\n          id: 1,\n          level: '警告',\n          message: 'QPS超过阈值1000',\n          time: Date.now() - 300000,\n          status: 'unread'\n        },\n        {\n          id: 2,\n          level: '信息',\n          message: '系统运行正常',\n          time: Date.now() - 600000,\n          status: 'read'\n        }\n      ]\n      commit('SET_ALERTS', alerts)\n    } catch (error) {\n      console.error('获取告警信息失败:', error)\n      commit('SET_ALERTS', [])\n    }\n  }\n}\n\nconst getters = {\n  realTimeData: state => state.realTimeData,\n  historicalData: state => state.historicalData,\n  alerts: state => state.alerts,\n  isLoading: state => state.loading\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions,\n  getters\n}", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\language.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\i18n\\locales\\zh-CN.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\config.js", ["216", "217", "218", "219", "220", "221", "222"], "// 系统配置模块\nconst state = {\n  // 全局参数配置\n  globalConfig: {\n    maxQps: 1000,\n    maxResponseTime: 3000,\n    statisticInterval: 60,\n    timeout: 5000,\n    warmUpPeriod: 10,\n    coldFactor: 3\n  },\n  \n  // 告警阈值配置\n  alertThresholds: {\n    qpsThreshold: 800,\n    responseTimeThreshold: 2000,\n    errorRateThreshold: 5.0,\n    enableQpsAlert: true,\n    enableRtAlert: true,\n    enableErrorRateAlert: true\n  },\n  \n  // 通知配置\n  notificationConfig: {\n    email: {\n      enabled: false,\n      smtpServer: '',\n      smtpPort: 587,\n      username: '',\n      password: '',\n      recipients: [],\n      enableSsl: true\n    },\n    sms: {\n      enabled: false,\n      provider: 'aliyun',\n      accessKey: '',\n      secretKey: '',\n      signName: '',\n      templateCode: '',\n      phoneNumbers: []\n    },\n    webhook: {\n      enabled: false,\n      url: '',\n      method: 'POST',\n      headers: {},\n      timeout: 10000\n    }\n  },\n  \n  // 系统日志配置\n  logConfig: {\n    logLevel: 'INFO',\n    maxLogSize: 100,\n    retentionDays: 30,\n    enableFileLog: true,\n    enableConsoleLog: true\n  },\n  \n  // 系统日志数据\n  systemLogs: [],\n  \n  // 加载状态\n  loading: {\n    config: false,\n    logs: false,\n    saving: false,\n    testing: false\n  }\n}\n\nconst mutations = {\n  // 设置全局配置\n  SET_GLOBAL_CONFIG(state, config) {\n    state.globalConfig = { ...state.globalConfig, ...config }\n  },\n  \n  // 设置告警阈值\n  SET_ALERT_THRESHOLDS(state, thresholds) {\n    state.alertThresholds = { ...state.alertThresholds, ...thresholds }\n  },\n  \n  // 设置通知配置\n  SET_NOTIFICATION_CONFIG(state, config) {\n    state.notificationConfig = { ...state.notificationConfig, ...config }\n  },\n  \n  // 设置邮件配置\n  SET_EMAIL_CONFIG(state, emailConfig) {\n    state.notificationConfig.email = { ...state.notificationConfig.email, ...emailConfig }\n  },\n  \n  // 设置短信配置\n  SET_SMS_CONFIG(state, smsConfig) {\n    state.notificationConfig.sms = { ...state.notificationConfig.sms, ...smsConfig }\n  },\n  \n  // 设置Webhook配置\n  SET_WEBHOOK_CONFIG(state, webhookConfig) {\n    state.notificationConfig.webhook = { ...state.notificationConfig.webhook, ...webhookConfig }\n  },\n  \n  // 设置日志配置\n  SET_LOG_CONFIG(state, logConfig) {\n    state.logConfig = { ...state.logConfig, ...logConfig }\n  },\n  \n  // 设置系统日志\n  SET_SYSTEM_LOGS(state, logs) {\n    state.systemLogs = logs\n  },\n  \n  // 添加系统日志\n  ADD_SYSTEM_LOG(state, log) {\n    state.systemLogs.unshift(log)\n    // 限制日志数量\n    if (state.systemLogs.length > state.logConfig.maxLogSize) {\n      state.systemLogs = state.systemLogs.slice(0, state.logConfig.maxLogSize)\n    }\n  },\n  \n  // 清空系统日志\n  CLEAR_SYSTEM_LOGS(state) {\n    state.systemLogs = []\n  },\n  \n  // 设置加载状态\n  SET_LOADING(state, { type, status }) {\n    state.loading[type] = status\n  }\n}\n\nconst actions = {\n  // 加载系统配置\n  async loadConfig({ commit }) {\n    commit('SET_LOADING', { type: 'config', status: true })\n    try {\n      // TODO: 调用API获取配置\n      // const response = await api.getSystemConfig()\n      // commit('SET_GLOBAL_CONFIG', response.data.globalConfig)\n      // commit('SET_ALERT_THRESHOLDS', response.data.alertThresholds)\n      // commit('SET_NOTIFICATION_CONFIG', response.data.notificationConfig)\n      // commit('SET_LOG_CONFIG', response.data.logConfig)\n      \n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // 使用默认配置\n      console.log('配置加载完成')\n    } catch (error) {\n      console.error('加载配置失败:', error)\n      throw error\n    } finally {\n      commit('SET_LOADING', { type: 'config', status: false })\n    }\n  },\n  \n  // 保存全局配置\n  async saveGlobalConfig({ commit }, config) {\n    commit('SET_LOADING', { type: 'saving', status: true })\n    try {\n      // TODO: 调用API保存配置\n      // await api.saveGlobalConfig(config)\n      \n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      commit('SET_GLOBAL_CONFIG', config)\n      return { success: true, message: '全局配置保存成功' }\n    } catch (error) {\n      console.error('保存全局配置失败:', error)\n      return { success: false, message: '保存失败: ' + error.message }\n    } finally {\n      commit('SET_LOADING', { type: 'saving', status: false })\n    }\n  },\n  \n  // 保存告警阈值\n  async saveAlertThresholds({ commit }, thresholds) {\n    commit('SET_LOADING', { type: 'saving', status: true })\n    try {\n      // TODO: 调用API保存告警阈值\n      // await api.saveAlertThresholds(thresholds)\n      \n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      commit('SET_ALERT_THRESHOLDS', thresholds)\n      return { success: true, message: '告警阈值保存成功' }\n    } catch (error) {\n      console.error('保存告警阈值失败:', error)\n      return { success: false, message: '保存失败: ' + error.message }\n    } finally {\n      commit('SET_LOADING', { type: 'saving', status: false })\n    }\n  },\n  \n  // 保存通知配置\n  async saveNotificationConfig({ commit }, config) {\n    commit('SET_LOADING', { type: 'saving', status: true })\n    try {\n      // TODO: 调用API保存通知配置\n      // await api.saveNotificationConfig(config)\n      \n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      commit('SET_NOTIFICATION_CONFIG', config)\n      return { success: true, message: '通知配置保存成功' }\n    } catch (error) {\n      console.error('保存通知配置失败:', error)\n      return { success: false, message: '保存失败: ' + error.message }\n    } finally {\n      commit('SET_LOADING', { type: 'saving', status: false })\n    }\n  },\n  \n  // 测试邮件通知\n  async testEmailNotification({ commit, state }) {\n    commit('SET_LOADING', { type: 'testing', status: true })\n    try {\n      // TODO: 调用API测试邮件\n      // await api.testEmailNotification(state.notificationConfig.email)\n      \n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      return { success: true, message: '邮件测试成功' }\n    } catch (error) {\n      console.error('邮件测试失败:', error)\n      return { success: false, message: '测试失败: ' + error.message }\n    } finally {\n      commit('SET_LOADING', { type: 'testing', status: false })\n    }\n  },\n  \n  // 测试短信通知\n  async testSmsNotification({ commit, state }) {\n    commit('SET_LOADING', { type: 'testing', status: true })\n    try {\n      // TODO: 调用API测试短信\n      // await api.testSmsNotification(state.notificationConfig.sms)\n      \n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      return { success: true, message: '短信测试成功' }\n    } catch (error) {\n      console.error('短信测试失败:', error)\n      return { success: false, message: '测试失败: ' + error.message }\n    } finally {\n      commit('SET_LOADING', { type: 'testing', status: false })\n    }\n  },\n  \n  // 测试Webhook通知\n  async testWebhookNotification({ commit, state }) {\n    commit('SET_LOADING', { type: 'testing', status: true })\n    try {\n      // TODO: 调用API测试Webhook\n      // await api.testWebhookNotification(state.notificationConfig.webhook)\n      \n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      return { success: true, message: 'Webhook测试成功' }\n    } catch (error) {\n      console.error('Webhook测试失败:', error)\n      return { success: false, message: '测试失败: ' + error.message }\n    } finally {\n      commit('SET_LOADING', { type: 'testing', status: false })\n    }\n  },\n  \n  // 加载系统日志\n  async loadSystemLogs({ commit }, { level, startDate, endDate, keyword } = {}) {\n    commit('SET_LOADING', { type: 'logs', status: true })\n    try {\n      // TODO: 调用API获取系统日志\n      // const response = await api.getSystemLogs({ level, startDate, endDate, keyword })\n      // commit('SET_SYSTEM_LOGS', response.data)\n      \n      // 模拟API调用和数据\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      const mockLogs = generateMockSystemLogs(50)\n      commit('SET_SYSTEM_LOGS', mockLogs)\n      \n      return mockLogs\n    } catch (error) {\n      console.error('加载系统日志失败:', error)\n      throw error\n    } finally {\n      commit('SET_LOADING', { type: 'logs', status: false })\n    }\n  },\n  \n  // 清空系统日志\n  async clearSystemLogs({ commit }) {\n    try {\n      // TODO: 调用API清空日志\n      // await api.clearSystemLogs()\n      \n      commit('CLEAR_SYSTEM_LOGS')\n      return { success: true, message: '日志清空成功' }\n    } catch (error) {\n      console.error('清空日志失败:', error)\n      return { success: false, message: '清空失败: ' + error.message }\n    }\n  }\n}\n\n// 生成模拟系统日志的辅助函数\nfunction generateMockSystemLogs(count = 50) {\n    const levels = ['DEBUG', 'INFO', 'WARN', 'ERROR']\n    const loggers = ['com.example.controller', 'com.example.service', 'com.example.dao', 'com.example.util']\n    const messages = [\n      '用户登录成功',\n      '数据库连接建立',\n      '缓存更新完成',\n      '配置文件加载',\n      '定时任务执行',\n      '接口调用异常',\n      '内存使用率过高',\n      '网络连接超时',\n      '文件读取失败',\n      '权限验证通过'\n    ]\n    \n    const logs = []\n    for (let i = 0; i < count; i++) {\n      logs.push({\n        id: i + 1,\n        timestamp: Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000, // 最近7天\n        level: levels[Math.floor(Math.random() * levels.length)],\n        logger: loggers[Math.floor(Math.random() * loggers.length)],\n        message: messages[Math.floor(Math.random() * messages.length)],\n        thread: `thread-${Math.floor(Math.random() * 10) + 1}`\n      })\n    }\n    \n    return logs.sort((a, b) => b.timestamp - a.timestamp)\n}\n\nconst getters = {\n  // 获取全局配置\n  globalConfig: state => state.globalConfig,\n  \n  // 获取告警阈值\n  alertThresholds: state => state.alertThresholds,\n  \n  // 获取通知配置\n  notificationConfig: state => state.notificationConfig,\n  \n  // 获取邮件配置\n  emailConfig: state => state.notificationConfig.email,\n  \n  // 获取短信配置\n  smsConfig: state => state.notificationConfig.sms,\n  \n  // 获取Webhook配置\n  webhookConfig: state => state.notificationConfig.webhook,\n  \n  // 获取日志配置\n  logConfig: state => state.logConfig,\n  \n  // 获取系统日志\n  systemLogs: state => state.systemLogs,\n  \n  // 获取加载状态\n  loading: state => state.loading,\n  \n  // 获取过滤后的系统日志\n  filteredSystemLogs: state => (filters) => {\n    let logs = state.systemLogs\n    \n    if (filters.level && filters.level !== 'ALL') {\n      logs = logs.filter(log => log.level === filters.level)\n    }\n    \n    if (filters.keyword) {\n      const keyword = filters.keyword.toLowerCase()\n      logs = logs.filter(log => \n        log.message.toLowerCase().includes(keyword) ||\n        log.logger.toLowerCase().includes(keyword)\n      )\n    }\n    \n    if (filters.startDate) {\n      const startTime = new Date(filters.startDate).getTime()\n      logs = logs.filter(log => log.timestamp >= startTime)\n    }\n    \n    if (filters.endDate) {\n      const endTime = new Date(filters.endDate).getTime() + 24 * 60 * 60 * 1000 - 1\n      logs = logs.filter(log => log.timestamp <= endTime)\n    }\n    \n    return logs\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions,\n  getters\n}", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\i18n\\locales\\en-US.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\TenantManagementTab.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\Layout.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\IpRulesTab.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\TenantDialog.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\InterfaceRulesTab.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\InterfaceRuleDialog.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\IpRuleDialog.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\utils\\export.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\HelpCenter.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\UserGuide.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\tenantFlowRules.js", ["223"], "const state = {\n\trules: [],\n\tcurrentRule: null,\n\tloading: false,\n\tsearchKeyword: '',\n\tfilters: {\n\t\tstatus: '',\n\t\tthresholdType: '',\n\t\tcontrolBehavior: ''\n\t},\n\tpagination: {\n\t\tcurrent: 1,\n\t\tpageSize: 10,\n\t\ttotal: 0\n\t},\n\tselectedRuleIds: []\n}\n\nconst mutations = {\n\tSET_RULES (state, { rules, total }) {\n\t\tstate.rules = rules\n\t\tstate.pagination.total = total\n\t},\n\tSET_CURRENT_RULE (state, rule) {\n\t\tstate.currentRule = rule\n\t},\n\tADD_RULE (state, rule) {\n\t\tstate.rules.unshift(rule)\n\t\tstate.pagination.total += 1\n\t},\n\tUPDATE_RULE (state, updatedRule) {\n\t\tconst index = state.rules.findIndex(rule => rule.id === updatedRule.id)\n\t\tif (index !== -1) {\n\t\t\tstate.rules.splice(index, 1, updatedRule)\n\t\t}\n\t},\n\tDELETE_RULE (state, ruleId) {\n\t\tstate.rules = state.rules.filter(rule => rule.id !== ruleId)\n\t\tstate.pagination.total -= 1\n\t},\n\tBATCH_DELETE_RULES (state, ruleIds) {\n\t\tstate.rules = state.rules.filter(rule => !ruleIds.includes(rule.id))\n\t\tstate.pagination.total -= ruleIds.length\n\t\tstate.selectedRuleIds = []\n\t},\n\tSET_LOADING (state, loading) {\n\t\tstate.loading = loading\n\t},\n\tSET_SEARCH_KEYWORD (state, keyword) {\n\t\tstate.searchKeyword = keyword\n\t},\n\tSET_FILTERS (state, filters) {\n\t\tstate.filters = { ...state.filters, ...filters }\n\t},\n\tSET_PAGINATION (state, pagination) {\n\t\tstate.pagination = { ...state.pagination, ...pagination }\n\t},\n\tSET_SELECTED_RULE_IDS (state, ids) {\n\t\tstate.selectedRuleIds = ids\n\t},\n\tTOGGLE_RULE_SELECTION (state, ruleId) {\n\t\tconst index = state.selectedRuleIds.indexOf(ruleId)\n\t\tif (index > -1) {\n\t\t\tstate.selectedRuleIds.splice(index, 1)\n\t\t} else {\n\t\t\tstate.selectedRuleIds.push(ruleId)\n\t\t}\n\t},\n\tCLEAR_SELECTION (state) {\n\t\tstate.selectedRuleIds = []\n\t}\n}\n\nconst actions = {\n\tasync fetchRules ({ commit, state }) {\n\t\tcommit('SET_LOADING', true)\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst params = {\n\t\t\t\tpage: state.pagination.current,\n\t\t\t\tpageSize: state.pagination.pageSize,\n\t\t\t\tkeyword: state.searchKeyword,\n\t\t\t\t...state.filters\n\t\t\t}\n\t\t\tconst response = await api.tenantFlowRules.getList(params)\n\t\t\t// 转换后端数据格式到前端期望格式\n\t\t\tconst transformedRules = (response.data.records || []).map(rule => ({\n\t\t\t\tid: rule.id,\n\t\t\t\tname: rule.ruleName,\n\t\t\t\tresource: rule.resourceName,  // 前端期望的字段名\n\t\t\t\tresourceName: rule.resourceName,\n\t\t\t\tlimitApp: rule.tenantName || '',  // 前端搜索过滤需要的字段\n\t\t\t\ttenantId: rule.tenantId,\n\t\t\t\ttenantName: rule.tenantName,\n\t\t\t\tlimitMode: rule.limitMode,\n\t\t\t\tlimitModeName: rule.limitModeName,\n\t\t\t\tgrade: rule.limitMode,  // 阈值类型，前端期望的字段名\n\t\t\t\tcount: rule.threshold,  // 阈值，前端期望的字段名\n\t\t\t\tthreshold: rule.threshold,\n\t\t\t\tstrategy: rule.strategy,\n\t\t\t\tstrategyName: rule.strategyName,\n\t\t\t\trelatedResource: rule.relatedResource,\n\t\t\t\tcontrolBehavior: rule.behavior,  // 前端期望的字段名\n\t\t\t\tbehavior: rule.behavior,\n\t\t\t\tbehaviorName: rule.behaviorName,\n\t\t\t\twarmUpPeriod: rule.warmUpPeriod,\n\t\t\t\tqueueTimeout: rule.queueTimeout,\n\t\t\t\tclusterMode: rule.clusterMode,\n\t\t\t\tclusterModeName: rule.clusterModeName,\n\t\t\t\tstatus: rule.status,\n\t\t\t\tstatusName: rule.statusName,\n\t\t\t\tpriority: rule.priority,\n\t\t\t\tdescription: rule.description,\n\t\t\t\tcreateTime: rule.createTime,\n\t\t\t\tupdateTime: rule.updateTime,\n\t\t\t\tenabled: rule.status === 1  // 将后端的status转换为前端的enabled布尔值\n\t\t\t}))\n\t\t\tcommit('SET_RULES', {\n\t\t\t\trules: transformedRules,\n\t\t\t\ttotal: response.data.total || 0\n\t\t\t})\n\t\t} catch (error) {\n\t\t\tconsole.error('获取流量规则失败:', error)\n\t\t\tcommit('SET_RULES', { rules: [], total: 0 })\n\t\t} finally {\n\t\t\tcommit('SET_LOADING', false)\n\t\t}\n\t},\n\n\tasync createRule ({ commit, dispatch }, rule) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.tenantFlowRules.create(rule)\n\t\t\tcommit('ADD_RULE', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('创建流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync updateRule ({ commit }, rule) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.tenantFlowRules.update(rule.id, rule)\n\t\t\tcommit('UPDATE_RULE', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('更新流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync deleteRule ({ commit }, ruleId) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.tenantFlowRules.delete(ruleId)\n\t\t\tcommit('DELETE_RULE', ruleId)\n\t\t} catch (error) {\n\t\t\tconsole.error('删除流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync batchDeleteRules ({ commit, state }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.tenantFlowRules.batchDelete(state.selectedRuleIds)\n\t\t\tcommit('BATCH_DELETE_RULES', state.selectedRuleIds)\n\t\t} catch (error) {\n\t\t\tconsole.error('批量删除流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync enableRule ({ commit }, ruleId) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.tenantFlowRules.enable(ruleId)\n\t\t\tcommit('UPDATE_RULE', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('启用流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync disableRule ({ commit }, ruleId) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.tenantFlowRules.disable(ruleId)\n\t\t\tcommit('UPDATE_RULE', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('禁用流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tsetSearchKeyword ({ commit, dispatch }, keyword) {\n\t\tcommit('SET_SEARCH_KEYWORD', keyword)\n\t\tcommit('SET_PAGINATION', { current: 1 })\n\t\tdispatch('fetchRules')\n\t},\n\n\tsetFilters ({ commit, dispatch }, filters) {\n\t\tcommit('SET_FILTERS', filters)\n\t\tcommit('SET_PAGINATION', { current: 1 })\n\t\tdispatch('fetchRules')\n\t},\n\n\tsetPagination ({ commit, dispatch }, pagination) {\n\t\tcommit('SET_PAGINATION', pagination)\n\t\tdispatch('fetchRules')\n\t},\n\n\tsetSelectedRuleIds ({ commit }, ids) {\n\t\tcommit('SET_SELECTED_RULE_IDS', ids)\n\t},\n\n\ttoggleRuleSelection ({ commit }, ruleId) {\n\t\tcommit('TOGGLE_RULE_SELECTION', ruleId)\n\t},\n\n\tclearSelection ({ commit }) {\n\t\tcommit('CLEAR_SELECTION')\n\t}\n}\n\nconst getters = {\n\tallRules: state => state.rules,\n\tcurrentRule: state => state.currentRule,\n\tisLoading: state => state.loading,\n\tsearchKeyword: state => state.searchKeyword,\n\tfilters: state => state.filters,\n\tpagination: state => state.pagination,\n\tselectedRuleIds: state => state.selectedRuleIds,\n\tselectedRulesCount: state => state.selectedRuleIds.length,\n\thasSelectedRules: state => state.selectedRuleIds.length > 0,\n\tfilteredRules: state => {\n\t\tlet filtered = state.rules\n\n\t\t// 搜索关键词过滤\n\t\tif (state.searchKeyword) {\n\t\t\tconst keyword = state.searchKeyword.toLowerCase()\n\t\t\tfiltered = filtered.filter(rule =>\n\t\t\t\trule.resource?.toLowerCase().includes(keyword) ||\n\t\t\t\trule.limitApp?.toLowerCase().includes(keyword)\n\t\t\t)\n\t\t}\n\n\t\treturn filtered\n\t},\n\tenabledRulesCount: state => state.rules.filter(rule => rule.status === 'enabled').length,\n\tdisabledRulesCount: state => state.rules.filter(rule => rule.status === 'disabled').length\n}\n\nexport default {\n\tnamespaced: true,\n\tstate,\n\tmutations,\n\tactions,\n\tgetters\n}", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\ipWhitelist.js", ["224", "225", "226"], "const state = {\n\tlists: [],\n\tcurrentList: null,\n\tloading: false,\n\tsearchKeyword: '',\n\tfilters: {\n\t\tstatus: '',\n\t\tipType: '',\n\t\ttenantId: ''\n\t},\n\tpagination: {\n\t\tcurrent: 1,\n\t\tpageSize: 10,\n\t\ttotal: 0\n\t},\n\tselectedListIds: []\n}\n\nconst mutations = {\n\tSET_LISTS (state, { lists, total }) {\n\t\tstate.lists = lists\n\t\tstate.pagination.total = total\n\t},\n\tSET_CURRENT_LIST (state, list) {\n\t\tstate.currentList = list\n\t},\n\tADD_LIST (state, list) {\n\t\tstate.lists.unshift(list)\n\t\tstate.pagination.total += 1\n\t},\n\tUPDATE_LIST (state, updatedList) {\n\t\tconst index = state.lists.findIndex(list => list.id === updatedList.id)\n\t\tif (index !== -1) {\n\t\t\tstate.lists.splice(index, 1, updatedList)\n\t\t}\n\t},\n\tDELETE_LIST (state, listId) {\n\t\tstate.lists = state.lists.filter(list => list.id !== listId)\n\t\tstate.pagination.total -= 1\n\t},\n\tBATCH_DELETE_LISTS (state, listIds) {\n\t\tstate.lists = state.lists.filter(list => !listIds.includes(list.id))\n\t\tstate.pagination.total -= listIds.length\n\t\tstate.selectedListIds = []\n\t},\n\tSET_LOADING (state, loading) {\n\t\tstate.loading = loading\n\t},\n\tSET_SEARCH_KEYWORD (state, keyword) {\n\t\tstate.searchKeyword = keyword\n\t},\n\tSET_FILTERS (state, filters) {\n\t\tstate.filters = { ...state.filters, ...filters }\n\t},\n\tSET_PAGINATION (state, pagination) {\n\t\tstate.pagination = { ...state.pagination, ...pagination }\n\t},\n\tSET_SELECTED_LIST_IDS (state, ids) {\n\t\tstate.selectedListIds = ids\n\t},\n\tTOGGLE_LIST_SELECTION (state, listId) {\n\t\tconst index = state.selectedListIds.indexOf(listId)\n\t\tif (index > -1) {\n\t\t\tstate.selectedListIds.splice(index, 1)\n\t\t} else {\n\t\t\tstate.selectedListIds.push(listId)\n\t\t}\n\t},\n\tCLEAR_SELECTION (state) {\n\t\tstate.selectedListIds = []\n\t}\n}\n\nconst actions = {\n\tasync fetchLists ({ commit, state }) {\n\t\tcommit('SET_LOADING', true)\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst params = {\n\t\t\t\tpage: state.pagination.current,\n\t\t\t\tpageSize: state.pagination.pageSize,\n\t\t\t\tkeyword: state.searchKeyword,\n\t\t\t\t...state.filters\n\t\t\t}\n\t\t\tconst response = await api.ipWhitelists.getList(params)\n\t\t\t// 转换后端数据格式到前端期望格式\n\t\t\tconst transformedLists = (response.data.records || []).map(list => ({\n\t\t\t\tid: list.id,\n\t\t\t\tname: list.listName,\n\t\t\t\tlistName: list.listName,\n\t\t\t\tipAddress: list.ipAddress,\n\t\t\t\tipType: list.ipType,\n\t\t\t\tipTypeName: list.ipTypeName,\n\t\t\t\ttenantId: list.tenantId,\n\t\t\t\ttenantName: list.tenantName,\n\t\t\t\tpriority: list.priority,\n\t\t\t\tstatus: list.status,\n\t\t\t\tstatusName: list.statusName,\n\t\t\t\tdescription: list.description,\n\t\t\t\teffectiveTime: list.effectiveTime,\n\t\t\t\texpirationTime: list.expirationTime,\n\t\t\t\tcreateTime: list.createTime,\n\t\t\t\tupdateTime: list.updateTime,\n\t\t\t\tenabled: list.status === 1  // 将后端的status转换为前端的enabled布尔值\n\t\t\t}))\n\t\t\tcommit('SET_LISTS', {\n\t\t\t\tlists: transformedLists,\n\t\t\t\ttotal: response.data.total || 0\n\t\t\t})\n\t\t} catch (error) {\n\t\t\tconsole.error('获取IP白名单失败:', error)\n\t\t\tcommit('SET_LISTS', { lists: [], total: 0 })\n\t\t} finally {\n\t\t\tcommit('SET_LOADING', false)\n\t\t}\n\t},\n\n\tasync createList ({ commit }, list) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipWhitelists.create(list)\n\t\t\tcommit('ADD_LIST', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('创建IP白名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync updateList ({ commit }, list) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipWhitelists.update(list.id, list)\n\t\t\tcommit('UPDATE_LIST', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('更新IP白名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync deleteList ({ commit }, listId) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipWhitelists.delete(listId)\n\t\t\tcommit('DELETE_LIST', listId)\n\t\t} catch (error) {\n\t\t\tconsole.error('删除IP白名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync batchDeleteLists ({ commit, state }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipWhitelists.batchDelete(state.selectedListIds)\n\t\t\tcommit('BATCH_DELETE_LISTS', state.selectedListIds)\n\t\t} catch (error) {\n\t\t\tconsole.error('批量删除IP白名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync updateStatus ({ commit }, { id, enabled }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipWhitelists.updateStatus(id, enabled)\n\t\t\tcommit('UPDATE_LIST', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('更新IP白名单状态失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync batchUpdateStatus ({ commit, state }, enabled) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipWhitelists.batchUpdateStatus(state.selectedListIds, enabled)\n\t\t\t// 重新获取数据以更新状态\n\t\t\tawait this.dispatch('ipWhitelist/fetchLists')\n\t\t} catch (error) {\n\t\t\tconsole.error('批量更新IP白名单状态失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync copyList ({ commit }, { id, newListName, targetTenantId }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipWhitelists.copy(id, newListName, targetTenantId)\n\t\t\tcommit('ADD_LIST', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('复制IP白名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync importLists ({ dispatch }, { data, overwrite }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipWhitelists.import(data, overwrite)\n\t\t\t// 重新获取数据\n\t\t\tawait dispatch('fetchLists')\n\t\t} catch (error) {\n\t\t\tconsole.error('导入IP白名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync exportLists ({ state }, { tenantId, enabled }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\treturn await api.ipWhitelists.export(tenantId, enabled)\n\t\t} catch (error) {\n\t\t\tconsole.error('导出IP白名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync checkMatch ({ state }, { ip, tenantId }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\treturn await api.ipWhitelists.checkMatch(ip, tenantId)\n\t\t} catch (error) {\n\t\t\tconsole.error('检查IP匹配失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tsetSearchKeyword ({ commit, dispatch }, keyword) {\n\t\tcommit('SET_SEARCH_KEYWORD', keyword)\n\t\tcommit('SET_PAGINATION', { current: 1 })\n\t\tdispatch('fetchLists')\n\t},\n\n\tsetFilters ({ commit, dispatch }, filters) {\n\t\tcommit('SET_FILTERS', filters)\n\t\tcommit('SET_PAGINATION', { current: 1 })\n\t\tdispatch('fetchLists')\n\t},\n\n\tsetPagination ({ commit, dispatch }, pagination) {\n\t\tcommit('SET_PAGINATION', pagination)\n\t\tdispatch('fetchLists')\n\t},\n\n\tsetSelectedListIds ({ commit }, ids) {\n\t\tcommit('SET_SELECTED_LIST_IDS', ids)\n\t},\n\n\ttoggleListSelection ({ commit }, listId) {\n\t\tcommit('TOGGLE_LIST_SELECTION', listId)\n\t},\n\n\tclearSelection ({ commit }) {\n\t\tcommit('CLEAR_SELECTION')\n\t}\n}\n\nconst getters = {\n\tallLists: state => state.lists,\n\tcurrentList: state => state.currentList,\n\tisLoading: state => state.loading,\n\tsearchKeyword: state => state.searchKeyword,\n\tfilters: state => state.filters,\n\tpagination: state => state.pagination,\n\tselectedListIds: state => state.selectedListIds,\n\tselectedListsCount: state => state.selectedListIds.length,\n\thasSelectedLists: state => state.selectedListIds.length > 0,\n\tfilteredLists: state => {\n\t\tlet filtered = state.lists\n\n\t\t// 搜索关键词过滤\n\t\tif (state.searchKeyword) {\n\t\t\tconst keyword = state.searchKeyword.toLowerCase()\n\t\t\tfiltered = filtered.filter(list =>\n\t\t\t\tlist.listName?.toLowerCase().includes(keyword) ||\n\t\t\t\tlist.ipAddress?.toLowerCase().includes(keyword) ||\n\t\t\t\tlist.tenantName?.toLowerCase().includes(keyword)\n\t\t\t)\n\t\t}\n\n\t\treturn filtered\n\t},\n\tenabledListsCount: state => state.lists.filter(list => list.status === 1).length,\n\tdisabledListsCount: state => state.lists.filter(list => list.status === 0).length\n}\n\nexport default {\n\tnamespaced: true,\n\tstate,\n\tmutations,\n\tactions,\n\tgetters\n}", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\ipBlacklist.js", ["227", "228", "229"], "const state = {\n\tlists: [],\n\tcurrentList: null,\n\tloading: false,\n\tsearchKeyword: '',\n\tfilters: {\n\t\tstatus: '',\n\t\tipType: '',\n\t\ttenantId: ''\n\t},\n\tpagination: {\n\t\tcurrent: 1,\n\t\tpageSize: 10,\n\t\ttotal: 0\n\t},\n\tselectedListIds: []\n}\n\nconst mutations = {\n\tSET_LISTS (state, { lists, total }) {\n\t\tstate.lists = lists\n\t\tstate.pagination.total = total\n\t},\n\tSET_CURRENT_LIST (state, list) {\n\t\tstate.currentList = list\n\t},\n\tADD_LIST (state, list) {\n\t\tstate.lists.unshift(list)\n\t\tstate.pagination.total += 1\n\t},\n\tUPDATE_LIST (state, updatedList) {\n\t\tconst index = state.lists.findIndex(list => list.id === updatedList.id)\n\t\tif (index !== -1) {\n\t\t\tstate.lists.splice(index, 1, updatedList)\n\t\t}\n\t},\n\tDELETE_LIST (state, listId) {\n\t\tstate.lists = state.lists.filter(list => list.id !== listId)\n\t\tstate.pagination.total -= 1\n\t},\n\tBATCH_DELETE_LISTS (state, listIds) {\n\t\tstate.lists = state.lists.filter(list => !listIds.includes(list.id))\n\t\tstate.pagination.total -= listIds.length\n\t\tstate.selectedListIds = []\n\t},\n\tSET_LOADING (state, loading) {\n\t\tstate.loading = loading\n\t},\n\tSET_SEARCH_KEYWORD (state, keyword) {\n\t\tstate.searchKeyword = keyword\n\t},\n\tSET_FILTERS (state, filters) {\n\t\tstate.filters = { ...state.filters, ...filters }\n\t},\n\tSET_PAGINATION (state, pagination) {\n\t\tstate.pagination = { ...state.pagination, ...pagination }\n\t},\n\tSET_SELECTED_LIST_IDS (state, ids) {\n\t\tstate.selectedListIds = ids\n\t},\n\tTOGGLE_LIST_SELECTION (state, listId) {\n\t\tconst index = state.selectedListIds.indexOf(listId)\n\t\tif (index > -1) {\n\t\t\tstate.selectedListIds.splice(index, 1)\n\t\t} else {\n\t\t\tstate.selectedListIds.push(listId)\n\t\t}\n\t},\n\tCLEAR_SELECTION (state) {\n\t\tstate.selectedListIds = []\n\t}\n}\n\nconst actions = {\n\tasync fetchLists ({ commit, state }) {\n\t\tcommit('SET_LOADING', true)\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst params = {\n\t\t\t\tpage: state.pagination.current,\n\t\t\t\tpageSize: state.pagination.pageSize,\n\t\t\t\tkeyword: state.searchKeyword,\n\t\t\t\t...state.filters\n\t\t\t}\n\t\t\tconst response = await api.ipBlacklists.getList(params)\n\t\t\t// 转换后端数据格式到前端期望格式\n\t\t\tconst transformedLists = (response.data.records || []).map(list => ({\n\t\t\t\tid: list.id,\n\t\t\t\tname: list.listName,\n\t\t\t\tlistName: list.listName,\n\t\t\t\tipAddress: list.ipAddress,\n\t\t\t\tipType: list.ipType,\n\t\t\t\tipTypeName: list.ipTypeName,\n\t\t\t\ttenantId: list.tenantId,\n\t\t\t\ttenantName: list.tenantName,\n\t\t\t\tpriority: list.priority,\n\t\t\t\tstatus: list.status,\n\t\t\t\tstatusName: list.statusName,\n\t\t\t\tdescription: list.description,\n\t\t\t\teffectiveTime: list.effectiveTime,\n\t\t\t\texpirationTime: list.expirationTime,\n\t\t\t\tcreateTime: list.createTime,\n\t\t\t\tupdateTime: list.updateTime,\n\t\t\t\tenabled: list.status === 1  // 将后端的status转换为前端的enabled布尔值\n\t\t\t}))\n\t\t\tcommit('SET_LISTS', {\n\t\t\t\tlists: transformedLists,\n\t\t\t\ttotal: response.data.total || 0\n\t\t\t})\n\t\t} catch (error) {\n\t\t\tconsole.error('获取IP黑名单失败:', error)\n\t\t\tcommit('SET_LISTS', { lists: [], total: 0 })\n\t\t} finally {\n\t\t\tcommit('SET_LOADING', false)\n\t\t}\n\t},\n\n\tasync createList ({ commit }, list) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipBlacklists.create(list)\n\t\t\tcommit('ADD_LIST', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('创建IP黑名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync updateList ({ commit }, list) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipBlacklists.update(list.id, list)\n\t\t\tcommit('UPDATE_LIST', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('更新IP黑名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync deleteList ({ commit }, listId) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipBlacklists.delete(listId)\n\t\t\tcommit('DELETE_LIST', listId)\n\t\t} catch (error) {\n\t\t\tconsole.error('删除IP黑名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync batchDeleteLists ({ commit, state }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipBlacklists.batchDelete(state.selectedListIds)\n\t\t\tcommit('BATCH_DELETE_LISTS', state.selectedListIds)\n\t\t} catch (error) {\n\t\t\tconsole.error('批量删除IP黑名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync updateStatus ({ commit }, { id, enabled }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipBlacklists.updateStatus(id, enabled)\n\t\t\tcommit('UPDATE_LIST', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('更新IP黑名单状态失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync batchUpdateStatus ({ commit, state }, enabled) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipBlacklists.batchUpdateStatus(state.selectedListIds, enabled)\n\t\t\t// 重新获取数据以更新状态\n\t\t\tawait this.dispatch('ipBlacklist/fetchLists')\n\t\t} catch (error) {\n\t\t\tconsole.error('批量更新IP黑名单状态失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync copyList ({ commit }, { id, newListName, targetTenantId }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipBlacklists.copy(id, newListName, targetTenantId)\n\t\t\tcommit('ADD_LIST', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('复制IP黑名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync importLists ({ dispatch }, { data, overwrite }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipBlacklists.import(data, overwrite)\n\t\t\t// 重新获取数据\n\t\t\tawait dispatch('fetchLists')\n\t\t} catch (error) {\n\t\t\tconsole.error('导入IP黑名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync exportLists ({ state }, { tenantId, enabled }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\treturn await api.ipBlacklists.export(tenantId, enabled)\n\t\t} catch (error) {\n\t\t\tconsole.error('导出IP黑名单失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync checkMatch ({ state }, { ip, tenantId }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\treturn await api.ipBlacklists.checkMatch(ip, tenantId)\n\t\t} catch (error) {\n\t\t\tconsole.error('检查IP匹配失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tsetSearchKeyword ({ commit, dispatch }, keyword) {\n\t\tcommit('SET_SEARCH_KEYWORD', keyword)\n\t\tcommit('SET_PAGINATION', { current: 1 })\n\t\tdispatch('fetchLists')\n\t},\n\n\tsetFilters ({ commit, dispatch }, filters) {\n\t\tcommit('SET_FILTERS', filters)\n\t\tcommit('SET_PAGINATION', { current: 1 })\n\t\tdispatch('fetchLists')\n\t},\n\n\tsetPagination ({ commit, dispatch }, pagination) {\n\t\tcommit('SET_PAGINATION', pagination)\n\t\tdispatch('fetchLists')\n\t},\n\n\tsetSelectedListIds ({ commit }, ids) {\n\t\tcommit('SET_SELECTED_LIST_IDS', ids)\n\t},\n\n\ttoggleListSelection ({ commit }, listId) {\n\t\tcommit('TOGGLE_LIST_SELECTION', listId)\n\t},\n\n\tclearSelection ({ commit }) {\n\t\tcommit('CLEAR_SELECTION')\n\t}\n}\n\nconst getters = {\n\tallLists: state => state.lists,\n\tcurrentList: state => state.currentList,\n\tisLoading: state => state.loading,\n\tsearchKeyword: state => state.searchKeyword,\n\tfilters: state => state.filters,\n\tpagination: state => state.pagination,\n\tselectedListIds: state => state.selectedListIds,\n\tselectedListsCount: state => state.selectedListIds.length,\n\thasSelectedLists: state => state.selectedListIds.length > 0,\n\tfilteredLists: state => {\n\t\tlet filtered = state.lists\n\n\t\t// 搜索关键词过滤\n\t\tif (state.searchKeyword) {\n\t\t\tconst keyword = state.searchKeyword.toLowerCase()\n\t\t\tfiltered = filtered.filter(list =>\n\t\t\t\tlist.listName?.toLowerCase().includes(keyword) ||\n\t\t\t\tlist.ipAddress?.toLowerCase().includes(keyword) ||\n\t\t\t\tlist.tenantName?.toLowerCase().includes(keyword)\n\t\t\t)\n\t\t}\n\n\t\treturn filtered\n\t},\n\tenabledListsCount: state => state.lists.filter(list => list.status === 1).length,\n\tdisabledListsCount: state => state.lists.filter(list => list.status === 0).length\n}\n\nexport default {\n\tnamespaced: true,\n\tstate,\n\tmutations,\n\tactions,\n\tgetters\n}", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\store\\modules\\ipFlowRules.js", ["230", "231", "232", "233", "234"], "const state = {\n\trules: [],\n\tcurrentRule: null,\n\tloading: false,\n\tsearchKeyword: '',\n\tfilters: {\n\t\tstatus: '',\n\t\tipType: '',\n\t\ttenantId: '',\n\t\tbehavior: ''\n\t},\n\tpagination: {\n\t\tcurrent: 1,\n\t\tpageSize: 10,\n\t\ttotal: 0\n\t},\n\tselectedRuleIds: []\n}\n\nconst mutations = {\n\tSET_RULES (state, { rules, total }) {\n\t\tstate.rules = rules\n\t\tstate.pagination.total = total\n\t},\n\tSET_CURRENT_RULE (state, rule) {\n\t\tstate.currentRule = rule\n\t},\n\tADD_RULE (state, rule) {\n\t\tstate.rules.unshift(rule)\n\t\tstate.pagination.total += 1\n\t},\n\tUPDATE_RULE (state, updatedRule) {\n\t\tconst index = state.rules.findIndex(rule => rule.id === updatedRule.id)\n\t\tif (index !== -1) {\n\t\t\tstate.rules.splice(index, 1, updatedRule)\n\t\t}\n\t},\n\tDELETE_RULE (state, ruleId) {\n\t\tstate.rules = state.rules.filter(rule => rule.id !== ruleId)\n\t\tstate.pagination.total -= 1\n\t},\n\tBATCH_DELETE_RULES (state, ruleIds) {\n\t\tstate.rules = state.rules.filter(rule => !ruleIds.includes(rule.id))\n\t\tstate.pagination.total -= ruleIds.length\n\t\tstate.selectedRuleIds = []\n\t},\n\tSET_LOADING (state, loading) {\n\t\tstate.loading = loading\n\t},\n\tSET_SEARCH_KEYWORD (state, keyword) {\n\t\tstate.searchKeyword = keyword\n\t},\n\tSET_FILTERS (state, filters) {\n\t\tstate.filters = { ...state.filters, ...filters }\n\t},\n\tSET_PAGINATION (state, pagination) {\n\t\tstate.pagination = { ...state.pagination, ...pagination }\n\t},\n\tSET_SELECTED_RULE_IDS (state, ids) {\n\t\tstate.selectedRuleIds = ids\n\t},\n\tTOGGLE_RULE_SELECTION (state, ruleId) {\n\t\tconst index = state.selectedRuleIds.indexOf(ruleId)\n\t\tif (index > -1) {\n\t\t\tstate.selectedRuleIds.splice(index, 1)\n\t\t} else {\n\t\t\tstate.selectedRuleIds.push(ruleId)\n\t\t}\n\t},\n\tCLEAR_SELECTION (state) {\n\t\tstate.selectedRuleIds = []\n\t}\n}\n\nconst actions = {\n\tasync fetchRules ({ commit, state }) {\n\t\tcommit('SET_LOADING', true)\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst params = {\n\t\t\t\tpage: state.pagination.current,\n\t\t\t\tpageSize: state.pagination.pageSize,\n\t\t\t\tkeyword: state.searchKeyword,\n\t\t\t\t...state.filters\n\t\t\t}\n\t\t\tconst response = await api.ipFlowRules.getList(params)\n\t\t\t// 转换后端数据格式到前端期望格式\n\t\t\tconst transformedRules = (response.data.records || []).map(rule => ({\n\t\t\t\tid: rule.id,\n\t\t\t\tname: rule.ruleName,\n\t\t\t\truleName: rule.ruleName,\n\t\t\t\tipAddress: rule.ipAddress,\n\t\t\t\tipType: rule.ipType,\n\t\t\t\tipTypeName: rule.ipTypeName,\n\t\t\t\ttenantId: rule.tenantId,\n\t\t\t\ttenantName: rule.tenantName,\n\t\t\t\tresource: rule.resource,\n\t\t\t\tcount: rule.count,\n\t\t\t\tgrade: rule.grade,\n\t\t\t\tgradeName: rule.gradeName,\n\t\t\t\tlimitApp: rule.limitApp,\n\t\t\t\tstrategy: rule.strategy,\n\t\t\t\tstrategyName: rule.strategyName,\n\t\t\t\tcontrolBehavior: rule.controlBehavior,\n\t\t\t\tcontrolBehaviorName: rule.controlBehaviorName,\n\t\t\t\twarmUpPeriodSec: rule.warmUpPeriodSec,\n\t\t\t\tmaxQueueingTimeMs: rule.maxQueueingTimeMs,\n\t\t\t\tpriority: rule.priority,\n\t\t\t\tstatus: rule.status,\n\t\t\t\tstatusName: rule.statusName,\n\t\t\t\tdescription: rule.description,\n\t\t\t\teffectiveTime: rule.effectiveTime,\n\t\t\t\texpirationTime: rule.expirationTime,\n\t\t\t\tcreateTime: rule.createTime,\n\t\t\t\tupdateTime: rule.updateTime,\n\t\t\t\tenabled: rule.status === 1  // 将后端的status转换为前端的enabled布尔值\n\t\t\t}))\n\t\t\tcommit('SET_RULES', {\n\t\t\t\trules: transformedRules,\n\t\t\t\ttotal: response.data.total || 0\n\t\t\t})\n\t\t} catch (error) {\n\t\t\tconsole.error('获取IP流量规则失败:', error)\n\t\t\tcommit('SET_RULES', { rules: [], total: 0 })\n\t\t} finally {\n\t\t\tcommit('SET_LOADING', false)\n\t\t}\n\t},\n\n\tasync createRule ({ commit }, rule) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipFlowRules.create(rule)\n\t\t\tcommit('ADD_RULE', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('创建IP流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync updateRule ({ commit }, rule) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipFlowRules.update(rule.id, rule)\n\t\t\tcommit('UPDATE_RULE', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('更新IP流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync deleteRule ({ commit }, ruleId) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipFlowRules.delete(ruleId)\n\t\t\tcommit('DELETE_RULE', ruleId)\n\t\t} catch (error) {\n\t\t\tconsole.error('删除IP流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync batchDeleteRules ({ commit, state }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipFlowRules.batchDelete(state.selectedRuleIds)\n\t\t\tcommit('BATCH_DELETE_RULES', state.selectedRuleIds)\n\t\t} catch (error) {\n\t\t\tconsole.error('批量删除IP流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync updateStatus ({ commit }, { id, enabled }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tconst response = await api.ipFlowRules.updateStatus(id, enabled)\n\t\t\tcommit('UPDATE_RULE', response.data)\n\t\t\treturn response.data\n\t\t} catch (error) {\n\t\t\tconsole.error('更新IP流量规则状态失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync batchUpdateStatus ({ commit, state }, enabled) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipFlowRules.batchUpdateStatus(state.selectedRuleIds, enabled)\n\t\t\t// 重新获取数据以更新状态\n\t\t\tawait this.dispatch('ipFlowRules/fetchRules')\n\t\t} catch (error) {\n\t\t\tconsole.error('批量更新IP流量规则状态失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync syncToSentinel ({ state }, { tenantId, ruleIds }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\treturn await api.ipFlowRules.syncToSentinel(tenantId, ruleIds)\n\t\t} catch (error) {\n\t\t\tconsole.error('同步规则到Sentinel失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync importRules ({ dispatch }, { data, overwrite }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\tawait api.ipFlowRules.import(data, overwrite)\n\t\t\t// 重新获取数据\n\t\t\tawait dispatch('fetchRules')\n\t\t} catch (error) {\n\t\t\tconsole.error('导入IP流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync exportRules ({ state }, { tenantId, enabled }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\treturn await api.ipFlowRules.export(tenantId, enabled)\n\t\t} catch (error) {\n\t\t\tconsole.error('导出IP流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync validateRule ({ state }, rule) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\treturn await api.ipFlowRules.validate(rule)\n\t\t} catch (error) {\n\t\t\tconsole.error('验证IP流量规则失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tasync refreshCache ({ state }) {\n\t\ttry {\n\t\t\tconst { api } = await import('@/api')\n\t\t\treturn await api.ipFlowRules.refreshCache()\n\t\t} catch (error) {\n\t\t\tconsole.error('刷新规则缓存失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\tsetSearchKeyword ({ commit, dispatch }, keyword) {\n\t\tcommit('SET_SEARCH_KEYWORD', keyword)\n\t\tcommit('SET_PAGINATION', { current: 1 })\n\t\tdispatch('fetchRules')\n\t},\n\n\tsetFilters ({ commit, dispatch }, filters) {\n\t\tcommit('SET_FILTERS', filters)\n\t\tcommit('SET_PAGINATION', { current: 1 })\n\t\tdispatch('fetchRules')\n\t},\n\n\tsetPagination ({ commit, dispatch }, pagination) {\n\t\tcommit('SET_PAGINATION', pagination)\n\t\tdispatch('fetchRules')\n\t},\n\n\tsetSelectedRuleIds ({ commit }, ids) {\n\t\tcommit('SET_SELECTED_RULE_IDS', ids)\n\t},\n\n\ttoggleRuleSelection ({ commit }, ruleId) {\n\t\tcommit('TOGGLE_RULE_SELECTION', ruleId)\n\t},\n\n\tclearSelection ({ commit }) {\n\t\tcommit('CLEAR_SELECTION')\n\t}\n}\n\nconst getters = {\n\tallRules: state => state.rules,\n\tcurrentRule: state => state.currentRule,\n\tisLoading: state => state.loading,\n\tsearchKeyword: state => state.searchKeyword,\n\tfilters: state => state.filters,\n\tpagination: state => state.pagination,\n\tselectedRuleIds: state => state.selectedRuleIds,\n\tselectedRulesCount: state => state.selectedRuleIds.length,\n\thasSelectedRules: state => state.selectedRuleIds.length > 0,\n\tfilteredRules: state => {\n\t\tlet filtered = state.rules\n\n\t\t// 搜索关键词过滤\n\t\tif (state.searchKeyword) {\n\t\t\tconst keyword = state.searchKeyword.toLowerCase()\n\t\t\tfiltered = filtered.filter(rule =>\n\t\t\t\trule.ruleName?.toLowerCase().includes(keyword) ||\n\t\t\t\trule.resource?.toLowerCase().includes(keyword) ||\n\t\t\t\trule.ipAddress?.toLowerCase().includes(keyword) ||\n\t\t\t\trule.tenantName?.toLowerCase().includes(keyword)\n\t\t\t)\n\t\t}\n\n\t\treturn filtered\n\t},\n\tenabledRulesCount: state => state.rules.filter(rule => rule.status === 1).length,\n\tdisabledRulesCount: state => state.rules.filter(rule => rule.status === 0).length,\n\trulesByBehavior: state => {\n\t\tconst grouped = {}\n\t\tstate.rules.forEach(rule => {\n\t\t\tconst behavior = rule.controlBehaviorName || '未知'\n\t\t\tif (!grouped[behavior]) {\n\t\t\t\tgrouped[behavior] = []\n\t\t\t}\n\t\t\tgrouped[behavior].push(rule)\n\t\t})\n\t\treturn grouped\n\t}\n}\n\nexport default {\n\tnamespaced: true,\n\tstate,\n\tmutations,\n\tactions,\n\tgetters\n}", "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\IpManagement.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\IpWhitelistTab.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\IpBlacklistTab.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\components\\IpFlowRulesTab.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\TenantFlowRules.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\InterfaceFlowRules.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\IpFlowRules.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\views\\IpBlackWhiteList.vue", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\interfaceRule.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\ipRule.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\ipBlackWhiteList.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\utils\\request.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\tenantRule.js", [], [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\api\\tenantFlowRule.js", [], "D:\\java\\openplatform\\flow-control-vue-frontend\\src\\utils\\validation.js", [], {"ruleId": null, "fatal": true, "severity": 2, "message": "235", "line": 495, "column": 62}, {"ruleId": "236", "severity": 1, "message": "237", "line": 871, "column": 35, "nodeType": "238", "messageId": "239", "endLine": 871, "endColumn": 46}, {"ruleId": "236", "severity": 1, "message": "240", "line": 1203, "column": 11, "nodeType": "238", "messageId": "239", "endLine": 1203, "endColumn": 19}, {"ruleId": "236", "severity": 1, "message": "241", "line": 76, "column": 43, "nodeType": "238", "messageId": "239", "endLine": 76, "endColumn": 52}, {"ruleId": "236", "severity": 1, "message": "242", "line": 76, "column": 54, "nodeType": "238", "messageId": "239", "endLine": 76, "endColumn": 61}, {"ruleId": "236", "severity": 1, "message": "243", "line": 220, "column": 41, "nodeType": "238", "messageId": "239", "endLine": 220, "endColumn": 46}, {"ruleId": "236", "severity": 1, "message": "243", "line": 239, "column": 39, "nodeType": "238", "messageId": "239", "endLine": 239, "endColumn": 44}, {"ruleId": "236", "severity": 1, "message": "243", "line": 258, "column": 43, "nodeType": "238", "messageId": "239", "endLine": 258, "endColumn": 48}, {"ruleId": "236", "severity": 1, "message": "244", "line": 277, "column": 38, "nodeType": "238", "messageId": "239", "endLine": 277, "endColumn": 43}, {"ruleId": "236", "severity": 1, "message": "245", "line": 277, "column": 45, "nodeType": "238", "messageId": "239", "endLine": 277, "endColumn": 54}, {"ruleId": "236", "severity": 1, "message": "246", "line": 277, "column": 56, "nodeType": "238", "messageId": "239", "endLine": 277, "endColumn": 63}, {"ruleId": "236", "severity": 1, "message": "247", "line": 277, "column": 65, "nodeType": "238", "messageId": "239", "endLine": 277, "endColumn": 72}, {"ruleId": "236", "severity": 1, "message": "248", "line": 130, "column": 30, "nodeType": "238", "messageId": "239", "endLine": 130, "endColumn": 38}, {"ruleId": "236", "severity": 1, "message": "249", "line": 176, "column": 29, "nodeType": "238", "messageId": "239", "endLine": 176, "endColumn": 35}, {"ruleId": "236", "severity": 1, "message": "243", "line": 212, "column": 23, "nodeType": "238", "messageId": "239", "endLine": 212, "endColumn": 28}, {"ruleId": "236", "severity": 1, "message": "243", "line": 222, "column": 22, "nodeType": "238", "messageId": "239", "endLine": 222, "endColumn": 27}, {"ruleId": "236", "severity": 1, "message": "249", "line": 176, "column": 29, "nodeType": "238", "messageId": "239", "endLine": 176, "endColumn": 35}, {"ruleId": "236", "severity": 1, "message": "243", "line": 212, "column": 23, "nodeType": "238", "messageId": "239", "endLine": 212, "endColumn": 28}, {"ruleId": "236", "severity": 1, "message": "243", "line": 222, "column": 22, "nodeType": "238", "messageId": "239", "endLine": 222, "endColumn": 27}, {"ruleId": "236", "severity": 1, "message": "249", "line": 188, "column": 29, "nodeType": "238", "messageId": "239", "endLine": 188, "endColumn": 35}, {"ruleId": "236", "severity": 1, "message": "243", "line": 200, "column": 26, "nodeType": "238", "messageId": "239", "endLine": 200, "endColumn": 31}, {"ruleId": "236", "severity": 1, "message": "243", "line": 222, "column": 23, "nodeType": "238", "messageId": "239", "endLine": 222, "endColumn": 28}, {"ruleId": "236", "severity": 1, "message": "243", "line": 232, "column": 24, "nodeType": "238", "messageId": "239", "endLine": 232, "endColumn": 29}, {"ruleId": "236", "severity": 1, "message": "243", "line": 242, "column": 24, "nodeType": "238", "messageId": "239", "endLine": 242, "endColumn": 29}, "Parsing error: Unexpected token (403:62)", "no-unused-vars", "'exportToCSV' is defined but never used.", "Identifier", "unusedVar", "'response' is assigned a value but never used.", "'startTime' is defined but never used.", "'endTime' is defined but never used.", "'state' is defined but never used.", "'level' is assigned a value but never used.", "'startDate' is assigned a value but never used.", "'endDate' is assigned a value but never used.", "'keyword' is assigned a value but never used.", "'dispatch' is defined but never used.", "'commit' is defined but never used."]