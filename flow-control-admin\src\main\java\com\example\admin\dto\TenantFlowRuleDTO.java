package com.example.admin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 租户流控规则DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Schema(description = "租户流控规则DTO")
public class TenantFlowRuleDTO {

    @Schema(description = "规则ID")
    private Long id;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 100, message = "规则名称长度不能超过100个字符")
    private String ruleName;



    @Schema(description = "限流阈值类型：0-线程数，1-QPS", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "限流阈值类型不能为空")
    @Min(value = 0, message = "限流阈值类型值必须为0或1")
    @Max(value = 1, message = "限流阈值类型值必须为0或1")
    private Integer grade;

    @Schema(description = "限流阈值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "限流阈值不能为空")
    @Min(value = 1, message = "限流阈值必须大于等于1")
    @Max(value = 999999, message = "限流阈值不能超过999999")
    private Double count;

    @Schema(description = "流控针对的调用来源策略：0-直接，1-关联，2-链路")
    @Min(value = 0, message = "调用来源策略值必须在0-2之间")
    @Max(value = 2, message = "调用来源策略值必须在0-2之间")
    private Integer strategy;

    @Schema(description = "关联资源")
    @Size(max = 256, message = "关联资源长度不能超过256个字符")
    private String refResource;

    @Schema(description = "流控效果：0-快速失败，1-Warm Up，2-排队等待，3-Warm Up + 排队等待")
    @Min(value = 0, message = "流控效果值必须在0-3之间")
    @Max(value = 3, message = "流控效果值必须在0-3之间")
    private Integer controlBehavior;

    @Schema(description = "Warm Up预热时间（秒）")
    @Min(value = 1, message = "预热时间必须大于等于1秒")
    @Max(value = 3600, message = "预热时间不能超过3600秒")
    private Integer warmUpPeriodSec;

    @Schema(description = "排队等待超时时间（毫秒）")
    @Min(value = 1, message = "排队超时时间必须大于等于1毫秒")
    @Max(value = 60000, message = "排队超时时间不能超过60000毫秒")
    private Integer maxQueueingTimeMs;

    @Schema(description = "是否集群模式")
    private Boolean clusterMode;

    @Schema(description = "集群配置信息")
    private String clusterConfig;

    @Schema(description = "优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "优先级不能为空")
    @Min(value = 1, message = "优先级必须大于等于1")
    @Max(value = 100, message = "优先级不能超过100")
    private Integer priority;

    @Schema(description = "是否启用：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用状态不能为空")
    @Min(value = 0, message = "启用状态值必须为0或1")
    @Max(value = 1, message = "启用状态值必须为0或1")
    private Integer enabled;

    @Schema(description = "生效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "生效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "规则描述")
    @Size(max = 500, message = "规则描述长度不能超过500个字符")
    private String description;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新人")
    private String updateBy;

    // Constructors
    public TenantFlowRuleDTO() {}

    public TenantFlowRuleDTO(String tenantId, String ruleName, Integer grade, Double count) {
        this.tenantId = tenantId;
        this.ruleName = ruleName;
        this.grade = grade;
        this.count = count;
        this.strategy = 0;
        this.controlBehavior = 0;
        this.warmUpPeriodSec = 10;
        this.maxQueueingTimeMs = 500;
        this.clusterMode = false;
        this.priority = 1;
        this.enabled = 1;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }



    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public Double getCount() {
        return count;
    }

    public void setCount(Double count) {
        this.count = count;
    }

    public Integer getStrategy() {
        return strategy;
    }

    public void setStrategy(Integer strategy) {
        this.strategy = strategy;
    }

    public String getRefResource() {
        return refResource;
    }

    public void setRefResource(String refResource) {
        this.refResource = refResource;
    }

    public Integer getControlBehavior() {
        return controlBehavior;
    }

    public void setControlBehavior(Integer controlBehavior) {
        this.controlBehavior = controlBehavior;
    }

    public Integer getWarmUpPeriodSec() {
        return warmUpPeriodSec;
    }

    public void setWarmUpPeriodSec(Integer warmUpPeriodSec) {
        this.warmUpPeriodSec = warmUpPeriodSec;
    }

    public Integer getMaxQueueingTimeMs() {
        return maxQueueingTimeMs;
    }

    public void setMaxQueueingTimeMs(Integer maxQueueingTimeMs) {
        this.maxQueueingTimeMs = maxQueueingTimeMs;
    }

    public Boolean getClusterMode() {
        return clusterMode;
    }

    public void setClusterMode(Boolean clusterMode) {
        this.clusterMode = clusterMode;
    }

    public String getClusterConfig() {
        return clusterConfig;
    }

    public void setClusterConfig(String clusterConfig) {
        this.clusterConfig = clusterConfig;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 验证流控行为相关参数
     */
    public boolean isValidControlBehaviorParams() {
        if (controlBehavior == null) {
            return false;
        }
        
        switch (controlBehavior) {
            case 0: // 快速失败
                return true;
            case 1: // Warm Up
                return warmUpPeriodSec != null && warmUpPeriodSec > 0;
            case 2: // 排队等待
                return maxQueueingTimeMs != null && maxQueueingTimeMs > 0;
            case 3: // Warm Up + 排队等待
                return warmUpPeriodSec != null && warmUpPeriodSec > 0 && 
                       maxQueueingTimeMs != null && maxQueueingTimeMs > 0;
            default:
                return false;
        }
    }

    /**
     * 验证时间范围
     */
    public boolean isValidTimeRange() {
        if (startTime == null && endTime == null) {
            return true;
        }
        if (startTime != null && endTime != null) {
            return startTime.isBefore(endTime);
        }
        return true;
    }

    /**
     * 获取流控效果描述
     */
    public String getControlBehaviorDesc() {
        if (controlBehavior == null) {
            return "未知";
        }
        switch (controlBehavior) {
            case 0:
                return "快速失败";
            case 1:
                return "Warm Up";
            case 2:
                return "排队等待";
            case 3:
                return "Warm Up + 排队等待";
            default:
                return "未知";
        }
    }

    /**
     * 获取限流类型描述
     */
    public String getGradeDesc() {
        if (grade == null) {
            return "未知";
        }
        return grade == 0 ? "线程数" : "QPS";
    }

    /**
     * 获取启用状态描述
     */
    public String getEnabledDesc() {
        if (enabled == null) {
            return "未知";
        }
        return enabled == 1 ? "启用" : "禁用";
    }

    @Override
    public String toString() {
        return "TenantFlowRuleDTO{" +
                "id=" + id +
                ", tenantId='" + tenantId + '\'' +
                ", ruleName='" + ruleName + '\'' +

                ", priority=" + priority +
                ", enabled=" + enabled +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                '}';
    }
}