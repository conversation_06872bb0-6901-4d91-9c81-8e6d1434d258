package com.example.admin.vo;

import java.time.LocalDateTime;

/**
 * IP流量规则VO
 */

public class IPFlowRuleVO {

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 规则名称
	 */
	private String ruleName;

	/**
	 * 租户ID
	 */
	private String tenantId;

	/**
	 * 租户名称
	 */
	private String tenantName;

	/**
	 * 规则类型：SINGLE_IP-单个IP，IP_RANGE-IP范围，CIDR-IP段
	 */
	private String ruleType;

	/**
	 * 规则类型名称
	 */
	private String ruleTypeName;

	/**
	 * IP值
	 */
	private String ipValue;

	/**
	 * QPS限制
	 */
	private Integer qpsLimit;

	/**
	 * 优先级
	 */
	private Integer priority;

	/**
	 * 规则描述
	 */
	private String description;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 规则状态：0-禁用，1-启用
	 */
	private Integer status;

	/**
	 * 规则状态名称
	 */
	private String statusName;

	/**
	 * 名单类型（用于规则匹配）
	 */
	private String listType = "LIMIT";

	/**
	 * 时间窗口（秒）
	 */
	private Integer timeWindow;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 更新人
	 */
	private String updateBy;

	/**
	 * 是否启用
	 */
	public boolean isEnabled() {
		return status != null && status == 1;
	}

	/**
	 * 获取规则摘要
	 */
	public String getRuleSummary() {
		StringBuilder sb = new StringBuilder();
		sb.append("IP: ").append(ipValue);

		if (qpsLimit != null && qpsLimit > 0) {
			sb.append(", QPS限制: ").append(qpsLimit);
		}

		sb.append(", 优先级: ").append(priority);

		return sb.toString();
	}

	/**
	 * 获取IP范围描述
	 */
	public String getIpRangeDescription() {
		switch (ruleType) {
		case "SINGLE_IP":
			return "单个IP: " + ipValue;
		case "IP_RANGE":
			return "IP范围: " + ipValue;
		case "CIDR":
			return "IP段: " + ipValue;
		default:
			return ipValue;
		}
	}

	// Getter and Setter methods
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public String getRuleType() {
		return ruleType;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}

	public String getRuleTypeName() {
		return ruleTypeName;
	}

	public void setRuleTypeName(String ruleTypeName) {
		this.ruleTypeName = ruleTypeName;
	}

	public String getIpValue() {
		return ipValue;
	}

	public void setIpValue(String ipValue) {
		this.ipValue = ipValue;
	}

	public Integer getQpsLimit() {
		return qpsLimit;
	}

	public void setQpsLimit(Integer qpsLimit) {
		this.qpsLimit = qpsLimit;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public LocalDateTime getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(LocalDateTime updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getStatusName() {
		return statusName;
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	public Integer getTimeWindow() {
		return timeWindow;
	}

	public void setTimeWindow(Integer timeWindow) {
		this.timeWindow = timeWindow;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

	public String getListType() {
		return listType;
	}

	public void setListType(String listType) {
		this.listType = listType;
	}
}