package com.example.gateway.monitor;

import com.alibaba.csp.sentinel.node.ClusterNode;
import com.alibaba.csp.sentinel.slotchain.ResourceWrapper;
import com.alibaba.csp.sentinel.slots.clusterbuilder.ClusterBuilderSlot;
import com.example.common.constant.FlowControlConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 流量控制监控组件
 * 负责收集和上报流量控制相关的监控数据
 */
@Slf4j
@Component
public class FlowControlMonitor {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String MONITOR_KEY_PREFIX = "flow:monitor:";
    private static final String STATISTICS_KEY_PREFIX = "flow:statistics:";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 定时收集监控数据（每分钟执行一次）
     */
    @Scheduled(fixedRate = 60000)
    public void collectMonitorData() {
        try {
            String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
            Map<String, Object> monitorData = new HashMap<>();

            // 收集Sentinel节点统计数据
            Map<ResourceWrapper, ClusterNode> clusterNodeMap = ClusterBuilderSlot.getClusterNodeMap();
            for (Map.Entry<ResourceWrapper, ClusterNode> entry : clusterNodeMap.entrySet()) {
                String resourceName = entry.getKey().getName();
                ClusterNode node = entry.getValue();

                Map<String, Object> nodeData = new HashMap<>();
                nodeData.put("passQps", node.passQps());
                nodeData.put("blockQps", node.blockQps());
                nodeData.put("totalQps", node.totalQps());
                nodeData.put("successQps", node.successQps());
                nodeData.put("exceptionQps", node.exceptionQps());
                nodeData.put("avgRt", node.avgRt());
                nodeData.put("minRt", node.minRt());
                nodeData.put("curThreadNum", node.curThreadNum());
                nodeData.put("timestamp", timestamp);

                monitorData.put(resourceName, nodeData);
            }

            // 存储到Redis
            String monitorKey = MONITOR_KEY_PREFIX + timestamp.substring(0, 16); // 精确到分钟
            redisTemplate.opsForHash().putAll(monitorKey, monitorData);
            redisTemplate.expire(monitorKey, 24, TimeUnit.HOURS); // 保留24小时

            log.debug("收集监控数据完成，资源数量: {}", monitorData.size());
        } catch (Exception e) {
            log.error("收集监控数据失败", e);
        }
    }

    /**
     * 记录流量控制事件
     */
    public void recordFlowControlEvent(String resourceName, String tenantId, String clientIp, 
                                     String action, String reason) {
        try {
            String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("resourceName", resourceName);
            eventData.put("tenantId", tenantId);
            eventData.put("clientIp", clientIp);
            eventData.put("action", action); // PASS, BLOCK, QUEUE
            eventData.put("reason", reason);
            eventData.put("timestamp", timestamp);

            String eventKey = "flow:event:" + System.currentTimeMillis();
            redisTemplate.opsForValue().set(eventKey, eventData, 1, TimeUnit.HOURS);

            // 更新统计计数
            updateStatistics(resourceName, tenantId, action);
        } catch (Exception e) {
            log.error("记录流量控制事件失败", e);
        }
    }

    /**
     * 更新统计数据
     */
    private void updateStatistics(String resourceName, String tenantId, String action) {
        String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String hour = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH"));

        // 按天统计
        String dayKey = STATISTICS_KEY_PREFIX + "day:" + date;
        redisTemplate.opsForHash().increment(dayKey, resourceName + ":" + action, 1);
        redisTemplate.opsForHash().increment(dayKey, "tenant:" + tenantId + ":" + action, 1);
        redisTemplate.expire(dayKey, 30, TimeUnit.DAYS);

        // 按小时统计
        String hourKey = STATISTICS_KEY_PREFIX + "hour:" + hour;
        redisTemplate.opsForHash().increment(hourKey, resourceName + ":" + action, 1);
        redisTemplate.opsForHash().increment(hourKey, "tenant:" + tenantId + ":" + action, 1);
        redisTemplate.expire(hourKey, 7, TimeUnit.DAYS);
    }

    /**
     * 获取资源监控数据
     */
    public Map<String, Object> getResourceMonitorData(String resourceName, String startTime, String endTime) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 这里可以根据时间范围查询Redis中的监控数据
            // 简化实现，返回当前时刻的数据
            ClusterNode node = ClusterBuilderSlot.getClusterNode(resourceName);
            if (node != null) {
                result.put("passQps", node.passQps());
                result.put("blockQps", node.blockQps());
                result.put("totalQps", node.totalQps());
                result.put("successQps", node.successQps());
                result.put("exceptionQps", node.exceptionQps());
                result.put("avgRt", node.avgRt());
                result.put("curThreadNum", node.curThreadNum());
            }
        } catch (Exception e) {
            log.error("获取资源监控数据失败: {}", resourceName, e);
        }
        return result;
    }

    /**
     * 获取租户统计数据
     */
    public Map<String, Object> getTenantStatistics(String tenantId, String date) {
        Map<String, Object> result = new HashMap<>();
        try {
            String dayKey = STATISTICS_KEY_PREFIX + "day:" + date;
            Map<Object, Object> dayStats = redisTemplate.opsForHash().entries(dayKey);
            
            String tenantPrefix = "tenant:" + tenantId + ":";
            for (Map.Entry<Object, Object> entry : dayStats.entrySet()) {
                String key = (String) entry.getKey();
                if (key.startsWith(tenantPrefix)) {
                    String action = key.substring(tenantPrefix.length());
                    result.put(action, entry.getValue());
                }
            }
        } catch (Exception e) {
            log.error("获取租户统计数据失败: {}", tenantId, e);
        }
        return result;
    }

    /**
     * 获取系统整体监控数据
     */
    public Map<String, Object> getSystemMonitorData() {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<ResourceWrapper, ClusterNode> clusterNodeMap = ClusterBuilderSlot.getClusterNodeMap();
            
            double totalPassQps = 0;
            double totalBlockQps = 0;
            double totalQps = 0;
            int activeThreads = 0;
            
            for (ClusterNode node : clusterNodeMap.values()) {
                totalPassQps += node.passQps();
                totalBlockQps += node.blockQps();
                totalQps += node.totalQps();
                activeThreads += node.curThreadNum();
            }
            
            result.put("totalPassQps", totalPassQps);
            result.put("totalBlockQps", totalBlockQps);
            result.put("totalQps", totalQps);
            result.put("activeThreads", activeThreads);
            result.put("resourceCount", clusterNodeMap.size());
            result.put("blockRate", totalQps > 0 ? totalBlockQps / totalQps : 0);
            
        } catch (Exception e) {
            log.error("获取系统监控数据失败", e);
        }
        return result;
    }
}