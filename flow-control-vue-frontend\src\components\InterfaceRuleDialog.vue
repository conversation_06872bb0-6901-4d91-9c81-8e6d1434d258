<template>
	<el-dialog
		:title="isEdit ? '编辑接口规则' : '新增接口规则'"
		:visible.sync="dialogVisible"
		width="700px"
		@close="handleClose"
	>
		<el-form
			:model="formData"
			:rules="rules"
			ref="interfaceRuleForm"
			label-width="120px"
		>
			<el-form-item label="规则名称" prop="ruleName">
				<el-input
					v-model="formData.ruleName"
					placeholder="请输入规则名称"
				/>
			</el-form-item>

			<el-form-item label="租户" prop="tenantId">
				<el-select
					v-model="formData.tenantId"
					placeholder="请选择租户"
					style="width: 100%"
					filterable
				>
					<el-option
						v-for="tenant in tenants"
						:key="tenant.tenantId"
						:label="`${tenant.tenantName} (${tenant.tenantId})`"
						:value="tenant.tenantId"
					/>
				</el-select>
			</el-form-item>

			<el-form-item label="资源名称" prop="resourceName">
				<el-input
					v-model="formData.resourceName"
					placeholder="请输入资源名称，如：/api/users"
				/>
				<div class="form-tip">资源名称通常是API接口路径，如：/api/users、/api/orders等</div>
			</el-form-item>

			<el-divider content-position="left">流量控制配置</el-divider>

			<el-form-item label="限流模式" prop="limitMode">
				<el-radio-group v-model="formData.limitMode">
					<el-radio :label="0">QPS限流</el-radio>
					<el-radio :label="1">并发数限流</el-radio>
				</el-radio-group>
				<div class="form-tip">
					QPS限流：限制每秒请求数量；并发数限流：限制同时处理的请求数量
				</div>
			</el-form-item>

			<el-form-item label="阈值" prop="threshold">
				<el-input-number
					v-model="formData.threshold"
					:min="1"
					:max="999999"
					style="width: 200px"
				/>
				<span class="unit-text">
					{{ formData.limitMode === 0 ? '次/秒' : '个' }}
				</span>
			</el-form-item>

			<el-form-item label="流控行为" prop="behavior">
				<el-radio-group v-model="formData.behavior">
					<el-radio :label="0">快速失败</el-radio>
					<el-radio :label="1">预热</el-radio>
					<el-radio :label="2">排队等待</el-radio>
				</el-radio-group>
				<div class="form-tip">
					快速失败：直接拒绝超出阈值的请求；预热：逐渐增加到设定阈值；排队等待：请求排队处理
				</div>
			</el-form-item>

			<el-form-item
				v-if="formData.behavior === 1"
				label="预热时长"
				prop="warmUpPeriod"
			>
				<el-input-number
					v-model="formData.warmUpPeriod"
					:min="1"
					:max="3600"
					style="width: 200px"
				/>
				<span class="unit-text">秒</span>
				<div class="form-tip">预热时长，系统在此时间内逐渐增加到设定的阈值</div>
			</el-form-item>

			<el-form-item
				v-if="formData.behavior === 2"
				label="排队超时"
				prop="queueTimeout"
			>
				<el-input-number
					v-model="formData.queueTimeout"
					:min="1"
					:max="60000"
					style="width: 200px"
				/>
				<span class="unit-text">毫秒</span>
				<div class="form-tip">请求在队列中的最大等待时间，超时后直接拒绝</div>
			</el-form-item>

			<el-form-item label="状态" prop="status">
				<el-radio-group v-model="formData.status">
					<el-radio :label="1">启用</el-radio>
					<el-radio :label="0">禁用</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>

		<div slot="footer" class="dialog-footer">
			<el-button @click="handleClose">取消</el-button>
			<el-button
				type="primary"
				@click="handleSubmit"
				:loading="submitting"
			>
				{{ isEdit ? '更新' : '创建' }}
			</el-button>
		</div>
	</el-dialog>
</template>

<script>
export default {
	name: 'InterfaceRuleDialog',
	props: {
		visible: {
			type: Boolean,
			default: false,
		},
		form: {
			type: Object,
			default: () => ({}),
		},
		isEdit: {
			type: Boolean,
			default: false,
		},
		submitting: {
			type: Boolean,
			default: false,
		},
		tenants: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			formData: {
				ruleName: '',
				tenantId: '',
				resourceName: '',
				limitMode: 0,
				threshold: null,
				behavior: 0,
				warmUpPeriod: null,
				queueTimeout: null,
				status: 1,
			},
			rules: {
				ruleName: [
					{ required: true, message: '请输入规则名称', trigger: 'blur' },
					{ min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' },
				],
				tenantId: [
					{ required: true, message: '请选择租户', trigger: 'change' },
				],
				resourceName: [
					{ required: true, message: '请输入资源名称', trigger: 'blur' },
					{ min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' },
				],
				threshold: [
					{ required: true, message: '请输入阈值', trigger: 'blur' },
					{
						type: 'number',
						min: 1,
						message: '阈值必须大于0',
						trigger: 'blur',
					},
				],
				warmUpPeriod: [
					{
						type: 'number',
						min: 1,
						message: '预热时长必须大于0',
						trigger: 'blur',
						required: false,
					},
				],
				queueTimeout: [
					{
						type: 'number',
						min: 1,
						message: '排队超时必须大于0',
						trigger: 'blur',
						required: false,
					},
				],
			},
		};
	},
	computed: {
		dialogVisible: {
			get() {
				return this.visible;
			},
			set(value) {
				if (!value) {
					this.$emit('close');
				}
			},
		},
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.formData = { ...this.form };
				this.$nextTick(() => {
					if (this.$refs.interfaceRuleForm) {
						this.$refs.interfaceRuleForm.clearValidate();
					}
				});
			}
		},

		// 监听流控行为变化，清空相关字段
		'formData.behavior'(newVal) {
			if (newVal !== 1) {
				this.formData.warmUpPeriod = null;
			}
			if (newVal !== 2) {
				this.formData.queueTimeout = null;
			}
		},
	},
	methods: {
		handleClose() {
			this.$emit('close');
		},

		handleSubmit() {
			this.$refs.interfaceRuleForm.validate((valid) => {
				if (valid) {
					// 根据流控行为处理相关字段
					const submitData = { ...this.formData };
					
					// 预热模式需要预热时长
					if (submitData.behavior === 1 && !submitData.warmUpPeriod) {
						this.$message.error('预热模式需要设置预热时长');
						return;
					}
					
					// 排队等待模式需要排队超时
					if (submitData.behavior === 2 && !submitData.queueTimeout) {
						this.$message.error('排队等待模式需要设置排队超时时间');
						return;
					}
					
					// 清理不需要的字段
					if (submitData.behavior !== 1) {
						submitData.warmUpPeriod = null;
					}
					if (submitData.behavior !== 2) {
						submitData.queueTimeout = null;
					}
					
					this.$emit('submit', submitData);
				} else {
					return false;
				}
			});
		},
	},
};
</script>

<style scoped>
.form-tip {
	color: #909399;
	font-size: 12px;
	margin-top: 5px;
	line-height: 1.4;
}

.unit-text {
	margin-left: 10px;
	color: #909399;
	font-size: 14px;
}

.dialog-footer {
	text-align: right;
}

.el-divider {
	margin: 20px 0;
}
</style>