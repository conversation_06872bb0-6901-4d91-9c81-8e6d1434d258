package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.entity.TenantConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租户配置Mapper接口
 */
@Mapper
public interface TenantConfigMapper extends BaseMapper<TenantConfig> {
    
    /**
     * 分页查询租户配置
     *
     * @param page 分页参数
     * @param tenantName 租户名称
     * @param status 状态
     * @return 租户配置列表
     */
    Page<TenantConfig> selectTenantConfigPage(Page<TenantConfig> page,
                                          @Param("tenantName") String tenantName,
                                          @Param("status") Integer status);
    
    /**
     * 根据租户ID查询租户配置
     *
     * @param tenantId 租户ID
     * @return 租户配置
     */
    TenantConfig selectByTenantId(@Param("tenantId") String tenantId);
    
    /**
     * 根据租户名称查询租户配置（用于重名检查）
     *
     * @param tenantName 租户名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 租户配置
     */
    TenantConfig selectByTenantName(@Param("tenantName") String tenantName,
                                  @Param("excludeId") Long excludeId);
    
    /**
     * 查询所有启用的租户配置
     *
     * @return 启用的租户配置列表
     */
    List<TenantConfig> selectAllEnabled();
    
    /**
     * 批量更新租户配置状态
     *
     * @param ids 租户ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids,
                          @Param("status") Integer status,
                          @Param("updateBy") String updateBy);
    
    /**
     * 统计租户配置总数
     *
     * @return 租户配置总数
     */
    int countTotal();
    
    /**
     * 统计启用的租户配置数量
     *
     * @return 启用的租户配置数量
     */
    int countEnabled();
    
    /**
     * 检查租户ID是否存在
     *
     * @param tenantId 租户ID
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在的数量
     */
    int existsByTenantId(@Param("tenantId") String tenantId, @Param("excludeId") Long excludeId);
    
    /**
     * 检查租户名称是否存在
     *
     * @param tenantName 租户名称
     * @param excludeId 排除的ID
     * @return 存在的数量
     */
    int existsByTenantName(@Param("tenantName") String tenantName,
                               @Param("excludeId") Long excludeId);
    
    /**
     * 获取租户配置总数
     *
     * @return 租户配置总数
     */
    int getTotalCount();
    
    /**
     * 获取启用的租户配置数量
     *
     * @return 启用的租户配置数量
     */
    int getEnabledCount();
    
    /**
     * 根据状态统计租户配置数量
     *
     * @param status 状态
     * @return 租户配置数量
     */
    int countByStatus(@Param("status") Integer status);
}