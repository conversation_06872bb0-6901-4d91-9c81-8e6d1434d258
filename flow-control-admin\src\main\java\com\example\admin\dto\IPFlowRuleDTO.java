package com.example.admin.dto;

import jakarta.validation.constraints.*;

/**
 * IP流量规则DTO
 */
public class IPFlowRuleDTO {

	/**
	 * 规则名称
	 */
	@NotBlank(message = "规则名称不能为空")
	@Size(max = 100, message = "规则名称长度不能超过100个字符")
	private String ruleName;

	/**
	 * 租户ID
	 */
	@Size(max = 50, message = "租户ID长度不能超过50个字符")
	private String tenantId;

	/**
	 * 规则类型：SINGLE_IP-单个IP，IP_RANGE-IP范围，CIDR-IP段，WILDCARD-通配符
	 */
	@NotBlank(message = "规则类型不能为空")
	@Pattern(regexp = "^(SINGLE_IP|IP_RANGE|CIDR|WILDCARD)$", message = "规则类型必须为SINGLE_IP、IP_RANGE、CIDR或WILDCARD")
	private String ruleType;

	/**
	 * IP值
	 */
	@NotBlank(message = "IP值不能为空")
	@Size(max = 200, message = "IP值长度不能超过200个字符")
	private String ipValue;

	/**
	 * QPS限制
	 */
	@Min(value = 1, message = "QPS限制必须大于0")
	@Max(value = 1000000, message = "QPS限制不能超过1000000")
	private Integer qpsLimit;

	/**
	 * 优先级
	 */
	@Min(value = 1, message = "优先级必须大于0")
	@Max(value = 1000, message = "优先级不能超过1000")
	private Integer priority = 1;

	/**
	 * 规则描述
	 */
	@Size(max = 500, message = "规则描述长度不能超过500个字符")
	private String description;

	/**
	 * 规则状态：0-禁用，1-启用
	 */
	@Min(value = 0, message = "规则状态值必须为0或1")
	@Max(value = 1, message = "规则状态值必须为0或1")
	private Integer status = 1;

	/**
	 * 名单类型：WHITELIST-白名单，BLACKLIST-黑名单，LIMIT-限流
	 */
	@NotBlank(message = "名单类型不能为空")
	private String listType = "LIMIT";

	/**
	 * 时间窗口（秒）
	 */
	@Min(value = 1, message = "时间窗口必须大于等于1秒")
	@Max(value = 3600, message = "时间窗口不能超过3600秒")
	private Integer timeWindow = 1;

	// 手动添加getter方法以解决Lombok问题
	public String getRuleName() {
		return ruleName;
	}

	public String getTenantId() {
		return tenantId;
	}

	public String getRuleType() {
		return ruleType;
	}

	public String getIpValue() {
		return ipValue;
	}

	public Integer getQpsLimit() {
		return qpsLimit;
	}

	public Integer getPriority() {
		return priority;
	}

	public String getDescription() {
		return description;
	}

	public Integer getStatus() {
		return status;
	}

	public String getListType() {
		return listType;
	}

	public Integer getTimeWindow() {
		return timeWindow;
	}

	// 手动添加setter方法
	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}

	public void setIpValue(String ipValue) {
		this.ipValue = ipValue;
	}

	public void setQpsLimit(Integer qpsLimit) {
		this.qpsLimit = qpsLimit;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public void setListType(String listType) {
		this.listType = listType;
	}

	public void setTimeWindow(Integer timeWindow) {
		this.timeWindow = timeWindow;
	}
}