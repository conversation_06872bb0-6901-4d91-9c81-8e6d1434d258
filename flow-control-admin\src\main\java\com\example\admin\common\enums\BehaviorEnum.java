package com.example.admin.common.enums;

/**
 * 流控行为枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public enum BehaviorEnum {
    
    /**
     * 快速失败
     */
    REJECT(0, "快速失败", "直接拒绝多余的请求，抛出FlowException"),
    
    /**
     * 预热
     */
    WARM_UP(1, "预热", "根据冷启动因子逐渐增加阈值，直到达到设置的QPS阈值"),
    
    /**
     * 排队等待
     */
    RATE_LIMITER(2, "排队等待", "让请求以均匀的速度通过，对应的是漏桶算法");
    
    /**
     * 行为值
     */
    private final Integer value;
    
    /**
     * 行为名称
     */
    private final String name;
    
    /**
     * 行为描述
     */
    private final String description;
    
    BehaviorEnum(Integer value, String name, String description) {
        this.value = value;
        this.name = name;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 根据值获取枚举
     * 
     * @param value 值
     * @return 枚举
     */
    public static BehaviorEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (BehaviorEnum behavior : values()) {
            if (behavior.getValue().equals(value)) {
                return behavior;
            }
        }
        return null;
    }
    
    /**
     * 检查值是否有效
     * 
     * @param value 值
     * @return 是否有效
     */
    public static boolean isValid(Integer value) {
        return getByValue(value) != null;
    }
}