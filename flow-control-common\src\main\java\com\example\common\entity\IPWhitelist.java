package com.example.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * IP白名单实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@TableName("ip_whitelist")
public class IPWhitelist {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名单名称
     */
    @TableField("list_name")
    private String listName;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * IP类型：SINGLE-单个IP，RANGE-IP范围，CIDR-IP段
     */
    @TableField("ip_type")
    private String ipType;

    /**
     * 单个IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * IP范围起始地址
     */
    @TableField("ip_start")
    private String ipStart;

    /**
     * IP范围结束地址
     */
    @TableField("ip_end")
    private String ipEnd;

    /**
     * CIDR格式IP段
     */
    @TableField("ip_cidr")
    private String ipCidr;

    /**
     * 优先级，数值越大优先级越高
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 是否启用：0-禁用，1-启用
     */
    @TableField("status")
    private Integer enabled;

    /**
     * 生效开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 来源：MANUAL-手动添加，IMPORT-批量导入，AUTO-自动生成
     */
    @TableField("source")
    private String source;

    /**
     * 描述信息
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    // Constructors
    public IPWhitelist() {}

    public IPWhitelist(String listName, String tenantId, String ipType) {
        this.listName = listName;
        this.tenantId = tenantId;
        this.ipType = ipType;
        this.priority = 1;
        this.enabled = 1;
        this.source = "MANUAL";
        this.deleted = 0;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getListName() {
        return listName;
    }

    public void setListName(String listName) {
        this.listName = listName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getIpType() {
        return ipType;
    }

    public void setIpType(String ipType) {
        this.ipType = ipType;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getIpStart() {
        return ipStart;
    }

    public void setIpStart(String ipStart) {
        this.ipStart = ipStart;
    }

    public String getIpEnd() {
        return ipEnd;
    }

    public void setIpEnd(String ipEnd) {
        this.ipEnd = ipEnd;
    }

    public String getIpCidr() {
        return ipCidr;
    }

    public void setIpCidr(String ipCidr) {
        this.ipCidr = ipCidr;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    /**
     * 获取IP配置信息（根据类型返回相应的IP配置）
     */
    public String getIpConfig() {
        switch (ipType) {
            case "SINGLE":
                return ipAddress;
            case "RANGE":
                return ipStart + " - " + ipEnd;
            case "CIDR":
                return ipCidr;
            default:
                return "";
        }
    }

    /**
     * 检查IP配置是否有效
     */
    public boolean isValidIpConfig() {
        switch (ipType) {
            case "SINGLE":
                return ipAddress != null && !ipAddress.trim().isEmpty();
            case "RANGE":
                return ipStart != null && !ipStart.trim().isEmpty() && 
                       ipEnd != null && !ipEnd.trim().isEmpty();
            case "CIDR":
                return ipCidr != null && !ipCidr.trim().isEmpty();
            default:
                return false;
        }
    }

    /**
     * 检查当前时间是否在有效期内
     */
    public boolean isCurrentlyValid() {
        if (enabled == null || enabled != 1) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 检查开始时间
        if (startTime != null && now.isBefore(startTime)) {
            return false;
        }
        
        // 检查结束时间
        if (endTime != null && now.isAfter(endTime)) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查是否在指定小时内过期
     */
    public boolean isExpiringWithin(int hours) {
        if (endTime == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiryThreshold = now.plusHours(hours);
        
        return endTime.isBefore(expiryThreshold) && endTime.isAfter(now);
    }

    /**
     * 计算剩余有效时间（小时）
     */
    public long calculateRemainingHours() {
        if (endTime == null) {
            return Long.MAX_VALUE; // 永不过期
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(endTime)) {
            return 0; // 已过期
        }
        
        return java.time.Duration.between(now, endTime).toHours();
    }

    /**
     * 计算IP数量（估算）
     */
    public int calculateIpCount() {
        switch (ipType) {
            case "SINGLE":
                return 1;
            case "RANGE":
                // 简化计算，实际应该解析IP地址范围
                return 256; // 估算值
            case "CIDR":
                if (ipCidr != null && ipCidr.contains("/")) {
                    try {
                        String[] parts = ipCidr.split("/");
                        int prefixLength = Integer.parseInt(parts[1]);
                        return (int) Math.pow(2, 32 - prefixLength);
                    } catch (Exception e) {
                        return 1;
                    }
                }
                return 1;
            default:
                return 1;
        }
    }

    /**
     * 检查指定IP是否匹配此白名单规则
     */
    public boolean matchesIP(String targetIp) {
        if (targetIp == null || targetIp.trim().isEmpty()) {
            return false;
        }
        
        switch (ipType) {
            case "SINGLE":
                return targetIp.equals(ipAddress);
            case "RANGE":
                // 简化实现，实际应该进行IP地址范围比较
                return isIpInRange(targetIp, ipStart, ipEnd);
            case "CIDR":
                return isIpInCidr(targetIp, ipCidr);
            default:
                return false;
        }
    }

    /**
     * 获取CIDR格式的IP
     */
    public String getCidrIp() {
        return ipCidr;
    }

    /**
     * 检查IP是否在指定范围内（简化实现）
     */
    private boolean isIpInRange(String targetIp, String startIp, String endIp) {
        // 简化实现，实际应该进行IP地址数值比较
        try {
            long target = ipToLong(targetIp);
            long start = ipToLong(startIp);
            long end = ipToLong(endIp);
            return target >= start && target <= end;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查IP是否在CIDR范围内（简化实现）
     */
    private boolean isIpInCidr(String targetIp, String cidr) {
        // 简化实现，实际应该进行CIDR匹配
        if (cidr == null || !cidr.contains("/")) {
            return false;
        }
        
        try {
            String[] parts = cidr.split("/");
            String networkIp = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);
            
            long targetLong = ipToLong(targetIp);
            long networkLong = ipToLong(networkIp);
            long mask = (-1L << (32 - prefixLength)) & 0xFFFFFFFFL;
            
            return (targetLong & mask) == (networkLong & mask);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 将IP地址转换为长整型
     */
    private long ipToLong(String ip) {
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            throw new IllegalArgumentException("Invalid IP address: " + ip);
        }
        
        long result = 0;
        for (int i = 0; i < 4; i++) {
            result = (result << 8) + Integer.parseInt(parts[i]);
        }
        return result & 0xFFFFFFFFL;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IPWhitelist that = (IPWhitelist) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(listName, that.listName) &&
                Objects.equals(tenantId, that.tenantId) &&
                Objects.equals(ipType, that.ipType) &&
                Objects.equals(ipAddress, that.ipAddress) &&
                Objects.equals(ipStart, that.ipStart) &&
                Objects.equals(ipEnd, that.ipEnd) &&
                Objects.equals(ipCidr, that.ipCidr) &&
                Objects.equals(priority, that.priority) &&
                Objects.equals(enabled, that.enabled) &&
                Objects.equals(startTime, that.startTime) &&
                Objects.equals(endTime, that.endTime) &&
                Objects.equals(source, that.source) &&
                Objects.equals(description, that.description) &&
                Objects.equals(createTime, that.createTime) &&
                Objects.equals(updateTime, that.updateTime) &&
                Objects.equals(createBy, that.createBy) &&
                Objects.equals(updateBy, that.updateBy) &&
                Objects.equals(deleted, that.deleted);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, listName, tenantId, ipType, ipAddress, ipStart, ipEnd,
                ipCidr, priority, enabled, startTime, endTime, source, description,
                createTime, updateTime, createBy, updateBy, deleted);
    }

    @Override
    public String toString() {
        return "IPWhitelist{"
                + "id=" + id +
                ", listName='" + listName + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", ipType='" + ipType + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", ipStart='" + ipStart + '\'' +
                ", ipEnd='" + ipEnd + '\'' +
                ", ipCidr='" + ipCidr + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", source='" + source + '\'' +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", deleted=" + deleted +
                '}';
    }
}
