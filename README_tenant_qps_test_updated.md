# 租户QPS限流测试脚本 - 更新版本

## 概述

本脚本是基于用户提供的租户限流规则数据重新生成的测试脚本，用于测试多个租户的QPS限流功能。

## 更新内容

### 租户规则配置

根据用户提供的数据库记录，脚本包含以下5个租户的限流规则：

1. **tenant1** - 租户1默认QPS限流
   - QPS限制: 5
   - 控制行为: 0 (快速失败)
   - 描述: 租户1的默认QPS限流规则

2. **tenant2** - 租户2预热限流
   - QPS限制: 5
   - 控制行为: 1 (预热启动)
   - 预热时间: 10秒
   - 描述: 租户2的预热限流规则

3. **tenant3** - 租户3排队限流
   - QPS限制: 5
   - 控制行为: 2 (排队等待)
   - 最大排队时间: 5000ms
   - 描述: 租户3的排队等待限流规则

4. **tenant4** - 租户4线程数限流
   - QPS限制: 5
   - 控制行为: 0 (快速失败)
   - 描述: 租户4的线程数限流规则

5. **tenant5** - 租户5综合限流
   - QPS限制: 5
   - 控制行为: 3 (预热+排队)
   - 预热时间: 15秒
   - 最大排队时间: 3000ms
   - 描述: 租户5的Warm Up + 排队等待限流规则

## 使用方法

### 基本命令

```bash
# 查看所有租户规则
python tenant_qps_test_updated.py --rules-only

# 运行完整测试（默认30秒，500 RPS）
python tenant_qps_test_updated.py

# 自定义测试参数
python tenant_qps_test_updated.py --duration 60 --rps 100 --url http://localhost:8080

# 只测试特定租户
python tenant_qps_test_updated.py --tenant tenant1

# 指定输出报告文件名
python tenant_qps_test_updated.py --output my_test_report.json
```

### 命令行参数

- `--url URL`: 服务器地址 (默认: http://localhost:8088)
- `--duration DURATION`: 测试持续时间(秒) (默认: 30)
- `--rps RPS`: 每秒请求数 (默认: 500)
- `--endpoint ENDPOINT`: 测试端点 (默认: /api/test)
- `--output OUTPUT`: 输出报告文件名
- `--rules-only`: 只显示租户规则
- `--tenant TENANT`: 只测试指定租户ID

## 功能特性

### 测试功能
- 支持多租户并发测试
- 支持4种控制行为：快速失败、预热启动、排队等待、预热+排队
- 实时统计请求成功率、限流率、QPS准确度
- 自动分析限流效果和控制行为有效性

### 报告功能
- 生成详细的JSON格式测试报告
- 提供控制台实时输出和总结报告
- 包含每个租户的详细测试数据和错误信息
- 统计不同控制行为的效果对比

### 高级特性
- 异步并发请求处理
- 智能QPS控制和准确度计算
- 预热期和排队时间的特殊处理逻辑
- 错误处理和异常恢复机制

## 输出示例

### 控制台输出
```
租户限流规则列表:
1. 租户1默认QPS限流 (tenant1)
   QPS限制: 5, 控制行为: 0
   描述: 租户1的默认QPS限流规则

2. 租户2预热限流 (tenant2)
   QPS限制: 5, 控制行为: 1
   预热时间: 10秒
   描述: 租户2的预热限流规则
...
```

### 测试报告
```
================================================================================
租户总QPS限流测试总结报告
================================================================================
测试时间: 2024-12-19T10:30:00.000000
服务地址: http://localhost:8088
测试配置: 30秒, 500 RPS
测试端点: /api/test

租户测试统计:
  总租户数: 5
  成功测试: 5
  失败测试: 0

请求统计:
  总请求数: 750
  成功请求: 150 (20.0%)
  被限流: 600 (80.0%)
  错误请求: 0

限流效果:
  平均QPS准确度偏差: 2.5%
  有效限流租户: 5/5
  限流有效率: 100.0%

控制行为统计:
  快速失败: 2个租户, 成功率20.0%, 限流率80.0%, QPS偏差2.1%
  预热启动: 1个租户, 成功率25.0%, 限流率75.0%, QPS偏差3.2%
  排队等待: 1个租户, 成功率18.0%, 限流率82.0%, QPS偏差2.8%
  预热+排队: 1个租户, 成功率22.0%, 限流率78.0%, QPS偏差1.9%
================================================================================
```

## 系统要求

- Python 3.7+
- aiohttp 库
- asyncio 支持

## 安装依赖

```bash
pip install aiohttp
```

## 注意事项

1. 确保目标服务器支持租户ID头部 (`X-Tenant-ID`)
2. 测试前请确认服务器的限流配置已正确部署
3. 高并发测试时注意服务器和网络的承载能力
4. 建议在测试环境中运行，避免影响生产服务

## 文件说明

- `tenant_qps_test_updated.py`: 主测试脚本
- `README_tenant_qps_test_updated.md`: 本说明文档
- `tenant_qps_flow_control_test_report_*.json`: 生成的测试报告文件