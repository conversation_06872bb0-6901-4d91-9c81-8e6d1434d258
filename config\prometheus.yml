# Prometheus配置文件
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'sentinel-flow-control'
    environment: 'docker'

# 规则文件
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # 网关服务监控
  - job_name: 'gateway-service'
    static_configs:
      - targets: ['gateway-service:8080']
    scrape_interval: 15s
    metrics_path: /actuator/prometheus
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # 管理后台服务监控
  - job_name: 'admin-service'
    static_configs:
      - targets: ['admin-service:8081']
    scrape_interval: 15s
    metrics_path: /actuator/prometheus
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # MySQL监控（需要mysql_exporter）
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis监控（需要redis_exporter）
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Nginx监控（需要nginx-prometheus-exporter）
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node Exporter监控（系统指标）
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s

  # cAdvisor监控（容器指标）
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    scrape_timeout: 10s
    metrics_path: /metrics

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-storage:9201/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"