package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.Result;
import com.example.admin.dto.TenantConfigDTO;
import com.example.admin.service.TenantConfigService;
import com.example.admin.vo.TenantConfigVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 租户配置管理控制器
 * 提供租户配置的CRUD操作
 */
@Tag(name = "租户配置管理")
@RestController
@RequestMapping("/api/tenant-configs")
@Validated
public class TenantConfigController {

    private static final Logger log = LoggerFactory.getLogger(TenantConfigController.class);

    @Autowired
    private TenantConfigService tenantConfigService;

    /**
     * 分页查询租户配置
     */
    @Operation(summary = "分页查询租户配置")
    @GetMapping
    public Result<Page<TenantConfigVO>> pageTenantConfigs(
        @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
        @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
        @Parameter(description = "租户名称") @RequestParam(required = false) String tenantName,
        @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        try {
            Page<TenantConfigVO> pageParam = new Page<>(page, size);
            Page<TenantConfigVO> result = tenantConfigService.selectTenantConfigPage(pageParam, tenantName, status);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询租户配置失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询租户配置
     */
    @Operation(summary = "根据ID查询租户配置")
    @GetMapping("/{id}")
    public Result<TenantConfigVO> getTenantConfigById(
            @Parameter(description = "租户ID") @PathVariable @NotNull Long id) {
        try {
            TenantConfigVO result = tenantConfigService.getTenantConfigById(id);
            if (result == null) {
                return Result.error("租户配置不存在");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询租户配置失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据租户ID查询租户配置
     */
    @Operation(summary = "根据租户ID查询租户配置")
    @GetMapping("/tenant/{tenantId}")
    public Result<TenantConfigVO> getTenantConfigByTenantId(
            @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId) {
        try {
            TenantConfigVO result = tenantConfigService.getTenantConfigByTenantId(tenantId);
            if (result == null) {
                return Result.error("租户配置不存在");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询租户配置失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建租户配置
     */
    @Operation(summary = "创建租户配置")
    @PostMapping
    public Result<String> createTenantConfig(
            @Parameter(description = "租户配置信息") @RequestBody @Valid TenantConfigDTO tenantConfigDTO) {
        try {
            boolean result = tenantConfigService.createTenantConfig(tenantConfigDTO);
            if (result) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建租户配置失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新租户配置
     */
    @Operation(summary = "更新租户配置")
    @PutMapping("/{id}")
    public Result<String> updateTenantConfig(
            @Parameter(description = "租户ID") @PathVariable @NotNull Long id,
            @Parameter(description = "租户配置信息") @RequestBody @Valid TenantConfigDTO tenantConfigDTO) {
        try {
            boolean result = tenantConfigService.updateTenantConfig(id, tenantConfigDTO);
            if (result) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新租户配置失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除租户配置
     */
    @Operation(summary = "删除租户配置")
    @DeleteMapping("/{id}")
    public Result<String> deleteTenantConfig(
            @Parameter(description = "租户ID") @PathVariable @NotNull Long id) {
        try {
            boolean result = tenantConfigService.deleteTenantConfig(id);
            if (result) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除租户配置失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除租户配置
     */
    @Operation(summary = "批量删除租户配置")
    @DeleteMapping("/batch")
    public Result<String> batchDeleteTenantConfigs(
            @Parameter(description = "租户ID列表") @RequestBody @NotEmpty List<Long> ids) {
        try {
            boolean result = tenantConfigService.batchDeleteTenantConfigs(ids);
            if (result) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除租户配置失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用租户配置
     */
    @Operation(summary = "启用/禁用租户配置")
    @PutMapping("/{id}/status")
    public Result<String> toggleTenantConfigStatus(
            @Parameter(description = "租户ID") @PathVariable @NotNull Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer status) {
        try {
            boolean result;
            if (status == 1) {
                result = tenantConfigService.enableTenant(id);
            } else {
                result = tenantConfigService.disableTenant(id);
            }
            if (result) {
                String message = status == 1 ? "启用成功" : "禁用成功";
                return Result.success(message);
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("切换租户配置状态失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 批量启用/禁用租户配置
     */
    @Operation(summary = "批量启用/禁用租户配置")
    @PutMapping("/batch/status")
    public Result<String> batchToggleTenantConfigStatus(
            @Parameter(description = "租户ID列表") @RequestBody @NotEmpty List<Long> ids,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer status) {
        try {
            boolean result = tenantConfigService.batchUpdateStatus(ids, status);
            if (result) {
                String message = status == 1 ? "批量启用成功" : "批量禁用成功";
                return Result.success(message);
            } else {
                return Result.error("批量操作失败");
            }
        } catch (Exception e) {
            log.error("批量切换租户配置状态失败", e);
            return Result.error("批量操作失败: " + e.getMessage());
        }
    }

    /**
     * 检查租户ID是否存在
     */
    @Operation(summary = "检查租户ID是否存在")
    @GetMapping("/exists/tenant-id/{tenantId}")
    public Result<Boolean> existsByTenantId(
            @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId) {
        try {
            boolean result = tenantConfigService.existsByTenantId(tenantId, null);
            return Result.success(result);
        } catch (Exception e) {
            log.error("检查租户ID是否存在失败", e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 检查租户名称是否存在
     */
    @Operation(summary = "检查租户名称是否存在")
    @GetMapping("/exists/tenant-name/{tenantName}")
    public Result<Boolean> existsByTenantName(
            @Parameter(description = "租户名称") @PathVariable @NotNull String tenantName) {
        try {
            boolean result = tenantConfigService.existsByTenantName(tenantName, null);
            return Result.success(result);
        } catch (Exception e) {
            log.error("检查租户名称是否存在失败", e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户总数
     */
    @Operation(summary = "获取租户总数")
    @GetMapping("/count")
    public Result<Long> getTenantCount() {
        try {
            long result = tenantConfigService.getTenantCount();
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取租户总数失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取启用租户数量
     */
    @Operation(summary = "获取启用租户数量")
    @GetMapping("/count/enabled")
    public Result<Long> getEnabledTenantCount() {
        try {
            long result = tenantConfigService.getEnabledTenantCount();
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取启用租户数量失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据状态获取租户数量
     */
    @Operation(summary = "根据状态获取租户数量")
    @GetMapping("/count/status/{status}")
    public Result<Long> getTenantCountByStatus(
            @Parameter(description = "状态") @PathVariable @NotNull Integer status) {
        try {
            long result = tenantConfigService.getTenantCountByStatus(status);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据状态获取租户数量失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
}