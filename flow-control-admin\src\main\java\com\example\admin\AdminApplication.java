package com.example.admin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Sentinel流量控制系统主启动类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@SpringBootApplication
@MapperScan("com.example.admin.mapper")
public class AdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
        System.out.println("\n==================================================");
        System.out.println("    Flow Control Admin System Started Successfully");
        System.out.println("    Version: v1.0.0");
        System.out.println("    Port: 8081");
        System.out.println("    Swagger UI: http://localhost:8081/doc.html");
        System.out.println("==================================================");
    }
}