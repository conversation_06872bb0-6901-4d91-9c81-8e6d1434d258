package com.example.common.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

/**
 * 租户配置实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@TableName("tenant_info")
public class TenantConfig {

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 租户ID
	 */
	@TableField("tenant_id")
	private String tenantId;

	/**
	 * 租户名称
	 */
	@TableField("tenant_name")
	private String tenantName;

	/**
	 * 租户描述
	 */
	@TableField("description")
	private String description;

	/**
	 * 联系人
	 */
	@TableField("contact_person")
	private String contactPerson;

	/**
	 * 联系电话
	 */
	@TableField("contact_phone")
	private String contactPhone;

	/**
	 * 联系邮箱
	 */
	@TableField("contact_email")
	private String contactEmail;



	/**
	 * 状态：0-禁用，1-启用
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 逻辑删除标识：0-未删除，1-已删除
	 */
	@TableField("deleted")
	@TableLogic
	private Integer deleted;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getContactPerson() {
		return contactPerson;
	}

	public void setContactPerson(String contactPerson) {
		this.contactPerson = contactPerson;
	}

	public String getContactPhone() {
		return contactPhone;
	}

	public void setContactPhone(String contactPhone) {
		this.contactPhone = contactPhone;
	}

	public String getContactEmail() {
		return contactEmail;
	}

	public void setContactEmail(String contactEmail) {
		this.contactEmail = contactEmail;
	}



	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public LocalDateTime getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(LocalDateTime updateTime) {
		this.updateTime = updateTime;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

	public Integer getDeleted() {
		return deleted;
	}

	public void setDeleted(Integer deleted) {
		this.deleted = deleted;
	}
}