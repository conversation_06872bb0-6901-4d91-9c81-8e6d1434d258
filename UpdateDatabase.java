import java.sql.*;

public class UpdateDatabase {
    private static final String URL = "*****************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "123456";
    
    public static void main(String[] args) {
        try {
            System.out.println("开始更新租户限流规则...");
            
            Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            
            // 清空现有的租户限流规则
            String deleteSql = "DELETE FROM tenant_flow_rules";
            PreparedStatement deleteStmt = conn.prepareStatement(deleteSql);
            int deleted = deleteStmt.executeUpdate();
            System.out.println("删除了 " + deleted + " 条旧规则");
            deleteStmt.close();
            
            // 插入新的租户限流规则
            String insertSql = "INSERT INTO tenant_flow_rules (id, tenant_id, rule_name, grade, count, control_behavior, warm_up_period_sec, max_queueing_time_ms, cluster_mode, ref_resource, limit_app, cluster_threshold_type, priority, enabled, start_time, end_time, description, create_time, update_time, create_by, update_by, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            PreparedStatement insertStmt = conn.prepareStatement(insertSql);
            
            // tenant1: 线程数限流，count=5
            insertStmt.setInt(1, 1);
            insertStmt.setString(2, "tenant1");
            insertStmt.setString(3, "租户1线程数限流");
            insertStmt.setInt(4, 0); // grade=0 线程数限流
            insertStmt.setDouble(5, 5.0);
            insertStmt.setInt(6, 0);
            insertStmt.setObject(7, null);
            insertStmt.setObject(8, null);
            insertStmt.setInt(9, 0);
            insertStmt.setObject(10, null);
            insertStmt.setInt(11, 0);
            insertStmt.setObject(12, null);
            insertStmt.setInt(13, 1);
            insertStmt.setInt(14, 1);
            insertStmt.setObject(15, null);
            insertStmt.setObject(16, null);
            insertStmt.setString(17, "租户1的线程数限流规则");
            insertStmt.setTimestamp(18, new Timestamp(System.currentTimeMillis()));
            insertStmt.setTimestamp(19, new Timestamp(System.currentTimeMillis()));
            insertStmt.setString(20, "system");
            insertStmt.setObject(21, null);
            insertStmt.setInt(22, 0);
            insertStmt.executeUpdate();
            
            // tenant2: QPS限流，count=20
            insertStmt.setInt(1, 2);
            insertStmt.setString(2, "tenant2");
            insertStmt.setString(3, "租户2QPS限流");
            insertStmt.setInt(4, 1); // grade=1 QPS限流
            insertStmt.setDouble(5, 20.0);
            insertStmt.setString(17, "租户2的QPS限流规则");
            insertStmt.executeUpdate();
            
            // tenant3: 线程数限流，count=3
            insertStmt.setInt(1, 3);
            insertStmt.setString(2, "tenant3");
            insertStmt.setString(3, "租户3线程数限流");
            insertStmt.setInt(4, 0); // grade=0 线程数限流
            insertStmt.setDouble(5, 3.0);
            insertStmt.setString(17, "租户3的线程数限流规则");
            insertStmt.executeUpdate();
            
            System.out.println("成功插入3条新规则");
            
            insertStmt.close();
            conn.close();
            
            System.out.println("数据库更新完成！");
            
        } catch (Exception e) {
            System.out.println("更新数据库失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}