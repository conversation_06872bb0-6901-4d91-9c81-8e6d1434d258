package com.example.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 监控统计数据传输对象
 *
 * <AUTHOR>
 * @date 2024-01-01
 */

@Schema(description = "监控统计数据传输对象")
public class MonitorStatisticsDTO {

    @Schema(description = "统计ID")
    private Long id;

    @NotBlank(message = "租户ID不能为空")
    @Schema(description = "租户ID", required = true)
    private String tenantId;

    @NotBlank(message = "资源名称不能为空")
    @Schema(description = "资源名称", required = true)
    private String resource;

    @NotNull(message = "QPS不能为空")
    @Schema(description = "每秒查询率", required = true)
    private Double qps;

    @NotNull(message = "响应时间不能为空")
    @Schema(description = "平均响应时间(ms)", required = true)
    private Double avgResponseTime;

    @NotNull(message = "成功数不能为空")
    @Schema(description = "成功请求数", required = true)
    private Long successCount;

    @NotNull(message = "失败数不能为空")
    @Schema(description = "失败请求数", required = true)
    private Long failureCount;

    @NotNull(message = "阻塞数不能为空")
    @Schema(description = "被阻塞请求数", required = true)
    private Long blockedCount;

    @Schema(description = "异常数")
    private Long exceptionCount;

    @Schema(description = "统计时间")
    private LocalDateTime statisticsTime;

    @Schema(description = "备注")
    private String remark;

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public Double getQps() {
        return qps;
    }

    public void setQps(Double qps) {
        this.qps = qps;
    }

    public Double getAvgResponseTime() {
        return avgResponseTime;
    }

    public void setAvgResponseTime(Double avgResponseTime) {
        this.avgResponseTime = avgResponseTime;
    }

    public Long getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Long successCount) {
        this.successCount = successCount;
    }

    public Long getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(Long failureCount) {
        this.failureCount = failureCount;
    }

    public Long getBlockedCount() {
        return blockedCount;
    }

    public void setBlockedCount(Long blockedCount) {
        this.blockedCount = blockedCount;
    }

    public Long getExceptionCount() {
        return exceptionCount;
    }

    public void setExceptionCount(Long exceptionCount) {
        this.exceptionCount = exceptionCount;
    }

    public LocalDateTime getStatisticsTime() {
        return statisticsTime;
    }

    public void setStatisticsTime(LocalDateTime statisticsTime) {
        this.statisticsTime = statisticsTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 计算总请求数
    public Long getTotalRequests() {
        return (successCount != null ? successCount : 0L) + 
               (failureCount != null ? failureCount : 0L) + 
               (blockedCount != null ? blockedCount : 0L);
    }

    // 获取平均响应时间的别名方法
    public Double getAvgRt() {
        return avgResponseTime;
    }

    // 获取资源名称的别名方法
    public String getResourceName() {
        return resource;
    }
}