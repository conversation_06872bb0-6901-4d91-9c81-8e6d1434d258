<template>
	<div class="tenant-management-tab">
		<!-- 操作栏 -->
		<div class="operation-bar">
			<el-button type="primary" @click="$emit('add-tenant')">
				<i class="el-icon-plus"></i>
				新增租户
			</el-button>
			<el-button @click="$emit('load-tenants')">
				<i class="el-icon-refresh"></i>
				刷新
			</el-button>
		</div>

		<!-- 租户表格 -->
		<el-table
			:data="tenants"
			v-loading="loading"
			border
			style="width: 100%"
			height="600"
		>
			<el-table-column prop="tenantId" label="租户ID" width="120" />
			<el-table-column prop="tenantName" label="租户名称" width="150" />
			<el-table-column prop="description" label="描述" width="200" show-overflow-tooltip />
			<el-table-column prop="contactPerson" label="联系人" width="120" />
			<el-table-column prop="contactEmail" label="联系邮箱" width="180" show-overflow-tooltip />
			<el-table-column prop="contactPhone" label="联系电话" width="130" />

			<el-table-column label="状态" width="80">
				<template slot-scope="scope">
					<el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
						{{ scope.row.status === 1 ? '启用' : '禁用' }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="createTime" label="创建时间" width="160">
				<template slot-scope="scope">
					{{ formatDate(scope.row.createTime) }}
				</template>
			</el-table-column>
			<el-table-column label="操作" width="280" min-width="280">
				<template slot-scope="scope">
					<div class="action-buttons">
						<el-button
							type="text"
							size="small"
							@click="$emit('edit-tenant', scope.row)"
						>
							编辑
						</el-button>
						<el-button
							type="text"
							size="small"
							class="delete-btn"
							@click="$emit('delete-tenant', scope.row)"
						>
							删除
						</el-button>
					</div>
				</template>
			</el-table-column>
		</el-table>

		<!-- 分页 -->
		<div class="pagination-wrapper">
			<el-pagination
				@size-change="$emit('size-change', $event)"
				@current-change="$emit('current-change', $event)"
				:current-page="pagination.current"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="pagination.size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="pagination.total"
			>
			</el-pagination>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TenantManagementTab',
	props: {
		tenants: {
			type: Array,
			default: () => [],
		},
		loading: {
			type: Boolean,
			default: false,
		},
		pagination: {
			type: Object,
			default: () => ({
				current: 1,
				size: 20,
				total: 0,
			}),
		},
	},
	methods: {
		formatDate(dateString) {
			if (!dateString) return '-';
			const date = new Date(dateString);
			return date.toLocaleString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit',
			});
		},
	},
};
</script>

<style scoped>
.tenant-management-tab {
	padding: 0;
}

.operation-bar {
	margin-bottom: 20px;
	display: flex;
	gap: 10px;
}

.no-limit {
	color: #909399;
	font-style: italic;
}

.action-buttons {
	display: flex;
	gap: 8px;
	align-items: center;
	justify-content: flex-start;
	flex-wrap: nowrap;
}

.delete-btn {
	color: #f56c6c;
}

.delete-btn:hover {
	color: #f56c6c;
}

.pagination-wrapper {
	margin-top: 20px;
	display: flex;
	justify-content: center;
}
</style>