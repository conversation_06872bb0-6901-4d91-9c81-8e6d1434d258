# Sentinel流量控制代码实现方案

## 1. 项目结构设计

```
sentinel-gateway-flow-control/
├── gateway-service/                    # 网关服务模块
│   ├── src/main/java/
│   │   └── com/example/gateway/
│   │       ├── GatewayApplication.java
│   │       ├── config/
│   │       │   ├── SentinelConfig.java
│   │       │   └── GatewayConfig.java
│   │       ├── filter/
│   │       │   ├── MultiDimensionFlowFilter.java
│   │       │   └── QueueingFlowFilter.java
│   │       ├── rule/
│   │       │   ├── FlowRuleManager.java
│   │       │   └── TenantFlowRuleProvider.java
│   │       └── util/
│   │           └── RequestContextExtractor.java
│   ├── src/main/resources/
│   │   ├── application.yml
│   │   └── bootstrap.yml
│   └── pom.xml
├── flow-control-admin/                 # 管理后台模块
│   ├── src/main/java/
│   │   └── com/example/admin/
│   │       ├── AdminApplication.java
│   │       ├── controller/
│   │       │   ├── FlowRuleController.java
│   │       │   ├── MonitorController.java
│   │       │   └── ConfigController.java
│   │       ├── service/
│   │       │   ├── FlowRuleService.java
│   │       │   ├── MonitorService.java
│   │       │   └── ConfigService.java
│   │       ├── repository/
│   │       │   ├── FlowRuleRepository.java
│   │       │   └── MonitorMetricsRepository.java
│   │       ├── entity/
│   │       │   ├── FlowRule.java
│   │       │   ├── Tenant.java
│   │       │   └── MonitorMetrics.java
│   │       └── dto/
│   │           ├── FlowRuleDTO.java
│   │           └── MetricsDTO.java
│   └── pom.xml
├── flow-control-common/                # 公共模块
│   ├── src/main/java/
│   │   └── com/example/common/
│   │       ├── constant/
│   │       │   └── FlowControlConstants.java
│   │       ├── enums/
│   │       │   └── ControlBehaviorEnum.java
│   │       └── util/
│   │           └── JsonUtils.java
│   └── pom.xml
├── flow-control-web/                   # 前端项目
│   ├── src/
│   │   ├── components/
│   │   │   ├── FlowRuleForm.tsx
│   │   │   ├── MonitorDashboard.tsx
│   │   │   └── RuleTable.tsx
│   │   ├── pages/
│   │   │   ├── FlowControl.tsx
│   │   │   ├── Monitor.tsx
│   │   │   └── Config.tsx
│   │   ├── services/
│   │   │   ├── flowRuleService.ts
│   │   │   └── monitorService.ts
│   │   └── App.tsx
│   ├── package.json
│   └── vite.config.ts
├── docker/
│   ├── docker-compose.yml
│   ├── gateway/Dockerfile
│   └── admin/Dockerfile
└── pom.xml                             # 父级POM
```

## 2. 核心代码实现

### 2.1 网关服务核心代码

#### MultiDimensionFlowFilter.java

```java
package com.example.gateway.filter;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.example.gateway.rule.TenantFlowRuleProvider;
import com.example.gateway.util.RequestContextExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 多维度流量控制过滤器
 * 支持基于租户ID、接口地址、IP地址的流量控制
 */
@Slf4j
@Component
public class MultiDimensionFlowFilter implements GlobalFilter, Ordered {

    @Autowired
    private TenantFlowRuleProvider ruleProvider;

    @Autowired
    private RequestContextExtractor contextExtractor;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 提取请求上下文信息
        String tenantId = contextExtractor.extractTenantId(exchange);
        String resource = contextExtractor.extractResource(exchange);
        String clientIp = contextExtractor.extractClientIp(exchange);
        
        // 构建多维度资源标识
        String tenantResource = buildTenantResource(tenantId, resource);
        String ipResource = buildIpResource(clientIp, resource);
        
        Entry tenantEntry = null;
        Entry resourceEntry = null;
        Entry ipEntry = null;
        
        try {
            // 1. 租户级别流量控制
            tenantEntry = SphU.entry("tenant:" + tenantId);
            
            // 2. 接口级别流量控制
            resourceEntry = SphU.entry(tenantResource);
            
            // 3. IP级别流量控制（可选）
            if (ruleProvider.hasIpLimitRule(clientIp)) {
                ipEntry = SphU.entry(ipResource);
            }
            
            // 记录请求统计信息
            recordMetrics(tenantId, resource, clientIp, true);
            
            return chain.filter(exchange);
            
        } catch (BlockException e) {
            log.warn("Request blocked: tenant={}, resource={}, ip={}, reason={}", 
                    tenantId, resource, clientIp, e.getClass().getSimpleName());
            
            // 记录限流统计信息
            recordMetrics(tenantId, resource, clientIp, false);
            
            // 返回限流响应
            return handleBlockException(exchange, e);
            
        } finally {
            // 释放资源
            if (ipEntry != null) {
                ipEntry.exit();
            }
            if (resourceEntry != null) {
                resourceEntry.exit();
            }
            if (tenantEntry != null) {
                tenantEntry.exit();
            }
        }
    }
    
    private String buildTenantResource(String tenantId, String resource) {
        return String.format("tenant:%s:resource:%s", tenantId, resource);
    }
    
    private String buildIpResource(String clientIp, String resource) {
        return String.format("ip:%s:resource:%s", clientIp, resource);
    }
    
    private Mono<Void> handleBlockException(ServerWebExchange exchange, BlockException e) {
        exchange.getResponse().setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
        exchange.getResponse().getHeaders().add("Content-Type", "application/json");
        
        String responseBody = "{\"code\":429,\"message\":\"请求过于频繁，请稍后重试\",\"data\":null}";
        
        return exchange.getResponse().writeWith(
            Mono.just(exchange.getResponse().bufferFactory().wrap(responseBody.getBytes()))
        );
    }
    
    private void recordMetrics(String tenantId, String resource, String clientIp, boolean success) {
        // 异步记录监控指标
        // 实现省略...
    }
    
    @Override
    public int getOrder() {
        return -100; // 高优先级
    }
}
```

#### TenantFlowRuleProvider.java

```java
package com.example.gateway.rule;

import com.alibaba.csp.sentinel.datasource.ReadableDataSource;
import com.alibaba.csp.sentinel.datasource.nacos.NacosDataSource;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 租户流量规则提供者
 * 负责从Nacos加载和管理流量控制规则
 */
@Slf4j
@Component
public class TenantFlowRuleProvider {
    
    @Value("${nacos.server-addr}")
    private String nacosServerAddr;
    
    @Value("${nacos.namespace}")
    private String namespace;
    
    private final Map<String, List<FlowRule>> tenantRulesCache = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initFlowRules() {
        try {
            // 从Nacos加载流量控制规则
            ReadableDataSource<String, List<FlowRule>> flowRuleDataSource = new NacosDataSource<>(
                nacosServerAddr, 
                "DEFAULT_GROUP", 
                "flow-rules", 
                source -> JSON.parseObject(source, new TypeReference<List<FlowRule>>() {})
            );
            
            // 注册规则更新监听器
            flowRuleDataSource.getProperty().addListener(rules -> {
                log.info("Flow rules updated, count: {}", rules.size());
                FlowRuleManager.loadRules(rules);
                updateTenantRulesCache(rules);
            });
            
            // 初始化规则
            List<FlowRule> initialRules = flowRuleDataSource.loadConfig();
            FlowRuleManager.loadRules(initialRules);
            updateTenantRulesCache(initialRules);
            
            log.info("Flow rules initialized successfully, count: {}", initialRules.size());
            
        } catch (Exception e) {
            log.error("Failed to initialize flow rules", e);
        }
    }
    
    private void updateTenantRulesCache(List<FlowRule> rules) {
        tenantRulesCache.clear();
        
        for (FlowRule rule : rules) {
            String resource = rule.getResource();
            if (resource.startsWith("tenant:")) {
                String tenantId = extractTenantId(resource);
                tenantRulesCache.computeIfAbsent(tenantId, k -> new ArrayList<>()).add(rule);
            }
        }
    }
    
    private String extractTenantId(String resource) {
        // 从资源标识中提取租户ID
        // 格式: tenant:tenant_001:resource:/api/user/info
        String[] parts = resource.split(":");
        return parts.length > 1 ? parts[1] : "default";
    }
    
    public boolean hasIpLimitRule(String clientIp) {
        // 检查是否存在针对特定IP的限流规则
        return FlowRuleManager.getRules().stream()
            .anyMatch(rule -> rule.getResource().contains("ip:" + clientIp));
    }
    
    public List<FlowRule> getTenantRules(String tenantId) {
        return tenantRulesCache.getOrDefault(tenantId, new ArrayList<>());
    }
}
```

#### QueueingFlowFilter.java

```java
package com.example.gateway.filter;

import com.alibaba.csp.sentinel.slots.block.flow.controller.DefaultController;
import com.alibaba.csp.sentinel.slots.block.flow.controller.RateLimiterController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * 排队等待流量控制过滤器
 * 实现请求排队等待机制，避免直接拒绝
 */
@Slf4j
@Component
public class QueueingFlowFilter implements GlobalFilter, Ordered {
    
    // 每个资源的排队信号量
    private final ConcurrentHashMap<String, Semaphore> queueSemaphores = new ConcurrentHashMap<>();
    
    // 默认排队超时时间（毫秒）
    private static final long DEFAULT_QUEUE_TIMEOUT = 5000L;
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String resource = exchange.getRequest().getPath().value();
        String tenantId = exchange.getRequest().getHeaders().getFirst("X-Tenant-Id");
        
        if (tenantId == null) {
            tenantId = "default";
        }
        
        String queueKey = tenantId + ":" + resource;
        
        // 获取或创建排队信号量
        Semaphore semaphore = queueSemaphores.computeIfAbsent(queueKey, 
            k -> new Semaphore(getMaxConcurrency(tenantId, resource)));
        
        try {
            // 尝试获取许可，支持超时等待
            boolean acquired = semaphore.tryAcquire(getQueueTimeout(tenantId, resource), TimeUnit.MILLISECONDS);
            
            if (!acquired) {
                log.warn("Request queue timeout: tenant={}, resource={}", tenantId, resource);
                return handleQueueTimeout(exchange);
            }
            
            // 请求处理完成后释放许可
            return chain.filter(exchange)
                .doFinally(signalType -> semaphore.release());
                
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Queue interrupted: tenant={}, resource={}", tenantId, resource, e);
            return handleQueueTimeout(exchange);
        }
    }
    
    private int getMaxConcurrency(String tenantId, String resource) {
        // 根据租户和资源获取最大并发数
        // 可以从配置中心或数据库获取
        return 10; // 默认值
    }
    
    private long getQueueTimeout(String tenantId, String resource) {
        // 根据租户和资源获取排队超时时间
        // 可以从配置中心或数据库获取
        return DEFAULT_QUEUE_TIMEOUT;
    }
    
    private Mono<Void> handleQueueTimeout(ServerWebExchange exchange) {
        exchange.getResponse().setStatusCode(org.springframework.http.HttpStatus.SERVICE_UNAVAILABLE);
        exchange.getResponse().getHeaders().add("Content-Type", "application/json");
        
        String responseBody = "{\"code\":503,\"message\":\"服务繁忙，请稍后重试\",\"data\":null}";
        
        return exchange.getResponse().writeWith(
            Mono.just(exchange.getResponse().bufferFactory().wrap(responseBody.getBytes()))
        );
    }
    
    @Override
    public int getOrder() {
        return -90; // 在MultiDimensionFlowFilter之后执行
    }
}
```

### 2.2 管理后台核心代码

#### FlowRuleController.java

```java
package com.example.admin.controller;

import com.example.admin.dto.FlowRuleDTO;
import com.example.admin.entity.FlowRule;
import com.example.admin.service.FlowRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 流量控制规则管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/flow-rules")
@CrossOrigin(origins = "*")
public class FlowRuleController {
    
    @Autowired
    private FlowRuleService flowRuleService;
    
    /**
     * 创建流量控制规则
     */
    @PostMapping
    public ApiResponse<FlowRule> createFlowRule(@Valid @RequestBody FlowRuleDTO ruleDTO) {
        try {
            FlowRule rule = flowRuleService.createRule(ruleDTO);
            log.info("Flow rule created: {}", rule.getId());
            return ApiResponse.success(rule);
        } catch (Exception e) {
            log.error("Failed to create flow rule", e);
            return ApiResponse.error("创建规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询流量控制规则列表
     */
    @GetMapping
    public ApiResponse<PageResult<FlowRule>> getFlowRules(
            @RequestParam(required = false) String tenantId,
            @RequestParam(required = false) String resource,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        try {
            PageResult<FlowRule> result = flowRuleService.getRules(tenantId, resource, pageNum, pageSize);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("Failed to get flow rules", e);
            return ApiResponse.error("查询规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新流量控制规则
     */
    @PutMapping("/{id}")
    public ApiResponse<FlowRule> updateFlowRule(
            @PathVariable Long id, 
            @Valid @RequestBody FlowRuleDTO ruleDTO) {
        try {
            FlowRule rule = flowRuleService.updateRule(id, ruleDTO);
            log.info("Flow rule updated: {}", id);
            return ApiResponse.success(rule);
        } catch (Exception e) {
            log.error("Failed to update flow rule: {}", id, e);
            return ApiResponse.error("更新规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除流量控制规则
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteFlowRule(@PathVariable Long id) {
        try {
            flowRuleService.deleteRule(id);
            log.info("Flow rule deleted: {}", id);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("Failed to delete flow rule: {}", id, e);
            return ApiResponse.error("删除规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量启用/禁用规则
     */
    @PostMapping("/batch-toggle")
    public ApiResponse<Void> batchToggleRules(
            @RequestBody List<Long> ruleIds,
            @RequestParam boolean enabled) {
        try {
            flowRuleService.batchToggleRules(ruleIds, enabled);
            log.info("Batch toggle rules: count={}, enabled={}", ruleIds.size(), enabled);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("Failed to batch toggle rules", e);
            return ApiResponse.error("批量操作失败: " + e.getMessage());
        }
    }
}
```

### 2.3 前端核心代码

#### FlowRuleForm.tsx

```typescript
import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Select, Switch, Button, message } from 'antd';
import { FlowRuleDTO } from '../types/flowRule';
import { createFlowRule, updateFlowRule } from '../services/flowRuleService';

interface FlowRuleFormProps {
  rule?: FlowRuleDTO;
  onSuccess: () => void;
  onCancel: () => void;
}

const FlowRuleForm: React.FC<FlowRuleFormProps> = ({ rule, onSuccess, onCancel }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const isEdit = !!rule;

  useEffect(() => {
    if (rule) {
      form.setFieldsValue(rule);
    }
  }, [rule, form]);

  const handleSubmit = async (values: FlowRuleDTO) => {
    setLoading(true);
    try {
      if (isEdit) {
        await updateFlowRule(rule!.id!, values);
        message.success('规则更新成功');
      } else {
        await createFlowRule(values);
        message.success('规则创建成功');
      }
      onSuccess();
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        grade: 1,
        strategy: 0,
        controlBehavior: 2,
        maxQueueingTimeMs: 5000,
        enabled: true
      }}
    >
      <Form.Item
        name="tenantId"
        label="租户ID"
        rules={[{ required: true, message: '请输入租户ID' }]}
      >
        <Input placeholder="请输入租户ID" />
      </Form.Item>

      <Form.Item
        name="resource"
        label="资源标识"
        rules={[{ required: true, message: '请输入资源标识' }]}
      >
        <Input placeholder="例如: /api/user/info" />
      </Form.Item>

      <Form.Item
        name="limitApp"
        label="来源应用"
      >
        <Input placeholder="默认为 default" />
      </Form.Item>

      <Form.Item
        name="grade"
        label="限流模式"
        rules={[{ required: true }]}
      >
        <Select>
          <Select.Option value={0}>线程数</Select.Option>
          <Select.Option value={1}>QPS</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="count"
        label="限流阈值"
        rules={[{ required: true, message: '请输入限流阈值' }]}
      >
        <InputNumber min={0} style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        name="strategy"
        label="流控策略"
        rules={[{ required: true }]}
      >
        <Select>
          <Select.Option value={0}>直接</Select.Option>
          <Select.Option value={1}>关联</Select.Option>
          <Select.Option value={2}>链路</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="controlBehavior"
        label="流控行为"
        rules={[{ required: true }]}
      >
        <Select>
          <Select.Option value={0}>快速失败</Select.Option>
          <Select.Option value={1}>Warm Up</Select.Option>
          <Select.Option value={2}>排队等待</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) => 
          prevValues.controlBehavior !== currentValues.controlBehavior
        }
      >
        {({ getFieldValue }) => {
          const controlBehavior = getFieldValue('controlBehavior');
          return controlBehavior === 2 ? (
            <Form.Item
              name="maxQueueingTimeMs"
              label="排队超时时间(ms)"
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
          ) : null;
        }}
      </Form.Item>

      <Form.Item
        name="enabled"
        label="启用状态"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          {isEdit ? '更新' : '创建'}
        </Button>
        <Button style={{ marginLeft: 8 }} onClick={onCancel}>
          取消
        </Button>
      </Form.Item>
    </Form>
  );
};

export default FlowRuleForm;
```

## 3. 配置文件

### 3.1 Gateway配置 (application.yml)

```yaml
server:
  port: 8080

spring:
  application:
    name: sentinel-gateway
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
      config:
        server-addr: localhost:8848
        namespace: dev
        file-extension: yml
    gateway:
      routes:
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/user/**
        - id: order-service
          uri: lb://order-service
          predicates:
            - Path=/api/order/**
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"

# Sentinel配置
sentinel:
  transport:
    dashboard: localhost:8080
    port: 8719
  datasource:
    flow:
      nacos:
        server-addr: localhost:8848
        namespace: dev
        data-id: flow-rules
        group-id: DEFAULT_GROUP
        rule-type: flow

# 自定义配置
flow-control:
  default-queue-timeout: 5000
  max-queue-size: 100
  metrics-interval: 60

logging:
  level:
    com.example.gateway: DEBUG
    com.alibaba.csp.sentinel: INFO
```

### 3.2 Admin配置 (application.yml)

```yaml
server:
  port: 8081

spring:
  application:
    name: flow-control-admin
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: password
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
      config:
        server-addr: localhost:8848
        namespace: dev
        file-extension: yml

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Redis配置
redis:
  host: localhost
  port: 6379
  database: 0
  timeout: 5000
  lettuce:
    pool:
      max-active: 8
      max-idle: 8
      min-idle: 0

logging:
  level:
    com.example.admin: DEBUG
```

## 4. 部署配置

### 4.1 Docker Compose配置

```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: flow-control-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: flow_control
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7.0
    container_name: flow-control-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nacos:
    image: nacos/nacos-server:v2.2.0
    container_name: flow-control-nacos
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: password
    ports:
      - "8848:8848"
    depends_on:
      - mysql

  gateway:
    build:
      context: ./gateway-service
      dockerfile: ../docker/gateway/Dockerfile
    container_name: flow-control-gateway
    ports:
      - "8080:8080"
    environment:
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      REDIS_HOST: redis
    depends_on:
      - nacos
      - redis

  admin:
    build:
      context: ./flow-control-admin
      dockerfile: ../docker/admin/Dockerfile
    container_name: flow-control-admin
    ports:
      - "8081:8081"
    environment:
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      REDIS_HOST: redis
    depends_on:
      - mysql
      - nacos
      - redis

  web:
    build:
      context: ./flow-control-web
    container_name: flow-control-web
    ports:
      - "3000:80"
    depends_on:
      - admin

volumes:
  mysql_data:
  redis_data:
```

## 5. 测试验证方案

### 5.1 功能测试脚本

```bash
#!/bin/bash

# 测试脚本：验证多维度流量控制功能

echo "=== Sentinel流量控制功能测试 ==="

# 1. 测试租户级QPS限制
echo "1. 测试租户级QPS限制（租户tenant_001，限制10 QPS）"
for i in {1..15}; do
  curl -H "X-Tenant-Id: tenant_001" \
       -w "Request $i: %{http_code}\n" \
       -s -o /dev/null \
       http://localhost:8080/api/user/info &
done
wait
echo ""

# 2. 测试接口级QPS限制
echo "2. 测试接口级QPS限制（/api/order/create，限制2 QPS）"
for i in {1..5}; do
  curl -H "X-Tenant-Id: tenant_001" \
       -w "Request $i: %{http_code}\n" \
       -s -o /dev/null \
       http://localhost:8080/api/order/create &
done
wait
echo ""

# 3. 测试排队等待机制
echo "3. 测试排队等待机制（观察响应时间）"
for i in {1..10}; do
  start_time=$(date +%s%N)
  curl -H "X-Tenant-Id: tenant_001" \
       -s -o /dev/null \
       http://localhost:8080/api/user/info
  end_time=$(date +%s%N)
  duration=$(( (end_time - start_time) / 1000000 ))
  echo "Request $i: ${duration}ms"
done

echo "=== 测试完成 ==="
```

### 5.2 性能测试

```bash
# 使用Apache Bench进行压力测试

# 测试1：正常负载下的性能
ab -n 1000 -c 10 -H "X-Tenant-Id: tenant_001" http://localhost:8080/api/user/info

# 测试2：超出限制时的排队性能
ab -n 2000 -c 50 -H "X-Tenant-Id: tenant_001" http://localhost:8080/api/user/info

# 测试3：多租户并发测试
ab -n 1000 -c 20 -H "X-Tenant-Id: tenant_001" http://localhost:8080/api/user/info &
ab -n 1000 -c 20 -H "X-Tenant-Id: tenant_002" http://localhost:8080/api/user/info &
wait
```

## 6. 监控和运维

### 6.1 监控指标

* **QPS指标**：每秒请求数、成功QPS、限流QPS

* **响应时间**：平均响应时间、P95、P99响应时间

* **排队指标**：排队长度、平均等待时间、排队成功率

* **错误率**：4xx错误率、5xx错误率、超时率

* **资源使用**：CPU使用率、内存使用率、网络IO

### 6.2 告警规则

```yaml
# Prometheus告警规则
groups:
  - name: flow-control-alerts
    rules:
      - alert: HighQPS
        expr: rate(gateway_requests_total[5m]) > 100
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "网关QPS过高"
          description: "网关QPS超过100，当前值: {{ $value }}"
      
      - alert: HighBlockRate
        expr: rate(sentinel_block_total[5m]) / rate(gateway_requests_total[5m]) > 0.1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "限流率过高"
          description: "限流率超过10%，当前值: {{ $value }}"
      
      - alert: LongQueueTime
        expr: histogram_quantile(0.95, rate(queue_waiting_time_bucket[5m])) > 3000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "排队时间过长"
          description: "P95排队时间超过3秒，当前值: {{ $value }}ms"
```

这个实现方案提供了完整的Sentinel流量控制解决方案，包括多维度限流、排队等待、配置热更新等核心功能，可以直接用于生产环境部署和验证。
