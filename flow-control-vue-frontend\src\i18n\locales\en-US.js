// English language pack
export default {
	// Common
	common: {
		confirm: 'Confirm',
		cancel: 'Cancel',
		save: 'Save',
		delete: 'Delete',
		edit: 'Edit',
		add: 'Add',
		search: 'Search',
		reset: 'Reset',
		submit: 'Submit',
		back: 'Back',
		close: 'Close',
		loading: 'Loading...',
		noData: 'No Data',
		operation: 'Operation',
		status: 'Status',
		createTime: 'Create Time',
		updateTime: 'Update Time',
		description: 'Description',
		name: 'Name',
		type: 'Type',
		value: 'Value',
		enabled: 'Enabled',
		disabled: 'Disabled',
		success: 'Success',
		failed: 'Failed',
		warning: 'Warning',
		info: 'Info',
		error: 'Error'
	},

	// Table and Pagination
	table: {
		emptyText: 'No Data',
		loading: 'Loading...',
		noDataText: 'No relevant data found'
	},

	pagination: {
		total: 'Total {total} items',
		goto: 'Go to',
		pageClassifier: '',
		page: 'page',
		prevPage: 'Previous',
		nextPage: 'Next',
		itemsPerPage: 'items per page'
	},

	// Element UI pagination component standard translation keys
	el: {
		pagination: {
			goto: 'Go to',
			pagesize: '/ page',
			total: 'Total {total}',
			pageClassifier: '',
			page: 'page',
			prev: 'Go to previous page',
			next: 'Go to next page',
			currentPage: 'page {pager}',
			prevPages: 'Previous {pager} pages',
			nextPages: 'Next {pager} pages'
		},
		table: {
			emptyText: 'No Data',
			confirmFilter: 'Confirm',
			resetFilter: 'Reset',
			clearFilter: 'All',
			sumText: 'Sum'
		}
	},

	// Navigation menu
	menu: {
		dashboard: 'Dashboard',
		tenants: 'Tenant Management',
		flowControl: 'Flow Control',
		tenantFlowRules: 'Tenant Flow Rules',
		ipFlowRules: 'IP Flow Rules',
		ipManagement: 'IP Blacklist/Whitelist',
		monitor: 'Real-time Monitor',
		statistics: 'Statistics',
		config: 'System Config'
	},

	// User related
	user: {
		login: 'Login',
		logout: 'Logout',
		username: 'Username',
		password: 'Password',
		profile: 'Profile',
		settings: 'Settings',
		themeSettings: 'Theme Settings',
		languageSettings: 'Language Settings'
	},

	// Dashboard
	dashboard: {
		title: 'Dashboard',
		totalRequests: 'Total Requests',
		blockedRequests: 'Blocked Requests',
		passedRequests: 'Passed Requests',
		systemLoad: 'System Load',
		memoryUsage: 'Memory Usage',
		cpuUsage: 'CPU Usage',
		requestTrend: 'Request Trend',
		topBlockedIPs: 'Top Blocked IPs',
		recentAlerts: 'Recent Alerts'
	},

	// Tenant flow rules
	tenantFlowRules: {
		title: 'Flow Rules',
		ruleName: 'Rule Name',
		resource: 'Resource',
		limitApp: 'Limit App',
		grade: 'Grade',
		count: 'Count',
		strategy: 'Strategy',
		controlBehavior: 'Control Behavior',
		warmUpPeriodSec: 'Warm Up Period',
		maxQueueingTimeMs: 'Max Queueing Time',
		clusterMode: 'Cluster Mode',
		addRule: 'Add Rule',
		editRule: 'Edit Rule',
		deleteRule: 'Delete Rule',
		enableRule: 'Enable Rule',
		disableRule: 'Disable Rule'
	},

	// IP flow rules
	ipFlowRules: {
		title: 'IP Rules',
		ipAddress: 'IP Address',
		ipRange: 'IP Range',
		ruleType: 'Rule Type',
		whitelist: 'Whitelist',
		blacklist: 'Blacklist',
		addRule: 'Add IP Rule',
		editRule: 'Edit IP Rule',
		deleteRule: 'Delete IP Rule',
		importRules: 'Import Rules',
		exportRules: 'Export Rules'
	},

	// Real-time monitor
	monitor: {
		title: 'Real-time Monitor',
		realTimeData: 'Real-time Data',
		requestRate: 'Request Rate',
		responseTime: 'Response Time',
		errorRate: 'Error Rate',
		activeConnections: 'Active Connections',
		systemMetrics: 'System Metrics',
		alertRules: 'Alert Rules',
		alertHistory: 'Alert History'
	},

	// Statistics
	statistics: {
		title: 'Statistics',
		timeRange: 'Time Range',
		today: 'Today',
		yesterday: 'Yesterday',
		lastWeek: 'Last 7 Days',
		lastMonth: 'Last 30 Days',
		custom: 'Custom',
		requestStatistics: 'Request Statistics',
		ipStatistics: 'IP Statistics',
		errorStatistics: 'Error Statistics',
		performanceAnalysis: 'Performance Analysis'
	},

	// System config
	config: {
		title: 'System Config',
		globalConfig: 'Global Config',
		alertConfig: 'Alert Config',
		notificationConfig: 'Notification Config',
		systemLogs: 'System Logs',

		// Global parameters
		globalParams: {
			title: 'Global Parameters',
			maxConnections: 'Max Connections',
			requestTimeout: 'Request Timeout',
			enableLogging: 'Enable Logging',
			logLevel: 'Log Level',
			enableMetrics: 'Enable Metrics',
			metricsInterval: 'Metrics Interval'
		},

		// Alert settings
		alertSettings: {
			title: 'Alert Settings',
			enableAlert: 'Enable Alert',
			cpuThreshold: 'CPU Threshold',
			memoryThreshold: 'Memory Threshold',
			diskThreshold: 'Disk Threshold',
			errorRateThreshold: 'Error Rate Threshold',
			responseTimeThreshold: 'Response Time Threshold'
		},

		// Notification config
		notification: {
			title: 'Notification Config',
			emailNotification: 'Email Notification',
			smsNotification: 'SMS Notification',
			webhookNotification: 'Webhook Notification',
			emailSettings: 'Email Settings',
			smtpServer: 'SMTP Server',
			smtpPort: 'SMTP Port',
			emailAccount: 'Email Account',
			emailPassword: 'Email Password',
			recipients: 'Recipients',
			smsSettings: 'SMS Settings',
			smsProvider: 'SMS Provider',
			apiKey: 'API Key',
			apiSecret: 'API Secret',
			phoneNumbers: 'Phone Numbers',
			webhookSettings: 'Webhook Settings',
			webhookUrl: 'Webhook URL',
			webhookMethod: 'Request Method',
			webhookHeaders: 'Request Headers',
			webhookSecret: 'Signature Secret'
		}
	},

	// Theme
	theme: {
		light: 'Light Mode',
		dark: 'Dark Mode',
		switchToLight: 'Switched to Light Mode',
		switchToDark: 'Switched to Dark Mode',
		themeSettings: 'Theme Settings',
		selectTheme: 'Select your preferred theme mode'
	},

	// Language
	language: {
		chinese: '简体中文',
		english: 'English',
		switchLanguage: 'Switch Language',
		languageChanged: 'Language Changed'
	},

	// Messages
	message: {
		saveSuccess: 'Save Successfully',
		saveFailed: 'Save Failed',
		deleteSuccess: 'Delete Successfully',
		deleteFailed: 'Delete Failed',
		operationSuccess: 'Operation Successfully',
		operationFailed: 'Operation Failed',
		loginSuccess: 'Login Successfully',
		loginFailed: 'Login Failed',
		logoutSuccess: 'Logout Successfully',
		logoutFailed: 'Logout Failed',
		networkError: 'Network Error',
		serverError: 'Server Error',
		parameterError: 'Parameter Error',
		permissionDenied: 'Permission Denied',
		confirmDelete: 'Confirm to delete this item?',
		confirmOperation: 'Confirm to execute this operation?'
	},

	// Form validation
	validation: {
		required: 'This field is required',
		email: 'Please enter a valid email address',
		phone: 'Please enter a valid phone number',
		url: 'Please enter a valid URL',
		number: 'Please enter a valid number',
		integer: 'Please enter a valid integer',
		minLength: 'Length cannot be less than {min} characters',
		maxLength: 'Length cannot exceed {max} characters',
		min: 'Value cannot be less than {min}',
		max: 'Value cannot be greater than {max}'
	},

	// User guide
	guide: {
		previous: 'Previous',
		next: 'Next',
		skip: 'Skip',
		finish: 'Finish',
		completed: 'Guide completed!',

		welcome: {
			title: 'Welcome to Flow Control System',
			content: 'This is a powerful network traffic management platform. Let\'s take a simple tour to understand the main features.'
		},

		sidebar: {
			title: 'Navigation Menu',
			content: 'The left sidebar is the main navigation menu where you can access various system modules.'
		},

		dashboard: {
			title: 'Dashboard',
			content: 'The dashboard is the system homepage, displaying important statistics and system status overview.'
		},

		theme: {
			title: 'Theme Toggle',
			content: 'Click here to switch between light and dark themes, choose your preferred interface style.'
		},

		language: {
			title: 'Language Toggle',
			content: 'Click here to switch between Chinese and English interface languages.'
		},

		complete: {
			title: 'Guide Complete',
			content: 'Congratulations! You have learned about the basic features of the system. To view the guide again, please click the help button in the bottom right corner.'
		}
	},

	// Help documentation
	help: {
		title: 'Help Documentation',
		search: 'Search help content',
		categories: {
			getting_started: 'Getting Started',
			tenant_flow_rules: 'Tenant Flow Rules',
			ip_flow_rules: 'IP Flow Rules',
			monitoring: 'Monitoring',
			configuration: 'Configuration',
			troubleshooting: 'Troubleshooting'
		},

		getting_started: {
			title: 'Getting Started',
			overview: 'System Overview',
			first_login: 'First Login',
			basic_setup: 'Basic Setup'
		},

		tenant_flow_rules: {
			title: 'Tenant Flow Rules Management',
			create_rule: 'Create Rule',
			edit_rule: 'Edit Rule',
			rule_priority: 'Rule Priority'
		},

		ip_flow_rules: {
			title: 'IP Flow Rules Management',
			whitelist: 'Whitelist Settings',
			blacklist: 'Blacklist Settings',
			ip_range: 'IP Range Configuration'
		}
	}
}