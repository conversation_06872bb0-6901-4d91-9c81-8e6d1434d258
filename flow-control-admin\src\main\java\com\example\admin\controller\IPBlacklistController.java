package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.Result;
import com.example.admin.dto.IPBlacklistDTO;
import com.example.admin.service.IPBlacklistService;
import com.example.admin.vo.IPBlacklistVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * IP黑名单控制器
 * 提供IP黑名单的CRUD操作、批量管理、文件导入导出、IP匹配检查等功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "IP黑名单管理")
@RestController
@RequestMapping("/api/ip-blacklists")
@Validated
public class IPBlacklistController {

    private static final Logger log = LoggerFactory.getLogger(IPBlacklistController.class);

    @Autowired
    private IPBlacklistService ipBlacklistService;

    /**
     * 分页查询IP黑名单
     */
    @Operation(summary = "分页查询IP黑名单")
    @GetMapping
    public Result<Page<IPBlacklistVO>> pageIPBlacklists(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "名单名称") @RequestParam(required = false) String listName,
            @Parameter(description = "IP类型") @RequestParam(required = false) String ipType,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled) {
        try {
            Page<IPBlacklistVO> pageParam = new Page<>(page, size);
            Page<IPBlacklistVO> result = ipBlacklistService.selectIPBlacklistPage(
                pageParam, tenantId, listName, ipType, enabled);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询IP黑名单失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询IP黑名单
     */
    @Operation(summary = "根据ID查询IP黑名单")
    @GetMapping("/{id}")
    public Result<IPBlacklistVO> getIPBlacklistById(
            @Parameter(description = "名单ID") @PathVariable @NotNull Long id) {
        try {
            IPBlacklistVO result = ipBlacklistService.getIPBlacklistById(id);
            if (result == null) {
                return Result.error("IP黑名单不存在");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询IP黑名单失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建IP黑名单
     */
    @Operation(summary = "创建IP黑名单")
    @PostMapping
    public Result<String> createIPBlacklist(
            @Parameter(description = "IP黑名单信息") @RequestBody @Valid IPBlacklistDTO ipListDTO) {
        try {
            boolean result = ipBlacklistService.createIPBlacklist(ipListDTO);
            if (result) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建IP黑名单失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新IP黑名单
     */
    @Operation(summary = "更新IP黑名单")
    @PutMapping("/{id}")
    public Result<String> updateIPBlacklist(
            @Parameter(description = "名单ID") @PathVariable @NotNull Long id,
            @Parameter(description = "IP黑名单信息") @RequestBody @Valid IPBlacklistDTO ipListDTO) {
        try {
            boolean result = ipBlacklistService.updateIPBlacklist(id, ipListDTO);
            if (result) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新IP黑名单失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除IP黑名单
     */
    @Operation(summary = "删除IP黑名单")
    @DeleteMapping("/{id}")
    public Result<String> deleteIPBlacklist(
            @Parameter(description = "名单ID") @PathVariable @NotNull Long id) {
        try {
            boolean result = ipBlacklistService.deleteIPBlacklist(id);
            if (result) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除IP黑名单失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除IP黑名单
     */
    @Operation(summary = "批量删除IP黑名单")
    @DeleteMapping("/batch")
    public Result<String> batchDeleteIPBlacklists(
            @Parameter(description = "名单ID列表") @RequestBody @NotEmpty List<Long> ids) {
        try {
            boolean result = ipBlacklistService.batchDeleteIPBlacklists(ids);
            if (result) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除IP黑名单失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用IP黑名单
     */
    @Operation(summary = "启用/禁用IP黑名单")
    @PutMapping("/{id}/status")
    public Result<String> updateIPBlacklistStatus(
            @Parameter(description = "名单ID") @PathVariable @NotNull Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer enabled) {
        try {
            boolean result;
            if (enabled == 1) {
                result = ipBlacklistService.enableIPBlacklist(id);
            } else {
                result = ipBlacklistService.disableIPBlacklist(id);
            }
            if (result) {
                String message = enabled == 1 ? "启用成功" : "禁用成功";
                return Result.success(message);
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("更新IP黑名单状态失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 批量启用/禁用IP黑名单
     */
    @Operation(summary = "批量启用/禁用IP黑名单")
    @PutMapping("/batch/status")
    public Result<String> batchUpdateIPBlacklistStatus(
            @Parameter(description = "名单ID列表") @RequestBody @NotEmpty List<Long> ids,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer enabled) {
        try {
            boolean result = ipBlacklistService.batchUpdateEnabled(ids, enabled);
            if (result) {
                String message = enabled == 1 ? "批量启用成功" : "批量禁用成功";
                return Result.success(message);
            } else {
                return Result.error("批量操作失败");
            }
        } catch (Exception e) {
            log.error("批量更新IP黑名单状态失败", e);
            return Result.error("批量操作失败: " + e.getMessage());
        }
    }

    /**
     * 根据租户ID查询IP黑名单
     */
    @Operation(summary = "根据租户ID查询IP黑名单")
    @GetMapping("/tenant/{tenantId}")
    public Result<List<IPBlacklistVO>> getIPBlacklistsByTenantId(
            @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled,
            @Parameter(description = "限制数量") @RequestParam(required = false) Integer limit) {
        try {
            List<IPBlacklistVO> result = ipBlacklistService.getIPBlacklistsByTenantId(
                tenantId, enabled, limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据租户ID查询IP黑名单失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建IP黑名单
     */
    @Operation(summary = "批量创建IP黑名单")
    @PostMapping("/batch")
    public Result<String> batchCreateIPBlacklists(
            @Parameter(description = "IP黑名单列表") @RequestBody @NotEmpty List<IPBlacklistDTO> ipListDTOList) {
        try {
            boolean result = ipBlacklistService.batchCreateIPBlacklists(ipListDTOList);
            if (result) {
                return Result.success("批量创建成功，共创建" + ipListDTOList.size() + "条名单");
            } else {
                return Result.error("批量创建失败");
            }
        } catch (Exception e) {
            log.error("批量创建IP黑名单失败", e);
            return Result.error("批量创建失败: " + e.getMessage());
        }
    }

    /**
     * 复制IP黑名单
     */
    @Operation(summary = "复制IP黑名单")
    @PostMapping("/{id}/copy")
    public Result<String> copyIPBlacklist(
            @Parameter(description = "源名单ID") @PathVariable @NotNull Long id,
            @Parameter(description = "新名单名称") @RequestParam @NotNull String newListName,
            @Parameter(description = "目标租户ID") @RequestParam(required = false) String targetTenantId) {
        try {
            boolean result = ipBlacklistService.copyIPBlacklist(id, newListName, targetTenantId);
            if (result) {
                return Result.success("复制成功");
            } else {
                return Result.error("复制失败");
            }
        } catch (Exception e) {
            log.error("复制IP黑名单失败", e);
            return Result.error("复制失败: " + e.getMessage());
        }
    }

    /**
     * 批量复制到其他租户
     */
    @Operation(summary = "批量复制到其他租户")
    @PostMapping("/batch/copy")
    public Result<Map<String, Object>> batchCopyToTenant(
            @Parameter(description = "源名单ID列表") @RequestBody @NotEmpty List<Long> sourceIds,
            @Parameter(description = "目标租户ID") @RequestParam @NotNull String targetTenantId,
            @Parameter(description = "名称前缀") @RequestParam(defaultValue = "copy_") String namePrefix) {
        try {
            Map<String, Object> result = ipBlacklistService.batchCopyToTenant(
                sourceIds, targetTenantId, namePrefix);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量复制IP黑名单失败", e);
            return Result.error("批量复制失败: " + e.getMessage());
        }
    }

    /**
     * 导入IP黑名单
     */
    @Operation(summary = "导入IP黑名单")
    @PostMapping("/import")
    public Result<Map<String, Object>> importIPBlacklists(
            @Parameter(description = "IP黑名单列表") @RequestBody @NotEmpty List<IPBlacklistDTO> ipListDTOList,
            @Parameter(description = "是否覆盖已存在的名单") @RequestParam(defaultValue = "false") boolean overwrite) {
        try {
            Map<String, Object> result = ipBlacklistService.importIPBlacklists(ipListDTOList, overwrite);
            return Result.success(result);
        } catch (Exception e) {
            log.error("导入IP黑名单失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出IP黑名单
     */
    @Operation(summary = "导出IP黑名单")
    @GetMapping("/export")
    public Result<List<IPBlacklistDTO>> exportIPBlacklists(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled) {
        try {
            List<IPBlacklistDTO> result = ipBlacklistService.exportIPBlacklists(
                tenantId, enabled);
            return Result.success(result);
        } catch (Exception e) {
            log.error("导出IP黑名单失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    /**
     * 从文件导入IP地址
     */
    @Operation(summary = "从文件导入IP地址")
    @PostMapping("/import/file")
    public Result<Map<String, Object>> importIPsFromFile(
            @Parameter(description = "IP地址文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "租户ID") @RequestParam @NotNull String tenantId,
            @Parameter(description = "名单名称") @RequestParam @NotNull String listName,
            @Parameter(description = "IP类型") @RequestParam @NotNull String ipType,
            @Parameter(description = "优先级") @RequestParam(defaultValue = "100") Integer priority) {
        try {
            if (file.isEmpty()) {
                return Result.error("文件不能为空");
            }
            
            Map<String, Object> result = ipBlacklistService.importIPsFromFile(
                file, tenantId, listName, ipType, priority);
            return Result.success(result);
        } catch (Exception e) {
            log.error("从文件导入IP地址失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出IP地址到文件
     */
    @Operation(summary = "导出IP地址到文件")
    @GetMapping("/export/file")
    public ResponseEntity<String> exportIPsToFile(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled,
            @Parameter(description = "文件格式") @RequestParam(defaultValue = "txt") String format) {
        try {
            String content = ipBlacklistService.exportIPsToFile(tenantId, enabled, format);
            
            String filename = "ip_blacklist_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".txt";
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE + ";charset=UTF-8")
                .body(content);
        } catch (Exception e) {
            log.error("导出IP地址到文件失败", e);
            return ResponseEntity.badRequest()
                .body("导出失败: " + e.getMessage());
        }
    }
}
