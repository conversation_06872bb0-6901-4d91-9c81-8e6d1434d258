import java.sql.*;

public class CreateThreadLimitRule {
    private static final String DB_URL = "****************************************";
    private static final String USER = "root";
    private static final String PASS = "123456";
    
    public static void main(String[] args) {
        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            // 建立连接
            Connection conn = DriverManager.getConnection(DB_URL, USER, PASS);
            
            // 先删除可能存在的测试规则
            String deleteSql = "DELETE FROM tenant_flow_rules WHERE tenant_id = 'thread_test'";
            PreparedStatement deleteStmt = conn.prepareStatement(deleteSql);
            int deleted = deleteStmt.executeUpdate();
            System.out.println("删除了 " + deleted + " 条旧的测试规则");
            
            // 插入新的线程限流规则
            String insertSql = "INSERT INTO tenant_flow_rules (tenant_id, rule_name, resource, count, grade, enabled) VALUES (?, ?, ?, ?, ?, ?)";
            PreparedStatement insertStmt = conn.prepareStatement(insertSql);
            
            insertStmt.setString(1, "thread_test");  // tenant_id
            insertStmt.setString(2, "线程限流测试规则");   // rule_name
            insertStmt.setString(3, "/api/test");     // resource
            insertStmt.setDouble(4, 2.0);             // count (阈值：2个线程)
            insertStmt.setInt(5, 0);                  // grade (0=线程限流)
            insertStmt.setInt(6, 1);                  // enabled
            
            int result = insertStmt.executeUpdate();
            
            if (result > 0) {
                System.out.println("成功创建线程限流规则：");
                System.out.println("- 租户ID: thread_test");
                System.out.println("- 规则名称: 线程限流测试规则");
                System.out.println("- 资源: /api/test");
                System.out.println("- 阈值: 2个线程");
                System.out.println("- 限流模式: 0 (线程限流)");
                System.out.println("- 状态: 启用");
            } else {
                System.out.println("创建规则失败");
            }
            
            // 验证插入结果
            String querySql = "SELECT * FROM tenant_flow_rules WHERE tenant_id = 'thread_test'";
            PreparedStatement queryStmt = conn.prepareStatement(querySql);
            ResultSet rs = queryStmt.executeQuery();
            
            System.out.println("\n验证插入结果：");
            while (rs.next()) {
                System.out.println("ID: " + rs.getInt("id"));
                System.out.println("租户ID: " + rs.getString("tenant_id"));
                System.out.println("规则名称: " + rs.getString("rule_name"));
                System.out.println("资源: " + rs.getString("resource"));
                System.out.println("阈值: " + rs.getDouble("count"));
                System.out.println("限流模式: " + rs.getInt("grade") + " (0=线程限流, 1=QPS限流)");
                System.out.println("状态: " + (rs.getInt("enabled") == 1 ? "启用" : "禁用"));
            }
            
            // 关闭连接
            rs.close();
            queryStmt.close();
            insertStmt.close();
            deleteStmt.close();
            conn.close();
            
        } catch (Exception e) {
            System.err.println("操作失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}