const state = {
	lists: [],
	currentList: null,
	loading: false,
	searchKeyword: '',
	filters: {
		status: '',
		ipType: '',
		tenantId: ''
	},
	pagination: {
		current: 1,
		pageSize: 10,
		total: 0
	},
	selectedListIds: []
}

const mutations = {
	SET_LISTS (state, { lists, total }) {
		state.lists = lists
		state.pagination.total = total
	},
	SET_CURRENT_LIST (state, list) {
		state.currentList = list
	},
	ADD_LIST (state, list) {
		state.lists.unshift(list)
		state.pagination.total += 1
	},
	UPDATE_LIST (state, updatedList) {
		const index = state.lists.findIndex(list => list.id === updatedList.id)
		if (index !== -1) {
			state.lists.splice(index, 1, updatedList)
		}
	},
	DELETE_LIST (state, listId) {
		state.lists = state.lists.filter(list => list.id !== listId)
		state.pagination.total -= 1
	},
	BATCH_DELETE_LISTS (state, listIds) {
		state.lists = state.lists.filter(list => !listIds.includes(list.id))
		state.pagination.total -= listIds.length
		state.selectedListIds = []
	},
	SET_LOADING (state, loading) {
		state.loading = loading
	},
	SET_SEARCH_KEYWORD (state, keyword) {
		state.searchKeyword = keyword
	},
	SET_FILTERS (state, filters) {
		state.filters = { ...state.filters, ...filters }
	},
	SET_PAGINATION (state, pagination) {
		state.pagination = { ...state.pagination, ...pagination }
	},
	SET_SELECTED_LIST_IDS (state, ids) {
		state.selectedListIds = ids
	},
	TOGGLE_LIST_SELECTION (state, listId) {
		const index = state.selectedListIds.indexOf(listId)
		if (index > -1) {
			state.selectedListIds.splice(index, 1)
		} else {
			state.selectedListIds.push(listId)
		}
	},
	CLEAR_SELECTION (state) {
		state.selectedListIds = []
	}
}

const actions = {
	async fetchLists ({ commit, state }) {
		commit('SET_LOADING', true)
		try {
			const { api } = await import('@/api')
			const params = {
				page: state.pagination.current,
				pageSize: state.pagination.pageSize,
				keyword: state.searchKeyword,
				...state.filters
			}
			const response = await api.ipBlacklists.getList(params)
			// 转换后端数据格式到前端期望格式
			const transformedLists = (response.data.records || []).map(list => ({
				id: list.id,
				name: list.listName,
				listName: list.listName,
				ipAddress: list.ipAddress,
				ipType: list.ipType,
				ipTypeName: list.ipTypeName,
				tenantId: list.tenantId,
				tenantName: list.tenantName,
				priority: list.priority,
				status: list.status,
				statusName: list.statusName,
				description: list.description,
				effectiveTime: list.effectiveTime,
				expirationTime: list.expirationTime,
				createTime: list.createTime,
				updateTime: list.updateTime,
				enabled: list.status === 1  // 将后端的status转换为前端的enabled布尔值
			}))
			commit('SET_LISTS', {
				lists: transformedLists,
				total: response.data.total || 0
			})
		} catch (error) {
			console.error('获取IP黑名单失败:', error)
			commit('SET_LISTS', { lists: [], total: 0 })
		} finally {
			commit('SET_LOADING', false)
		}
	},

	async createList ({ commit }, list) {
		try {
			const { api } = await import('@/api')
			const response = await api.ipBlacklists.create(list)
			commit('ADD_LIST', response.data)
			return response.data
		} catch (error) {
			console.error('创建IP黑名单失败:', error)
			throw error
		}
	},

	async updateList ({ commit }, list) {
		try {
			const { api } = await import('@/api')
			const response = await api.ipBlacklists.update(list.id, list)
			commit('UPDATE_LIST', response.data)
			return response.data
		} catch (error) {
			console.error('更新IP黑名单失败:', error)
			throw error
		}
	},

	async deleteList ({ commit }, listId) {
		try {
			const { api } = await import('@/api')
			await api.ipBlacklists.delete(listId)
			commit('DELETE_LIST', listId)
		} catch (error) {
			console.error('删除IP黑名单失败:', error)
			throw error
		}
	},

	async batchDeleteLists ({ commit, state }) {
		try {
			const { api } = await import('@/api')
			await api.ipBlacklists.batchDelete(state.selectedListIds)
			commit('BATCH_DELETE_LISTS', state.selectedListIds)
		} catch (error) {
			console.error('批量删除IP黑名单失败:', error)
			throw error
		}
	},

	async updateStatus ({ commit }, { id, enabled }) {
		try {
			const { api } = await import('@/api')
			const response = await api.ipBlacklists.updateStatus(id, enabled)
			commit('UPDATE_LIST', response.data)
			return response.data
		} catch (error) {
			console.error('更新IP黑名单状态失败:', error)
			throw error
		}
	},

	async batchUpdateStatus ({ commit, state }, enabled) {
		try {
			const { api } = await import('@/api')
			await api.ipBlacklists.batchUpdateStatus(state.selectedListIds, enabled)
			// 重新获取数据以更新状态
			await this.dispatch('ipBlacklist/fetchLists')
		} catch (error) {
			console.error('批量更新IP黑名单状态失败:', error)
			throw error
		}
	},

	async copyList ({ commit }, { id, newListName, targetTenantId }) {
		try {
			const { api } = await import('@/api')
			const response = await api.ipBlacklists.copy(id, newListName, targetTenantId)
			commit('ADD_LIST', response.data)
			return response.data
		} catch (error) {
			console.error('复制IP黑名单失败:', error)
			throw error
		}
	},

	async importLists ({ dispatch }, { data, overwrite }) {
		try {
			const { api } = await import('@/api')
			await api.ipBlacklists.import(data, overwrite)
			// 重新获取数据
			await dispatch('fetchLists')
		} catch (error) {
			console.error('导入IP黑名单失败:', error)
			throw error
		}
	},

	async exportLists ({ state }, { tenantId, enabled }) {
		try {
			const { api } = await import('@/api')
			return await api.ipBlacklists.export(tenantId, enabled)
		} catch (error) {
			console.error('导出IP黑名单失败:', error)
			throw error
		}
	},

	async checkMatch ({ state }, { ip, tenantId }) {
		try {
			const { api } = await import('@/api')
			return await api.ipBlacklists.checkMatch(ip, tenantId)
		} catch (error) {
			console.error('检查IP匹配失败:', error)
			throw error
		}
	},

	setSearchKeyword ({ commit, dispatch }, keyword) {
		commit('SET_SEARCH_KEYWORD', keyword)
		commit('SET_PAGINATION', { current: 1 })
		dispatch('fetchLists')
	},

	setFilters ({ commit, dispatch }, filters) {
		commit('SET_FILTERS', filters)
		commit('SET_PAGINATION', { current: 1 })
		dispatch('fetchLists')
	},

	setPagination ({ commit, dispatch }, pagination) {
		commit('SET_PAGINATION', pagination)
		dispatch('fetchLists')
	},

	setSelectedListIds ({ commit }, ids) {
		commit('SET_SELECTED_LIST_IDS', ids)
	},

	toggleListSelection ({ commit }, listId) {
		commit('TOGGLE_LIST_SELECTION', listId)
	},

	clearSelection ({ commit }) {
		commit('CLEAR_SELECTION')
	}
}

const getters = {
	allLists: state => state.lists,
	currentList: state => state.currentList,
	isLoading: state => state.loading,
	searchKeyword: state => state.searchKeyword,
	filters: state => state.filters,
	pagination: state => state.pagination,
	selectedListIds: state => state.selectedListIds,
	selectedListsCount: state => state.selectedListIds.length,
	hasSelectedLists: state => state.selectedListIds.length > 0,
	filteredLists: state => {
		let filtered = state.lists

		// 搜索关键词过滤
		if (state.searchKeyword) {
			const keyword = state.searchKeyword.toLowerCase()
			filtered = filtered.filter(list =>
				list.listName?.toLowerCase().includes(keyword) ||
				list.ipAddress?.toLowerCase().includes(keyword) ||
				list.tenantName?.toLowerCase().includes(keyword)
			)
		}

		return filtered
	},
	enabledListsCount: state => state.lists.filter(list => list.status === 1).length,
	disabledListsCount: state => state.lists.filter(list => list.status === 0).length
}

export default {
	namespaced: true,
	state,
	mutations,
	actions,
	getters
}