# 租户总QPS限流测试脚本使用说明

## 概述

`tenant_total_qps_flow_control_test.py` 是一个专门用于测试租户级别总QPS限流功能的Python脚本。该脚本基于Sentinel流量控制规则，支持多种控制行为的测试验证，包括快速失败、预热启动、排队等待和综合限流模式。

## 主要功能

### 🎯 核心特性
- **多租户测试**: 支持同时测试多个租户的限流规则
- **多种控制行为**: 支持快速失败、预热启动、排队等待、预热+排队等控制模式
- **异步高并发**: 使用asyncio和aiohttp实现高性能异步请求
- **智能分析**: 根据不同控制行为智能分析预期结果和实际效果
- **详细统计**: 提供QPS准确度、响应时间、限流效果等详细统计信息
- **自动报告**: 生成JSON格式的详细测试报告

### 📊 测试指标
- QPS准确度分析
- 成功率和限流率统计
- 响应时间分布（平均值、中位数、P95等）
- 控制行为效果分析
- 租户间隔离效果验证

## 安装依赖

```bash
# 安装必需的Python包
pip install aiohttp asyncio

# 或者使用requirements.txt（如果有的话）
pip install -r requirements.txt
```

## 使用方法

### 基本用法

```bash
# 使用默认配置运行所有租户测试
python tenant_total_qps_flow_control_test.py

# 指定服务器地址和测试参数
python tenant_total_qps_flow_control_test.py --url http://localhost:8088 --duration 30 --rps 500
```

### 高级用法

```bash
# 测试特定租户
python tenant_total_qps_flow_control_test.py --tenant tenant1

# 自定义测试端点
python tenant_total_qps_flow_control_test.py --endpoint /api/admin/users

# 指定输出报告文件名
python tenant_total_qps_flow_control_test.py --output my_test_report.json

# 只显示租户规则列表
python tenant_total_qps_flow_control_test.py --rules-only
```

### 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--url` | `http://localhost:8088` | 服务器地址 |
| `--duration` | `30` | 测试持续时间（秒） |
| `--rps` | `500` | 每秒请求数 |
| `--endpoint` | `/api/test` | 测试API端点 |
| `--output` | 自动生成 | 输出报告文件名 |
| `--tenant` | 无 | 只测试指定租户ID |
| `--rules-only` | 无 | 只显示租户规则列表 |

## 租户限流规则

脚本内置了5个租户的限流规则，基于用户提供的数据库配置：

### 1. 租户1 - 默认QPS限流
- **租户ID**: `tenant1`
- **QPS限制**: 100
- **控制行为**: 快速失败 (0)
- **描述**: 标准的QPS限流，超出限制立即拒绝

### 2. 租户2 - 预热限流
- **租户ID**: `tenant2`
- **QPS限制**: 200
- **控制行为**: 预热启动 (1)
- **预热时间**: 10秒
- **描述**: 系统启动时逐渐增加到目标QPS

### 3. 租户3 - 排队限流
- **租户ID**: `tenant3`
- **QPS限制**: 50
- **控制行为**: 排队等待 (2)
- **最大排队时间**: 5000ms
- **描述**: 超出限制的请求进入队列等待处理

### 4. 租户4 - 线程数限流
- **租户ID**: `tenant4`
- **QPS限制**: 5（实际为线程数限制）
- **控制行为**: 快速失败 (0)
- **描述**: 基于并发线程数的限流控制

### 5. 租户5 - 综合限流
- **租户ID**: `tenant5`
- **QPS限制**: 300
- **控制行为**: 预热+排队 (3)
- **预热时间**: 15秒
- **最大排队时间**: 3000ms
- **描述**: 结合预热启动和排队等待的综合限流策略

## 测试结果分析

### 输出示例

```
================================================================================
租户总QPS限流测试开始
测试时间: 2025-01-20 15:30:00
服务地址: http://localhost:8088
测试配置: 30秒, 500 RPS
测试端点: /api/test
租户规则数量: 5
================================================================================

[1/5] 测试租户: tenant1 - 租户1默认QPS限流
租户ID: tenant1
QPS限制: 100
控制行为: 快速失败
测试配置: 30秒, 500 RPS
预期成功请求数: 3000

=== 测试结果 ===
总请求数: 15000
成功请求: 3000 (20.0%)
被限流: 12000 (80.0%)
错误请求: 0 (0.0%)
实际QPS: 500.00
成功QPS: 100.00 (限制: 100)
QPS准确度偏差: 0.0%
平均响应时间: 8.50ms
P95响应时间: 15.20ms
控制行为分析: 快速失败模式，共拦截12000个请求
```

### 关键指标说明

#### 1. QPS准确度分析
- **计算方式**: `|实际成功QPS - 限制QPS| / 限制QPS * 100%`
- **理想值**: < 5%
- **说明**: 衡量限流控制的精确度

#### 2. 控制行为效果
- **快速失败**: 关注拦截请求数量和响应时间
- **预热启动**: 分析预热期和正常期的请求分布
- **排队等待**: 关注响应时间增加和排队效果
- **综合模式**: 结合预热和排队的综合分析

#### 3. 限流有效性
- **有效限流**: 被限流请求数 > 0
- **限流率**: 被限流请求数 / 总请求数
- **成功率**: 成功请求数 / 总请求数

### 测试报告结构

生成的JSON报告包含以下部分：

```json
{
  "test_time": "2025-01-20T15:30:00",
  "base_url": "http://localhost:8088",
  "test_config": {
    "duration": 30,
    "requests_per_second": 500,
    "test_endpoint": "/api/test"
  },
  "tenant_rules": [...],
  "results": [
    {
      "rule_info": {...},
      "test_config": {...},
      "actual_results": {...},
      "statistics": {...},
      "analysis": {...}
    }
  ],
  "summary": {
    "total_tenant_tests": 5,
    "successful_tests": 5,
    "overall_success_rate": 25.6,
    "overall_block_rate": 74.4,
    "average_qps_accuracy_deviation": 2.3,
    "limit_effectiveness_rate": 100.0,
    "behavior_statistics": {...}
  }
}
```

## 预期测试效果

### 标准测试场景（30秒，500 RPS）

| 租户 | QPS限制 | 控制行为 | 预期成功请求 | 预期成功率 | 预期限流率 |
|------|---------|----------|--------------|------------|------------|
| tenant1 | 100 | 快速失败 | ~3000 | ~20% | ~80% |
| tenant2 | 200 | 预热启动 | ~4500 | ~30% | ~70% |
| tenant3 | 50 | 排队等待 | ~1800 | ~12% | ~88% |
| tenant4 | 5 | 快速失败 | ~150 | ~1% | ~99% |
| tenant5 | 300 | 预热+排队 | ~7500 | ~50% | ~50% |

### 不同测试场景

#### 1. 低频长时间测试（60秒，100 RPS）
```bash
python tenant_total_qps_flow_control_test.py --duration 60 --rps 100
```
- 更接近实际业务场景
- 预热效果更明显
- QPS控制更精确

#### 2. 高频压力测试（10秒，1000 RPS）
```bash
python tenant_total_qps_flow_control_test.py --duration 10 --rps 1000
```
- 测试系统极限性能
- 验证高并发下的限流效果
- 快速获得测试结果

#### 3. 单租户深度测试
```bash
python tenant_total_qps_flow_control_test.py --tenant tenant2 --duration 45 --rps 300
```
- 专注测试特定租户的限流规则
- 详细分析单个控制行为的效果
- 优化特定租户的配置

## 故障排除

### 常见问题

#### 1. 连接错误
```
错误: 无法连接到服务器
```
**解决方案**:
- 检查服务器是否启动
- 确认服务器地址和端口正确
- 检查防火墙设置

#### 2. QPS偏差过大
```
QPS准确度偏差: 25.5%
```
**可能原因**:
- 限流规则配置错误
- 系统负载过高
- 网络延迟影响
- 时间窗口计算问题

#### 3. 所有请求都成功（无限流效果）
```
成功请求: 15000 (100.0%)
被限流: 0 (0.0%)
```
**检查项目**:
- 限流规则是否正确配置
- 租户ID是否正确传递
- 限流组件是否正常工作
- 测试端点是否受限流控制

#### 4. 所有请求都失败
```
成功请求: 0 (0.0%)
错误请求: 15000 (100.0%)
```
**检查项目**:
- 服务器是否正常运行
- API端点是否存在
- 请求格式是否正确
- 认证和授权是否配置正确

### 调试建议

#### 1. 启用详细日志
在脚本中添加调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 2. 减少并发度测试
```bash
python tenant_total_qps_flow_control_test.py --duration 10 --rps 50
```

#### 3. 单租户测试
```bash
python tenant_total_qps_flow_control_test.py --tenant tenant1 --duration 15 --rps 200
```

#### 4. 检查服务器日志
查看服务器端的限流日志和错误信息

## 扩展功能

### 1. 自定义租户规则

修改 `create_tenant_rules()` 函数来添加或修改租户规则：

```python
def create_tenant_rules() -> List[Dict[str, Any]]:
    return [
        {
            'tenant_id': 'custom_tenant',
            'rule_name': '自定义租户规则',
            'count': 150,
            'control_behavior': 1,
            'warm_up_period_sec': 20,
            # ... 其他配置
        }
    ]
```

### 2. 添加新的统计指标

在 `analyze_control_behavior()` 方法中添加自定义分析逻辑：

```python
def analyze_control_behavior(self, rule, results, duration):
    # 添加自定义分析逻辑
    custom_metric = self.calculate_custom_metric(results)
    # ...
```

### 3. 集成到CI/CD

```bash
# 在CI/CD管道中运行测试
python tenant_total_qps_flow_control_test.py --duration 20 --rps 300 --output ci_test_report.json

# 检查测试结果
if [ $? -eq 0 ]; then
    echo "限流测试通过"
else
    echo "限流测试失败"
    exit 1
fi
```

## 技术实现说明

### 异步编程
- 使用 `asyncio` 实现高并发请求
- `aiohttp` 提供异步HTTP客户端
- 精确的时间控制和请求频率管理

### 统计算法
- 使用 `statistics` 模块计算响应时间分布
- 实时QPS计算和准确度分析
- 多维度数据聚合和分析

### 错误处理
- 完善的异常捕获和处理机制
- 超时控制和重试逻辑
- 详细的错误信息记录

### 报告生成
- JSON格式的结构化报告
- 时间戳和元数据记录
- 可扩展的报告格式

## 最佳实践

### 1. 测试环境准备
- 确保测试环境与生产环境配置一致
- 预先配置好所有租户的限流规则
- 准备充足的系统资源

### 2. 测试参数选择
- 根据实际业务场景选择合适的RPS
- 测试时间应足够长以观察预热效果
- 考虑系统预热时间和稳定时间

### 3. 结果分析
- 关注QPS准确度和限流有效性
- 分析不同控制行为的适用场景
- 结合业务需求评估限流配置

### 4. 持续监控
- 定期运行测试验证限流效果
- 监控生产环境的限流指标
- 根据业务变化调整限流配置

---

**注意**: 本脚本仅用于测试目的，请在测试环境中使用，避免对生产环境造成影响。