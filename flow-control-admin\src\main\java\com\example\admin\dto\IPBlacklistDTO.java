package com.example.admin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * IP黑名单DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Schema(description = "IP黑名单DTO")
public class IPBlacklistDTO {

    @Schema(description = "名单ID")
    private Long id;

    @Schema(description = "名单名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "名单名称不能为空")
    @Size(max = 100, message = "名单名称长度不能超过100个字符")
    private String listName;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "租户ID不能为空")
    @Size(max = 50, message = "租户ID长度不能超过50个字符")
    private String tenantId;

    @Schema(description = "IP类型：SINGLE_IP-单个IP，IP_RANGE-IP范围，IP_CIDR-CIDR格式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "IP类型不能为空")
    @Pattern(regexp = "^(SINGLE_IP|IP_RANGE|IP_CIDR)$", message = "IP类型只能是SINGLE_IP、IP_RANGE或IP_CIDR")
    private String ipType;

    @Schema(description = "单个IP地址（当ipType为SINGLE_IP时使用）")
    private String singleIp;

    @Schema(description = "IP范围起始地址（当ipType为IP_RANGE时使用）")
    private String startIp;

    @Schema(description = "IP范围结束地址（当ipType为IP_RANGE时使用）")
    private String endIp;

    @Schema(description = "CIDR格式IP（当ipType为IP_CIDR时使用）")
    private String cidrIp;

    @Schema(description = "优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "优先级不能为空")
    @Min(value = 1, message = "优先级必须大于0")
    @Max(value = 1000, message = "优先级不能超过1000")
    private Integer priority;

    @Schema(description = "是否启用：0-禁用，1-启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用状态不能为空")
    @Min(value = 0, message = "启用状态值无效")
    @Max(value = 1, message = "启用状态值无效")
    private Integer enabled;

    @Schema(description = "生效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "生效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "名单描述")
    @Size(max = 500, message = "名单描述长度不能超过500个字符")
    private String description;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新人")
    private String updateBy;

    // Constructors
    public IPBlacklistDTO() {}

    public IPBlacklistDTO(String listName, String tenantId, String ipType) {
        this.listName = listName;
        this.tenantId = tenantId;
        this.ipType = ipType;
        this.priority = 1;
        this.enabled = 1;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getListName() {
        return listName;
    }

    public void setListName(String listName) {
        this.listName = listName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getIpType() {
        return ipType;
    }

    public void setIpType(String ipType) {
        this.ipType = ipType;
    }

    public String getSingleIp() {
        return singleIp;
    }

    public void setSingleIp(String singleIp) {
        this.singleIp = singleIp;
    }

    public String getStartIp() {
        return startIp;
    }

    public void setStartIp(String startIp) {
        this.startIp = startIp;
    }

    public String getEndIp() {
        return endIp;
    }

    public void setEndIp(String endIp) {
        this.endIp = endIp;
    }

    public String getCidrIp() {
        return cidrIp;
    }

    public void setCidrIp(String cidrIp) {
        this.cidrIp = cidrIp;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 验证IP配置是否有效
     */
    public boolean isValidIpConfig() {
        if (ipType == null) {
            return false;
        }
        
        switch (ipType) {
            case "SINGLE_IP":
                return singleIp != null && !singleIp.trim().isEmpty();
            case "IP_RANGE":
                return startIp != null && !startIp.trim().isEmpty() && 
                       endIp != null && !endIp.trim().isEmpty();
            case "IP_CIDR":
                return cidrIp != null && !cidrIp.trim().isEmpty();
            default:
                return false;
        }
    }

    /**
     * 验证时间范围
     */
    public boolean isValidTimeRange() {
        if (startTime == null && endTime == null) {
            return true;
        }
        if (startTime != null && endTime != null) {
            return startTime.isBefore(endTime);
        }
        return true;
    }

    /**
     * 获取IP配置信息
     */
    public String getIpConfig() {
        if (ipType == null) {
            return "";
        }
        
        switch (ipType) {
            case "SINGLE_IP":
                return singleIp != null ? singleIp : "";
            case "IP_RANGE":
                return (startIp != null ? startIp : "") + "-" + (endIp != null ? endIp : "");
            case "IP_CIDR":
                return cidrIp != null ? cidrIp : "";
            default:
                return "";
        }
    }

    /**
     * 获取IP类型描述
     */
    public String getIpTypeDesc() {
        if (ipType == null) {
            return "未知";
        }
        switch (ipType) {
            case "SINGLE_IP":
                return "单个IP";
            case "IP_RANGE":
                return "IP范围";
            case "IP_CIDR":
                return "CIDR格式";
            default:
                return "未知";
        }
    }

    /**
     * 获取启用状态描述
     */
    public String getEnabledDesc() {
        if (enabled == null) {
            return "未知";
        }
        return enabled == 1 ? "启用" : "禁用";
    }

    /**
     * 验证IP范围是否有效（仅对IP_RANGE类型）
     */
    public boolean isValidIpRange() {
        if (!"IP_RANGE".equals(ipType) || startIp == null || endIp == null) {
            return true;
        }
        
        try {
            String[] startParts = startIp.split("\\.");
            String[] endParts = endIp.split("\\.");
            
            if (startParts.length != 4 || endParts.length != 4) {
                return false;
            }
            
            long startLong = 0;
            long endLong = 0;
            
            for (int i = 0; i < 4; i++) {
                startLong = (startLong << 8) + Integer.parseInt(startParts[i]);
                endLong = (endLong << 8) + Integer.parseInt(endParts[i]);
            }
            
            return startLong <= endLong;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    @Override
    public String toString() {
        return "IPBlacklistDTO{"
                + "id=" + id + 
                ", listName='" + listName + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", ipType='" + ipType + '\'' +
                ", singleIp='" + singleIp + '\'' +
                ", startIp='" + startIp + '\'' +
                ", endIp='" + endIp + '\'' +
                ", cidrIp='" + cidrIp + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                '}';
    }
}
