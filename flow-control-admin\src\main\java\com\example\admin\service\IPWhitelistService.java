package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.IPWhitelistDTO;
import com.example.common.entity.IPWhitelist;
import com.example.admin.vo.IPWhitelistVO;

import java.util.List;
import java.util.Map;

/**
 * IP白名单服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IPWhitelistService extends IService<IPWhitelist> {
    
    /**
     * 分页查询IP白名单
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param listName 名单名称
     * @param ipType IP类型
     * @param enabled 启用状态
     * @return IP白名单VO分页结果
     */
    Page<IPWhitelistVO> selectIPWhitelistPage(Page<IPWhitelistVO> page, String tenantId, String listName, 
                                                  String ipType, Integer enabled);
    
    /**
     * 根据ID查询IP白名单详情
     *
     * @param id 名单ID
     * @return IP白名单VO
     */
    IPWhitelistVO getIPWhitelistById(Long id);
    
    /**
     * 创建IP白名单
     *
     * @param ipWhitelistDTO IP白名单DTO
     * @return 是否成功
     */
    boolean createIPWhitelist(IPWhitelistDTO ipWhitelistDTO);
    
    /**
     * 更新IP白名单
     *
     * @param id 名单ID
     * @param ipWhitelistDTO IP白名单DTO
     * @return 是否成功
     */
    boolean updateIPWhitelist(Long id, IPWhitelistDTO ipWhitelistDTO);
    
    /**
     * 删除IP白名单
     *
     * @param id 名单ID
     * @return 是否成功
     */
    boolean deleteIPWhitelist(Long id);
    
    /**
     * 批量删除IP白名单
     *
     * @param ids 名单ID列表
     * @return 是否成功
     */
    boolean batchDeleteIPWhitelists(List<Long> ids);
    
    /**
     * 启用IP白名单
     *
     * @param id 名单ID
     * @return 是否成功
     */
    boolean enableIPWhitelist(Long id);
    
    /**
     * 禁用IP白名单
     *
     * @param id 名单ID
     * @return 是否成功
     */
    boolean disableIPWhitelist(Long id);
    
    /**
     * 批量更新名单状态
     *
     * @param ids 名单ID列表
     * @param enabled 启用状态
     * @return 是否成功
     */
    boolean batchUpdateEnabled(List<Long> ids, Integer enabled);
    
    /**
     * 根据租户ID查询IP白名单
     *
     * @param tenantId 租户ID
     * @param enabled 启用状态（可选）
     * @param limit 限制数量
     * @return IP白名单列表
     */
    List<IPWhitelistVO> getIPWhitelistsByTenantId(String tenantId, Integer enabled, Integer limit);
    
    /**
     * 检查名单名称是否存在
     *
     * @param tenantId 租户ID
     * @param listName 名单名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByListName(String tenantId, String listName, Long excludeId);
    
    /**
     * 统计租户IP白名单数量
     *
     * @param tenantId 租户ID
     * @param enabled 启用状态（可选）
     * @return 名单数量
     */
    int countByTenantId(String tenantId, Integer enabled);
    
    /**
     * 查询启用的白名单（按优先级排序）
     *
     * @param tenantId 租户ID（可选）
     * @param limit 限制数量
     * @return 启用的白名单列表
     */
    List<IPWhitelistVO> getEnabledWhitelists(String tenantId, Integer limit);
    
    /**
     * 查询优先级排序的白名单
     *
     * @param tenantId 租户ID（可选）
     * @param enabled 启用状态（可选）
     * @param limit 限制数量
     * @return 优先级排序的白名单列表
     */
    List<IPWhitelistVO> getWhitelistsByPriorityOrder(String tenantId, Integer enabled, Integer limit);
    
    /**
     * 统计名单状态分布
     *
     * @param tenantId 租户ID（可选）
     * @return 状态统计结果
     */
    List<Map<String, Object>> getEnabledStatistics(String tenantId);
    
    /**
     * 统计IP类型分布
     *
     * @param tenantId 租户ID（可选）
     * @return IP类型统计结果
     */
    List<Map<String, Object>> getIPTypeStatistics(String tenantId);
    
    /**
     * 统计租户IP白名单分布
     *
     * @param limit 限制数量
     * @return 租户IP白名单统计结果
     */
    List<Map<String, Object>> getTenantIPStatistics(Integer limit);
    
    /**
     * 批量创建IP白名单
     *
     * @param ipWhitelistDTOList IP白名单DTO列表
     * @return 是否成功
     */
    boolean batchCreateIPWhitelists(List<IPWhitelistDTO> ipWhitelistDTOList);
    
    /**
     * 复制IP白名单
     *
     * @param id 原名单ID
     * @param newListName 新名单名称
     * @param targetTenantId 目标租户ID（可选，为空则复制到同一租户）
     * @return 是否成功
     */
    boolean copyIPWhitelist(Long id, String newListName, String targetTenantId);
    
    /**
     * 导入IP白名单
     *
     * @param ipWhitelistDTOList IP白名单DTO列表
     * @param overwrite 是否覆盖已存在的名单
     * @return 导入结果
     */
    Map<String, Object> importIPWhitelists(List<IPWhitelistDTO> ipWhitelistDTOList, boolean overwrite);
    
    /**
     * 导出IP白名单
     *
     * @param tenantId 租户ID（可选）
     * @param enabled 启用状态（可选）
     * @return IP白名单DTO列表
     */
    List<IPWhitelistDTO> exportIPWhitelists(String tenantId, Integer enabled);
    
    /**
     * 验证IP白名单配置
     *
     * @param ipWhitelistDTO IP白名单DTO
     * @return 验证结果
     */
    Map<String, Object> validateIPWhitelist(IPWhitelistDTO ipWhitelistDTO);
    
    /**
     * 获取租户IP白名单的最大优先级
     *
     * @param tenantId 租户ID
     * @return 最大优先级
     */
    Integer getMaxPriority(String tenantId);
    
    /**
     * 按优先级范围查询白名单
     *
     * @param tenantId 租户ID
     * @param minPriority 最小优先级
     * @param maxPriority 最大优先级
     * @param enabled 启用状态（可选）
     * @return 白名单列表
     */
    List<IPWhitelistVO> getWhitelistsByPriorityRange(String tenantId, Integer minPriority, Integer maxPriority, 
                                                         Integer enabled);
    
    /**
     * 查询即将过期的白名单
     *
     * @param tenantId 租户ID（可选）
     * @param hours 提前小时数
     * @return 即将过期的白名单列表
     */
    List<IPWhitelistVO> getExpiringWhitelists(String tenantId, Integer hours);
    
    /**
     * 自动禁用过期白名单
     *
     * @return 禁用的名单数量
     */
    int disableExpiredWhitelists();
    
    /**
     * 批量复制名单到其他租户
     *
     * @param sourceIds 源名单ID列表
     * @param targetTenantId 目标租户ID
     * @param namePrefix 新名单名称前缀
     * @return 复制结果
     */
    Map<String, Object> batchCopyToTenant(List<Long> sourceIds, String targetTenantId, String namePrefix);
    
    /**
     * 获取租户IP白名单汇总统计
     *
     * @param tenantId 租户ID
     * @return 汇总统计结果
     */
    Map<String, Object> getTenantIPSummary(String tenantId);
    
    /**
     * 获取全局IP白名单汇总统计
     *
     * @return 全局汇总统计结果
     */
    Map<String, Object> getGlobalIPSummary();
    
    /**
     * 获取当前有效白名单
     *
     * @param tenantId 租户ID（可选）
     * @return 有效白名单列表
     */
    List<IPWhitelistVO> getValidWhitelists(String tenantId);
    
    /**
     * 检查IP地址是否匹配白名单
     *
     * @param ipAddress 待检查的IP地址
     * @param tenantId 租户ID
     * @return 匹配结果
     */
    Map<String, Object> checkIPMatch(String ipAddress, String tenantId);
    
    /**
     * 批量IP地址匹配检查
     *
     * @param tenantId 租户ID
     * @param ipAddresses 待检查的IP地址列表
     * @return 批量匹配结果
     */
    List<Map<String, Object>> batchCheckIPMatch(String tenantId, List<String> ipAddresses);
    
    /**
     * 从文件导入IP地址
     *
     * @param tenantId 租户ID
     * @param listName 名单名称
     * @param fileContent 文件内容
     * @param fileType 文件类型（txt, csv, json等）
     * @return 导入结果
     */
    Map<String, Object> importIPsFromFile(String tenantId, String listName, 
                                         String fileContent, String fileType);
    
    /**
     * 从上传文件导入IP地址
     *
     * @param file 上传的文件
     * @param tenantId 租户ID
     * @param listName 名单名称
     * @param ipType IP类型
     * @param priority 优先级
     * @return 导入结果
     */
    Map<String, Object> importIPsFromFile(org.springframework.web.multipart.MultipartFile file, String tenantId, String listName, 
                                         String ipType, Integer priority);
    
    /**
     * 导出IP地址到文件
     *
     * @param tenantId 租户ID
     * @param enabled 启用状态（可选）
     * @param fileType 文件类型（txt, csv, json等）
     * @return 文件内容
     */
    String exportIPsToFile(String tenantId, Integer enabled, String fileType);
}