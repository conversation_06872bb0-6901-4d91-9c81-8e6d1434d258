package com.example.admin.common.enums;

/**
 * IP规则类型枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public enum IPRuleTypeEnum {
    
    /**
     * 单个IP
     */
    SINGLE_IP("SINGLE_IP", "单个IP", "针对单个IP地址的规则"),
    
    /**
     * IP范围
     */
    IP_RANGE("IP_RANGE", "IP范围", "针对IP地址范围的规则，如***********-***********00"),
    
    /**
     * CIDR网段
     */
    CIDR("CIDR", "CIDR网段", "针对CIDR网段的规则，如***********/24");
    
    /**
     * 类型值
     */
    private final String value;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 类型描述
     */
    private final String description;

    /**
     * 构造器
     */
    IPRuleTypeEnum(String value, String name, String description) {
        this.value = value;
        this.name = name;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
    
    /**
     * 根据值获取枚举
     * 
     * @param value 值
     * @return 枚举
     */
    public static IPRuleTypeEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (IPRuleTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取枚举
     * 
     * @param name 名称
     * @return 枚举
     */
    public static IPRuleTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (IPRuleTypeEnum type : values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
}