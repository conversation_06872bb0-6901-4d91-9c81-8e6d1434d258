import request from '@/utils/request'

// 租户流量规则API
export const tenantFlowRuleApi = {
  // 分页查询租户流量规则
  getTenantFlowRuleList(params) {
    return request({
      url: '/api/tenant-flow-rules',
      method: 'get',
      params
    })
  },

  // 根据ID查询租户流量规则详情
  getTenantFlowRuleById(id) {
    return request({
      url: `/api/tenant-flow-rules/${id}`,
      method: 'get'
    })
  },

  // 创建租户流量规则
  createTenantFlowRule(data) {
    return request({
      url: '/api/tenant-flow-rules',
      method: 'post',
      data
    })
  },

  // 更新租户流量规则
  updateTenantFlowRule(id, data) {
    return request({
      url: `/api/tenant-flow-rules/${id}`,
      method: 'put',
      data
    })
  },

  // 删除租户流量规则
  deleteTenantFlowRule(id) {
    return request({
      url: `/api/tenant-flow-rules/${id}`,
      method: 'delete'
    })
  },

  // 批量删除租户流量规则
  batchDeleteTenantFlowRules(ids) {
    return request({
      url: '/api/tenant-flow-rules/batch',
      method: 'delete',
      data: { ids }
    })
  },

  // 启用租户流量规则
  enableTenantFlowRule(id) {
    return request({
      url: `/api/tenant-flow-rules/${id}/enable`,
      method: 'put'
    })
  },

  // 禁用租户流量规则
  disableTenantFlowRule(id) {
    return request({
      url: `/api/tenant-flow-rules/${id}/disable`,
      method: 'put'
    })
  },

  // 批量更新流量规则状态
  batchUpdateEnabled(ids, enabled) {
    return request({
      url: '/api/tenant-flow-rules/batch/status',
      method: 'put',
      data: { ids, enabled }
    })
  },

  // 根据租户ID查询租户流量规则
  getTenantFlowRulesByTenantId(tenantId, enabled, limit) {
    return request({
      url: `/api/tenant-flow-rules/tenant/${tenantId}`,
      method: 'get',
      params: { enabled, limit }
    })
  },

  // 租户限流为全局生效，移除基于资源匹配模式的查询方法
  // getTenantFlowRulesByResourcePattern(resourcePattern, tenantId, enabled, limit) {
  //   return request({
  //     url: '/api/tenant-flow-rules/resource',
  //     method: 'get',
  //     params: { resourcePattern, tenantId, enabled, limit }
  //   })
  // },

  // 复制租户流量规则
  copyTenantFlowRule(id, newRuleName, targetTenantId) {
    return request({
      url: `/api/tenant-flow-rules/${id}/copy`,
      method: 'post',
      data: { newRuleName, targetTenantId }
    })
  },

  // 批量创建租户流量规则
  batchCreateTenantFlowRules(rules) {
    return request({
      url: '/api/tenant-flow-rules/batch',
      method: 'post',
      data: { rules }
    })
  },

  // 导入租户流量规则
  importTenantFlowRules(rules, overwrite = false) {
    return request({
      url: '/api/tenant-flow-rules/import',
      method: 'post',
      data: { rules, overwrite }
    })
  },

  // 导出租户流量规则
  exportTenantFlowRules(tenantId, enabled) {
    return request({
      url: '/api/tenant-flow-rules/export',
      method: 'get',
      params: { tenantId, enabled }
    })
  },

  // 验证租户流量规则配置
  validateTenantFlowRule(rule) {
    return request({
      url: '/api/tenant-flow-rules/validate',
      method: 'post',
      data: rule
    })
  },

  // 获取租户流量规则统计
  getTenantFlowRuleStatistics(limit) {
    return request({
      url: '/api/tenant-flow-rules/statistics',
      method: 'get',
      params: { limit }
    })
  },

  // 获取流控效果统计
  getControlBehaviorStatistics(tenantId) {
    return request({
      url: '/tenant-flow-rules/statistics/control-behavior',
      method: 'get',
      params: { tenantId }
    })
  },

  // 获取启用状态统计
  getEnabledStatistics(tenantId) {
    return request({
      url: '/tenant-flow-rules/statistics/enabled',
      method: 'get',
      params: { tenantId }
    })
  },

  // 获取租户流量规则汇总统计
  getTenantFlowRuleSummary(tenantId) {
    return request({
      url: `/api/tenant-flow-rules/summary/${tenantId}`,
      method: 'get'
    })
  },

  // 获取全局流量规则汇总统计
  getGlobalFlowRuleSummary() {
    return request({
      url: '/api/tenant-flow-rules/summary/global',
      method: 'get'
    })
  },

  // 获取租户列表（用于下拉选择）
  getTenantList() {
    return request({
      url: '/api/tenant-configs',
      method: 'get'
    })
  }
}