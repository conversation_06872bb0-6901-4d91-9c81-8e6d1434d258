package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.entity.TenantFlowRule;
import com.example.admin.vo.TenantFlowRuleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * 租户流量规则Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface TenantFlowRuleMapper extends BaseMapper<TenantFlowRule> {

	/**
	 * 分页查询租户流量规则VO
	 * 
	 * @param page            分页参数
	 * @param tenantId        租户ID（可选）
	 * @param ruleName        规则名称（可选，模糊查询）
	 * @param enabled         启用状态（可选）
	 * @param controlBehavior 流控效果（可选）
	 * @return 分页结果
	 */
	Page<TenantFlowRuleVO> selectTenantFlowRuleVOPage(Page<TenantFlowRuleVO> page, @Param("tenantId") String tenantId,
			@Param("ruleName") String ruleName, @Param("enabled") Integer enabled,
			@Param("controlBehavior") Integer controlBehavior);

	/**
	 * 根据租户ID查询流量规则列表
	 * 
	 * @param tenantId 租户ID
	 * @return 流量规则列表
	 */
	@Select("SELECT * FROM tenant_flow_rules WHERE tenant_id = #{tenantId} AND deleted = 0 ORDER BY priority DESC, create_time DESC")
	List<TenantFlowRule> selectByTenantId(@Param("tenantId") String tenantId);

	/**
	 * 根据租户ID和启用状态查询流量规则列表
	 * 
	 * @param tenantId 租户ID
	 * @param enabled  启用状态
	 * @return 流量规则列表
	 */
	@Select("SELECT * FROM tenant_flow_rules WHERE tenant_id = #{tenantId} AND enabled = #{enabled} AND deleted = 0 ORDER BY priority DESC")
	List<TenantFlowRule> selectByTenantIdAndEnabled(@Param("tenantId") String tenantId,
			@Param("enabled") Integer enabled);

	// 租户限流为全局生效，移除基于资源模式的查询方法
	// @Select("SELECT * FROM tenant_flow_rules WHERE resource_pattern =
	// #{resourcePattern} AND deleted = 0 ORDER BY priority DESC")
	// List<TenantFlowRule> selectByResourcePattern(@Param("resourcePattern") String
	// resourcePattern);

	/**
	 * 分页查询租户流量规则
	 * 
	 * @param page     分页参数
	 * @param tenantId 租户ID（可选）
	 * @param enabled  启用状态（可选）
	 * @param ruleName 规则名称（可选，模糊查询）
	 * @return 分页结果
	 */
	IPage<TenantFlowRule> selectPageWithConditions(Page<TenantFlowRule> page, @Param("tenantId") String tenantId,
			@Param("enabled") Integer enabled, @Param("ruleName") String ruleName);

	/**
	 * 批量启用/禁用流量规则
	 * 
	 * @param ids      规则ID列表
	 * @param enabled  启用状态
	 * @param updateBy 更新人
	 * @return 更新数量
	 */
	@Update("<script>"
			+ "UPDATE tenant_flow_rules SET enabled = #{enabled}, update_by = #{updateBy}, update_time = NOW() "
			+ "WHERE id IN " + "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" + "#{id}"
			+ "</foreach>" + "AND deleted = 0" + "</script>")
	int batchUpdateEnabled(@Param("ids") List<Long> ids, @Param("enabled") Integer enabled,
			@Param("updateBy") String updateBy);

	/**
	 * 批量删除流量规则（逻辑删除）
	 * 
	 * @param ids      规则ID列表
	 * @param updateBy 更新人
	 * @return 删除数量
	 */
	@Update("<script>" + "UPDATE tenant_flow_rules SET deleted = 1, update_by = #{updateBy}, update_time = NOW() "
			+ "WHERE id IN " + "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" + "#{id}"
			+ "</foreach>" + "AND deleted = 0" + "</script>")
	int batchDelete(@Param("ids") List<Long> ids, @Param("updateBy") String updateBy);

	/**
	 * 检查流量规则名称是否存在（同一租户下）
	 * 
	 * @param tenantId  租户ID
	 * @param ruleName  规则名称
	 * @param excludeId 排除的规则ID（用于更新时检查）
	 * @return 存在的数量
	 */
	@Select("<script>" + "SELECT COUNT(*) FROM tenant_flow_rules "
			+ "WHERE tenant_id = #{tenantId} AND rule_name = #{ruleName} AND deleted = 0 "
			+ "<if test='excludeId != null'>" + "AND id != #{excludeId}" + "</if>" + "</script>")
	int countByTenantIdAndRuleName(@Param("tenantId") String tenantId, @Param("ruleName") String ruleName,
			@Param("excludeId") Long excludeId);

	/**
	 * 获取租户流量规则统计信息
	 * 
	 * @param tenantId 租户ID
	 * @return 统计信息
	 */
	@Select("SELECT " + "COUNT(*) as totalRules, " + "SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as activeRules, "
			+ "SUM(CASE WHEN enabled = 0 THEN 1 ELSE 0 END) as inactiveRules, " + "AVG(count) as avgCount, "
			+ "MAX(count) as maxCount, " + "MIN(count) as minCount " + "FROM tenant_flow_rules "
			+ "WHERE tenant_id = #{tenantId} AND deleted = 0")
	Map<String, Object> getTenantFlowRuleStats(@Param("tenantId") String tenantId);

	/**
	 * 获取所有租户的流量规则统计信息
	 * 
	 * @return 统计信息列表
	 */
	@Select("SELECT " + "t.tenant_name, " + "r.tenant_id, " + "COUNT(*) as totalRules, "
			+ "SUM(CASE WHEN r.enabled = 1 THEN 1 ELSE 0 END) as activeRules, "
			+ "SUM(CASE WHEN r.enabled = 0 THEN 1 ELSE 0 END) as inactiveRules, " + "AVG(r.count) as avgCount, "
			+ "MAX(r.count) as maxCount, " + "MIN(r.count) as minCount " + "FROM tenant_flow_rules r "
			+ "LEFT JOIN tenant_flow_rules t ON r.tenant_id = t.tenant_id " + "WHERE r.deleted = 0 "
			+ "GROUP BY r.tenant_id, t.tenant_name")
	List<Map<String, Object>> getAllTenantFlowRuleStats();

	/**
	 * 根据优先级范围查询流量规则
	 * 
	 * @param tenantId    租户ID
	 * @param minPriority 最小优先级
	 * @param maxPriority 最大优先级
	 * @return 流量规则列表
	 */
	@Select("SELECT * FROM tenant_flow_rules " + "WHERE tenant_id = #{tenantId} "
			+ "AND priority BETWEEN #{minPriority} AND #{maxPriority} " + "AND deleted = 0 " + "ORDER BY priority DESC")
	List<TenantFlowRule> selectByPriorityRange(@Param("tenantId") String tenantId,
			@Param("minPriority") Integer minPriority, @Param("maxPriority") Integer maxPriority);

	/**
	 * 获取租户的最大优先级
	 * 
	 * @param tenantId 租户ID
	 * @return 最大优先级
	 */
	@Select("SELECT COALESCE(MAX(priority), 0) FROM tenant_flow_rules WHERE tenant_id = #{tenantId} AND deleted = 0")
	Integer getMaxPriorityByTenantId(@Param("tenantId") String tenantId);

	/**
	 * 根据时间范围查询有效流量规则
	 * 
	 * @param tenantId 租户ID
	 * @return 有效流量规则列表
	 */
	@Select("SELECT * FROM tenant_flow_rules " + "WHERE tenant_id = #{tenantId} " + "AND enabled = 1 "
			+ "AND deleted = 0 " + "AND (start_time IS NULL OR start_time <= NOW()) "
			+ "AND (end_time IS NULL OR end_time >= NOW()) " + "ORDER BY priority DESC")
	List<TenantFlowRule> selectEffectiveRules(@Param("tenantId") String tenantId);

	/**
	 * 复制流量规则到其他租户
	 * 
	 * @param sourceRuleId   源规则ID
	 * @param targetTenantId 目标租户ID
	 * @param newRuleName    新规则名称
	 * @param createBy       创建人
	 * @return 影响行数
	 */
	@Select("INSERT INTO tenant_flow_rules (tenant_id, rule_name, grade, count, "
			+ "control_behavior, warm_up_period_sec, max_queueing_time_ms, strategy, ref_resource, "
			+ "cluster_mode, cluster_config, priority, enabled, start_time, end_time, description, "
			+ "create_by, create_time, update_time) " + "SELECT #{targetTenantId}, #{newRuleName}, grade, count, "
			+ "control_behavior, warm_up_period_sec, max_queueing_time_ms, strategy, ref_resource, "
			+ "cluster_mode, cluster_config, priority, enabled, start_time, end_time, description, "
			+ "#{createBy}, NOW(), NOW() " + "FROM tenant_flow_rules WHERE id = #{sourceRuleId} AND deleted = 0")
	int copyRuleToTenant(@Param("sourceRuleId") Long sourceRuleId, @Param("targetTenantId") String targetTenantId,
			@Param("newRuleName") String newRuleName, @Param("createBy") String createBy);

	/**
	 * 查询租户流量规则摘要信息
	 * 
	 * @param tenantId 租户ID
	 * @return 租户流量规则摘要
	 */
	@Select("SELECT " + "COUNT(*) as totalRules, " + "SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabledRules, "
			+ "SUM(CASE WHEN enabled = 0 THEN 1 ELSE 0 END) as disabledRules, " + "AVG(count) as avgCount "
			+ "FROM tenant_flow_rules " + "WHERE tenant_id = #{tenantId} AND deleted = 0")
	Map<String, Object> selectTenantFlowRuleSummary(@Param("tenantId") String tenantId);

	/**
	 * 查询全局流量规则摘要信息
	 * 
	 * @return 全局流量规则摘要
	 */
	@Select("SELECT " + "COUNT(*) as totalRules, " + "SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabledRules, "
			+ "SUM(CASE WHEN enabled = 0 THEN 1 ELSE 0 END) as disabledRules, "
			+ "COUNT(DISTINCT tenant_id) as totalTenants, " + "AVG(count) as avgCount " + "FROM tenant_flow_rules "
			+ "WHERE deleted = 0")
	Map<String, Object> selectGlobalFlowRuleSummary();

	/**
	 * 查询即将过期的流量规则
	 * 
	 * @param hours 提前小时数
	 * @return 即将过期的流量规则列表
	 */
	@Select("SELECT * FROM tenant_flow_rules " + "WHERE enabled = 1 " + "AND deleted = 0 " + "AND end_time IS NOT NULL "
			+ "AND end_time > NOW() " + "AND end_time <= DATE_ADD(NOW(), INTERVAL #{hours} HOUR) "
			+ "ORDER BY end_time ASC")
	List<TenantFlowRule> selectExpiringRules(@Param("hours") int hours);

	/**
	 * 禁用已过期的流量规则
	 * 
	 * @param updateBy 更新人
	 * @return 更新数量
	 */
	@Update("UPDATE tenant_flow_rules " + "SET enabled = 0, update_by = #{updateBy}, update_time = NOW() "
			+ "WHERE enabled = 1 " + "AND deleted = 0 " + "AND end_time IS NOT NULL " + "AND end_time < NOW()")
	int disableExpiredRules(@Param("updateBy") String updateBy);

	/**
	 * 查询当前有效的流量规则
	 * 
	 * @param tenantId 租户ID
	 * @return 有效的流量规则列表
	 */
	@Select("SELECT * FROM tenant_flow_rules " + "WHERE tenant_id = #{tenantId} " + "AND enabled = 1 "
			+ "AND deleted = 0 " + "AND (start_time IS NULL OR start_time <= NOW()) "
			+ "AND (end_time IS NULL OR end_time >= NOW()) " + "ORDER BY priority DESC")
	List<TenantFlowRule> selectValidRules(@Param("tenantId") String tenantId);

	/**
	 * 根据规则名称查询流量规则
	 */
	TenantFlowRule selectByRuleName(@Param("tenantId") String tenantId, @Param("ruleName") String ruleName,
			@Param("excludeId") Long excludeId);

	/**
	 * 查询用于导出的流量规则
	 */
	List<TenantFlowRule> selectForExport(@Param("tenantId") String tenantId, @Param("enabled") Integer enabled);

	/**
	 * 查询租户流量规则统计信息
	 */
	List<Map<String, Object>> selectTenantFlowRuleStatistics(@Param("enabled") Integer enabled);

	/**
	 * 查询流控效果统计信息
	 */
	List<Map<String, Object>> selectControlBehaviorStatistics(@Param("tenantId") String tenantId);

	/**
	 * 查询租户的最大优先级
	 * 
	 * @param tenantId 租户ID
	 * @return 最大优先级值
	 */
	Integer selectMaxPriority(@Param("tenantId") String tenantId);

	/**
	 * 查询启用的流量规则
	 * 
	 * @param tenantIds 租户ID列表
	 * @return 启用的流量规则列表
	 */
	List<TenantFlowRule> selectEnabledRules(@Param("tenantIds") List<String> tenantIds);

	/**
	 * 按优先级顺序查询流量规则
	 * 
	 * @param tenantIds 租户ID列表
	 * @return 按优先级排序的流量规则列表
	 */
	List<TenantFlowRule> selectByPriorityOrder(@Param("tenantIds") List<String> tenantIds);

	/**
	 * 查询启用规则的统计信息
	 * 
	 * @param tenantIds 租户ID列表
	 * @return 统计信息列表
	 */
	List<Map<String, Object>> selectEnabledStatistics(@Param("tenantIds") List<String> tenantIds);
}