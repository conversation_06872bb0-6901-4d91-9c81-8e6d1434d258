package com.example.admin.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * IP黑名单VO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Schema(description = "IP黑名单VO")
public class IPBlacklistVO {
    
    @Schema(description = "名单ID")
    private Long id;
    
    @Schema(description = "名单名称")
    private String listName;
    
    @Schema(description = "租户ID")
    private String tenantId;
    
    @Schema(description = "租户名称")
    private String tenantName;
    
    @Schema(description = "IP类型：SINGLE_IP-单个IP，IP_RANGE-IP范围，IP_CIDR-CIDR格式")
    private String ipType;
    
    @Schema(description = "IP类型名称")
    private String ipTypeName;
    
    @Schema(description = "单个IP地址")
    private String singleIp;
    
    @Schema(description = "IP范围起始地址")
    private String startIp;
    
    @Schema(description = "IP范围结束地址")
    private String endIp;
    
    @Schema(description = "CIDR格式IP")
    private String cidrIp;
    
    @Schema(description = "IP配置信息（格式化显示）")
    private String ipConfig;
    
    @Schema(description = "优先级")
    private Integer priority;
    
    @Schema(description = "是否启用：0-禁用，1-启用")
    private Integer enabled;
    
    @Schema(description = "启用状态名称")
    private String enabledName;
    
    @Schema(description = "生效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    @Schema(description = "生效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    @Schema(description = "名单描述")
    private String description;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    @Schema(description = "创建人")
    private String createBy;
    
    @Schema(description = "更新人")
    private String updateBy;
    
    @Schema(description = "是否有效（当前时间在生效时间范围内且启用）")
    private Boolean isValid;
    
    @Schema(description = "是否即将过期")
    private Boolean isExpiring;
    
    @Schema(description = "剩余有效时间（小时）")
    private Long remainingHours;
    
    @Schema(description = "IP地址数量（对于范围和CIDR）")
    private Long ipCount;
    
    // Constructors
    public IPBlacklistVO() {}
    
    public IPBlacklistVO(Long id, String listName, String tenantId, String ipType) {
        this.id = id;
        this.listName = listName;
        this.tenantId = tenantId;
        this.ipType = ipType;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getListName() {
        return listName;
    }
    
    public void setListName(String listName) {
        this.listName = listName;
    }
    
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    public String getTenantName() {
        return tenantName;
    }
    
    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }
    
    public String getIpType() {
        return ipType;
    }
    
    public void setIpType(String ipType) {
        this.ipType = ipType;
        // 自动设置IP类型名称
        this.ipTypeName = getIpTypeDesc(ipType);
        // 自动设置IP配置信息
        this.ipConfig = buildIpConfig();
    }
    
    public String getIpTypeName() {
        return ipTypeName;
    }
    
    public void setIpTypeName(String ipTypeName) {
        this.ipTypeName = ipTypeName;
    }
    
    public String getSingleIp() {
        return singleIp;
    }
    
    public void setSingleIp(String singleIp) {
        this.singleIp = singleIp;
        // 更新IP配置信息
        this.ipConfig = buildIpConfig();
    }
    
    public String getStartIp() {
        return startIp;
    }
    
    public void setStartIp(String startIp) {
        this.startIp = startIp;
        // 更新IP配置信息
        this.ipConfig = buildIpConfig();
    }
    
    public String getEndIp() {
        return endIp;
    }
    
    public void setEndIp(String endIp) {
        this.endIp = endIp;
        // 更新IP配置信息
        this.ipConfig = buildIpConfig();
    }
    
    public String getCidrIp() {
        return cidrIp;
    }
    
    public void setCidrIp(String cidrIp) {
        this.cidrIp = cidrIp;
        // 更新IP配置信息
        this.ipConfig = buildIpConfig();
        // 计算IP数量
        this.ipCount = calculateIpCount();
    }
    
    public String getIpConfig() {
        return ipConfig;
    }
    
    public void setIpConfig(String ipConfig) {
        this.ipConfig = ipConfig;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public Integer getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
        // 自动设置启用状态名称
        this.enabledName = getEnabledDesc(enabled);
    }
    
    public String getEnabledName() {
        return enabledName;
    }
    
    public void setEnabledName(String enabledName) {
        this.enabledName = enabledName;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getCreateBy() {
        return createBy;
    }
    
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    
    public String getUpdateBy() {
        return updateBy;
    }
    
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    
    public Boolean getIsValid() {
        return isValid;
    }
    
    public void setIsValid(Boolean isValid) {
        this.isValid = isValid;
    }
    
    public Boolean getIsExpiring() {
        return isExpiring;
    }
    
    public void setIsExpiring(Boolean isExpiring) {
        this.isExpiring = isExpiring;
    }
    
    public Long getRemainingHours() {
        return remainingHours;
    }
    
    public void setRemainingHours(Long remainingHours) {
        this.remainingHours = remainingHours;
    }
    
    public Long getIpCount() {
        return ipCount;
    }
    
    public void setIpCount(Long ipCount) {
        this.ipCount = ipCount;
    }
    
    // Compatibility methods for legacy code
    public String getIpAddress() {
        return getSingleIp();
    }
    
    public String getIpStart() {
        return getStartIp();
    }
    
    public String getIpEnd() {
        return getEndIp();
    }
    
    public String getIpCidr() {
        return getCidrIp();
    }
    
    public Long getListId() {
        return getId();
    }
    
    // Utility methods
    
    /**
     * 获取IP类型描述
     */
    private String getIpTypeDesc(String ipType) {
        if (ipType == null) {
            return "未知";
        }
        switch (ipType) {
            case "SINGLE_IP":
                return "单个IP";
            case "IP_RANGE":
                return "IP范围";
            case "IP_CIDR":
                return "CIDR格式";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取启用状态描述
     */
    private String getEnabledDesc(Integer enabled) {
        if (enabled == null) {
            return "未知";
        }
        return enabled == 1 ? "启用" : "禁用";
    }
    
    /**
     * 构建IP配置信息
     */
    private String buildIpConfig() {
        if (ipType == null) {
            return "";
        }
        
        switch (ipType) {
            case "SINGLE_IP":
                return singleIp != null ? singleIp : "";
            case "IP_RANGE":
                return (startIp != null ? startIp : "") + " - " + (endIp != null ? endIp : "");
            case "IP_CIDR":
                return cidrIp != null ? cidrIp : "";
            default:
                return "";
        }
    }
    
    /**
     * 计算IP地址数量
     */
    private Long calculateIpCount() {
        if (ipType == null) {
            return 0L;
        }
        
        switch (ipType) {
            case "SINGLE_IP":
                return 1L;
            case "IP_RANGE":
                return calculateRangeCount();
            case "IP_CIDR":
                return calculateCidrCount();
            default:
                return 0L;
        }
    }
    
    /**
     * 计算IP范围的地址数量
     */
    private Long calculateRangeCount() {
        if (startIp == null || endIp == null) {
            return 0L;
        }
        
        try {
            String[] startParts = startIp.split("\\.");
            String[] endParts = endIp.split("\\.");
            
            if (startParts.length != 4 || endParts.length != 4) {
                return 0L;
            }
            
            long startLong = 0;
            long endLong = 0;
            
            for (int i = 0; i < 4; i++) {
                startLong = (startLong << 8) + Integer.parseInt(startParts[i]);
                endLong = (endLong << 8) + Integer.parseInt(endParts[i]);
            }
            
            return Math.max(0, endLong - startLong + 1);
        } catch (NumberFormatException e) {
            return 0L;
        }
    }
    
    /**
     * 计算CIDR的地址数量
     */
    private Long calculateCidrCount() {
        if (cidrIp == null || !cidrIp.contains("/")) {
            return 0L;
        }
        
        try {
            String[] parts = cidrIp.split("/");
            if (parts.length != 2) {
                return 0L;
            }
            
            int prefixLength = Integer.parseInt(parts[1]);
            if (prefixLength < 0 || prefixLength > 32) {
                return 0L;
            }
            
            return 1L << (32 - prefixLength);
        } catch (NumberFormatException e) {
            return 0L;
        }
    }
    
    /**
     * 检查名单是否当前有效
     */
    public boolean isCurrentlyValid() {
        if (enabled == null || enabled != 1) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 检查开始时间
        if (startTime != null && now.isBefore(startTime)) {
            return false;
        }
        
        // 检查结束时间
        if (endTime != null && now.isAfter(endTime)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查名单是否即将过期
     */
    public boolean isExpiringWithin(int hours) {
        if (endTime == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threshold = now.plusHours(hours);
        
        return endTime.isBefore(threshold) && endTime.isAfter(now);
    }
    
    /**
     * 计算剩余有效时间（小时）
     */
    public long calculateRemainingHours() {
        if (endTime == null) {
            return -1; // 表示永久有效
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(endTime)) {
            return 0; // 已过期
        }
        
        return java.time.Duration.between(now, endTime).toHours();
    }
    
    /**
     * 获取时间范围描述
     */
    public String getTimeRangeDesc() {
        if (startTime == null && endTime == null) {
            return "永久有效";
        }
        
        StringBuilder desc = new StringBuilder();
        if (startTime != null) {
            desc.append("从 ").append(startTime.toString().replace("T", " "));
        }
        if (endTime != null) {
            if (desc.length() > 0) {
                desc.append(" 到 ");
            } else {
                desc.append("到 ");
            }
            desc.append(endTime.toString().replace("T", " "));
        }
        
        return desc.toString();
    }
    
    /**
     * 获取完整的名单描述
     */
    public String getFullDescription() {
        StringBuilder desc = new StringBuilder();
        
        desc.append(ipTypeName != null ? ipTypeName : "未知IP类型");
        desc.append(": ");
        desc.append(ipConfig != null ? ipConfig : "未配置");
        
        if (ipCount != null && ipCount > 1) {
            desc.append(" (").append(ipCount).append("个地址)");
        }
        
        return desc.toString();
    }
    
    /**
     * 检查IP地址是否匹配此名单
     */
    public boolean matchesIP(String targetIp) {
        if (targetIp == null || ipType == null) {
            return false;
        }
        
        switch (ipType) {
            case "SINGLE_IP":
                return targetIp.equals(singleIp);
            case "IP_RANGE":
                return isIpInRange(targetIp);
            case "IP_CIDR":
                return isIpInCidr(targetIp);
            default:
                return false;
        }
    }
    
    /**
     * 检查IP是否在范围内
     */
    private boolean isIpInRange(String targetIp) {
        if (startIp == null || endIp == null) {
            return false;
        }
        
        try {
            long targetLong = ipToLong(targetIp);
            long startLong = ipToLong(startIp);
            long endLong = ipToLong(endIp);
            
            return targetLong >= startLong && targetLong <= endLong;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查IP是否在CIDR范围内
     */
    private boolean isIpInCidr(String targetIp) {
        if (cidrIp == null || !cidrIp.contains("/")) {
            return false;
        }
        
        try {
            String[] parts = cidrIp.split("/");
            String networkIp = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);
            
            long targetLong = ipToLong(targetIp);
            long networkLong = ipToLong(networkIp);
            
            long mask = (0xFFFFFFFFL << (32 - prefixLength)) & 0xFFFFFFFFL;
            
            return (targetLong & mask) == (networkLong & mask);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * IP地址转换为长整型
     */
    private long ipToLong(String ip) {
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            throw new IllegalArgumentException("Invalid IP address: " + ip);
        }
        
        long result = 0;
        for (int i = 0; i < 4; i++) {
            result = (result << 8) + Integer.parseInt(parts[i]);
        }
        
        return result;
    }
    
    @Override
    public String toString() {
        return "IPBlacklistVO{"
                + "id=" + id +
                ", listName='" + listName + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", ipType='" + ipType + '\'' +
                ", ipTypeName='" + ipTypeName + '\'' +
                ", singleIp='" + singleIp + '\'' +
                ", startIp='" + startIp + '\'' +
                ", endIp='" + endIp + '\'' +
                ", cidrIp='" + cidrIp + '\'' +
                ", ipConfig='" + ipConfig + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                ", enabledName='" + enabledName + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", isValid=" + isValid +
                ", isExpiring=" + isExpiring +
                ", remainingHours=" + remainingHours +
                ", ipCount=" + ipCount +
                '}';
    }
}
