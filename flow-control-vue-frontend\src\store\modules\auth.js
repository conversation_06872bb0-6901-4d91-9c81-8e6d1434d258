const state = {
  token: localStorage.getItem('token') || null,
  user: JSON.parse(localStorage.getItem('user')) || null,
  isAuthenticated: !!localStorage.getItem('token')
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
    state.isAuthenticated = !!token
    if (token) {
      localStorage.setItem('token', token)
    } else {
      localStorage.removeItem('token')
    }
  },
  SET_USER(state, user) {
    state.user = user
    if (user) {
      localStorage.setItem('user', JSON.stringify(user))
    } else {
      localStorage.removeItem('user')
    }
  },
  LOGOUT(state) {
    state.token = null
    state.user = null
    state.isAuthenticated = false
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }
}

const actions = {
  async login({ commit }, { token, user }) {
    commit('SET_TOKEN', token)
    commit('SET_USER', user)
    return Promise.resolve()
  },
  
  async logout({ commit }) {
    try {
      // 调用后端登出接口
      // await api.auth.logout()
    } catch (error) {
      console.error('Logout API call failed:', error)
    } finally {
      commit('LOGOUT')
    }
  },
  
  async getUserInfo({ commit, state }) {
    if (!state.token) {
      throw new Error('No token available')
    }
    
    try {
      // const response = await api.auth.getUserInfo()
      // commit('SET_USER', response.data)
      // return response.data
    } catch (error) {
      // Token可能已过期，清除本地存储
      commit('LOGOUT')
      throw error
    }
  },
  
  async refreshToken({ commit, state }) {
    if (!state.token) {
      throw new Error('No token to refresh')
    }
    
    try {
      // const response = await api.auth.refreshToken()
      // commit('SET_TOKEN', response.data.token)
      // return response.data.token
    } catch (error) {
      // 刷新失败，清除本地存储
      commit('LOGOUT')
      throw error
    }
  }
}

const getters = {
  isAuthenticated: state => state.isAuthenticated,
  currentUser: state => state.user,
  token: state => state.token
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}