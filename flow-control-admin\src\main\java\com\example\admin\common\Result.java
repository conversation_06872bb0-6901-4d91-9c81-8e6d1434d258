package com.example.admin.common;

import java.io.Serializable;

/**
 * 统一返回结果
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class Result<T> implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 成功状态码
	 */
	public static final int SUCCESS_CODE = 200;

	/**
	 * 失败状态码
	 */
	public static final int ERROR_CODE = 500;

	/**
	 * 参数错误状态码
	 */
	public static final int PARAM_ERROR_CODE = 400;

	/**
	 * 未授权状态码
	 */
	public static final int UNAUTHORIZED_CODE = 401;

	/**
	 * 禁止访问状态码
	 */
	public static final int FORBIDDEN_CODE = 403;

	/**
	 * 资源不存在状态码
	 */
	public static final int NOT_FOUND_CODE = 404;

	/**
	 * 状态码
	 */
	private int code;

	/**
	 * 消息
	 */
	private String message;

	/**
	 * 数据
	 */
	private T data;

	/**
	 * 时间戳
	 */
	private long timestamp;

	public Result() {
		this.timestamp = System.currentTimeMillis();
	}

	public Result(int code, String message) {
		this();
		this.code = code;
		this.message = message;
	}

	public Result(int code, String message, T data) {
		this(code, message);
		this.data = data;
	}

	/**
	 * 成功返回
	 * 
	 * @param <T> 数据类型
	 * @return 结果
	 */
	public static <T> Result<T> success() {
		return new Result<>(SUCCESS_CODE, "操作成功");
	}

	/**
	 * 成功返回
	 * 
	 * @param data 数据
	 * @param <T>  数据类型
	 * @return 结果
	 */
	public static <T> Result<T> success(T data) {
		return new Result<>(SUCCESS_CODE, "操作成功", data);
	}

	/**
	 * 成功返回
	 * 
	 * @param message 消息
	 * @param data    数据
	 * @param <T>     数据类型
	 * @return 结果
	 */
	public static <T> Result<T> success(String message, T data) {
		return new Result<>(SUCCESS_CODE, message, data);
	}

	/**
	 * 失败返回
	 * 
	 * @param <T> 数据类型
	 * @return 结果
	 */
	public static <T> Result<T> error() {
		return new Result<>(ERROR_CODE, "操作失败");
	}

	/**
	 * 失败返回
	 * 
	 * @param message 消息
	 * @param <T>     数据类型
	 * @return 结果
	 */
	public static <T> Result<T> error(String message) {
		return new Result<>(ERROR_CODE, message);
	}

	/**
	 * 失败返回
	 *
	 * @param code    状态码
	 * @param message 消息
	 * @param <T>     数据类型
	 * @return 结果
	 */
	public static <T> Result<T> error(int code, String message) {
		return new Result<>(code, message);
	}

	/**
	 * 失败返回
	 *
	 * @param message 消息
	 * @param data    数据
	 * @param <T>     数据类型
	 * @return 结果
	 */
	public static <T> Result<T> error(String message, T data) {
		return new Result<>(ERROR_CODE, message, data);
	}

	/**
	 * 参数错误返回
	 * 
	 * @param message 消息
	 * @param <T>     数据类型
	 * @return 结果
	 */
	public static <T> Result<T> paramError(String message) {
		return new Result<>(PARAM_ERROR_CODE, message);
	}

	/**
	 * 未授权返回
	 * 
	 * @param <T> 数据类型
	 * @return 结果
	 */
	public static <T> Result<T> unauthorized() {
		return new Result<>(UNAUTHORIZED_CODE, "未授权访问");
	}

	/**
	 * 禁止访问返回
	 * 
	 * @param <T> 数据类型
	 * @return 结果
	 */
	public static <T> Result<T> forbidden() {
		return new Result<>(FORBIDDEN_CODE, "禁止访问");
	}

	/**
	 * 资源不存在返回
	 * 
	 * @param <T> 数据类型
	 * @return 结果
	 */
	public static <T> Result<T> notFound() {
		return new Result<>(NOT_FOUND_CODE, "资源不存在");
	}

	/**
	 * 判断是否成功
	 * 
	 * @return 是否成功
	 */
	public boolean isSuccess() {
		return SUCCESS_CODE == this.code;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}
}