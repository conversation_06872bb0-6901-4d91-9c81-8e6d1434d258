package com.example.gateway;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.handler.FilteringWebHandler;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.context.ApplicationContext;
import org.springframework.web.server.WebHandler;

import java.util.Map;

/**
 * Gateway应用启动类
 */
@Slf4j
@SpringBootApplication
@MapperScan("com.example.gateway.mapper")
public class GatewayApplication implements CommandLineRunner {

	@Autowired
	private ApplicationContext applicationContext;

	public static void main(String[] args) {
		SpringApplication.run(GatewayApplication.class, args);
	}

	@Override
	public void run(String... args) throws Exception {
		// 检查Spring容器中的GlobalFilter Bean
		Map<String, GlobalFilter> globalFilters = applicationContext.getBeansOfType(GlobalFilter.class);
		log.info("🔍 检查Spring容器中的GlobalFilter Bean:");
		for (Map.Entry<String, GlobalFilter> entry : globalFilters.entrySet()) {
			log.info("  - Bean名称: {}, 类型: {}", entry.getKey(), entry.getValue().getClass().getSimpleName());
		}
		log.info("🔍 总共找到 {} 个GlobalFilter Bean", globalFilters.size());

		// 检查Spring Cloud Gateway相关的Bean
		try {
			Object routeLocator = applicationContext.getBean("routeDefinitionRouteLocator");
			log.info("✅ RouteDefinitionRouteLocator Bean存在: {}", routeLocator.getClass().getSimpleName());
		} catch (Exception e) {
			log.error("❌ RouteDefinitionRouteLocator Bean不存在: {}", e.getMessage());
		}

		try {
			Object filteringWebHandler = applicationContext.getBean("filteringWebHandler");
			log.info("✅ FilteringWebHandler Bean存在: {}", filteringWebHandler.getClass().getSimpleName());
		} catch (Exception e) {
			log.error("❌ FilteringWebHandler Bean不存在: {}", e.getMessage());
		}

		try {
			Object gatewayFilterAdapter = applicationContext.getBean("gatewayFilterAdapter");
			log.info("✅ GatewayFilterAdapter Bean存在: {}", gatewayFilterAdapter.getClass().getSimpleName());
		} catch (Exception e) {
			log.error("❌ GatewayFilterAdapter Bean不存在: {}", e.getMessage());
		}
	}
}