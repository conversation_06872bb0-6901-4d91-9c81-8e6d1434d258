package com.example.admin.dto;



import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 监控数据DTO
 */

public class MonitorDataDTO {
    
    /**
     * 统计时间
     */
    @NotNull(message = "统计时间不能为空")
    private LocalDateTime statTime;
    
    /**
     * 资源名称
     */
    @NotBlank(message = "资源名称不能为空")
    @Size(max = 200, message = "资源名称长度不能超过200个字符")
    private String resourceName;
    
    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    @Size(max = 50, message = "租户ID长度不能超过50个字符")
    private String tenantId;
    
    /**
     * 总请求数
     */
    @Min(value = 0, message = "总请求数不能为负数")
    private Long totalRequests = 0L;
    
    /**
     * 通过请求数
     */
    @Min(value = 0, message = "通过请求数不能为负数")
    private Long passRequests = 0L;
    
    /**
     * 阻塞请求数
     */
    @Min(value = 0, message = "阻塞请求数不能为负数")
    private Long blockRequests = 0L;
    
    /**
     * 排队请求数
     */
    @Min(value = 0, message = "排队请求数不能为负数")
    private Long queueRequests = 0L;
    
    /**
     * 总响应时间（毫秒）
     */
    @Min(value = 0, message = "总响应时间不能为负数")
    private Long totalRt = 0L;
    
    /**
     * 平均响应时间（毫秒）
     */
    @DecimalMin(value = "0.0", message = "平均响应时间不能为负数")
    private Double avgRt = 0.0;
    
    /**
     * 最大响应时间（毫秒）
     */
    @Min(value = 0, message = "最大响应时间不能为负数")
    private Integer maxRt = 0;
    
    /**
     * 最小响应时间（毫秒）
     */
    @Min(value = 0, message = "最小响应时间不能为负数")
    private Integer minRt = 0;
    
    /**
     * 统计类型：HOURLY-小时统计，DAILY-日统计
     */
    @NotBlank(message = "统计类型不能为空")
    @Pattern(regexp = "^(HOURLY|DAILY)$", message = "统计类型必须为HOURLY或DAILY")
    private String statType;
    
    /**
     * 计算通过率
     */
    public Double getPassRate() {
        if (totalRequests == null || totalRequests == 0) {
            return 0.0;
        }
        return (double) passRequests / totalRequests * 100;
    }
    
    /**
     * 计算阻塞率
     */
    public Double getBlockRate() {
        if (totalRequests == null || totalRequests == 0) {
            return 0.0;
        }
        return (double) blockRequests / totalRequests * 100;
    }
    
    /**
     * 计算排队率
     */
    public Double getQueueRate() {
        if (totalRequests == null || totalRequests == 0) {
            return 0.0;
        }
        return (double) queueRequests / totalRequests * 100;
    }
}