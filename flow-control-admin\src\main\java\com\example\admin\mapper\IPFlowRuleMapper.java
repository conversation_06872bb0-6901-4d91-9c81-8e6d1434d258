package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.entity.IPFlowRule;
import com.example.admin.vo.IPFlowRuleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IP流量规则Mapper接口
 */
@Mapper
public interface IPFlowRuleMapper extends BaseMapper<IPFlowRule> {

	/**
	 * 分页查询IP流量规则（带租户名称）
	 *
	 * @param page     分页参数
	 * @param tenantId 租户ID
	 * @param ruleType 规则类型
	 * @param status   规则状态
	 * @return IP流量规则VO列表
	 */
	Page<IPFlowRuleVO> selectIPFlowRuleVOPage(Page<IPFlowRuleVO> page, @Param("tenantId") String tenantId,
			@Param("ruleType") String ruleType, @Param("status") Integer status);

	/**
	 * 根据租户ID查询IP流量规则
	 *
	 * @param tenantId 租户ID
	 * @param status   状态
	 * @param limit    限制数量
	 * @return IP流量规则列表
	 */
	List<IPFlowRuleVO> selectByTenantId(@Param("tenantId") String tenantId, @Param("status") Integer status,
			@Param("limit") Integer limit);

	/**
	 * 根据规则名称查询IP流量规则（用于重名检查）
	 *
	 * @param ruleName  规则名称
	 * @param excludeId 排除的ID（可选，用于更新时检查）
	 * @return IP流量规则
	 */
	IPFlowRule selectByRuleName(@Param("ruleName") String ruleName, @Param("excludeId") Long excludeId);

	/**
	 * 批量更新规则状态
	 *
	 * @param ids    规则ID列表
	 * @param status 状态
	 * @return 更新数量
	 */
	int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

	/**
	 * 根据租户ID统计IP规则数量
	 *
	 * @param tenantId 租户ID
	 * @return IP规则数量
	 */
	int countByTenantId(@Param("tenantId") String tenantId, @Param("status") Integer status);

	/**
	 * 查询所有启用的IP流量规则
	 *
	 * @return 启用的IP流量规则列表
	 */
	List<IPFlowRule> selectAllEnabled();

	/**
	 * 批量插入IP流量规则
	 *
	 * @param ipFlowRules IP流量规则列表
	 * @return 插入数量
	 */
	int batchInsert(@Param("list") List<IPFlowRule> ipFlowRules);

	/**
	 * 查询启用的规则
	 *
	 * @param tenantId 租户ID
	 * @param limit    限制数量
	 * @return IP流量规则VO列表
	 */
	List<IPFlowRuleVO> selectEnabledRules(@Param("tenantId") String tenantId, @Param("limit") Integer limit);

	/**
	 * 获取规则类型统计
	 *
	 * @param tenantId 租户ID
	 * @return 统计结果
	 */
	List<java.util.Map<String, Object>> getRuleTypeStatistics(@Param("tenantId") String tenantId);

	/**
	 * 获取租户IP规则统计
	 *
	 * @param limit 限制数量
	 * @return 统计结果
	 */
	List<java.util.Map<String, Object>> getTenantIPRuleStatistics(@Param("limit") Integer limit);

	/**
	 * 查询匹配的规则
	 *
	 * @param clientIp 客户端IP
	 * @param tenantId 租户ID
	 * @return IP流量规则VO列表
	 */
	List<IPFlowRuleVO> selectMatchingRules(@Param("clientIp") String clientIp, @Param("tenantId") String tenantId);
}