// 响应式断点
$breakpoint-xs: 480px;   // 超小屏幕（手机）
$breakpoint-sm: 768px;   // 小屏幕（平板）
$breakpoint-md: 992px;   // 中等屏幕（小桌面）
$breakpoint-lg: 1200px;  // 大屏幕（桌面）
$breakpoint-xl: 1920px;  // 超大屏幕

// 响应式混合器
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == sm {
    @media (min-width: #{$breakpoint-xs}) and (max-width: #{$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == md {
    @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == lg {
    @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == xl {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
}

// 最大宽度响应式
@mixin max-width($width) {
  @media (max-width: #{$width}) {
    @content;
  }
}

// 最小宽度响应式
@mixin min-width($width) {
  @media (min-width: #{$width}) {
    @content;
  }
}

// 响应式字体大小
@mixin responsive-font-size($mobile: 14px, $tablet: 16px, $desktop: 18px) {
  font-size: $mobile;
  
  @include respond-to(sm) {
    font-size: $tablet;
  }
  
  @include respond-to(md) {
    font-size: $desktop;
  }
}

// 响应式间距
@mixin responsive-spacing($mobile: 10px, $tablet: 15px, $desktop: 20px) {
  padding: $mobile;
  
  @include respond-to(sm) {
    padding: $tablet;
  }
  
  @include respond-to(md) {
    padding: $desktop;
  }
}

// 响应式网格
@mixin responsive-grid($mobile-cols: 1, $tablet-cols: 2, $desktop-cols: 3) {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat($mobile-cols, 1fr);
  
  @include respond-to(sm) {
    grid-template-columns: repeat($tablet-cols, 1fr);
  }
  
  @include respond-to(md) {
    grid-template-columns: repeat($desktop-cols, 1fr);
  }
}

// 响应式容器
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 15px;
  
  @include respond-to(sm) {
    max-width: 750px;
    padding: 0 20px;
  }
  
  @include respond-to(md) {
    max-width: 970px;
  }
  
  @include respond-to(lg) {
    max-width: 1170px;
  }
  
  @include respond-to(xl) {
    max-width: 1400px;
  }
}

// 响应式显示/隐藏
.hidden-xs {
  @include max-width($breakpoint-xs - 1px) {
    display: none !important;
  }
}

.hidden-sm {
  @include max-width($breakpoint-sm - 1px) {
    display: none !important;
  }
}

.hidden-md {
  @include max-width($breakpoint-md - 1px) {
    display: none !important;
  }
}

.visible-xs {
  @include min-width($breakpoint-xs) {
    display: none !important;
  }
}

.visible-sm {
  @include min-width($breakpoint-sm) {
    display: none !important;
  }
}

.visible-md {
  @include min-width($breakpoint-md) {
    display: none !important;
  }
}

// 响应式文本对齐
.text-center-mobile {
  @include max-width($breakpoint-sm - 1px) {
    text-align: center;
  }
}

.text-left-mobile {
  @include max-width($breakpoint-sm - 1px) {
    text-align: left;
  }
}

// 响应式间距工具类
@for $i from 1 through 5 {
  .m-#{$i} {
    margin: #{$i * 5}px;
    
    @include max-width($breakpoint-sm - 1px) {
      margin: #{$i * 3}px;
    }
  }
  
  .p-#{$i} {
    padding: #{$i * 5}px;
    
    @include max-width($breakpoint-sm - 1px) {
      padding: #{$i * 3}px;
    }
  }
}

// 响应式Flex布局
.flex-responsive {
  display: flex;
  flex-wrap: wrap;
  
  @include max-width($breakpoint-sm - 1px) {
    flex-direction: column;
  }
}

.flex-center-mobile {
  @include max-width($breakpoint-sm - 1px) {
    justify-content: center;
    align-items: center;
  }
}