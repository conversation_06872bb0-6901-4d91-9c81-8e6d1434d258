# JMeter流量控制测试使用说明

## 概述

本文档介绍如何使用JMeter对流量控制系统进行性能测试和限流功能验证。测试套件包含完整的测试计划和自动化执行脚本。

## 文件说明

### 核心文件

1. **flow-control-jmeter-test.jmx** - JMeter测试计划文件
2. **run-jmeter-flow-control-test.bat** - Windows执行脚本
3. **run-jmeter-flow-control-test.sh** - Linux/Mac执行脚本
4. **JMeter流量控制测试使用说明.md** - 本说明文档

## 测试规则配置

基于以下6条租户流量控制规则创建测试用例：

| 规则ID | 接口路径 | 租户ID | QPS限制 | 控制行为 | 预热时间 | 排队超时 | 说明 |
|--------|----------|--------|---------|----------|----------|----------|------|
| 1 | /api/test | tenant_001 | 10 | 快速失败 | - | - | 用户查询接口限流 |
| 2 | /api/admin/users | tenant_001 | 10 | 预热启动 | 30秒 | - | 用户管理接口限流 |
| 3 | /api/admin/ip-rules | tenant_001 | 10 | 排队等待 | - | 5秒 | 支付接口限流 |
| 4 | /api/admin/ip-rules | tenant_002 | 10 | 快速失败 | - | - | 数据导出接口限流 |
| 5 | /api/test | tenant_002 | 10 | 快速失败 | - | - | 文件上传接口限流 |
| 6 | /api/admin/users | tenant_002 | 10 | 快速失败 | - | - | 租户2用户管理接口限流 |

## 环境准备

### 1. 安装JMeter

#### Windows系统
```bash
# 下载JMeter 5.4.1或更高版本
# 解压到 C:\apache-jmeter-5.4.1
# 修改脚本中的JMETER_HOME路径
```

#### Linux/Mac系统
```bash
# 下载并解压JMeter
wget https://archive.apache.org/dist/jmeter/binaries/apache-jmeter-5.4.1.tgz
tar -xzf apache-jmeter-5.4.1.tgz
sudo mv apache-jmeter-5.4.1 /opt/

# 修改脚本中的JMETER_HOME路径
```

### 2. 确保流量控制服务运行

```bash
# 启动网关服务
cd gateway-service
java -jar target/gateway-service-1.0.0.jar

# 确保服务在 http://localhost:8080 可访问
curl http://localhost:8080/actuator/health
```

### 3. 配置测试参数

编辑测试计划文件中的用户定义变量：
- `BASE_URL`: 服务器地址（默认: http://localhost:8080）
- `TEST_DURATION`: 测试持续时间（默认: 60秒）

## 执行测试

### Windows系统

```cmd
# 直接运行脚本
run-jmeter-flow-control-test.bat

# 或者手动执行JMeter
jmeter -n -t flow-control-jmeter-test.jmx -l results.jtl
```

### Linux/Mac系统

```bash
# 给脚本执行权限
chmod +x run-jmeter-flow-control-test.sh

# 运行脚本
./run-jmeter-flow-control-test.sh

# 或者手动执行JMeter
/opt/apache-jmeter-5.4.1/bin/jmeter -n -t flow-control-jmeter-test.jmx -l results.jtl
```

## 测试配置详解

### 线程组配置

每个租户规则对应一个线程组：
- **线程数**: 20（模拟并发用户）
- **启动时间**: 10秒（除预热测试为30秒）
- **测试持续时间**: 60秒（可配置）
- **循环次数**: 无限循环直到时间结束

### QPS控制

使用`ConstantThroughputTimer`控制请求频率：
- **目标QPS**: 600请求/分钟 = 10请求/秒
- **计算模式**: 基于所有活动线程

### 请求配置

每个HTTP请求包含：
- **请求头**: `X-Tenant-Id`（租户标识）
- **Content-Type**: `application/json`
- **超时设置**: 连接和响应超时（排队测试为5秒）

### 断言验证

响应断言检查：
- **正常响应**: HTTP 200
- **限流响应**: HTTP 429
- **其他响应**: 视为异常

## 结果分析

### 自动生成报告

脚本执行后自动生成：
1. **JTL结果文件**: 详细的请求响应数据
2. **HTML报告**: 可视化性能报告
3. **执行日志**: JMeter运行日志

### 关键指标

#### 1. 基础统计
- 总请求数
- 成功请求数
- 失败请求数
- 成功率/错误率

#### 2. 响应码分布
- HTTP 200: 正常处理
- HTTP 429: 触发限流
- 其他: 异常情况

#### 3. 限流效果验证
- 被限流请求数
- 限流率（应该接近预期值）
- 限流功能是否正常工作

### 预期结果

对于QPS=10的限流规则：
- **理论限流率**: 约83.3%（10/60 ≈ 16.7%通过率）
- **实际限流率**: 80-90%（考虑时间窗口和系统延迟）
- **HTTP 429响应**: 应占大部分失败请求

## 故障排查

### 常见问题

#### 1. JMeter未找到
```
错误: 未找到JMeter安装路径
解决: 修改脚本中的JMETER_HOME变量
```

#### 2. 测试计划文件缺失
```
错误: 未找到测试计划文件
解决: 确保flow-control-jmeter-test.jmx在当前目录
```

#### 3. 服务连接失败
```
错误: Connection refused
解决: 确保流量控制服务正在运行
```

#### 4. 限流未生效
```
现象: 限流率为0%
排查: 
- 检查服务配置
- 验证租户ID是否正确
- 确认限流规则是否生效
```

### 日志分析

检查JMeter执行日志：
```bash
# 查看错误信息
grep -i error jmeter-results/jmeter-test_*.log

# 查看警告信息
grep -i warn jmeter-results/jmeter-test_*.log
```

## 高级配置

### 自定义测试参数

修改测试计划中的用户定义变量：

```xml
<elementProp name="BASE_URL" elementType="Argument">
    <stringProp name="Argument.value">http://your-server:8080</stringProp>
</elementProp>
<elementProp name="TEST_DURATION" elementType="Argument">
    <stringProp name="Argument.value">120</stringProp>
</elementProp>
```

### 添加新的测试规则

1. 复制现有线程组
2. 修改线程组名称和HTTP请求路径
3. 更新租户ID和QPS设置
4. 调整特殊配置（预热时间、排队超时等）

### 分布式测试

对于大规模测试，可配置JMeter分布式执行：

```bash
# 主控机执行
jmeter -n -t flow-control-jmeter-test.jmx -R server1,server2 -l results.jtl
```

## 最佳实践

### 1. 测试环境隔离
- 使用独立的测试环境
- 避免与生产数据混合
- 确保网络环境稳定

### 2. 测试数据管理
- 定期清理历史测试结果
- 保留关键测试报告用于对比
- 建立测试结果基线

### 3. 监控和观察
- 同时监控服务端性能指标
- 观察系统资源使用情况
- 记录测试环境配置

### 4. 结果验证
- 对比多次测试结果的一致性
- 验证限流阈值的准确性
- 检查不同租户间的隔离效果

## 扩展功能

### 1. 集成CI/CD

```yaml
# Jenkins Pipeline示例
stage('Performance Test') {
    steps {
        sh './run-jmeter-flow-control-test.sh'
        publishHTML([
            allowMissing: false,
            alwaysLinkToLastBuild: true,
            keepAll: true,
            reportDir: 'jmeter-results/html-report_*',
            reportFiles: 'index.html',
            reportName: 'JMeter Report'
        ])
    }
}
```

### 2. 结果对比分析

```bash
# 对比两次测试结果
jmeter -g results1.jtl results2.jtl -o comparison-report
```

### 3. 自动化告警

```bash
# 基于限流率的告警脚本
if [ $RATE_LIMIT_RATE -lt 80 ]; then
    echo "警告: 限流率过低，可能存在配置问题"
    # 发送告警通知
fi
```

## 联系支持

如遇到问题，请提供以下信息：
1. 错误日志内容
2. 测试环境配置
3. JMeter版本信息
4. 服务端配置详情

---

**注意**: 本测试套件专门针对流量控制系统设计，请根据实际部署环境调整相关配置参数。