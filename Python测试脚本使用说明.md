# Python流量控制测试脚本使用说明

## 概述

`flow_control_test_configurable.py` 是一个可配置的Python流量控制测试脚本，支持自定义测试参数，能够验证流量控制系统的限流效果。

## 功能特点

- **可配置参数**: 支持自定义测试时间、每秒请求数、服务器地址等
- **异步并发**: 使用asyncio和aiohttp实现高并发请求
- **详细统计**: 提供成功率、限流率、响应时间等详细统计信息
- **自动报告**: 自动生成JSON格式的测试报告
- **多租户支持**: 支持多个租户的流量控制规则测试

## 安装依赖

```bash
pip install aiohttp asyncio
```

## 基本使用

### 1. 默认参数测试

```bash
python flow_control_test_configurable.py
```

默认配置：
- 服务器地址: `http://localhost:8088`
- 测试时间: 10秒
- 每秒请求数: 100
- 预期每个接口通过: 100个请求 (10秒 × 10 QPS)
- 预期被拦截: 900个请求

### 2. 自定义参数测试

```bash
# 自定义测试时间和请求频率
python flow_control_test_configurable.py --duration 30 --rps 50

# 自定义服务器地址
python flow_control_test_configurable.py --url http://*************:8088

# 指定输出报告文件名
python flow_control_test_configurable.py --output my_test_report.json
```

### 3. 查看测试规则

```bash
python flow_control_test_configurable.py --rules-only
```

## 命令行参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|---------|
| `--url` | 服务器地址 | `http://localhost:8088` | `--url http://localhost:8088` |
| `--duration` | 测试持续时间(秒) | 10 | `--duration 30` |
| `--rps` | 每秒请求数 | 100 | `--rps 50` |
| `--output` | 输出报告文件名 | 自动生成 | `--output test_report.json` |
| `--rules-only` | 只显示测试规则 | - | `--rules-only` |

## 测试规则配置

当前脚本包含6个测试规则，所有规则都统一配置为：
- **QPS限制**: 10
- **控制行为**: 快速失败 (control_behavior=0)
- **涵盖接口**: `/api/test`, `/api/admin/users`, `/api/admin/ip-rules`
- **涵盖租户**: `tenant_001`, `tenant_002`

### 规则列表

1. **用户查询接口限流** - `/api/test` (tenant_001)
2. **用户管理接口限流** - `/api/admin/users` (tenant_001)
3. **支付接口限流** - `/api/admin/ip-rules` (tenant_001)
4. **数据导出接口限流** - `/api/admin/ip-rules` (tenant_002)
5. **文件上传接口限流** - `/api/test` (tenant_002)
6. **租户2用户管理接口限流** - `/api/admin/users` (tenant_002)

## 测试结果分析

### 实时输出

测试过程中会实时显示：
- 当前测试的规则信息
- 预期通过和拦截的请求数量
- 实际测试结果统计
- QPS准确度分析

### 测试报告

测试完成后会生成JSON格式的详细报告，包含：

```json
{
  "test_time": "测试时间",
  "base_url": "服务器地址",
  "test_config": {
    "duration": "测试时长",
    "requests_per_second": "每秒请求数"
  },
  "results": [
    {
      "rule_name": "规则名称",
      "actual_results": {
        "total_requests": "总请求数",
        "success_requests": "成功请求数",
        "blocked_requests": "被限流请求数",
        "success_qps": "实际成功QPS"
      },
      "statistics": {
        "success_rate": "成功率(%)",
        "block_rate": "限流率(%)",
        "avg_response_time": "平均响应时间"
      },
      "analysis": {
        "qps_accuracy": "QPS准确度偏差(%)",
        "limit_effectiveness": "限流是否有效"
      }
    }
  ],
  "summary": {
    "overall_success_rate": "总体成功率",
    "overall_block_rate": "总体限流率",
    "limit_effectiveness_rate": "限流有效率"
  }
}
```

### 关键指标说明

- **成功率**: 返回HTTP 200的请求占比
- **限流率**: 返回HTTP 429的请求占比
- **QPS准确度偏差**: 实际成功QPS与预期限制的偏差百分比
- **限流有效率**: 有多少规则成功触发了限流机制

## 预期测试效果

基于当前配置（10秒，100 RPS，QPS限制10）：

- **每个接口总请求**: 1000个
- **预期通过**: 100个 (10 QPS × 10秒)
- **预期被限流**: 900个
- **预期成功率**: ~10%
- **预期限流率**: ~90%

## 使用示例

### 示例1: 标准测试

```bash
# 运行标准测试
python flow_control_test_configurable.py

# 预期输出
# 总请求数: 6000 (6个规则 × 1000请求)
# 成功请求: ~600 (6个规则 × 100成功)
# 被限流: ~5400 (6个规则 × 900限流)
# 总体成功率: ~10%
# 总体限流率: ~90%
```

### 示例2: 长时间低频测试

```bash
# 30秒，每秒20个请求
python flow_control_test_configurable.py --duration 30 --rps 20

# 预期每个接口:
# 总请求: 600个 (20 RPS × 30秒)
# 通过: 300个 (10 QPS × 30秒)
# 限流: 300个
# 成功率: ~50%
```

### 示例3: 高频压力测试

```bash
# 5秒，每秒200个请求
python flow_control_test_configurable.py --duration 5 --rps 200

# 预期每个接口:
# 总请求: 1000个 (200 RPS × 5秒)
# 通过: 50个 (10 QPS × 5秒)
# 限流: 950个
# 成功率: ~5%
```

## 故障排除

### 常见问题

1. **连接错误**
   - 检查服务器是否启动
   - 确认端口号是否正确
   - 检查防火墙设置

2. **Python版本错误**
   - 需要Python 3.7或更高版本
   - 确保安装了aiohttp库

3. **测试结果异常**
   - 检查流量控制规则是否正确配置
   - 确认服务器时间窗口设置
   - 检查并发处理能力

### 调试建议

1. **先运行单个规则测试**
2. **降低并发度进行测试**
3. **检查服务器日志**
4. **使用较长的测试时间**

## 扩展功能

### 修改测试规则

编辑 `create_test_rules()` 函数来添加或修改测试规则：

```python
def create_test_rules():
    return [
        {
            'rule_name': '自定义规则名称',
            'resource': '/api/custom',
            'tenant_id': 'tenant_003',
            'grade': 0,
            'count': 20,  # QPS限制
            'control_behavior': 0  # 控制行为
        }
    ]
```

### 添加新的统计指标

可以在 `test_single_rule()` 方法中添加更多的统计分析逻辑。

## 注意事项

1. **测试环境**: 建议在测试环境中运行，避免影响生产服务
2. **资源消耗**: 高并发测试会消耗较多系统资源
3. **网络延迟**: 网络延迟可能影响测试结果的准确性
4. **服务器性能**: 确保服务器有足够的处理能力
5. **测试间隔**: 脚本会在测试规则间自动添加2秒间隔

## 技术实现

- **异步编程**: 使用asyncio实现高并发
- **HTTP客户端**: 使用aiohttp进行HTTP请求
- **精确计时**: 使用time.time()进行精确的时间测量
- **错误处理**: 完善的异常处理机制
- **结果统计**: 详细的统计分析功能