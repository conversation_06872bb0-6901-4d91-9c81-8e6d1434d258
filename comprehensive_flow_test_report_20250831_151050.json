{"test_time": "2025-08-31T15:10:50.211618", "total_tests": 4, "passed_tests": 2, "failed_tests": 2, "results": [{"test_type": "tenant_level", "rule_config": {"id": 1, "tenant_id": "tenant1", "rule_name": "租户1线程限流", "ref_resource": null, "grade": 0, "count": 5, "control_behavior": 0, "enabled": 1, "description": "租户1的线程数限流规则"}, "total_requests": 2830, "success_requests": 2290, "blocked_requests": 540, "error_requests": 0, "avg_response_time": 0.027668095730218785, "test_passed": false, "success_rate": 0.8091872791519434, "block_rate": 0.19081272084805653}, {"test_type": "tenant_resource_level", "rule_config": {"id": 2, "tenant_id": "tenant1", "rule_name": "租户1接口限流", "ref_resource": "userInfo", "grade": 1, "count": 10, "control_behavior": 0, "enabled": 1, "description": "租户1的接口QPS限流规则"}, "total_requests": 682, "success_requests": 681, "blocked_requests": 1, "error_requests": 0, "avg_response_time": 0.01747904634895213, "test_passed": true, "success_rate": 0.998533724340176, "block_rate": 0.001466275659824047}, {"test_type": "tenant_level", "rule_config": {"id": 3, "tenant_id": "tenant2", "rule_name": "租户2QPS限流", "ref_resource": null, "grade": 1, "count": 20, "control_behavior": 0, "enabled": 1, "description": "租户2的QPS限流规则"}, "total_requests": 680, "success_requests": 678, "blocked_requests": 2, "error_requests": 0, "avg_response_time": 0.01799725890159607, "test_passed": true, "success_rate": 0.9970588235294118, "block_rate": 0.0029411764705882353}, {"test_type": "tenant_level", "rule_config": {"id": 4, "tenant_id": "tenant3", "rule_name": "租户3线程限流", "ref_resource": null, "grade": 0, "count": 3, "control_behavior": 0, "enabled": 1, "description": "租户3的线程数限流规则"}, "total_requests": 3129, "success_requests": 2551, "blocked_requests": 578, "error_requests": 0, "avg_response_time": 0.024978874666159737, "test_passed": false, "success_rate": 0.8152764461489294, "block_rate": 0.18472355385107064}]}