package com.example.admin.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.*;

import java.text.SimpleDateFormat;
import java.util.List;

/**
 * Web配置类 配置跨域、拦截器、消息转换器、静态资源等
 */
@Configuration
@EnableWebMvc
public class WebConfig implements WebMvcConfigurer {

	private static final Logger log = LoggerFactory.getLogger(WebConfig.class);

	/**
	 * 配置跨域过滤器
	 */
	@Bean
	public CorsFilter corsFilter() {
		CorsConfiguration config = new CorsConfiguration();

		// 允许所有域名进行跨域调用
		config.addAllowedOriginPattern("*");

		// 允许所有请求头
		config.addAllowedHeader("*");

		// 允许所有请求方法
		config.addAllowedMethod("*");

		// 允许携带凭证
		config.setAllowCredentials(true);

		// 预检请求的缓存时间（秒）
		config.setMaxAge(3600L);

		// 允许的响应头
		config.addExposedHeader("Content-Disposition");
		config.addExposedHeader("Content-Type");
		config.addExposedHeader("Authorization");
		config.addExposedHeader("X-Total-Count");

		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
		source.registerCorsConfiguration("/**", config);

		log.info("CORS configuration completed");
		return new CorsFilter(source);
	}

	/**
	 * 配置消息转换器
	 */
	@Override
	public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
		// 添加UTF-8字符串消息转换器
		org.springframework.http.converter.StringHttpMessageConverter stringConverter = new org.springframework.http.converter.StringHttpMessageConverter(
				java.nio.charset.StandardCharsets.UTF_8);
		converters.add(stringConverter);

		// 添加JSON消息转换器
		converters.add(mappingJackson2HttpMessageConverter());
		log.info("Message converter configuration completed");
	}

	/**
	 * 配置Jackson消息转换器
	 */
	@Bean
	public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
		MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
		converter.setObjectMapper(objectMapper());

		// 设置支持的媒体类型，确保UTF-8编码
		java.util.List<org.springframework.http.MediaType> supportedMediaTypes = new java.util.ArrayList<>();
		supportedMediaTypes.add(
				new org.springframework.http.MediaType("application", "json", java.nio.charset.StandardCharsets.UTF_8));
		supportedMediaTypes
				.add(new org.springframework.http.MediaType("text", "json", java.nio.charset.StandardCharsets.UTF_8));
		converter.setSupportedMediaTypes(supportedMediaTypes);

		return converter;
	}

	/**
	 * 配置ObjectMapper
	 */
	@Bean
	public ObjectMapper objectMapper() {
		ObjectMapper objectMapper = new ObjectMapper();

		// 注册JavaTimeModule以支持Java 8时间类型
		objectMapper.registerModule(new JavaTimeModule());

		// 禁用将日期写为时间戳的功能
		objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

		// 设置日期格式
		objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

		// 配置字段命名策略：将驼峰命名转换为下划线命名（用于数据库映射）
		// 注意：这里保持驼峰命名，因为前端期望驼峰格式
		objectMapper.setPropertyNamingStrategy(com.fasterxml.jackson.databind.PropertyNamingStrategies.LOWER_CAMEL_CASE);

		// 忽略未知属性
		objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

		// 忽略空值属性
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

		// 配置时间序列化格式
		objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

		log.info("ObjectMapper configuration completed with camelCase naming strategy");
		return objectMapper;
	}

	/**
	 * 配置静态资源处理
	 */
	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {
		// Swagger UI资源
		registry.addResourceHandler("/swagger-ui/**")
				.addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/")
				.resourceChain(false);

		// 静态资源
		registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/").setCachePeriod(3600);

		// 文档资源
		registry.addResourceHandler("/doc.html").addResourceLocations("classpath:/META-INF/resources/");

		registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");

		log.info("Static resource handling configuration completed");
	}

	/**
	 * 配置拦截器
	 */
	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		// 日志拦截器
		registry.addInterceptor(new LoggingInterceptor()).addPathPatterns("/**").excludePathPatterns("/swagger-ui/**",
				"/doc.html", "/webjars/**", "/static/**");

		// 认证拦截器（如果需要的话）
		// registry.addInterceptor(new AuthInterceptor())
		// .addPathPatterns("/api/**")
		// .excludePathPatterns("/api/auth/**", "/api/public/**");

		log.info("Interceptor configuration completed");
	}

	/**
	 * 配置视图控制器
	 */
	@Override
	public void addViewControllers(ViewControllerRegistry registry) {
		// 重定向根路径到Swagger UI
		registry.addRedirectViewController("/", "/doc.html");
		registry.addRedirectViewController("/docs", "/doc.html");

		log.info("View controller configuration completed");
	}

	/**
	 * 配置内容协商
	 */
	@Override
	public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
		configurer.favorParameter(true).parameterName("mediaType")
				.defaultContentType(org.springframework.http.MediaType.APPLICATION_JSON)
				.mediaType("json", org.springframework.http.MediaType.APPLICATION_JSON)
				.mediaType("xml", org.springframework.http.MediaType.APPLICATION_XML);

		log.info("Content negotiation configuration completed");
	}

	/**
	 * 配置路径匹配
	 */
	@Override
	public void configurePathMatch(PathMatchConfigurer configurer) {
		// 设置URL后缀模式匹配
		configurer.setUseSuffixPatternMatch(false);
		// 设置URL尾部斜杠匹配
		configurer.setUseTrailingSlashMatch(true);

		log.info("Path matching configuration completed");
	}

	/**
	 * 日志拦截器
	 */
	public static class LoggingInterceptor implements org.springframework.web.servlet.HandlerInterceptor {

		private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(LoggingInterceptor.class);

		@Override
		public boolean preHandle(jakarta.servlet.http.HttpServletRequest request,
				jakarta.servlet.http.HttpServletResponse response, Object handler) throws Exception {
			long startTime = System.currentTimeMillis();
			request.setAttribute("startTime", startTime);

			logger.debug("Request started - Method: {}, URI: {}, RemoteAddr: {}", request.getMethod(),
					request.getRequestURI(), request.getRemoteAddr());

			return true;
		}

		@Override
		public void afterCompletion(jakarta.servlet.http.HttpServletRequest request,
				jakarta.servlet.http.HttpServletResponse response, Object handler, Exception ex) throws Exception {
			Long startTime = (Long) request.getAttribute("startTime");
			if (startTime != null) {
				long endTime = System.currentTimeMillis();
				long executeTime = endTime - startTime;

				logger.debug("Request completed - Method: {}, URI: {}, Status: {}, ExecuteTime: {}ms",
						request.getMethod(), request.getRequestURI(), response.getStatus(), executeTime);

				// 记录慢请求
				if (executeTime > 1000) {
					logger.warn("Slow request warning - Method: {}, URI: {}, ExecuteTime: {}ms", request.getMethod(),
							request.getRequestURI(), executeTime);
				}
			}

			if (ex != null) {
				logger.error("Request exception - Method: {}, URI: {}", request.getMethod(), request.getRequestURI(),
						ex);
			}
		}
	}

	/**
	 * 认证拦截器（示例）
	 */
	/*
	 * public static class AuthInterceptor implements HandlerInterceptor {
	 * 
	 * private static final Logger logger =
	 * LoggerFactory.getLogger(AuthInterceptor.class);
	 * 
	 * @Override public boolean preHandle(HttpServletRequest request,
	 * HttpServletResponse response, Object handler) throws Exception { String token
	 * = request.getHeader("Authorization");
	 * 
	 * if (StringUtils.isEmpty(token)) {
	 * response.setStatus(HttpStatus.UNAUTHORIZED.value());
	 * response.setContentType("application/json;charset=UTF-8");
	 * response.getWriter().write("{\"code\":401,\"message\":\"未授权访问\"}"); return
	 * false; }
	 * 
	 * // TODO: 验证token有效性
	 * 
	 * return true; } }
	 */

	/**
	 * 全局异常处理器
	 */
	@org.springframework.web.bind.annotation.ControllerAdvice
	public static class GlobalExceptionHandler {

		private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(GlobalExceptionHandler.class);

		/**
		 * 处理参数验证异常
		 */
		@org.springframework.web.bind.annotation.ExceptionHandler(org.springframework.web.bind.MethodArgumentNotValidException.class)
		@org.springframework.web.bind.annotation.ResponseBody
		public com.example.admin.common.Result<?> handleValidationException(
				org.springframework.web.bind.MethodArgumentNotValidException e) {
			logger.warn("参数验证失败", e);

			StringBuilder message = new StringBuilder("参数验证失败: ");
			e.getBindingResult().getFieldErrors().forEach(error -> message.append(error.getField()).append(" ")
					.append(error.getDefaultMessage()).append("; "));

			return com.example.admin.common.Result.error(message.toString());
		}

		/**
		 * 处理约束违反异常
		 */
		@org.springframework.web.bind.annotation.ExceptionHandler(jakarta.validation.ConstraintViolationException.class)
		@org.springframework.web.bind.annotation.ResponseBody
		public com.example.admin.common.Result<?> handleConstraintViolationException(
				jakarta.validation.ConstraintViolationException e) {
			logger.warn("约束验证失败", e);

			StringBuilder message = new StringBuilder("约束验证失败: ");
			e.getConstraintViolations().forEach(violation -> message.append(violation.getPropertyPath()).append(" ")
					.append(violation.getMessage()).append("; "));

			return com.example.admin.common.Result.error(message.toString());
		}

		/**
		 * 处理通用异常
		 */
		@org.springframework.web.bind.annotation.ExceptionHandler(Exception.class)
		@org.springframework.web.bind.annotation.ResponseBody
		public com.example.admin.common.Result<?> handleException(Exception e) {
			logger.error("系统异常", e);
			return com.example.admin.common.Result.error("系统异常: " + e.getMessage());
		}
	}
}