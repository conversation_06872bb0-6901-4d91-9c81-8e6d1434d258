package com.example.admin.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * IP白名单/黑名单通用VO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Schema(description = "IP白名单/黑名单通用VO")
public class IPWhitelistBlacklistVO {
    
    @Schema(description = "名单ID")
    private Long id;
    
    @Schema(description = "名单名称")
    private String listName;
    
    @Schema(description = "租户ID")
    private String tenantId;
    
    @Schema(description = "租户名称")
    private String tenantName;
    
    @Schema(description = "名单类型：WHITELIST-白名单，BLACKLIST-黑名单")
    private String listType;
    
    @Schema(description = "IP类型：SINGLE_IP-单个IP，IP_RANGE-IP范围，IP_CIDR-CIDR格式")
    private String ipType;
    
    @Schema(description = "IP类型名称")
    private String ipTypeName;
    
    @Schema(description = "单个IP地址")
    private String singleIp;
    
    @Schema(description = "IP地址（用于单个IP类型）")
    private String ipAddress;
    
    @Schema(description = "IP范围起始地址")
    private String startIp;
    
    @Schema(description = "IP范围结束地址")
    private String endIp;
    
    @Schema(description = "CIDR格式IP")
    private String cidrIp;
    
    @Schema(description = "IP起始地址（用于范围类型）")
    private String ipStart;
    
    @Schema(description = "IP结束地址（用于范围类型）")
    private String ipEnd;
    
    @Schema(description = "CIDR格式IP段")
    private String ipCidr;
    
    @Schema(description = "名单ID")
    private Long listId;
    
    @Schema(description = "IP配置信息（格式化显示）")
    private String ipConfig;
    
    @Schema(description = "优先级")
    private Integer priority;
    
    @Schema(description = "是否启用：0-禁用，1-启用")
    private Integer enabled;
    
    @Schema(description = "启用状态名称")
    private String enabledName;
    
    @Schema(description = "生效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    @Schema(description = "生效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    @Schema(description = "名单描述")
    private String description;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    @Schema(description = "创建人")
    private String createBy;
    
    @Schema(description = "更新人")
    private String updateBy;
    
    @Schema(description = "是否有效（当前时间在生效时间范围内且启用）")
    private Boolean isValid;
    
    @Schema(description = "是否即将过期")
    private Boolean isExpiring;
    
    @Schema(description = "剩余有效时间（小时）")
    private Long remainingHours;
    
    @Schema(description = "IP地址数量（对于范围和CIDR）")
    private Long ipCount;
    
    // Constructors
    public IPWhitelistBlacklistVO() {}
    
    public IPWhitelistBlacklistVO(Long id, String listName, String tenantId, String listType, String ipType) {
        this.id = id;
        this.listName = listName;
        this.tenantId = tenantId;
        this.listType = listType;
        this.ipType = ipType;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getListName() {
        return listName;
    }
    
    public void setListName(String listName) {
        this.listName = listName;
    }
    
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    public String getTenantName() {
        return tenantName;
    }
    
    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }
    
    public String getListType() {
        return listType;
    }
    
    public void setListType(String listType) {
        this.listType = listType;
    }
    
    public String getIpAddress() {
        return ipAddress != null ? ipAddress : singleIp;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public String getIpStart() {
        return ipStart != null ? ipStart : startIp;
    }
    
    public void setIpStart(String ipStart) {
        this.ipStart = ipStart;
    }
    
    public String getIpEnd() {
        return ipEnd != null ? ipEnd : endIp;
    }
    
    public void setIpEnd(String ipEnd) {
        this.ipEnd = ipEnd;
    }
    
    public String getIpCidr() {
        return ipCidr != null ? ipCidr : cidrIp;
    }
    
    public void setIpCidr(String ipCidr) {
        this.ipCidr = ipCidr;
    }
    
    public Long getListId() {
        return listId != null ? listId : id;
    }
    
    public void setListId(Long listId) {
        this.listId = listId;
    }
    
    public String getIpType() {
        return ipType;
    }
    
    public void setIpType(String ipType) {
        this.ipType = ipType;
        // 自动设置IP类型名称
        this.ipTypeName = getIpTypeDesc(ipType);
        // 自动设置IP配置信息
        this.ipConfig = buildIpConfig();
    }
    
    public String getIpTypeName() {
        return ipTypeName;
    }
    
    public void setIpTypeName(String ipTypeName) {
        this.ipTypeName = ipTypeName;
    }
    
    public String getSingleIp() {
        return singleIp;
    }
    
    public void setSingleIp(String singleIp) {
        this.singleIp = singleIp;
        // 更新IP配置信息
        this.ipConfig = buildIpConfig();
    }
    
    public String getStartIp() {
        return startIp;
    }
    
    public void setStartIp(String startIp) {
        this.startIp = startIp;
        // 更新IP配置信息
        this.ipConfig = buildIpConfig();
    }
    
    public String getEndIp() {
        return endIp;
    }
    
    public void setEndIp(String endIp) {
        this.endIp = endIp;
        // 更新IP配置信息
        this.ipConfig = buildIpConfig();
    }
    
    public String getCidrIp() {
        return cidrIp;
    }
    
    public void setCidrIp(String cidrIp) {
        this.cidrIp = cidrIp;
        // 更新IP配置信息
        this.ipConfig = buildIpConfig();
        // 计算IP数量
        this.ipCount = calculateIpCount();
    }
    
    public String getIpConfig() {
        return ipConfig;
    }
    
    public void setIpConfig(String ipConfig) {
        this.ipConfig = ipConfig;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public Integer getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
        // 自动设置启用状态名称
        this.enabledName = enabled != null && enabled == 1 ? "启用" : "禁用";
    }
    
    public String getEnabledName() {
        return enabledName;
    }
    
    public void setEnabledName(String enabledName) {
        this.enabledName = enabledName;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getCreateBy() {
        return createBy;
    }
    
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    
    public String getUpdateBy() {
        return updateBy;
    }
    
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    
    public Boolean getIsValid() {
        return isValid;
    }
    
    public void setIsValid(Boolean isValid) {
        this.isValid = isValid;
    }
    
    public Boolean getIsExpiring() {
        return isExpiring;
    }
    
    public void setIsExpiring(Boolean isExpiring) {
        this.isExpiring = isExpiring;
    }
    
    public Long getRemainingHours() {
        return remainingHours;
    }
    
    public void setRemainingHours(Long remainingHours) {
        this.remainingHours = remainingHours;
    }
    
    public Long getIpCount() {
        return ipCount;
    }
    
    public void setIpCount(Long ipCount) {
        this.ipCount = ipCount;
    }
    
    // 便利方法
    public boolean isEnabled() {
        return enabled != null && enabled == 1;
    }
    
    /**
     * 获取IP类型描述
     */
    private String getIpTypeDesc(String ipType) {
        if (ipType == null) return "";
        switch (ipType) {
            case "SINGLE_IP":
                return "单个IP";
            case "IP_RANGE":
                return "IP范围";
            case "IP_CIDR":
                return "CIDR格式";
            default:
                return ipType;
        }
    }
    
    /**
     * 构建IP配置信息
     */
    private String buildIpConfig() {
        if (ipType == null) return "";
        switch (ipType) {
            case "SINGLE_IP":
                return singleIp != null ? singleIp : "";
            case "IP_RANGE":
                return (startIp != null && endIp != null) ? startIp + " - " + endIp : "";
            case "IP_CIDR":
                return cidrIp != null ? cidrIp : "";
            default:
                return "";
        }
    }
    
    /**
     * 计算IP数量（仅对CIDR格式）
     */
    private Long calculateIpCount() {
        if ("IP_CIDR".equals(ipType) && cidrIp != null && cidrIp.contains("/")) {
            try {
                String[] parts = cidrIp.split("/");
                int prefixLength = Integer.parseInt(parts[1]);
                return (long) Math.pow(2, 32 - prefixLength);
            } catch (Exception e) {
                return 0L;
            }
        }
        return 1L;
    }
    
    @Override
    public String toString() {
        return "IPWhitelistBlacklistVO{" +
                "id=" + id +
                ", listName='" + listName + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", listType='" + listType + '\'' +
                ", ipType='" + ipType + '\'' +
                ", ipConfig='" + ipConfig + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}