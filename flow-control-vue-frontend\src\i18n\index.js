// 国际化配置文件
import Vue from 'vue'
import VueI18n from 'vue-i18n'
import zhCN from './locales/zh-CN'
import enUS from './locales/en-US'

Vue.use(VueI18n)

// 语言包
const messages = {
  'zh-CN': zhCN,
  'en-US': enUS
}

// 获取浏览器语言
function getLanguage() {
  // 优先从localStorage获取
  const savedLanguage = localStorage.getItem('language')
  if (savedLanguage) {
    return savedLanguage
  }
  
  // 从浏览器语言获取
  const language = navigator.language || navigator.userLanguage
  const locales = Object.keys(messages)
  
  for (const locale of locales) {
    if (language.indexOf(locale) > -1) {
      return locale
    }
  }
  
  // 默认返回中文
  return 'zh-CN'
}

// 创建i18n实例
const i18n = new VueI18n({
  locale: getLanguage(),
  fallbackLocale: 'zh-CN',
  messages,
  silentTranslationWarn: true
})

export default i18n

// 设置语言
export function setLanguage(lang) {
  i18n.locale = lang
  localStorage.setItem('language', lang)
  document.querySelector('html').setAttribute('lang', lang)
  return lang
}

// 获取当前语言
export function getLocale() {
  return i18n.locale
}

// 获取支持的语言列表
export function getSupportedLanguages() {
  return [
    { value: 'zh-CN', label: '简体中文', flag: '🇨🇳' },
    { value: 'en-US', label: 'English', flag: '🇺🇸' }
  ]
}