package com.example.admin.common;

/**
 * 系统常量定义
 */
public class Constants {
    
    /**
     * 默认租户ID
     */
    public static final String DEFAULT_TENANT_ID = "default";
    
    /**
     * 系统管理员用户名
     */
    public static final String SYSTEM_ADMIN = "system";
    
    /**
     * 默认页码
     */
    public static final int DEFAULT_PAGE_CURRENT = 1;
    
    /**
     * 默认页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 10;
    
    /**
     * 最大页大小
     */
    public static final int MAX_PAGE_SIZE = 1000;
    
    /**
     * 状态常量
     */
    public static class Status {
        /** 禁用 */
        public static final int DISABLED = 0;
        /** 启用 */
        public static final int ENABLED = 1;
    }
    
    /**
     * 删除标志常量
     */
    public static class Deleted {
        /** 未删除 */
        public static final int NOT_DELETED = 0;
        /** 已删除 */
        public static final int DELETED = 1;
    }
    
    /**
     * 限流模式常量
     */
    public static class LimitMode {
        /** QPS模式 */
        public static final int QPS = 0;
        /** 线程数模式 */
        public static final int THREAD = 1;
    }
    
    /**
     * 流控策略常量
     */
    public static class Strategy {
        /** 直接 */
        public static final int DIRECT = 0;
        /** 关联 */
        public static final int RELATE = 1;
        /** 链路 */
        public static final int CHAIN = 2;
    }
    
    /**
     * 流控行为常量
     */
    public static class Behavior {
        /** 快速失败 */
        public static final int REJECT = 0;
        /** 预热 */
        public static final int WARM_UP = 1;
        /** 排队等待 */
        public static final int RATE_LIMITER = 2;
    }
    
    /**
     * 集群模式常量
     */
    public static class ClusterMode {
        /** 单机模式 */
        public static final int STANDALONE = 0;
        /** 集群模式 */
        public static final int CLUSTER = 1;
    }
    
    /**
     * IP规则类型常量
     */
    public static class IPRuleType {
        /** 单个IP */
        public static final String SINGLE_IP = "SINGLE_IP";
        /** IP范围 */
        public static final String IP_RANGE = "IP_RANGE";
        /** CIDR */
        public static final String CIDR = "CIDR";
    }
    
    /**
     * 列表类型常量
     */
    public static class ListType {
        /** 白名单 */
        public static final String WHITELIST = "WHITELIST";
        /** 黑名单 */
        public static final String BLACKLIST = "BLACKLIST";
    }
    
    /**
     * 统计类型常量
     */
    public static class StatType {
        /** 小时统计 */
        public static final String HOURLY = "HOURLY";
        /** 日统计 */
        public static final String DAILY = "DAILY";
    }
    
    /**
     * 事件类型常量
     */
    public static class EventType {
        /** 通过 */
        public static final String PASS = "PASS";
        /** 阻塞 */
        public static final String BLOCK = "BLOCK";
        /** 排队 */
        public static final String QUEUE = "QUEUE";
        /** 异常 */
        public static final String EXCEPTION = "EXCEPTION";
    }
    
    /**
     * 配置类型常量
     */
    public static class ConfigType {
        /** 系统配置 */
        public static final String SYSTEM = "SYSTEM";
        /** 租户配置 */
        public static final String TENANT = "TENANT";
        /** 告警配置 */
        public static final String ALARM = "ALARM";
    }
    
    /**
     * Redis Key前缀
     */
    public static class RedisKey {
        /** 流量规则前缀 */
        public static final String FLOW_RULE_PREFIX = "flow:rule:";
        /** IP规则前缀 */
        public static final String IP_RULE_PREFIX = "ip:rule:";
        /** 监控统计前缀 */
        public static final String MONITOR_PREFIX = "monitor:";
        /** 系统配置前缀 */
        public static final String CONFIG_PREFIX = "config:";
        /** 租户信息前缀 */
        public static final String TENANT_PREFIX = "tenant:";
    }
    
    /**
     * 时间常量（秒）
     */
    public static class Time {
        /** 1分钟 */
        public static final int ONE_MINUTE = 60;
        /** 5分钟 */
        public static final int FIVE_MINUTES = 300;
        /** 10分钟 */
        public static final int TEN_MINUTES = 600;
        /** 30分钟 */
        public static final int THIRTY_MINUTES = 1800;
        /** 1小时 */
        public static final int ONE_HOUR = 3600;
        /** 1天 */
        public static final int ONE_DAY = 86400;
    }
    
    /**
     * 默认值常量
     */
    public static class Default {
        /** 默认QPS阈值 */
        public static final int DEFAULT_QPS_THRESHOLD = 100;
        /** 默认线程数阈值 */
        public static final int DEFAULT_THREAD_THRESHOLD = 10;
        /** 默认预热时长（秒） */
        public static final int DEFAULT_WARM_UP_PERIOD = 10;
        /** 默认排队超时时间（毫秒） */
        public static final int DEFAULT_QUEUE_TIMEOUT = 500;
        /** 默认优先级 */
        public static final int DEFAULT_PRIORITY = 1;
    }
}