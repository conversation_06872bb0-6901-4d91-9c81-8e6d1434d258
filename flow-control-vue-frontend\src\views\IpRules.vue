<template>
	<div class="ip-rules">
		<layout>
			<div class="ip-rules-content">
				<div class="page-header">
					<h1>IP流量规则管理</h1>
					<p>
						IP级限流独立于租户，直接基于客户端IP地址进行流量控制。支持单个IP、IP范围和CIDR格式的配置。
					</p>
					<div class="header-actions">
						<el-button
							type="danger"
							:disabled="!hasSelectedRules"
							@click="handleBatchDelete"
						>
							批量删除 ({{ selectedRulesCount }})
						</el-button>

						<el-button type="warning" @click="showImportDialog"
							>批量导入</el-button
						>
						<el-button type="info" @click="handleExport"
							>导出规则</el-button
						>
						<el-button
							type="primary"
							icon="el-icon-plus"
							@click="showCreateDialog"
						>
							新增规则
						</el-button>

					</div>
				</div>

				<!-- 搜索和筛选区域 -->
				<div class="search-filters">
					<el-row :gutter="20">
						<el-col :span="6">
							<el-input
								v-model="searchKeyword"
								placeholder="搜索IP地址或描述"
								prefix-icon="el-icon-search"
								clearable
								@input="handleSearch"
							/>
						</el-col>
						<el-col :span="4">
							<el-select
								v-model="filters.ruleType"
								placeholder="规则类型"
								clearable
								@change="handleFilterChange"
							>
								<el-option label="白名单" value="whitelist" />
								<el-option label="黑名单" value="blacklist" />
								<el-option label="限流" value="ratelimit" />
							</el-select>
						</el-col>
						<el-col :span="4">
							<el-select
								v-model="filters.status"
								placeholder="状态筛选"
								clearable
								@change="handleFilterChange"
							>
								<el-option label="启用" value="enabled" />
								<el-option label="禁用" value="disabled" />
							</el-select>
						</el-col>
						<el-col :span="4">
							<el-select
								v-model="filters.ipType"
								placeholder="IP类型"
								clearable
								@change="handleFilterChange"
							>
								<el-option label="单个IP" value="single" />
								<el-option label="IP段" value="range" />
								<el-option label="CIDR" value="cidr" />
							</el-select>
						</el-col>
						<el-col :span="6">
							<div class="stats-info">
								<span>白名单: {{ whitelistRulesCount }}</span>
								<span>黑名单: {{ blacklistRulesCount }}</span>
								<span>限流: {{ ratelimitRulesCount }}</span>
								<span>总计: {{ pagination.total }}</span>
							</div>
						</el-col>
					</el-row>
				</div>

				<!-- 规则列表 -->
				<div class="rules-table">
					<el-table
						:data="paginatedRules"
						:loading="isLoading"
						style="width: 100%"
						stripe
						@selection-change="handleSelectionChange"
					>
						<el-table-column
							type="selection"
							width="55"
						></el-table-column>
						<el-table-column
							prop="id"
							label="ID"
							width="80"
						></el-table-column>
						<el-table-column
							prop="name"
							label="规则名称"
							min-width="150"
						></el-table-column>
						<el-table-column
							prop="ipPattern"
							label="IP模式"
							min-width="150"
							show-overflow-tooltip
						>
							<template slot-scope="scope">
								<div class="ip-pattern-cell">
									<span class="ip-text">{{
										scope.row.ipPattern
									}}</span>
									<el-tag size="mini" class="ip-type-tag">{{
										getIpType(scope.row.ipPattern)
									}}</el-tag>
								</div>
							</template>
						</el-table-column>
						<el-table-column
							prop="type"
							label="规则类型"
							width="110"
						>
							<template slot-scope="scope">
								<el-tag
									size="mini"
									:type="getRuleTypeColor(scope.row.type)"
								>
									{{ getRuleTypeName(scope.row.type) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column
							prop="qpsLimit"
							label="QPS限制"
							width="100"
						>
							<template slot-scope="scope">
								<span
									v-if="scope.row.type === 'ratelimit'"
									class="qps-value"
								>
									{{ scope.row.qpsLimit || '-' }}
								</span>
								<span v-else>-</span>
							</template>
						</el-table-column>
						<el-table-column
							prop="priority"
							label="优先级"
							width="80"
						>
							<template slot-scope="scope">
								<el-tag
									size="mini"
									:type="getPriorityColor(scope.row.priority)"
								>
									{{ scope.row.priority }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column label="归属地" min-width="120">
							<template slot-scope="scope">
								<div
									class="location-cell"
									v-if="isValidSingleIp(scope.row.ipPattern)"
								>
									<el-button
										size="mini"
										type="text"
										@click="
											loadIpLocation(scope.row.ipPattern)
										"
										:loading="
											locationLoading[scope.row.ipPattern]
										"
									>
										{{
											getLocationText(scope.row.ipPattern)
										}}
									</el-button>
								</div>
								<span v-else class="location-na">-</span>
							</template>
						</el-table-column>
						<el-table-column prop="enabled" label="状态" width="80">
							<template slot-scope="scope">
								<el-switch
									v-model="scope.row.enabled"
									@change="toggleRule(scope.row)"
									active-color="#13ce66"
									inactive-color="#ff4949"
								>
								</el-switch>
							</template>
						</el-table-column>
						<el-table-column
							prop="createTime"
							label="创建时间"
							min-width="160"
							show-overflow-tooltip
						>
							<template slot-scope="scope">
								{{ formatTime(scope.row.createTime) }}
							</template>
						</el-table-column>
						<el-table-column label="操作" width="200" fixed="right">
							<template slot-scope="scope">
								<el-button
									size="mini"
									icon="el-icon-edit"
									@click="editRule(scope.row)"
									>编辑</el-button
								>
								<el-button
									size="mini"
									type="danger"
									icon="el-icon-delete"
									@click="deleteRule(scope.row)"
									>删除</el-button
								>
							</template>
						</el-table-column>
					</el-table>

					<!-- 分页 -->
					<div class="pagination-wrapper">
						<el-pagination
							@size-change="handleSizeChange"
							@current-change="handleCurrentChange"
							:current-page="pagination.currentPage"
							:page-sizes="[10, 20, 50, 100]"
							:page-size="pagination.pageSize"
							layout="total, sizes, prev, pager, next, jumper"
							:total="filteredRules.length"
							:prev-text="$t('pagination.prevPage')"
							:next-text="$t('pagination.nextPage')"
						>
						</el-pagination>
					</div>
				</div>

				<!-- 创建/编辑对话框 -->
				<el-dialog
					:title="dialogTitle"
					:visible.sync="dialogVisible"
					width="600px"
					@close="resetForm"
				>
					<el-form
						ref="ruleForm"
						:model="ruleForm"
						:rules="computedFormRules"
						label-width="120px"
					>
						<el-form-item label="规则名称" prop="ruleName">
							<el-input
								v-model="ruleForm.ruleName"
								placeholder="请输入规则名称"
							></el-input>
						</el-form-item>
						<el-form-item label="IP模式" prop="ipValue">
							<el-input
								v-model="ruleForm.ipValue"
								placeholder="如：192.168.1.*, 10.0.0.0/24"
							>
								<template slot="append">
									<el-button @click="showIpHelp"
										>帮助</el-button
									>
								</template>
							</el-input>
						</el-form-item>
						<el-form-item label="规则类型" prop="listType">
							<el-select
								v-model="ruleForm.listType"
								placeholder="请选择规则类型"
								@change="onTypeChange"
							>
								<el-option
									label="白名单"
									value="WHITELIST"
								></el-option>
								<el-option
									label="黑名单"
									value="BLACKLIST"
								></el-option>
								<el-option
									label="限流"
									value="LIMIT"
								></el-option>
							</el-select>
						</el-form-item>

						<el-form-item
							v-if="ruleForm.listType === 'LIMIT'"
							label="QPS限制"
							prop="qpsLimit"
						>
							<el-input-number
								v-model="ruleForm.qpsLimit"
								:min="1"
								:max="10000"
							></el-input-number>
						</el-form-item>
						<el-form-item label="优先级" prop="priority">
							<el-input-number
								v-model="ruleForm.priority"
								:min="1"
								:max="100"
							></el-input-number>
							<div class="form-tip">数值越小优先级越高</div>
						</el-form-item>
						<el-form-item label="描述">
							<el-input
								v-model="ruleForm.description"
								type="textarea"
								:rows="3"
								placeholder="请输入规则描述"
							></el-input>
						</el-form-item>
						<el-form-item label="启用状态">
							<el-switch
								v-model="ruleForm.status"
								:active-value="1"
								:inactive-value="0"
							>
							</el-switch>
						</el-form-item>
					</el-form>
					<div slot="footer" class="dialog-footer">
						<el-button @click="dialogVisible = false"
							>取消</el-button
						>
						<el-button type="primary" @click="saveRule"
							>确定</el-button
						>
					</div>
				</el-dialog>

				<!-- IP模式帮助对话框 -->
				<el-dialog
					title="IP模式帮助"
					:visible.sync="helpDialogVisible"
					width="500px"
				>
					<div class="help-content">
						<h4>支持的IP模式：</h4>
						<ul>
							<li><strong>单个IP：</strong> *************</li>
							<li>
								<strong>IP范围：</strong>
								***********-*************
							</li>
							<li><strong>CIDR：</strong> ***********/24</li>
							<li><strong>通配符：</strong> 192.168.1.*</li>
						</ul>
						<h4>示例：</h4>
						<ul>
							<li><strong>单个IP：</strong> *************</li>
							<li>
								<strong>IP范围：</strong>
								***********-*************
							</li>
							<li><strong>内网段：</strong> ***********/16</li>
							<li><strong>子网：</strong> ********/24</li>
							<li>
								<strong>通配符：</strong> 192.168.1.*, 10.0.*.*,
								*.*.1.100
							</li>
						</ul>
						<h4>注意：</h4>
						<ul>
							<li>每个规则只能包含一种IP模式</li>
							<li>系统会根据输入自动识别IP类型</li>
							<li>IP范围格式：起始IP-结束IP</li>
							<li>CIDR格式：网络地址/子网掩码位数</li>
							<li>通配符格式：使用 * 代替任意数字段</li>
						</ul>
					</div>
				</el-dialog>

				<!-- 导入对话框 -->
				<el-dialog
					title="批量导入IP规则"
					:visible.sync="importDialogVisible"
					width="600px"
					@close="resetImportDialog"
				>
					<div class="import-content">
						<div class="import-tips">
							<el-alert
								title="导入说明"
								type="info"
								:closable="false"
								show-icon
							>
								<div slot="description">
									<p>1. 支持CSV和JSON格式文件</p>
									<p>
										2.
										CSV格式：IP模式,规则类型,QPS限制,优先级,描述
									</p>
									<p>
										3.
										规则类型：whitelist(白名单)、blacklist(黑名单)、ratelimit(限流)
									</p>
									<p>4. 单次最多导入1000条规则</p>
								</div>
							</el-alert>
						</div>

						<div class="file-upload">
							<el-upload
								ref="upload"
								:auto-upload="false"
								:on-change="handleFileChange"
								:before-remove="handleFileRemove"
								accept=".csv,.json"
								drag
							>
								<i class="el-icon-upload"></i>
								<div class="el-upload__text">
									将文件拖到此处，或<em>点击上传</em>
								</div>
								<div class="el-upload__tip" slot="tip">
									只能上传csv/json文件，且不超过2MB
								</div>
							</el-upload>
						</div>

						<div
							v-if="importPreview.length > 0"
							class="import-preview"
						>
							<h4>预览数据 (前5条)</h4>
							<el-table
								:data="importPreview.slice(0, 5)"
								size="mini"
							>
								<el-table-column
									prop="ipPattern"
									label="IP模式"
									width="150"
								></el-table-column>
								<el-table-column
									prop="ruleType"
									label="规则类型"
									width="100"
								></el-table-column>
								<el-table-column
									prop="qpsLimit"
									label="QPS限制"
									width="100"
								></el-table-column>
								<el-table-column
									prop="priority"
									label="优先级"
									width="80"
								></el-table-column>
								<el-table-column
									prop="description"
									label="描述"
									show-overflow-tooltip
								></el-table-column>
							</el-table>
							<p class="preview-info">
								共 {{ importPreview.length }} 条数据
							</p>
						</div>
					</div>

					<div slot="footer" class="dialog-footer">
						<el-button @click="importDialogVisible = false"
							>取消</el-button
						>
						<el-button
							type="primary"
							@click="handleImport"
							:loading="importLoading"
							:disabled="importPreview.length === 0"
						>
							确认导入
						</el-button>
					</div>
				</el-dialog>
			</div>
		</layout>
	</div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import Layout from '../components/Layout.vue';
import validationRules from '@/utils/validation';

export default {
	name: 'IpRules',
	components: {
		Layout,
	},
	beforeCreate() {
		// 注册验证规则到Vue实例
		this.$validation = validationRules;
	},
	data() {
		return {
			dialogVisible: false,
			helpDialogVisible: false,
			importDialogVisible: false,
			isEdit: false,
			searchKeyword: '',
			selectedRules: [],
			publishLoading: false,
			filters: {
				ruleType: '',
				status: '',
				ipType: '',
			},
			pagination: {
				total: 0,
				currentPage: 1,
				pageSize: 10,
			},
			locationLoading: {},
			importPreview: [],
			importFile: null,
			importLoading: false,
			ruleForm: {
				id: null,
				ruleName: '',
				tenantId: 'default', // 默认租户ID
				ipValue: '',
				ruleType: 'SINGLE_IP',
				listType: 'WHITELIST',
				qpsLimit: null,
				priority: 10,
				description: '',
				status: 1,
				createTime: null,
			}
		};
	},
	computed: {
		// 动态计算表单验证规则
		computedFormRules() {
			const rules = {
				tenantId: this.$validation.ipFlowRuleRules.tenantId,
				ruleName: this.$validation.ipFlowRuleRules.ruleName,
				ruleType: this.$validation.ipFlowRuleRules.ruleType,
				ipValue: [
					{ required: true, message: 'IP值不能为空', trigger: 'blur' },
					{ max: 200, message: 'IP值长度不能超过200个字符', trigger: 'blur' },
					{ validator: this.validateIpPattern, trigger: 'blur' }
				],
				qpsLimit: this.$validation.ipFlowRuleRules.qpsLimit,
				priority: this.$validation.ipFlowRuleRules.priority,
				description: this.$validation.ipFlowRuleRules.description,
				status: this.$validation.ipFlowRuleRules.status,
				flowBehavior: this.$validation.ipFlowRuleRules.flowBehavior,
				warmUpPeriod: this.$validation.ipFlowRuleRules.warmUpPeriod,
				queueTimeout: this.$validation.ipFlowRuleRules.queueTimeout
			};
			
			// 根据规则类型动态调整验证规则
			if (this.ruleForm.listType === 'LIMIT') {
				// 限流模式：QPS限制必填
				rules.qpsLimit = [
					{ required: true, message: '请输入QPS限制', trigger: 'blur' },
					...this.$validation.ipFlowRuleRules.qpsLimit.slice(1)
				];
			}
			
			return rules;
		},
		...mapGetters('ipFlowRules', [
			'allRules',
			'loading',
			'importLoading',
			'exportLoading',
			'searchKeyword',
			'filters',
			'pagination',
			'selectedRuleIds',
			'selectedRulesCount',
			'hasSelectedRules',
			'ipLocationCache',
			'filteredRules',
			'whitelistRulesCount',
			'blacklistRulesCount',
			'ratelimitRulesCount',
			'enabledRulesCount',
			'disabledRulesCount',
			'getIpLocationFromCache',
		]),
		paginatedRules() {
			const start =
				(this.pagination.currentPage - 1) * this.pagination.pageSize;
			const end = start + this.pagination.pageSize;
			return this.filteredRules.slice(start, end);
		},
		totalRulesCount() {
			return this.allRules.length;
		},
		dialogTitle() {
			return this.isEdit ? '编辑IP规则' : '新增IP规则';
		},
	},
	mounted() {
		this.fetchRules();
	},
	methods: {
		...mapActions('ipFlowRules', [
			'fetchRules',
			'createRule',
			'updateRule',
			'deleteRule',
		]),
		showCreateDialog() {
			this.isEdit = false;
			this.dialogVisible = true;
		},
		editRule(rule) {
			this.isEdit = true;
			// 将前端显示格式转换回表单格式
			this.ruleForm = {
				id: rule.id,
				ruleName: rule.name,
				tenantId: rule.tenantId || 'default',
				ipValue: rule.ipPattern,
				ruleType: rule.ruleType || 'SINGLE_IP',
				listType:
					rule.type === 'blacklist'
						? 'BLACKLIST'
						: rule.type === 'whitelist'
						? 'WHITELIST'
						: 'LIMIT',
				qpsLimit: rule.qpsLimit,
				priority: rule.priority,
				description: rule.description,
				status: rule.status !== undefined ? rule.status : 1,
				createTime: rule.createTime,
			};
			this.dialogVisible = true;
		},
		async saveRule() {
			this.$refs.ruleForm.validate(async (valid) => {
				if (valid) {
					// 确保ruleType字段正确设置
					if (!this.ruleForm.ruleType && this.ruleForm.ipValue) {
						const validationResult = this.validateAndSetRuleType(this.ruleForm.ipValue);
						if (validationResult.valid) {
							this.ruleForm.ruleType = validationResult.ruleType;
						} else {
							this.$message.error('IP格式验证失败: ' + validationResult.message);
							return;
						}
					}
					
					try {
						if (this.isEdit) {
							await this.updateRule(this.ruleForm);
							this.$message.success('更新规则成功');
						} else {
							const newRule = {
								...this.ruleForm,
								id: Date.now(),
								createTime: Date.now(),
							};
							await this.createRule(newRule);
							this.$message.success('创建规则成功');
						}
						this.dialogVisible = false;
						this.fetchRules();
					} catch (error) {
						console.error('保存规则失败:', error);
						this.$message.error('操作失败: ' + (error.message || error));
					}
				}
			});
		},
		async deleteRule(rule) {
			try {
				await this.$confirm('确定要删除这条规则吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				});
				await this.deleteRule(rule.id);
				this.$message.success('删除成功');
				this.fetchRules();
			} catch (error) {
				if (error !== 'cancel') {
					this.$message.error('删除失败');
				}
			}
		},

		// 搜索和筛选
		handleSearch() {
			this.$store.dispatch(
				'ipFlowRules/setSearchKeyword',
				this.searchKeyword
			);
			this.pagination.currentPage = 1;
		},

		handleFilterChange() {
			this.$store.dispatch('ipFlowRules/setFilters', this.filters);
			this.pagination.currentPage = 1;
		},

		clearFilters() {
			this.searchKeyword = '';
			this.filters = {
				ruleType: '',
				status: '',
				ipType: '',
			};
			this.$store.dispatch('ipFlowRules/setSearchKeyword', '');
			this.$store.dispatch('ipFlowRules/setFilters', this.filters);
			this.pagination.currentPage = 1;
		},

		// 分页
		handleSizeChange(val) {
			this.pagination.pageSize = val;
			this.pagination.currentPage = 1;
			this.$store.dispatch('ipFlowRules/setPagination', this.pagination);
		},

		handleCurrentChange(val) {
			this.pagination.currentPage = val;
			this.$store.dispatch('ipFlowRules/setPagination', this.pagination);
		},

		// 批量操作
		handleSelectionChange(selection) {
			this.selectedRules = selection;
			const selectedIds = selection.map((rule) => rule.id);
			this.$store.dispatch('ipFlowRules/setSelectedRuleIds', selectedIds);
		},

		async handleBatchDelete() {
			if (this.selectedRules.length === 0) {
				this.$message.warning('请选择要删除的规则');
				return;
			}

			try {
				await this.$confirm(
					`确定要删除选中的 ${this.selectedRules.length} 条规则吗？`,
					'批量删除',
					{
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning',
					}
				);

				const ruleIds = this.selectedRules.map((rule) => rule.id);
				await this.$store.dispatch('ipFlowRules/batchDeleteRules', ruleIds);
				this.$message.success(`成功删除 ${ruleIds.length} 条规则`);
				this.selectedRules = [];
			} catch (error) {
				if (error !== 'cancel') {
					this.$message.error('批量删除失败');
				}
			}
		},

		// 导入导出
		showImportDialog() {
			this.importDialogVisible = true;
		},

		async handleExport() {
			try {
				await this.$store.dispatch('ipFlowRules/exportRules');
				this.$message.success('导出成功');
			} catch (error) {
				this.$message.error('导出失败');
			}
		},

		handleFileChange(file) {
			this.importFile = file.raw;
			this.parseImportFile(file.raw);
		},

		handleFileRemove() {
			this.importFile = null;
			this.importPreview = [];
		},

		parseImportFile(file) {
			const reader = new FileReader();
			reader.onload = (e) => {
				try {
					const content = e.target.result;
					let data = [];

					if (file.name.endsWith('.json')) {
						data = JSON.parse(content);
					} else if (file.name.endsWith('.csv')) {
						const lines = content
							.split('\n')
							.filter((line) => line.trim());
						const headers = lines[0].split(',');

						for (let i = 1; i < lines.length; i++) {
							const values = lines[i].split(',');
							if (values.length >= 5) {
								data.push({
									ipPattern: values[0].trim(),
									ruleType: values[1].trim(),
									qpsLimit: values[2].trim()
										? parseInt(values[2].trim())
										: null,
									priority: values[3].trim()
										? parseInt(values[3].trim())
										: 1,
									description: values[4].trim(),
								});
							}
						}
					}

					this.importPreview = data.slice(0, 1000); // 限制最多1000条
				} catch (error) {
					this.$message.error('文件格式错误，请检查文件内容');
					this.importPreview = [];
				}
			};
			reader.readAsText(file);
		},

		async handleImport() {
			if (this.importPreview.length === 0) {
				this.$message.warning('没有可导入的数据');
				return;
			}

			try {
				await this.$store.dispatch(
					'ipFlowRules/importRules',
					this.importPreview
				);
				this.$message.success(
					`成功导入 ${this.importPreview.length} 条规则`
				);
				this.importDialogVisible = false;
				this.resetImportDialog();
			} catch (error) {
				this.$message.error('导入失败');
			}
		},

		resetImportDialog() {
			this.importFile = null;
			this.importPreview = [];
			if (this.$refs.upload) {
				this.$refs.upload.clearFiles();
			}
		},

		// IP归属地
		async loadIpLocation(ipPattern) {
			if (this.locationLoading[ipPattern]) return;

			const cachedLocation = this.getIpLocationFromCache(ipPattern);
			if (cachedLocation) {
				this.$message.info(`IP归属地: ${cachedLocation}`);
				return;
			}

			this.$set(this.locationLoading, ipPattern, true);

			try {
				const location = await this.$store.dispatch(
					'ipFlowRules/getIpLocation',
					ipPattern
				);
				this.$message.info(`IP归属地: ${location}`);
			} catch (error) {
				this.$message.error('获取IP归属地失败');
			} finally {
				this.$set(this.locationLoading, ipPattern, false);
			}
		},

		getLocationText(ipPattern) {
			const cached = this.getIpLocationFromCache(ipPattern);
			return cached || '查询归属地';
		},

		isValidSingleIp(ipPattern) {
			const ipRegex = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
			return ipRegex.test(ipPattern);
		},

		getIpType(ipPattern) {
			if (ipPattern.includes('/')) {
				return 'CIDR';
			} else if (ipPattern.includes('*')) {
				return '通配符';
			} else if (ipPattern.includes('-')) {
				return 'IP段';
			} else {
				return '单IP';
			}
		},

		// 状态切换
		async handleStatusChange(rule) {
			try {
				if (rule.enabled) {
					await this.$store.dispatch('ipFlowRules/enableRule', rule.id);
					this.$message.success('规则已启用');
				} else {
					await this.$store.dispatch('ipFlowRules/disableRule', rule.id);
					this.$message.success('规则已禁用');
				}
			} catch (error) {
				// 恢复开关状态
				rule.enabled = !rule.enabled;
				this.$message.error('状态切换失败');
			}
		},
		resetForm() {
			this.ruleForm = {
				id: null,
				ruleName: '',
				tenantId: 'default', // 确保重置时也有默认租户ID
				ipValue: '',
				ruleType: 'SINGLE_IP',
				listType: 'WHITELIST',
				qpsLimit: null,
				priority: 10,
				description: '',
				status: 1,
				createTime: null,
			};
			if (this.$refs.ruleForm) {
				this.$refs.ruleForm.resetFields();
			}
		},
		onTypeChange(type) {
			if (type !== 'LIMIT') {
				this.ruleForm.qpsLimit = null;
			}
		},
		showIpHelp() {
			this.helpDialogVisible = true;
		},
		validateIpPattern(rule, value, callback) {
			if (!value) {
				callback(new Error('请输入IP模式'));
				return;
			}

			// 验证IP格式并自动设置ruleType
			const validationResult = this.validateAndSetRuleType(value);
			if (!validationResult.valid) {
				callback(new Error(validationResult.message));
				return;
			}

			// 自动设置ruleType
			this.ruleForm.ruleType = validationResult.ruleType;
			callback();
		},

		validateAndSetRuleType(ipValue) {
			if (!ipValue || !ipValue.trim()) {
				return {
					valid: false,
					message: '请输入IP值',
					ruleType: null,
				};
			}

			const value = ipValue.trim();

			// 单个IP地址验证 (*************)
			const singleIpRegex =
				/^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;

			// CIDR格式验证 (***********/24)
			const cidrRegex =
				/^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)\/([0-9]|[1-2][0-9]|3[0-2])$/;

			// IP范围验证 (***********-*************)
			const rangeRegex =
				/^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)-((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;

			// 通配符验证 (192.168.1.*, 10.0.*.*, *.*.*.*)
			const wildcardRegex =
				/^((25[0-5]|2[0-4]\d|[01]?\d\d?|\*)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?|\*)$/;

			// 检查单个IP
			if (singleIpRegex.test(value)) {
				return {
					valid: true,
					message: '',
					ruleType: 'SINGLE_IP',
				};
			}

			// 检查CIDR格式
			if (cidrRegex.test(value)) {
				const parts = value.split('/');
				const maskBits = parseInt(parts[1]);
				if (maskBits >= 0 && maskBits <= 32) {
					return {
						valid: true,
						message: '',
						ruleType: 'CIDR',
					};
				}
			}

			// 检查IP范围格式
			if (rangeRegex.test(value)) {
				const [startIp, endIp] = value.split('-');
				if (
					this.isValidIpAddress(startIp) &&
					this.isValidIpAddress(endIp)
				) {
					return {
						valid: true,
						message: '',
						ruleType: 'IP_RANGE',
					};
				}
			}

			// 检查通配符格式
			if (wildcardRegex.test(value)) {
				return {
					valid: true,
					message: '',
					ruleType: 'WILDCARD',
				};
			}

			return {
				valid: false,
				message:
					'IP格式不正确。支持的格式：单个IP(*************)、IP范围(***********-*************)、CIDR(***********/24)、通配符(192.168.1.*)',
				ruleType: null,
			};
		},

		isValidIpAddress(ip) {
			const parts = ip.split('.');
			if (parts.length !== 4) return false;

			for (const part of parts) {
				const num = parseInt(part);
				if (isNaN(num) || num < 0 || num > 255) return false;
			}
			return true;
		},
		getRuleTypeColor(type) {
			const colors = {
				whitelist: 'success',
				blacklist: 'danger',
				ratelimit: 'warning',
			};
			return colors[type] || 'info';
		},
		getRuleTypeName(type) {
			const names = {
				whitelist: '白名单',
				blacklist: '黑名单',
				ratelimit: '限流',
			};
			return names[type] || '未知';
		},
		getPriorityColor(priority) {
			if (priority <= 3) return 'danger';
			if (priority <= 6) return 'warning';
			return 'info';
		},
		formatTime(timestamp) {
			if (!timestamp) return '-';
			return new Date(timestamp).toLocaleString();
		},
		isValidIpPattern(pattern) {
			// 简单的IP模式验证逻辑
			const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
			const wildcardRegex = /^(\d{1,3}|\*)(\.(\d{1,3}|\*))*$/;
			const cidrRegex = /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/;
			const rangeRegex = /^(\d{1,3}\.){3}\d{1,3}-(\d{1,3}\.){3}\d{1,3}$/;

			return (
				ipRegex.test(pattern) ||
				wildcardRegex.test(pattern) ||
				cidrRegex.test(pattern) ||
				rangeRegex.test(pattern)
			);
		},
	},

	watch: {
		searchKeyword: {
			handler() {
				this.handleSearch();
			},
			immediate: false,
		},

		'filters.ruleType'() {
			this.handleFilterChange();
		},

		'filters.status'() {
			this.handleFilterChange();
		},

		'filters.ipType'() {
			this.handleFilterChange();
		},
	},
};
</script>

<style scoped>
.ip-rules-content {
	padding: 20px;
	max-width: 100%;
	width: 100%;
}

.page-header {
	margin-bottom: 30px;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}

.page-header h1 {
	color: #333;
	margin-bottom: 5px;
}

.page-header p {
	color: #666;
	font-size: 14px;
}

.header-actions {
	display: flex;
	gap: 10px;
	flex-wrap: wrap;
}

.search-filters {
	margin-bottom: 20px;
	padding: 15px;
	background: #f8f9fa;
	border-radius: 6px;
}

.stats-info {
	display: flex;
	gap: 15px;
	align-items: center;
	font-size: 14px;
	color: #666;
}

.stats-info span {
	padding: 4px 8px;
	background: white;
	border-radius: 4px;
	border: 1px solid #e4e7ed;
}

.rules-table {
	background: white;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	overflow: hidden;
}

.ip-pattern-cell {
	display: flex;
	align-items: center;
	gap: 8px;
}

.ip-text {
	font-family: monospace;
	font-size: 13px;
}

.ip-type-tag {
	font-size: 11px;
}

.qps-value {
	font-weight: bold;
	color: #e6a23c;
}

.location-cell {
	text-align: center;
}

.location-na {
	color: #c0c4cc;
}

.pagination-wrapper {
	margin-top: 20px;
	text-align: right;
}

.dialog-footer {
	text-align: right;
}

.form-tip {
	font-size: 12px;
	color: #999;
	margin-top: 5px;
}

.help-content h4 {
	color: #333;
	margin: 15px 0 10px 0;
}

.help-content ul {
	margin: 10px 0;
	padding-left: 20px;
}

.help-content li {
	margin: 5px 0;
	line-height: 1.5;
}

/* 导入对话框样式 */
.import-content {
	max-height: 500px;
	overflow-y: auto;
}

.import-tips {
	margin-bottom: 20px;
}

.file-upload {
	margin: 20px 0;
}

.import-preview {
	margin-top: 20px;
}

.import-preview h4 {
	margin-bottom: 10px;
	color: #409eff;
}

.preview-info {
	margin-top: 10px;
	color: #666;
	font-size: 14px;
	text-align: center;
}

/* 表格样式增强 */
.el-table .cell {
	padding: 0 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.page-header {
		flex-direction: column;
		gap: 15px;
	}

	.header-actions {
		width: 100%;
		justify-content: center;
	}

	.stats-info {
		flex-direction: column;
		gap: 8px;
	}

	.stats-info span {
		text-align: center;
	}
}
</style>
