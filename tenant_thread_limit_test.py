#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
租户线程数限流测试脚本

该脚本用于测试租户级别的线程数限流功能，支持以下控制行为：
- 快速失败（CONTROL_BEHAVIOR_DEFAULT）
- 预热启动（CONTROL_BEHAVIOR_WARM_UP）
- 排队等待（CONTROL_BEHAVIOR_RATE_LIMITER）
- 预热+排队（CONTROL_BEHAVIOR_WARM_UP_RATE_LIMITER）

通过模拟长时间运行的请求来占用线程，测试不同限流策略的效果。

注意：为了有效测试线程数限流，需要使用足够高的并发数（默认50个线程）。
这是因为线程数限流是基于同时占用的线程数量，只有当并发请求数超过
限制阈值时，才能观察到限流效果。
"""

import requests
import threading
import time
import json
import argparse
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
import statistics
import mysql.connector
from mysql.connector import Error


class TenantThreadLimitTester:
    def __init__(self, base_url="http://localhost:8088", request_duration=2.0, db_config=None):
        """
        初始化线程数限流测试器
        
        Args:
            base_url: 测试目标URL
            request_duration: 单个请求持续时间（秒），用于占用线程
            db_config: 数据库连接配置
        """
        self.base_url = base_url
        self.request_duration = request_duration
        self.results = defaultdict(list)
        self.lock = threading.Lock()
        self.db_config = db_config or {
            'host': 'localhost',
            'port': 33016,
            'user': 'root',
            'password': 'aids520a!',
            'database': 'openplatform'
        }
        
    def send_long_request(self, tenant_id, request_id):
        """
        发送长时间运行的请求来占用线程
        
        Args:
            tenant_id: 租户ID
            request_id: 请求ID
            
        Returns:
            dict: 请求结果信息
        """
        start_time = time.time()
        
        try:
            # 模拟长时间运行的请求
            url = f"{self.base_url}/api/test"
            headers = {
                'X-Tenant-ID': tenant_id,
                'Content-Type': 'application/json'
            }
            
            # 添加sleep参数来模拟长时间请求
            params = {'sleep': self.request_duration}
            
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            result = {
                'tenant_id': tenant_id,
                'request_id': request_id,
                'status_code': response.status_code,
                'response_time': response_time,
                'success': response.status_code == 200,
                'timestamp': start_time,
                'thread_id': threading.current_thread().ident
            }
            
            # 检查是否被限流
            if response.status_code == 429:
                result['blocked'] = True
                result['reason'] = 'Thread limit exceeded'
            elif response.status_code >= 500:
                result['blocked'] = True
                result['reason'] = 'Server error (possible thread exhaustion)'
            else:
                result['blocked'] = False
                
        except requests.exceptions.Timeout:
            end_time = time.time()
            result = {
                'tenant_id': tenant_id,
                'request_id': request_id,
                'status_code': 408,
                'response_time': end_time - start_time,
                'success': False,
                'blocked': True,
                'reason': 'Request timeout',
                'timestamp': start_time,
                'thread_id': threading.current_thread().ident
            }
        except Exception as e:
            end_time = time.time()
            result = {
                'tenant_id': tenant_id,
                'request_id': request_id,
                'status_code': 0,
                'response_time': end_time - start_time,
                'success': False,
                'blocked': True,
                'reason': f'Request failed: {str(e)}',
                'timestamp': start_time,
                'thread_id': threading.current_thread().ident
            }
            
        with self.lock:
            self.results[tenant_id].append(result)
            
        return result
    
    def calculate_expected_success_threads(self, thread_limit, control_behavior, concurrent_threads):
        """
        根据控制行为计算预期成功的线程数
        
        Args:
            thread_limit: 线程数限制
            control_behavior: 控制行为 (0=快速失败, 1=预热启动, 2=排队等待, 3=预热+排队)
            concurrent_threads: 并发线程数
            
        Returns:
            int: 预期成功的线程数
        """
        if control_behavior == 0:  # 快速失败
            # 快速失败模式：超过限制的请求立即被拒绝
            return min(thread_limit, concurrent_threads)
        elif control_behavior == 1:  # 预热启动
            # 预热模式：初期限制较严，逐渐放宽到设定值
            # 在预热期间，实际通过的线程数可能略少于限制值
            return max(1, min(thread_limit, concurrent_threads))
        elif control_behavior == 2:  # 排队等待
            # 排队模式：超过限制的请求会排队等待，而不是立即失败
            # 所有请求最终都可能成功，但会有延迟
            return concurrent_threads
        elif control_behavior == 3:  # 预热+排队
            # 组合模式：预热期间排队等待
            return concurrent_threads
        else:
            # 未知控制行为，使用保守估计
            return min(thread_limit, concurrent_threads)
    
    def test_tenant_thread_limit(self, tenant, thread_limit, control_behavior, concurrent_threads=10, test_duration=30):
        """
        测试单个租户的线程数限流规则
        
        Args:
            tenant: 租户ID
            thread_limit: 线程数限制
            control_behavior: 控制行为
            concurrent_threads: 并发线程数
            test_duration: 测试持续时间（秒）
            
        Returns:
            dict: 测试结果统计
        """
        tenant_id = tenant
        print(f"\n开始测试租户 {tenant_id} 的线程数限流规则...")
        print(f"规则配置: 线程数限制={thread_limit}, 控制行为={control_behavior}, 并发线程={concurrent_threads}")
        
        # 清空之前的结果
        self.results[tenant_id] = []
        
        start_time = time.time()
        
        # 使用线程池执行并发请求
        with ThreadPoolExecutor(max_workers=concurrent_threads) as executor:
            futures = []
            
            # 提交所有并发请求
            for i in range(concurrent_threads):
                future = executor.submit(self.send_long_request, tenant_id, i)
                futures.append(future)
                # 稍微错开请求启动时间
                time.sleep(0.1)
            
            # 等待所有请求完成或超时
            completed_futures = []
            for future in as_completed(futures, timeout=test_duration + 10):
                try:
                    result = future.result()
                    completed_futures.append(result)
                except Exception as e:
                    print(f"请求执行异常: {e}")
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        # 统计结果
        results = self.results[tenant_id]
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r['success'])
        blocked_requests = sum(1 for r in results if r['blocked'])
        
        # 计算响应时间统计
        response_times = [r['response_time'] for r in results if r['success']]
        avg_response_time = statistics.mean(response_times) if response_times else 0
        
        # 计算预期成功线程数
        expected_success = self.calculate_expected_success_threads(thread_limit, control_behavior, concurrent_threads)
        
        # 分析线程使用情况
        unique_threads = set(r['thread_id'] for r in results if r['success'])
        actual_concurrent_threads = len(unique_threads)
        
        # 判断测试是否通过
        # 对于不同的控制行为，采用不同的判断标准
        success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
        if control_behavior == 0:  # 快速失败
            # 快速失败：成功数应该不超过线程限制，允许小幅波动
            test_passed = successful_requests <= thread_limit + 1 and successful_requests >= min(thread_limit - 1, 1)
        elif control_behavior == 1:  # 预热启动
            # 预热模式：成功数应该在合理范围内，考虑预热过程的影响
            test_passed = successful_requests <= thread_limit + 2 and successful_requests >= 1
        elif control_behavior == 2:  # 排队等待
            # 排队模式：所有请求最终都应该成功（或大部分成功）
            test_passed = success_rate >= 85  # 允许少量请求因超时等原因失败
        elif control_behavior == 3:  # 预热+排队
            # 组合模式：结合预热和排队的特点
            test_passed = success_rate >= 80
        else:
            # 未知控制行为，使用保守判断
            test_passed = successful_requests <= thread_limit + 2
        
        stats = {
            'tenant_id': tenant_id,
            'rule_name': f'{tenant_id}_thread_limit_rule',
            'thread_limit': thread_limit,
            'control_behavior': control_behavior,
            'control_behavior_name': self.get_control_behavior_name(control_behavior),
            'concurrent_threads': concurrent_threads,
            'test_duration': actual_duration,
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'blocked_requests': blocked_requests,
            'success_rate': successful_requests / total_requests if total_requests > 0 else 0,
            'expected_success_threads': expected_success,
            'actual_concurrent_threads': actual_concurrent_threads,
            'avg_response_time': avg_response_time,
            'thread_limit_effective': actual_concurrent_threads <= thread_limit,
            'test_passed': test_passed,
            'requests': results
        }
        
        print(f"测试完成: 总请求={total_requests}, 成功={successful_requests}, 被阻止={blocked_requests}")
        print(f"实际并发线程数={actual_concurrent_threads}, 线程限制={thread_limit}, 限流有效={stats['thread_limit_effective']}")
        
        return stats
    
    def get_control_behavior_name(self, behavior):
        """获取控制行为名称"""
        behavior_names = {
            0: "快速失败",
            1: "预热启动", 
            2: "排队等待",
            3: "预热+排队"
        }
        return behavior_names.get(behavior, f"未知({behavior})")
    
    def save_report(self, all_stats, output_file=None):
        """
        保存测试报告到文件
        
        Args:
            all_stats: 所有测试统计结果列表
            output_file: 输出文件名，如果为None则自动生成
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"tenant_thread_limit_test_report_{timestamp}.json"
        
        # 计算总体统计
        total_tests = len(all_stats)
        passed_tests = sum(1 for stats in all_stats if stats.get('test_passed', False))
        total_requests = sum(stats['total_requests'] for stats in all_stats)
        total_success = sum(stats['successful_requests'] for stats in all_stats)
        overall_success_rate = (total_success / total_requests * 100) if total_requests > 0 else 0
        
        report = {
            'test_info': {
                'test_type': '租户线程数限流测试',
                'timestamp': datetime.now().isoformat(),
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'pass_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'total_requests': total_requests,
                'total_success': total_success,
                'overall_success_rate': overall_success_rate
            },
            'test_results': all_stats
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\n测试报告已保存到: {output_file}")
        except Exception as e:
            print(f"保存报告时发生错误: {e}")
    
    def print_summary(self, all_stats):
        """
        打印所有租户的测试结果摘要
        
        Args:
             all_stats: 所有租户的测试统计结果
        """
        print("\n" + "="*80)
        print("租户线程数限流测试结果摘要")
        print("="*80)
        
        for stats in all_stats:
            print(f"\n租户: {stats['tenant_id']}")
            print(f"  线程限制: {stats['thread_limit']}")
            print(f"  控制行为: {stats['control_behavior_name']}")
            print(f"  并发线程: {stats['concurrent_threads']}")
            print(f"  总请求数: {stats['total_requests']}")
            print(f"  成功请求: {stats['successful_requests']} ({stats['success_rate']*100:.1f}%)")
            print(f"  被阻止请求: {stats['blocked_requests']} ({(stats['blocked_requests']/stats['total_requests']*100 if stats['total_requests'] > 0 else 0):.1f}%)")
            print(f"  平均响应时间: {stats['avg_response_time']:.2f}秒")
            print(f"  线程限流有效: {'是' if stats['thread_limit_effective'] else '否'}")
            print(f"  测试通过: {'是' if stats['test_passed'] else '否'}")
        
        # 总体统计
        total_requests = sum(s['total_requests'] for s in all_stats)
        total_successful = sum(s['successful_requests'] for s in all_stats)
        total_blocked = sum(s['blocked_requests'] for s in all_stats)
        overall_success_rate = (total_successful / total_requests * 100) if total_requests > 0 else 0
        passed_tests = sum(1 for s in all_stats if s['test_passed'])
        
        print(f"\n总体统计:")
        print(f"  测试租户数: {len(all_stats)}")
        print(f"  测试通过租户数: {passed_tests}")
        print(f"  总请求数: {total_requests}")
        print(f"  总成功数: {total_successful}")
        print(f"  总阻止数: {total_blocked}")
        print(f"  总体成功率: {overall_success_rate:.1f}%")
        
        print("\n" + "="*80)

    
    def get_tenant_rules_from_db(self):
        """
        从数据库查询租户限流规则
        
        Returns:
            list: 租户规则列表
        """
        rules = []
        connection = None
        
        try:
            connection = mysql.connector.connect(**self.db_config)
            cursor = connection.cursor(dictionary=True)
            
            # 查询启用的租户限流规则
            query = """
            SELECT id, tenant_id, rule_name, grade, count, control_behavior, 
                   warm_up_period_sec, max_queueing_time_ms, priority, enabled, description
            FROM tenant_flow_rules 
            WHERE enabled = 1 AND grade = 0
            ORDER BY tenant_id, priority
            """
            
            cursor.execute(query)
            db_rules = cursor.fetchall()
            
            for rule in db_rules:
                rule_data = {
                    'id': rule['id'],
                    'tenant_id': rule['tenant_id'],
                    'rule_name': rule['rule_name'],
                    'grade': rule['grade'],  # 0表示线程限流
                    'count': rule['count'],
                    'control_behavior': rule['control_behavior'],
                    'warm_up_period_sec': rule['warm_up_period_sec'],
                    'max_queueing_time_ms': rule['max_queueing_time_ms'],
                    'priority': rule['priority'],
                    'enabled': rule['enabled'],
                    'description': rule['description'] or f'{rule["tenant_id"]}的线程限流规则'
                }
                rules.append(rule_data)
                print(f"查询到规则: 租户={rule['tenant_id']}, grade={rule['grade']}, count={rule['count']}, control_behavior={rule['control_behavior']}")
            
            print(f"从数据库查询到 {len(rules)} 条限流规则")
            
            # 过滤出线程限流规则（grade=0）
            thread_rules = [rule for rule in rules if rule['grade'] == 0]
            qps_rules = [rule for rule in rules if rule['grade'] == 1]
            
            print(f"其中线程限流规则: {len(thread_rules)} 条")
            print(f"其中QPS限流规则: {len(qps_rules)} 条")
            
            if not thread_rules:
                print("警告: 数据库中没有找到线程限流规则（grade=0），将使用默认规则")
                return create_tenant_rules()
            
            return thread_rules
            
        except Error as e:
            print(f"数据库查询错误: {e}")
            print("使用默认规则进行测试")
            return create_tenant_rules()
            
        finally:
            if connection and connection.is_connected():
                cursor.close()
                connection.close()
        



def create_tenant_rules():
    """
    创建租户线程数限流规则（基于用户提供的SQL数据）
    
    Returns:
        list: 租户规则列表
    """
    rules = [
        {
            'id': 1,
            'tenant_id': 'tenant1',
            'rule_name': '租户1默认线程数限流',
            'grade': 0,  # 0表示线程数限流
            'count': 5,  # 线程数限制
            'control_behavior': 0,  # 快速失败
            'warm_up_period_sec': None,
            'max_queueing_time_ms': None,
            'priority': 1,
            'enabled': 1,
            'description': '租户1的默认线程数限流规则'
        },
        {
            'id': 2,
            'tenant_id': 'tenant2',
            'rule_name': '租户2预热限流',
            'grade': 0,  # 0表示线程数限流
            'count': 5,  # 线程数限制
            'control_behavior': 1,  # 预热启动
            'warm_up_period_sec': 10,  # 预热时间10秒
            'max_queueing_time_ms': None,
            'priority': 2,
            'enabled': 1,
            'description': '租户2的预热限流规则'
        },
        {
            'id': 3,
            'tenant_id': 'tenant3',
            'rule_name': '租户3排队限流',
            'grade': 0,  # 0表示线程数限流
            'count': 5,  # 线程数限制
            'control_behavior': 2,  # 排队等待
            'warm_up_period_sec': None,
            'max_queueing_time_ms': 5000,  # 最大排队时间5000ms
            'priority': 1,
            'enabled': 1,
            'description': '租户3的排队等待限流规则'
        },
        {
            'id': 4,
            'tenant_id': 'tenant4',
            'rule_name': '租户4线程数限流',
            'grade': 0,  # 0表示线程数限流
            'count': 5,  # 线程数限制
            'control_behavior': 0,  # 快速失败
            'warm_up_period_sec': None,
            'max_queueing_time_ms': None,
            'priority': 3,
            'enabled': 1,
            'description': '租户4的线程数限流规则'
        },
        {
            'id': 5,
            'tenant_id': 'tenant5',
            'rule_name': '租户5综合限流',
            'grade': 0,  # 0表示线程数限流
            'count': 5,  # 线程数限制
            'control_behavior': 3,  # 预热+排队
            'warm_up_period_sec': 15,  # 预热时间15秒
            'max_queueing_time_ms': 3000,  # 最大排队时间3000ms
            'priority': 1,
            'enabled': 1,
            'description': '租户5的Warm Up + 排队等待限流规则'
        }
    ]
    
    return rules


def main():
    """
    主函数 - 解析命令行参数并执行测试
    """
    parser = argparse.ArgumentParser(description='租户线程数限流测试工具')
    parser.add_argument('--url', default='http://localhost:8088', help='测试目标URL (默认: http://localhost:8088)')
    parser.add_argument('--duration', type=float, default=2.0, help='单个请求持续时间，秒 (默认: 2.0)')
    parser.add_argument('--concurrent', type=int, default=10, help='并发线程数 (默认: 10，适合线程限流测试)')
    parser.add_argument('--test-duration', type=int, default=15, help='测试持续时间，秒 (默认: 15)')
    parser.add_argument('--tenant', help='只测试指定租户 (可选)')
    parser.add_argument('--list-rules', action='store_true', help='显示所有租户规则')
    parser.add_argument('--output', help='输出报告文件名 (可选)')
    parser.add_argument('--use-db', action='store_true', help='从数据库查询租户规则')
    parser.add_argument('--db-host', default='localhost', help='数据库主机 (默认: localhost)')
    parser.add_argument('--db-port', type=int, default=33016, help='数据库端口 (默认: 33016)')
    parser.add_argument('--db-user', default='root', help='数据库用户名 (默认: root)')
    parser.add_argument('--db-password', default='aids520a', help='数据库密码 (默认: aids520a)')
    parser.add_argument('--db-name', default='sentinel_flow_control', help='数据库名称 (默认: sentinel_flow_control)')
    
    args = parser.parse_args()
    
    # 创建数据库配置
    db_config = {
        'host': args.db_host,
        'port': args.db_port,
        'user': args.db_user,
        'password': args.db_password,
        'database': args.db_name
    }
    
    # 创建测试器
    tester = TenantThreadLimitTester(base_url=args.url, request_duration=args.duration, db_config=db_config)
    
    # 获取租户规则
    if args.use_db:
        print("从数据库查询租户限流规则...")
        rules = tester.get_tenant_rules_from_db()
    else:
        print("使用默认租户限流规则...")
        rules = create_tenant_rules()
    
    # 显示规则列表
    if args.list_rules:
        print("租户线程数限流规则列表:")
        print("-" * 100)
        for rule in rules:
            behavior_name = {
                0: "快速失败",
                1: "预热启动",
                2: "排队等待", 
                3: "预热+排队"
            }.get(rule['control_behavior'], "未知")
            
            print(f"租户ID: {rule['tenant_id']}")
            print(f"  规则名称: {rule['rule_name']}")
            print(f"  线程数限制: {rule['count']}")
            print(f"  控制行为: {behavior_name} ({rule['control_behavior']})")
            if rule['warm_up_period_sec']:
                print(f"  预热时间: {rule['warm_up_period_sec']}秒")
            if rule['max_queueing_time_ms']:
                print(f"  最大排队时间: {rule['max_queueing_time_ms']}毫秒")
            print(f"  优先级: {rule['priority']}")
            print(f"  描述: {rule['description']}")
            print()
        return
    
    # 过滤租户规则
    if args.tenant:
        rules = [rule for rule in rules if rule['tenant_id'] == args.tenant]
        if not rules:
            print(f"错误: 未找到租户 '{args.tenant}' 的规则")
            return
    
    print(f"开始租户线程数限流测试...")
    print(f"目标URL: {args.url}")
    print(f"请求持续时间: {args.duration}秒")
    print(f"并发线程数: {args.concurrent}")
    print(f"测试持续时间: {args.test_duration}秒")
    print(f"测试租户数: {len(rules)}")
    
    # 执行测试
    all_stats = []
    for rule in rules:
        try:
            stats = tester.test_tenant_thread_limit(
                tenant=rule['tenant_id'],
                thread_limit=rule['count'],
                control_behavior=rule['control_behavior'],
                concurrent_threads=args.concurrent,
                test_duration=args.test_duration
            )
            all_stats.append(stats)
        except Exception as e:
            print(f"测试租户 {rule['tenant_id']} 时发生错误: {e}")
    
    # 保存和显示结果
    if all_stats:
        tester.save_report(all_stats, args.output)
        tester.print_summary(all_stats)
    else:
        print("没有成功完成的测试")


if __name__ == '__main__':
    main()