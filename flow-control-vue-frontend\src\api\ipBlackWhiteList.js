import request from '@/utils/request'

// IP黑白名单API
export const ipBlackWhiteListApi = {
  // 获取黑名单列表
  getBlackList(params) {
    return request({
      url: '/api/ip-blacklists',
      method: 'get',
      params
    })
  },

  // 获取白名单列表
  getWhiteList(params) {
    return request({
      url: '/api/ip-whitelists',
      method: 'get',
      params
    })
  },

  // 创建黑名单记录
  createBlack(data) {
    return request({
      url: '/api/ip-blacklists',
      method: 'post',
      data
    })
  },

  // 创建白名单记录
  createWhite(data) {
    return request({
      url: '/api/ip-whitelists',
      method: 'post',
      data
    })
  },

  // 更新黑名单记录
  updateBlack(id, data) {
    return request({
      url: `/api/ip-blacklists/${id}`,
      method: 'put',
      data
    })
  },

  // 更新白名单记录
  updateWhite(id, data) {
    return request({
      url: `/api/ip-whitelists/${id}`,
      method: 'put',
      data
    })
  },

  // 删除黑名单记录
  deleteBlack(id) {
    return request({
      url: `/api/ip-blacklists/${id}`,
      method: 'delete'
    })
  },

  // 删除白名单记录
  deleteWhite(id) {
    return request({
      url: `/api/ip-whitelists/${id}`,
      method: 'delete'
    })
  },

  // 更新黑名单状态
  updateBlackStatus(id, enabled) {
    return request({
      url: `/api/ip-blacklists/${id}/status`,
      method: 'put',
      params: { enabled }
    })
  },

  // 更新白名单状态
  updateWhiteStatus(id, enabled) {
    return request({
      url: `/api/ip-whitelists/${id}/status`,
      method: 'put',
      params: { enabled }
    })
  },

  // 批量导入
  batchImport(data) {
    return request({
      url: '/api/ip-blackwhite-list/batch-import',
      method: 'post',
      data
    })
  },

  // 批量导出
  batchExport(params) {
    return request({
      url: '/api/ip-blackwhite-list/batch-export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}