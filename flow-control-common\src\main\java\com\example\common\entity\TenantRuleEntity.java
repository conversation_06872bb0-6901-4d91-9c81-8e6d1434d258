package com.example.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 租户流控规则实体类
 * 对应数据库表：tenant_flow_rules
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName("tenant_flow_rules")
public class TenantRuleEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;

    /**
     * 限流模式：0-线程数，1-QPS
     */
    @TableField("grade")
    private Integer grade;

    /**
     * 限流阈值
     */
    @TableField("count")
    private Double count;

    /**
     * 流控效果：0-快速失败，1-Warm Up，2-排队等待，3-Warm Up + 排队等待
     */
    @TableField("control_behavior")
    private Integer controlBehavior;

    /**
     * 预热时间（秒）
     */
    @TableField("warm_up_period_sec")
    private Integer warmUpPeriodSec;

    /**
     * 最大排队时间（毫秒）
     */
    @TableField("max_queueing_time_ms")
    private Integer maxQueueingTimeMs;

    /**
     * 流控策略：0-直接，1-关联，2-链路
     */
    @TableField("strategy")
    private Integer strategy;

    /**
     * 关联资源
     */
    @TableField("ref_resource")
    private String refResource;

    /**
     * 集群模式：0-单机，1-集群
     */
    @TableField("cluster_mode")
    private Boolean clusterMode;

    /**
     * 集群配置JSON
     */
    @TableField("cluster_config")
    private String clusterConfig;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 是否启用：0-禁用，1-启用
     */
    @TableField("enabled")
    private Integer enabled;

    /**
     * 生效开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 规则描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
    
}