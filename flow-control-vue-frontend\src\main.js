import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import zhCNLocale from 'element-ui/lib/locale/lang/zh-CN'
import enUSLocale from 'element-ui/lib/locale/lang/en'
import locale from 'element-ui/lib/locale'
import axios from 'axios'
import i18n from './i18n'
import './styles/index.scss'
import api from './api'

Vue.config.productionTip = false

// 动态获取Element UI语言包的函数
function getElementLocale () {
	return i18n.locale === 'en-US' ? enUSLocale : zhCNLocale
}

// 设置初始Element UI语言包
locale.use(getElementLocale())

// 使用Element UI（支持国际化）
Vue.use(ElementUI, {
	i18n: (key, value) => i18n.t(key, value)
})

// 监听语言切换，更新Element UI语言包
i18n.vm.$watch('locale', () => {
	// 动态切换Element UI语言包
	locale.use(getElementLocale())
})

// 配置axios
Vue.prototype.$http = axios
Vue.prototype.$api = api
axios.defaults.baseURL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8081'

new Vue({
	router,
	store,
	i18n,
	render: h => h(App)
}).$mount('#app')