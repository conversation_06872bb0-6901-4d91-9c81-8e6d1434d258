package com.example.admin.service.impl;

import com.example.admin.service.IPFlowRuleService;
import com.example.admin.service.IPRuleMatchService;
import com.example.admin.service.IPBlacklistService;
import com.example.admin.service.IPWhitelistService;
import com.example.admin.vo.IPBlacklistVO;
import com.example.admin.vo.IPWhitelistVO;
import com.example.admin.utils.IPUtils;
import com.example.admin.vo.IPFlowRuleVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.net.util.SubnetUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * IP规则匹配服务实现类
 */
@Service
public class IPRuleMatchServiceImpl implements IPRuleMatchService {

	private static final Logger log = LoggerFactory.getLogger(IPRuleMatchServiceImpl.class);

	@Autowired
	private IPFlowRuleService ipFlowRuleService;

	@Autowired
	private IPBlacklistService ipBlacklistService;

	@Autowired
	private IPWhitelistService ipWhitelistService;

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

	private static final String CACHE_KEY_PREFIX = "ip_rules:";
	private static final String CACHE_KEY_PREFIX_WHITELIST = "ip_whitelist:";
	private static final String CACHE_KEY_PREFIX_BLACKLIST = "ip_blacklist:";
	private static final long CACHE_EXPIRE_SECONDS = 300; // 5分钟缓存

	@Override
	public IPRuleMatchResult matchIPRule(String ipAddress, String tenantId) {
		if (!StringUtils.hasText(ipAddress)) {
			return new IPRuleMatchResult(null, false);
		}

		try {
			// 获取匹配的规则列表
			List<IPFlowRuleVO> matchingRules = getMatchingRules(ipAddress, tenantId);

			if (CollectionUtils.isEmpty(matchingRules)) {
				return new IPRuleMatchResult(null, false);
			}

			// 按优先级排序，优先级高的在前
			matchingRules.sort(Comparator.comparing(IPFlowRuleVO::getPriority).reversed());

			// 返回第一个匹配的规则
			IPFlowRuleVO firstRule = matchingRules.get(0);
			return new IPRuleMatchResult(firstRule, true, firstRule.getListType());

		} catch (Exception e) {
			log.error("IP规则匹配失败: ip={}, tenantId={}", ipAddress, tenantId, e);
			return new IPRuleMatchResult(null, false);
		}
	}

	@Override
	public List<IPFlowRuleVO> getMatchingRules(String ipAddress, String tenantId) {
		if (!StringUtils.hasText(ipAddress)) {
			return new ArrayList<>();
		}

		try {
			// 先从缓存获取
			List<IPFlowRuleVO> cachedRules = getCachedRules(tenantId);
			if (cachedRules == null) {
				// 缓存未命中，从数据库获取
				cachedRules = ipFlowRuleService.getIPFlowRulesByTenantId(tenantId, 1, 1000);
				// 缓存规则
				cacheRules(tenantId, cachedRules);
			}

			// 过滤匹配的规则
			return cachedRules.stream().filter(rule -> isIPMatched(ipAddress, rule)).collect(Collectors.toList());

		} catch (Exception e) {
			log.error("获取匹配规则失败: ip={}, tenantId={}", ipAddress, tenantId, e);
			return new ArrayList<>();
		}
	}

	@Override
	public boolean isIPInBlacklist(String ipAddress, String tenantId) {
		try {
			// 检查IP流量规则表中的黑名单
			List<IPFlowRuleVO> matchingRules = getMatchingRules(ipAddress, tenantId);
			boolean inFlowRuleBlacklist = matchingRules.stream()
					.anyMatch(rule -> "BLACKLIST".equals(rule.getListType()) && rule.isEnabled());
			
			if (inFlowRuleBlacklist) {
				return true;
			}
			
			// 检查IP黑白名单表中的黑名单
			List<IPBlacklistVO> blacklists = getCachedBlacklists(tenantId);
			if (blacklists == null) {
				blacklists = ipBlacklistService.getIPBlacklistsByTenantId(tenantId, 1, 1000);
				cacheBlacklists(tenantId, blacklists);
			}
			
			return blacklists.stream().anyMatch(list -> isIPMatchedInBlacklist(ipAddress, list));
			
		} catch (Exception e) {
			log.error("检查IP黑名单失败: ip={}, tenantId={}", ipAddress, tenantId, e);
			return false;
		}
	}

	@Override
	public boolean isIPInWhitelist(String ipAddress, String tenantId) {
		try {
			// 检查IP流量规则表中的白名单
			List<IPFlowRuleVO> matchingRules = getMatchingRules(ipAddress, tenantId);
			boolean inFlowRuleWhitelist = matchingRules.stream()
					.anyMatch(rule -> "WHITELIST".equals(rule.getListType()) && rule.isEnabled());
			
			if (inFlowRuleWhitelist) {
				return true;
			}
			
			// 检查IP黑白名单表中的白名单
			List<IPWhitelistVO> whitelists = getCachedWhitelists(tenantId);
			if (whitelists == null) {
				whitelists = ipWhitelistService.getIPWhitelistsByTenantId(tenantId, 1, 1000);
				cacheWhitelists(tenantId, whitelists);
			}
			
			return whitelists.stream().anyMatch(list -> isIPMatchedInWhitelist(ipAddress, list));
			
		} catch (Exception e) {
			log.error("检查IP白名单失败: ip={}, tenantId={}", ipAddress, tenantId, e);
			return false;
		}
	}

	@Override
	public IPFlowRuleVO getIPLimitRule(String ipAddress, String tenantId) {
		List<IPFlowRuleVO> matchingRules = getMatchingRules(ipAddress, tenantId);
		return matchingRules.stream().filter(rule -> "LIMIT".equals(rule.getListType()) && rule.isEnabled())
				.max(Comparator.comparing(IPFlowRuleVO::getPriority)).orElse(null);
	}

	@Override
	public void refreshRuleCache(String tenantId) {
		try {
			if (StringUtils.hasText(tenantId)) {
				// 刷新指定租户的缓存
				String flowRuleCacheKey = CACHE_KEY_PREFIX + tenantId;
				String whitelistCacheKey = CACHE_KEY_PREFIX_WHITELIST + tenantId;
				String blacklistCacheKey = CACHE_KEY_PREFIX_BLACKLIST + tenantId;
				
				redisTemplate.delete(flowRuleCacheKey);
				redisTemplate.delete(whitelistCacheKey);
				redisTemplate.delete(blacklistCacheKey);
				
				log.info("已刷新租户{}的IP规则缓存", tenantId);
			} else {
				// 刷新所有缓存
				String flowRulePattern = CACHE_KEY_PREFIX + "*";
				String whitelistPattern = CACHE_KEY_PREFIX_WHITELIST + "*";
				String blacklistPattern = CACHE_KEY_PREFIX_BLACKLIST + "*";
				
				redisTemplate.delete(redisTemplate.keys(flowRulePattern));
				redisTemplate.delete(redisTemplate.keys(whitelistPattern));
				redisTemplate.delete(redisTemplate.keys(blacklistPattern));
				
				log.info("已刷新所有IP规则缓存");
			}
		} catch (Exception e) {
			log.error("刷新IP规则缓存失败: tenantId={}", tenantId, e);
		}
	}

	/**
	 * 检查IP是否匹配单个规则
	 */
	private boolean isIPMatched(String ipAddress, IPFlowRuleVO rule) {
		if (rule == null || !StringUtils.hasText(rule.getIpValue())) {
			return false;
		}

		try {
			switch (rule.getRuleType()) {
		case "SINGLE_IP":
			return ipAddress.equals(rule.getIpValue());
		case "IP_RANGE":
			return isIPInRange(ipAddress, rule.getIpValue());
		case "CIDR":
			return isIPInCIDR(ipAddress, rule.getIpValue());
		case "WILDCARD":
			return isIPInWildcard(ipAddress, rule.getIpValue());
			default:
				log.warn("未知的规则类型: {}", rule.getRuleType());
				return false;
			}
		} catch (Exception e) {
			log.error("IP匹配检查失败: ip={}, rule={}", ipAddress, rule.getIpValue(), e);
			return false;
		}
	}

	/**
	 * 检查IP是否在CIDR范围内
	 */
	private boolean isIPInCIDR(String ipAddress, String cidr) {
		try {
			// 使用Apache Commons Net的SubnetUtils
			SubnetUtils subnet = new SubnetUtils(cidr);
			subnet.setInclusiveHostCount(true); // 包含网络地址和广播地址
			return subnet.getInfo().isInRange(ipAddress);
		} catch (Exception e) {
			log.error("CIDR格式检查失败: ip={}, cidr={}", ipAddress, cidr, e);
			// 降级使用自定义实现
			return IPUtils.isIPInRange(ipAddress, cidr);
		}
	}

	/**
	 * 检查IP是否在范围内（支持CIDR格式）
	 */
	private boolean isIPInRange(String ipAddress, String range) {
		try {
			// 如果包含/，按CIDR处理
			if (range.contains("/")) {
				return isIPInCIDR(ipAddress, range);
			}

			// 如果包含-，按范围处理
			if (range.contains("-")) {
				String[] parts = range.split("-");
				if (parts.length == 2) {
					String startIP = parts[0].trim();
					String endIP = parts[1].trim();
					return isIPInRange(ipAddress, startIP, endIP);
				}
			}

			// 否则按单个IP处理
			return ipAddress.equals(range);
		} catch (Exception e) {
			log.error("IP范围检查失败: ip={}, range={}", ipAddress, range, e);
			return false;
		}
	}

	/**
	 * 检查IP是否在指定范围内
	 */
	private boolean isIPInRange(String ipAddress, String startIP, String endIP) {
		try {
			long ip = IPUtils.ipToLong(ipAddress);
			long start = IPUtils.ipToLong(startIP);
			long end = IPUtils.ipToLong(endIP);
			return ip >= start && ip <= end;
		} catch (Exception e) {
			log.error("IP范围检查失败: ip={}, start={}, end={}", ipAddress, startIP, endIP, e);
			return false;
		}
	}

	/**
	 * 检查IP是否匹配通配符模式
	 */
	private boolean isIPInWildcard(String ipAddress, String wildcardPattern) {
		try {
			if (!StringUtils.hasText(ipAddress) || !StringUtils.hasText(wildcardPattern)) {
				return false;
			}

			String[] ipParts = ipAddress.split("\\.");
			String[] patternParts = wildcardPattern.split("\\.");

			if (ipParts.length != 4 || patternParts.length != 4) {
				return false;
			}

			for (int i = 0; i < 4; i++) {
				if (!"*".equals(patternParts[i]) && !ipParts[i].equals(patternParts[i])) {
					return false;
				}
			}

			return true;
		} catch (Exception e) {
			log.error("通配符匹配检查失败: ip={}, pattern={}", ipAddress, wildcardPattern, e);
			return false;
		}
	}

	/**
	 * 从缓存获取规则
	 */
	@SuppressWarnings("unchecked")
	private List<IPFlowRuleVO> getCachedRules(String tenantId) {
		try {
			String cacheKey = CACHE_KEY_PREFIX + tenantId;
			Object cached = redisTemplate.opsForValue().get(cacheKey);
			return cached != null ? (List<IPFlowRuleVO>) cached : null;
		} catch (Exception e) {
			log.warn("获取缓存规则失败: tenantId={}", tenantId, e);
			return null;
		}
	}

	/**
	 * 缓存规则
	 */
	private void cacheRules(String tenantId, List<IPFlowRuleVO> rules) {
		try {
			String cacheKey = CACHE_KEY_PREFIX + tenantId;
			redisTemplate.opsForValue().set(cacheKey, rules, CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
		} catch (Exception e) {
			log.warn("缓存规则失败: tenantId={}", tenantId, e);
		}
	}

	/**
	 * 从缓存获取白名单
	 */
	@SuppressWarnings("unchecked")
	private List<IPWhitelistVO> getCachedWhitelists(String tenantId) {
		try {
			String cacheKey = CACHE_KEY_PREFIX_WHITELIST + tenantId;
			Object cached = redisTemplate.opsForValue().get(cacheKey);
			return cached != null ? (List<IPWhitelistVO>) cached : null;
		} catch (Exception e) {
			log.warn("获取缓存白名单失败: tenantId={}", tenantId, e);
			return null;
		}
	}

	/**
	 * 缓存白名单
	 */
	private void cacheWhitelists(String tenantId, List<IPWhitelistVO> whitelists) {
		try {
			String cacheKey = CACHE_KEY_PREFIX_WHITELIST + tenantId;
			redisTemplate.opsForValue().set(cacheKey, whitelists, CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
		} catch (Exception e) {
			log.warn("缓存白名单失败: tenantId={}", tenantId, e);
		}
	}

	/**
	 * 从缓存获取黑名单
	 */
	@SuppressWarnings("unchecked")
	private List<IPBlacklistVO> getCachedBlacklists(String tenantId) {
		try {
			String cacheKey = CACHE_KEY_PREFIX_BLACKLIST + tenantId;
			Object cached = redisTemplate.opsForValue().get(cacheKey);
			return cached != null ? (List<IPBlacklistVO>) cached : null;
		} catch (Exception e) {
			log.warn("获取缓存黑名单失败: tenantId={}", tenantId, e);
			return null;
		}
	}

	/**
	 * 缓存黑名单
	 */
	private void cacheBlacklists(String tenantId, List<IPBlacklistVO> blacklists) {
		try {
			String cacheKey = CACHE_KEY_PREFIX_BLACKLIST + tenantId;
			redisTemplate.opsForValue().set(cacheKey, blacklists, CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
		} catch (Exception e) {
			log.warn("缓存黑名单失败: tenantId={}", tenantId, e);
		}
	}

	/**
	 * 检查IP是否匹配黑名单配置
	 */
	private boolean isIPMatchedInBlacklist(String ipAddress, IPBlacklistVO list) {
		if (list == null || !StringUtils.hasText(ipAddress)) {
			return false;
		}

		try {
			switch (list.getIpType()) {
		case "SINGLE":
			return ipAddress.equals(list.getIpAddress());
        case "RANGE":
            return isIPInRange(ipAddress, list.getIpStart(), list.getIpEnd());
        case "CIDR":
            return isIPInCIDR(ipAddress, list.getIpCidr());
			default:
				log.warn("未知的IP类型: {}", list.getIpType());
				return false;
			}
		} catch (Exception e) {
			log.error("IP匹配检查失败: ip={}, listId={}", ipAddress, list.getId(), e);
			return false;
		}
	}

	/**
	 * 检查IP是否匹配白名单配置
	 */
	private boolean isIPMatchedInWhitelist(String ipAddress, IPWhitelistVO list) {
		if (list == null || !StringUtils.hasText(ipAddress)) {
			return false;
		}

		try {
			switch (list.getIpType()) {
		case "SINGLE":
			return ipAddress.equals(list.getIpAddress());
        case "RANGE":
            return isIPInRange(ipAddress, list.getIpStart(), list.getIpEnd());
        case "CIDR":
            return isIPInCIDR(ipAddress, list.getIpCidr());
			default:
				log.warn("未知的IP类型: {}", list.getIpType());
				return false;
			}
		} catch (Exception e) {
			log.error("IP匹配检查失败: ip={}, listId={}", ipAddress, list.getId(), e);
			return false;
		}
	}
}
