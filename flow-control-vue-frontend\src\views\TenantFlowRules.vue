<template>
  <div class="tenant-flow-rules">
    <layout>
      <div class="page-content">
        <div class="page-header">
          <h1>租户限流规则配置</h1>
          <p>设置整个租户的总QPS或并发数量，以及Sentinel支持的限流配置</p>
        </div>

        <!-- 操作栏 -->
        <div class="operation-bar">
          <el-button type="primary" icon="el-icon-plus" @click="showAddDialog">
            新增租户规则
          </el-button>
          
          <div class="search-box">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入租户ID或名称"
              clearable
              @keyup.enter.native="handleSearch"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>
        </div>

        <!-- 数据表格 -->
        <el-table
          :data="tableData"
          :loading="loading"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column prop="tenantId" label="租户ID" width="120" show-overflow-tooltip />
          <el-table-column prop="ruleName" label="规则名称" min-width="150" show-overflow-tooltip />
          <!-- 租户限流为全局生效，无需资源匹配模式 -->
          <el-table-column label="限流模式" width="110">
            <template slot-scope="scope">
              <el-tag :type="scope.row.grade === 1 ? 'primary' : 'success'">
                {{ getGradeText(scope.row.grade) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="限流阈值" width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.grade === 1 && scope.row.count">
                {{ scope.row.count }} QPS
              </span>
              <span v-else-if="scope.row.grade === 0 && scope.row.count">
                {{ scope.row.count }} 线程
              </span>
              <span v-else class="text-muted">未设置</span>
            </template>
          </el-table-column>

          <el-table-column prop="controlBehavior" label="流控效果" width="110">
            <template slot-scope="scope">
              <el-tag :type="getBehaviorTagType(scope.row.controlBehavior)">
                {{ getBehaviorText(scope.row.controlBehavior) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="maxQueueingTimeMs" label="排队超时(ms)" width="130">
            <template slot-scope="scope">
              <span v-if="scope.row.controlBehavior === 2">{{ scope.row.maxQueueingTimeMs }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="warmUpPeriodSec" label="预热时长(s)" width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.controlBehavior === 1">{{ scope.row.warmUpPeriodSec }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="enabled" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.enabled === 1 ? 'success' : 'danger'">
                {{ scope.row.enabled === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" @click="editRule(scope.row)">编辑</el-button>
              <el-button
                size="mini"
                :type="scope.row.enabled === 1 ? 'warning' : 'success'"
                @click="toggleStatus(scope.row)"
              >
                {{ scope.row.enabled === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button size="mini" type="danger" @click="deleteRule(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          >
          </el-pagination>
        </div>

        <!-- 新增/编辑对话框 -->
        <el-dialog
          :title="isEdit ? '编辑租户规则' : '新增租户规则'"
          :visible.sync="dialogVisible"
          width="600px"
          :close-on-click-modal="false"
        >
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            :rules="computedFormRules"
            label-width="120px"
          >
            <el-form-item label="租户ID" prop="tenantId">
              <el-input
                v-model="ruleForm.tenantId"
                placeholder="请输入租户ID"
                :disabled="isEdit"
              />
            </el-form-item>
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称" />
            </el-form-item>
            <!-- 租户限流为全局生效，适用于所有API请求 -->
            <div class="global-rule-notice">
              <el-alert
                title="全局限流规则"
                description="此租户限流规则将应用于该租户的所有API请求，无需设置资源匹配模式"
                type="info"
                :closable="false"
                show-icon>
              </el-alert>
            </div>
            <el-form-item label="限流模式">
              <el-select v-model="ruleForm.grade" style="width: 100%" @change="onGradeChange">
                <el-option label="QPS模式" :value="1" />
                <el-option label="并发线程数模式" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item 
              v-if="ruleForm.grade === 1" 
              label="QPS限制" 
              prop="count"
            >
              <el-input-number
                v-model="ruleForm.count"
                :min="1"
                :max="999999"
                placeholder="请输入QPS限制"
                style="width: 100%"
              />
              <div class="form-tip">设置每秒允许通过的请求数量</div>
            </el-form-item>
            <el-form-item 
              v-if="ruleForm.grade === 0" 
              label="并发限制" 
              prop="count"
            >
              <el-input-number
                v-model="ruleForm.count"
                :min="1"
                :max="999999"
                placeholder="请输入并发线程数限制"
                style="width: 100%"
              />
              <div class="form-tip">设置同时处理请求的最大线程数</div>
            </el-form-item>


            <el-form-item label="优先级">
              <el-input-number
                v-model="ruleForm.priority"
                :min="0"
                :max="999"
                placeholder="优先级，数值越小优先级越高"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="流控效果" prop="controlBehavior">
              <el-select v-model="ruleForm.controlBehavior" style="width: 100%">
                <el-option label="快速失败" :value="0" />
                <el-option label="Warm Up" :value="1" />
                <el-option label="排队等待" :value="2" />
                <el-option label="Warm Up + 排队等待" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="ruleForm.controlBehavior === 1 || ruleForm.controlBehavior === 3"
              label="预热时长(秒)"
              prop="warmUpPeriodSec"
            >
              <el-input-number
                v-model="ruleForm.warmUpPeriodSec"
                :min="1"
                :max="3600"
                placeholder="预热时长"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item
              v-if="ruleForm.controlBehavior === 2 || ruleForm.controlBehavior === 3"
              label="排队超时(毫秒)"
              prop="maxQueueingTimeMs"
            >
              <el-input-number
                v-model="ruleForm.maxQueueingTimeMs"
                :min="1"
                :max="60000"
                placeholder="排队超时时间"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="集群模式">
              <el-switch
                v-model="ruleForm.clusterMode"
                :active-value="true"
                :inactive-value="false"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
            <el-form-item label="状态" prop="enabled">
              <el-switch
                v-model="ruleForm.enabled"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" :loading="submitting" @click="submitForm">
              {{ isEdit ? '更新' : '创建' }}
            </el-button>
          </div>
        </el-dialog>
      </div>
    </layout>
  </div>
</template>

<script>
import Layout from '@/components/Layout.vue'
import { tenantFlowRuleApi } from '@/api/tenantFlowRule'
import validationRules from '@/utils/validation'

export default {
  name: 'TenantFlowRules',
  components: {
    Layout
  },
  beforeCreate() {
    // 注册验证规则到Vue实例
    this.$validation = validationRules;
  },
  data() {
    return {
      loading: false,
      submitting: false,
      dialogVisible: false,
      isEdit: false,
      tableData: [],
      searchForm: {
        keyword: ''
      },
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },
      ruleForm: {
        tenantId: '',
        ruleName: '',
        grade: 1,  // 限流阈值类型：0-线程数，1-QPS
        count: null,  // 限流阈值
        strategy: 0,  // 调用来源策略
        refResource: '',  // 关联资源
        controlBehavior: 0,  // 流控效果
        warmUpPeriodSec: null,  // 预热时间（秒）
        maxQueueingTimeMs: null,  // 排队超时时间（毫秒）
        clusterMode: false,  // 是否集群模式
        clusterConfig: '',  // 集群配置
        priority: 1,  // 优先级
        enabled: 1,  // 启用状态：0-禁用，1-启用
        startTime: null,  // 生效开始时间
        endTime: null,  // 生效结束时间
        description: ''  // 规则描述
      },
      formRules: {}
    }
  },
  computed: {
    // 动态计算表单验证规则
    computedFormRules() {
      const rules = {
        tenantId: this.$validation.tenantFlowRuleRules.tenantId,
        ruleName: this.$validation.tenantFlowRuleRules.ruleName,
        grade: this.$validation.tenantFlowRuleRules.grade,
        count: this.$validation.tenantFlowRuleRules.count,
        strategy: this.$validation.tenantFlowRuleRules.strategy,
        refResource: this.$validation.tenantFlowRuleRules.refResource,
        controlBehavior: this.$validation.tenantFlowRuleRules.controlBehavior,
        warmUpPeriodSec: this.$validation.tenantFlowRuleRules.warmUpPeriodSec,
        maxQueueingTimeMs: this.$validation.tenantFlowRuleRules.maxQueueingTimeMs,
        priority: this.$validation.tenantFlowRuleRules.priority,
        enabled: this.$validation.tenantFlowRuleRules.enabled,
        description: this.$validation.tenantFlowRuleRules.description
      };
      
      // 根据限流模式动态调整验证规则
      if (this.ruleForm.grade === 1) {
        // QPS模式：count字段必填
        rules.count = [
          { required: true, message: '请输入QPS限制', trigger: 'blur' },
          ...this.$validation.tenantFlowRuleRules.count.slice(1)
        ];
      } else if (this.ruleForm.grade === 0) {
        // 线程数模式：count字段必填
        rules.count = [
          { required: true, message: '请输入并发限制', trigger: 'blur' },
          ...this.$validation.tenantFlowRuleRules.count.slice(1)
        ];
      }
      
      // 根据流控效果动态调整验证规则
      if (this.ruleForm.controlBehavior === 1) {
        // Warm Up模式：预热时间必填
        rules.warmUpPeriodSec = [
          { required: true, message: '请输入预热时长', trigger: 'blur' },
          ...this.$validation.tenantFlowRuleRules.warmUpPeriodSec
        ];
      }
      
      if (this.ruleForm.controlBehavior === 2 || this.ruleForm.controlBehavior === 3) {
        // 排队等待模式：排队超时时间必填
        rules.maxQueueingTimeMs = [
          { required: true, message: '请输入排队超时时间', trigger: 'blur' },
          ...this.$validation.tenantFlowRuleRules.maxQueueingTimeMs
        ];
      }
      
      return rules;
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          keyword: this.searchForm.keyword
        }
        const response = await tenantFlowRuleApi.getTenantFlowRuleList(params)
        this.tableData = response.data.records || []
        this.pagination.total = response.data.total || 0
      } catch (error) {
        this.$message.error('加载数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      this.pagination.current = 1
      this.loadData()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadData()
    },
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadData()
    },
    showAddDialog() {
      this.isEdit = false
      this.resetForm()
      this.dialogVisible = true
    },
    editRule(row) {
      this.isEdit = true
      this.ruleForm = { ...row }
      this.dialogVisible = true
    },
    async toggleStatus(row) {
      try {
        const newStatus = row.enabled === 1 ? 0 : 1
        await tenantFlowRuleApi.updateTenantFlowRuleStatus(row.id, newStatus)
        this.$message.success('状态更新成功')
        this.loadData()
      } catch (error) {
        this.$message.error('状态更新失败：' + error.message)
      }
    },
    async deleteRule(row) {
      try {
        await this.$confirm('确定要删除这条租户规则吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await tenantFlowRuleApi.deleteTenantFlowRule(row.id)
        this.$message.success('删除成功')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败：' + error.message)
        }
      }
    },
    onGradeChange(newGrade) {
      // 切换限流模式时设置默认值
      if (newGrade === 1) {
        // QPS模式：设置默认QPS值
        if (!this.ruleForm.count) {
          this.ruleForm.count = 100
        }
      } else {
        // 并发模式：设置默认并发值
        if (!this.ruleForm.count) {
          this.ruleForm.count = 10
        }
      }
      // 清除验证错误
      this.$nextTick(() => {
        if (this.$refs.ruleForm) {
          this.$refs.ruleForm.clearValidate()
        }
      })
    },
    async submitForm() {
      try {
        // 验证规则现在通过computedFormRules动态计算，无需手动设置
        
        await this.$refs.ruleForm.validate()
        this.submitting = true
        
        if (this.isEdit) {
          await tenantFlowRuleApi.updateTenantFlowRule(this.ruleForm.id, this.ruleForm)
          this.$message.success('更新成功')
        } else {
          await tenantFlowRuleApi.createTenantFlowRule(this.ruleForm)
          this.$message.success('创建成功')
        }
        
        this.dialogVisible = false
        this.loadData()
      } catch (error) {
        if (error.message) {
          this.$message.error('操作失败：' + error.message)
        }
      } finally {
        this.submitting = false
      }
    },
    resetForm() {
      this.ruleForm = {
        tenantId: '',
        ruleName: '',
        grade: 1,  // 限流阈值类型：0-线程数，1-QPS
        count: null,  // 限流阈值
        strategy: 0,  // 调用来源策略
        refResource: '',  // 关联资源
        controlBehavior: 0,  // 流控效果
        warmUpPeriodSec: null,  // 预热时间（秒）
        maxQueueingTimeMs: null,  // 排队超时时间（毫秒）
        clusterMode: false,  // 是否集群模式
        clusterConfig: '',  // 集群配置
        priority: 1,  // 优先级
        enabled: 1,  // 启用状态：0-禁用，1-启用
        startTime: null,  // 生效开始时间
        endTime: null,  // 生效结束时间
        description: ''  // 规则描述
      }
      
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate()
      }
    },
    getBehaviorText(behavior) {
      const map = {
        0: '快速失败',
        1: 'Warm Up',
        2: '排队等待',
        3: 'Warm Up + 排队等待'
      }
      return map[behavior] || '未知'
    },
    getBehaviorTagType(behavior) {
      const map = {
        0: 'danger',
        1: 'warning',
        2: 'info',
        3: 'success'
      }
      return map[behavior] || ''
    },
    getGradeText(grade) {
      const map = {
        0: '并发线程数',
        1: 'QPS'
      }
      return map[grade] || '未知'
    },
    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style scoped>
.tenant-flow-rules {
  height: 100%;
}

.page-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  width: 100%;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.search-box {
  width: 300px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.text-muted {
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

.global-rule-notice {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .operation-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>