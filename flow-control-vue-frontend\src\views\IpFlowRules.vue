<template>
  <div class="ip-flow-rules">
    <layout>
      <div class="page-content">
        <div class="page-header">
          <h1>IP限流规则配置</h1>
          <p>配置从接口维度对流量的限制，IP限制是跟租户无关的，配置项包括QPS或并发数量，以及Sentinel支持的限流配置</p>
        </div>

        <!-- 操作栏 -->
        <div class="operation-bar">
          <el-button type="primary" icon="el-icon-plus" @click="showAddDialog">
            新增IP规则
          </el-button>
          
          <div class="filter-group">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入规则名称或IP地址"
              clearable
              style="width: 250px"
              @keyup.enter.native="handleSearch"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>
        </div>

        <!-- 数据表格 -->
        <el-table
          :data="tableData"
          :loading="loading"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column prop="ruleName" label="规则名称" width="150" show-overflow-tooltip />
          <el-table-column prop="ipValue" label="IP地址" min-width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag size="mini" type="info">{{ getIpTypeText(scope.row.ipType) }}</el-tag>
              {{ scope.row.ipValue }}
            </template>
          </el-table-column>
          <el-table-column prop="listType" label="类型" width="90">
            <template slot-scope="scope">
              <el-tag :type="scope.row.listType === 'LIMIT' ? 'danger' : 'success'">
                {{ scope.row.listType === 'LIMIT' ? '限制' : '允许' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="limitMode" label="限制模式" width="110">
            <template slot-scope="scope">
              <el-tag :type="scope.row.limitMode === 0 ? 'primary' : 'success'">
                {{ scope.row.limitMode === 0 ? 'QPS' : '并发数' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="limitCount" label="限制数量" width="100" />
          <el-table-column prop="behavior" label="控制行为" width="120">
            <template slot-scope="scope">
              <el-tag :type="getBehaviorTagType(scope.row.behavior)">
                {{ getBehaviorText(scope.row.behavior) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="warmUpPeriod" label="预热时长(s)" width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.behavior === 1">{{ scope.row.warmUpPeriod }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="queueTimeout" label="排队超时(ms)" width="130">
            <template slot-scope="scope">
              <span v-if="scope.row.behavior === 2">{{ scope.row.queueTimeout }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" @click="editRule(scope.row)">编辑</el-button>
              <el-button
                size="mini"
                :type="scope.row.status === 1 ? 'warning' : 'success'"
                @click="toggleStatus(scope.row)"
              >
                {{ scope.row.status === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button size="mini" type="danger" @click="deleteRule(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          >
          </el-pagination>
        </div>

        <!-- 新增/编辑对话框 -->
        <el-dialog
          :title="isEdit ? '编辑IP规则' : '新增IP规则'"
          :visible.sync="dialogVisible"
          width="600px"
          :close-on-click-modal="false"
        >
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            :rules="computedFormRules"
            label-width="120px"
          >
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称" />
            </el-form-item>
            <el-form-item label="IP类型" prop="ipType">
              <el-radio-group v-model="ruleForm.ipType">
                <el-radio label="SINGLE_IP">单个IP</el-radio>
                <el-radio label="IP_RANGE">IP范围</el-radio>
                <el-radio label="CIDR">CIDR网段</el-radio>
                <el-radio label="WILDCARD">通配符</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="IP地址" prop="ipValue">
              <el-input
                v-model="ruleForm.ipValue"
                :placeholder="getIpPlaceholder()"
              />
              <div class="form-tip">
                <span v-if="ruleForm.ipType === 'SINGLE_IP'">示例：*************</span>
                <span v-else-if="ruleForm.ipType === 'IP_RANGE'">示例：***********-*************</span>
                <span v-else-if="ruleForm.ipType === 'CIDR'">示例：***********/24</span>
                <span v-else-if="ruleForm.ipType === 'WILDCARD'">示例：192.168.1.*</span>
              </div>
            </el-form-item>
            <!-- IP限流为全局生效，适用于匹配的IP地址 -->
            <div class="global-rule-notice">
              <el-alert
                title="IP限流规则"
                description="此IP限流规则将应用于匹配的IP地址的所有API请求"
                type="info"
                :closable="false"
                show-icon>
              </el-alert>
            </div>

            <el-form-item label="限制模式">
              <el-select v-model="ruleForm.limitMode" style="width: 100%" @change="onLimitModeChange">
                <el-option label="QPS模式" :value="0" />
                <el-option label="并发线程数模式" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item 
              v-if="ruleForm.limitMode === 0" 
              label="QPS限制" 
              prop="limitCount"
            >
              <el-input-number
                v-model="ruleForm.limitCount"
                :min="1"
                :max="999999"
                placeholder="请输入QPS限制"
                style="width: 100%"
              />
              <div class="form-tip">设置每秒允许通过的请求数量</div>
            </el-form-item>
            <el-form-item 
              v-if="ruleForm.limitMode === 1" 
              label="并发限制" 
              prop="limitCount"
            >
              <el-input-number
                v-model="ruleForm.limitCount"
                :min="1"
                :max="999999"
                placeholder="请输入并发线程数限制"
                style="width: 100%"
              />
              <div class="form-tip">设置同时处理请求的最大线程数</div>
            </el-form-item>
            <el-form-item label="流控效果" prop="behavior">
              <el-select v-model="ruleForm.behavior" style="width: 100%">
                <el-option label="快速失败" :value="0" />
                <el-option label="Warm Up" :value="1" />
                <el-option label="排队等待" :value="2" />
                <el-option label="Warm Up + 排队等待" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="ruleForm.behavior === 1 || ruleForm.behavior === 3"
              label="预热时长(秒)"
              prop="warmUpPeriod"
            >
              <el-input-number
                v-model="ruleForm.warmUpPeriod"
                :min="1"
                :max="3600"
                placeholder="预热时长"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item
              v-if="ruleForm.behavior === 2 || ruleForm.behavior === 3"
              label="排队超时(毫秒)"
              prop="queueTimeout"
            >
              <el-input-number
                v-model="ruleForm.queueTimeout"
                :min="1"
                :max="60000"
                placeholder="排队超时时间"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="生效时间">
              <el-date-picker
                v-model="ruleForm.effectiveTime"
                type="datetime"
                placeholder="选择生效时间"
                style="width: 100%"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="过期时间">
              <el-date-picker
                v-model="ruleForm.expiryTime"
                type="datetime"
                placeholder="选择过期时间"
                style="width: 100%"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-switch
                v-model="ruleForm.status"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" :loading="submitting" @click="submitForm">
              {{ isEdit ? '更新' : '创建' }}
            </el-button>
          </div>
        </el-dialog>
      </div>
    </layout>
  </div>
</template>

<script>
import Layout from '@/components/Layout.vue'
import { ipRuleApi } from '@/api/ipRule'
import validationRules from '@/utils/validation'

export default {
  name: 'IpFlowRules',
  components: {
    Layout
  },
  beforeCreate() {
    // 注册验证规则到Vue实例
    this.$validation = validationRules;
  },
  data() {
    return {
      loading: false,
      submitting: false,
      dialogVisible: false,
      isEdit: false,
      tableData: [],
      searchForm: {
        keyword: ''
      },
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },
      ruleForm: {
        ruleName: '',
        tenantId: 'default',
        ipType: 'SINGLE_IP',
        ipValue: '',
        limitMode: 0,
        limitCount: null,
        behavior: 0,
        warmUpPeriod: null,
        queueTimeout: null,
        effectiveTime: null,
        expiryTime: null,
        status: 1
      },
      formRules: {}
    }
  },
  computed: {
    // 动态计算表单验证规则
    computedFormRules() {
      const rules = {
        ruleName: this.$validation.ipFlowRuleRules.ruleName,
        ipType: this.$validation.ipFlowRuleRules.ruleType,
        ipValue: this.$validation.ipFlowRuleRules.ipValue,
        limitMode: this.$validation.ipFlowRuleRules.limitMode || this.$validation.commonRules.status,
        limitCount: this.$validation.ipFlowRuleRules.qpsLimit,
        behavior: this.$validation.ipFlowRuleRules.flowBehavior,
        warmUpPeriod: this.$validation.ipFlowRuleRules.warmUpPeriod,
        queueTimeout: this.$validation.ipFlowRuleRules.queueTimeout
      };
      
      // 根据控制行为动态调整验证规则
      if (this.ruleForm.behavior === 1 || this.ruleForm.behavior === 3) {
        // Warm Up 或 Warm Up + 排队等待模式需要预热时长
        rules.warmUpPeriod = this.$validation.ipFlowRuleRules.warmUpPeriod;
      } else {
        // 其他模式不需要预热时长
        rules.warmUpPeriod = [];
      }
      
      if (this.ruleForm.behavior === 2 || this.ruleForm.behavior === 3) {
        // 排队等待 或 Warm Up + 排队等待模式需要排队超时时间
        rules.queueTimeout = this.$validation.ipFlowRuleRules.queueTimeout;
      } else {
        // 其他模式不需要排队超时时间
        rules.queueTimeout = [];
      }
      
      return rules;
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          keyword: this.searchForm.keyword
        }
        const response = await ipRuleApi.getIpRuleList(params)
        this.tableData = response.data.records || []
        this.pagination.total = response.data.total || 0
      } catch (error) {
        this.$message.error('加载数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      this.pagination.current = 1
      this.loadData()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadData()
    },
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadData()
    },
    showAddDialog() {
      this.isEdit = false
      this.resetForm()
      this.dialogVisible = true
    },
    editRule(row) {
      this.isEdit = true
      this.ruleForm = { ...row }
      this.dialogVisible = true
    },
    async toggleStatus(row) {
      try {
        const newStatus = row.status === 1 ? 0 : 1
        await ipRuleApi.updateIpRuleStatus(row.id, newStatus)
        this.$message.success('状态更新成功')
        this.loadData()
      } catch (error) {
        this.$message.error('状态更新失败：' + error.message)
      }
    },
    async deleteRule(row) {
      try {
        await this.$confirm('确定要删除这条IP规则吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await ipRuleApi.deleteIpRule(row.id)
        this.$message.success('删除成功')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败：' + error.message)
        }
      }
    },
    async submitForm() {
      try {
        await this.$refs.ruleForm.validate()
        this.submitting = true
        
        // 构建提交数据，将前端字段映射为后端期望的字段
        const submitData = {
          ...this.ruleForm,
          ruleType: this.ruleForm.ipType, // 将 ipType 映射为 ruleType
          tenantId: this.ruleForm.tenantId || 'default', // 设置默认租户ID
          ipValue: this.ruleForm.ipValue,
          qpsLimit: this.ruleForm.limitCount,
          listType: 'LIMIT' // 设置默认列表类型
        }
        
        // 移除前端特有的字段
        delete submitData.ipType
        delete submitData.limitMode
        delete submitData.limitCount
        delete submitData.behavior
        delete submitData.warmUpPeriod
        delete submitData.queueTimeout
        delete submitData.effectiveTime
        delete submitData.expiryTime
        
        if (this.isEdit) {
          await ipRuleApi.updateIpRule(this.ruleForm.id, submitData)
          this.$message.success('更新成功')
        } else {
          await ipRuleApi.createIpRule(submitData)
          this.$message.success('创建成功')
        }
        
        this.dialogVisible = false
        this.loadData()
      } catch (error) {
        if (error.message) {
          this.$message.error('操作失败：' + error.message)
        }
      } finally {
        this.submitting = false
      }
    },
    onLimitModeChange() {
      // 切换限制模式时清空限制数量
      this.ruleForm.limitCount = null
      // 清除表单验证错误
      this.$nextTick(() => {
        if (this.$refs.ruleForm) {
          this.$refs.ruleForm.clearValidate(['limitCount'])
        }
      })
    },
    resetForm() {
      this.ruleForm = {
        ruleName: '',
        tenantId: 'default',
        ipType: 'SINGLE_IP',
        ipValue: '',
        limitMode: 0,
        limitCount: null,
        behavior: 0,
        warmUpPeriod: null,
        queueTimeout: null,
        effectiveTime: null,
        expiryTime: null,
        status: 1
      }
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate()
      }
    },
    getIpPlaceholder() {
      const placeholders = {
        'SINGLE_IP': '请输入单个IP地址，如：*************',
        'IP_RANGE': '请输入IP范围，如：***********-*************',
        'CIDR': '请输入CIDR网段，如：***********/24',
        'WILDCARD': '请输入通配符IP，如：192.168.1.*'
      }
      return placeholders[this.ruleForm.ipType] || '请输入IP地址'
    },
    getIpTypeText(ipType) {
      const map = {
        'SINGLE_IP': '单IP',
        'IP_RANGE': '范围',
        'CIDR': 'CIDR',
        'WILDCARD': '通配符'
      }
      return map[ipType] || '未知'
    },
    getBehaviorText(behavior) {
      const map = {
        0: '快速失败',
        1: '预热',
        2: '排队等待'
      }
      return map[behavior] || '未知'
    },
    getBehaviorTagType(behavior) {
      const map = {
        0: 'danger',
        1: 'warning',
        2: 'info'
      }
      return map[behavior] || ''
    },
    validateIpValue(rule, value, callback) {
      if (!value) {
        callback(new Error('请输入IP地址'))
        return
      }
      
      const ipType = this.ruleForm.ipType
      const ipRegex = {
        'SINGLE_IP': /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
        'IP_RANGE': /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)-((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
        'CIDR': /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)\/([0-9]|[1-2][0-9]|3[0-2])$/,
        'WILDCARD': /^((25[0-5]|2[0-4]\d|[01]?\d\d?|\*)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?|\*)$/
      }
      
      if (!ipRegex[ipType] || !ipRegex[ipType].test(value)) {
        callback(new Error('IP地址格式不正确'))
        return
      }
      
      callback()
    },
    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style scoped>
.ip-flow-rules {
  height: 100%;
}

.page-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  width: 100%;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.text-muted {
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.global-rule-notice {
  margin: 16px 0;
}

@media (max-width: 768px) {
  .operation-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    flex-direction: column;
    width: 100%;
  }
  
  .filter-group .el-select,
  .filter-group .el-input {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 10px;
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>