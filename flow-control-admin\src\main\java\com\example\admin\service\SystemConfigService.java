package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.SystemConfigDTO;
import com.example.common.entity.SystemConfig;
import com.example.admin.vo.SystemConfigVO;

import java.util.List;
import java.util.Map;

/**
 * 系统配置服务接口
 */
public interface SystemConfigService extends IService<SystemConfig> {
    
    /**
     * 分页查询系统配置
     *
     * @param page 分页参数
     * @param configKey 配置键
     * @param configType 配置类型
     * @param tenantId 租户ID
     * @param status 状态
     * @return 系统配置VO分页结果
     */
    Page<SystemConfigVO> selectSystemConfigPage(Page<SystemConfigVO> page, String configKey, String configType,
                                                String tenantId, Integer status);
    
    /**
     * 根据ID查询系统配置详情
     *
     * @param id 配置ID
     * @return 系统配置VO
     */
    SystemConfigVO getSystemConfigById(Long id);
    
    /**
     * 根据配置键查询系统配置
     *
     * @param configKey 配置键
     * @param tenantId 租户ID（可选）
     * @return 系统配置VO
     */
    SystemConfigVO getSystemConfigByKey(String configKey, String tenantId);
    
    /**
     * 创建系统配置
     *
     * @param systemConfigDTO 系统配置DTO
     * @return 是否成功
     */
    boolean createSystemConfig(SystemConfigDTO systemConfigDTO);
    
    /**
     * 更新系统配置
     *
     * @param id 配置ID
     * @param systemConfigDTO 系统配置DTO
     * @return 是否成功
     */
    boolean updateSystemConfig(Long id, SystemConfigDTO systemConfigDTO);
    
    /**
     * 删除系统配置
     *
     * @param id 配置ID
     * @return 是否成功
     */
    boolean deleteSystemConfig(Long id);
    
    /**
     * 批量删除系统配置
     *
     * @param ids 配置ID列表
     * @return 是否成功
     */
    boolean batchDeleteSystemConfigs(List<Long> ids);
    
    /**
     * 启用系统配置
     *
     * @param id 配置ID
     * @return 是否成功
     */
    boolean enableSystemConfig(Long id);
    
    /**
     * 禁用系统配置
     *
     * @param id 配置ID
     * @return 是否成功
     */
    boolean disableSystemConfig(Long id);
    
    /**
     * 批量更新配置状态
     *
     * @param ids 配置ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Long> ids, Integer status);
    
    /**
     * 批量更新配置状态（内部方法）
     *
     * @param ids 配置ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateConfigStatus(List<Long> ids, Integer status);
    
    /**
     * 根据配置类型查询系统配置
     *
     * @param configType 配置类型
     * @param tenantId 租户ID（可选）
     * @param status 状态（可选）
     * @param limit 限制数量
     * @return 系统配置列表
     */
    List<SystemConfigVO> getSystemConfigsByType(String configType, String tenantId, Integer status, Integer limit);
    
    /**
     * 根据租户ID查询系统配置
     *
     * @param tenantId 租户ID
     * @param configType 配置类型（可选）
     * @param status 状态（可选）
     * @param limit 限制数量
     * @return 系统配置列表
     */
    List<SystemConfigVO> getSystemConfigsByTenantId(String tenantId, String configType, Integer status, Integer limit);
    
    /**
     * 批量操作系统配置
     *
     * @param systemConfigDTOList 系统配置DTO列表
     * @param operation 操作类型（create, update, delete）
     * @return 操作结果
     */
    Map<String, Object> batchOperateSystemConfigs(List<SystemConfigDTO> systemConfigDTOList, String operation);
    
    /**
     * 检查配置键是否存在
     *
     * @param configKey 配置键
     * @param tenantId 租户ID（可选）
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByConfigKey(String configKey, String tenantId, Long excludeId);
    
    /**
     * 更新配置状态
     *
     * @param id 配置ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateConfigStatus(Long id, Integer status);
    
    /**
     * 统计系统配置数量
     *
     * @param configType 配置类型（可选）
     * @param tenantId 租户ID（可选）
     * @param status 状态（可选）
     * @return 配置数量
     */
    Long countSystemConfigs(String configType, String tenantId, Integer status);
    
    /**
     * 统计配置类型分布
     *
     * @param tenantId 租户ID（可选）
     * @return 配置类型统计结果
     */
    List<Map<String, Object>> getConfigTypeStatistics(String tenantId);
    
    /**
     * 统计配置状态分布
     *
     * @param tenantId 租户ID（可选）
     * @param configType 配置类型（可选）
     * @return 配置状态统计结果
     */
    List<Map<String, Object>> getConfigStatusStatistics(String tenantId, String configType);
    
    /**
     * 获取系统默认配置
     *
     * @param configType 配置类型（可选）
     * @param limit 限制数量
     * @return 系统默认配置列表
     */
    List<SystemConfigVO> getSystemDefaultConfigs(String configType, Integer limit);
    
    /**
     * 获取租户特定配置
     *
     * @param tenantId 租户ID
     * @param configType 配置类型（可选）
     * @param limit 限制数量
     * @return 租户特定配置列表
     */
    List<SystemConfigVO> getTenantSpecificConfigs(String tenantId, String configType, Integer limit);
    
    /**
     * 获取流控配置
     *
     * @param tenantId 租户ID（可选）
     * @param limit 限制数量
     * @return 流控配置列表
     */
    List<SystemConfigVO> getFlowControlConfigs(String tenantId, Integer limit);
    
    /**
     * 获取监控配置
     *
     * @param tenantId 租户ID（可选）
     * @param limit 限制数量
     * @return 监控配置列表
     */
    List<SystemConfigVO> getMonitorConfigs(String tenantId, Integer limit);
    
    /**
     * 获取告警配置
     *
     * @param tenantId 租户ID（可选）
     * @param limit 限制数量
     * @return 告警配置列表
     */
    List<SystemConfigVO> getAlarmConfigs(String tenantId, Integer limit);
    
    /**
     * 批量插入或更新配置
     *
     * @param systemConfigDTOList 系统配置DTO列表
     * @return 是否成功
     */
    boolean batchInsertOrUpdateConfigs(List<SystemConfigDTO> systemConfigDTOList);
    
    /**
     * 重置配置为默认值
     *
     * @param configKey 配置键
     * @param tenantId 租户ID（可选）
     * @return 是否成功
     */
    boolean resetConfigToDefault(String configKey, String tenantId);
    
    /**
     * 获取配置历史记录
     *
     * @param configKey 配置键
     * @param tenantId 租户ID（可选）
     * @param limit 限制数量
     * @return 配置历史记录列表
     */
    List<Map<String, Object>> getConfigHistory(String configKey, String tenantId, Integer limit);
    
    /**
     * 导出系统配置
     *
     * @param configType 配置类型（可选）
     * @param tenantId 租户ID（可选）
     * @param status 状态（可选）
     * @return 系统配置列表
     */
    List<SystemConfigVO> exportSystemConfigs(String configType, String tenantId, Integer status);
    
    /**
     * 导入系统配置
     *
     * @param systemConfigDTOList 系统配置DTO列表
     * @param overwrite 是否覆盖已存在的配置
     * @return 导入结果
     */
    Map<String, Object> importSystemConfigs(List<SystemConfigDTO> systemConfigDTOList, boolean overwrite);
    
    /**
     * 验证配置值格式
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @param configType 配置类型
     * @return 验证结果
     */
    Map<String, Object> validateConfigValue(String configKey, String configValue, String configType);
    
    /**
     * 获取配置值（带类型转换）
     *
     * @param configKey 配置键
     * @param tenantId 租户ID（可选）
     * @param defaultValue 默认值
     * @param targetType 目标类型
     * @return 配置值
     */
    <T> T getConfigValue(String configKey, String tenantId, T defaultValue, Class<T> targetType);
    
    /**
     * 设置配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @param tenantId 租户ID（可选）
     * @param configType 配置类型
     * @return 是否成功
     */
    boolean setConfigValue(String configKey, String configValue, String tenantId, String configType);
    
    /**
     * 刷新配置缓存
     *
     * @param configKey 配置键（可选，为空则刷新所有）
     * @param tenantId 租户ID（可选）
     * @return 是否成功
     */
    boolean refreshConfigCache(String configKey, String tenantId);
    
    /**
     * 获取配置缓存统计
     *
     * @return 缓存统计信息
     */
    Map<String, Object> getConfigCacheStatistics();
    
    /**
     * 同步配置到其他节点
     *
     * @param configKey 配置键（可选）
     * @param tenantId 租户ID（可选）
     * @return 同步结果
     */
    Map<String, Object> syncConfigToNodes(String configKey, String tenantId);
    
    /**
     * 备份系统配置
     *
     * @param tenantId 租户ID（可选）
     * @param configType 配置类型（可选）
     * @return 备份结果
     */
    Map<String, Object> backupSystemConfigs(String tenantId, String configType);
    
    /**
     * 恢复系统配置
     *
     * @param backupId 备份ID
     * @param tenantId 租户ID（可选）
     * @return 恢复结果
     */
    Map<String, Object> restoreSystemConfigs(String backupId, String tenantId);
    
    /**
     * 获取配置变更通知
     *
     * @param configKey 配置键
     * @param tenantId 租户ID（可选）
     * @param callback 回调函数
     * @return 是否成功
     */
    boolean subscribeConfigChange(String configKey, String tenantId, Runnable callback);
    
    /**
     * 取消配置变更通知
     *
     * @param configKey 配置键
     * @param tenantId 租户ID（可选）
     * @return 是否成功
     */
    boolean unsubscribeConfigChange(String configKey, String tenantId);
}