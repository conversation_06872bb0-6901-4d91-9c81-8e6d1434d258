<template>
	<el-dialog
		:title="isEdit ? '编辑IP规则' : '新增IP规则'"
		:visible.sync="dialogVisible"
		width="600px"
		@close="handleClose"
	>
		<el-form
			:model="formData"
			:rules="rules"
			ref="ipRuleForm"
			label-width="120px"
		>
			<el-form-item label="规则名称" prop="ruleName">
				<el-input
					v-model="formData.ruleName"
					placeholder="请输入规则名称"
				/>
			</el-form-item>

			<el-form-item label="IP地址" prop="ipValue">
				<el-input
					v-model="formData.ipValue"
					placeholder="请输入IP地址或IP段，如：*********** 或 ***********/24"
				/>
				<div class="form-tip">
					支持单个IP地址（如：***********）或IP段（如：***********/24）
				</div>
			</el-form-item>

			<el-form-item label="列表类型" prop="listType">
				<el-radio-group v-model="formData.listType">
					<el-radio label="LIMIT">黑名单（限制访问）</el-radio>
					<el-radio label="ALLOW">白名单（允许访问）</el-radio>
				</el-radio-group>
				<div class="form-tip">
					黑名单：限制该IP的访问；白名单：只允许该IP访问
				</div>
			</el-form-item>

			<el-divider content-position="left">流量控制配置</el-divider>

			<el-form-item
				v-if="formData.listType === 'LIMIT'"
				label="限流模式"
				prop="limitMode"
			>
				<el-radio-group v-model="formData.limitMode">
					<el-radio :label="0">QPS限流</el-radio>
					<el-radio :label="1">并发数限流</el-radio>
				</el-radio-group>
				<div class="form-tip">
					QPS限流：限制每秒请求数量；并发数限流：限制同时处理的请求数量
				</div>
			</el-form-item>

			<el-form-item
				v-if="formData.listType === 'LIMIT'"
				label="限制数量"
				prop="limitCount"
			>
				<el-input-number
					v-model="formData.limitCount"
					:min="1"
					:max="999999"
					style="width: 200px"
				/>
				<span class="unit-text">
					{{ formData.limitMode === 0 ? '次/秒' : '个' }}
				</span>
				<div class="form-tip">
					设置该IP的访问限制，超出限制将被拒绝
				</div>
			</el-form-item>

			<el-form-item label="描述" prop="description">
				<el-input
					v-model="formData.description"
					type="textarea"
					:rows="3"
					placeholder="请输入规则描述"
				/>
			</el-form-item>

			<el-form-item label="状态" prop="status">
				<el-radio-group v-model="formData.status">
					<el-radio :label="1">启用</el-radio>
					<el-radio :label="0">禁用</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>

		<div slot="footer" class="dialog-footer">
			<el-button @click="handleClose">取消</el-button>
			<el-button
				type="primary"
				@click="handleSubmit"
				:loading="submitting"
			>
				{{ isEdit ? '更新' : '创建' }}
			</el-button>
		</div>
	</el-dialog>
</template>

<script>
export default {
	name: 'IpRuleDialog',
	props: {
		visible: {
			type: Boolean,
			default: false,
		},
		form: {
			type: Object,
			default: () => ({}),
		},
		isEdit: {
			type: Boolean,
			default: false,
		},
		submitting: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			formData: {
				ruleName: '',
				ipValue: '',
				listType: 'LIMIT',
				limitMode: 0,
				limitCount: null,
				description: '',
				status: 1,
			},
			rules: {
				ruleName: [
					{ required: true, message: '请输入规则名称', trigger: 'blur' },
					{ min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' },
				],
				ipValue: [
					{ required: true, message: '请输入IP地址', trigger: 'blur' },
					{ validator: this.validateIpValue, trigger: 'blur' },
				],
				limitCount: [
					{
						type: 'number',
						min: 1,
						message: '限制数量必须大于0',
						trigger: 'blur',
						required: false,
					},
				],
				description: [
					{ max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' },
				],
			},
		};
	},
	computed: {
		dialogVisible: {
			get() {
				return this.visible;
			},
			set(value) {
				if (!value) {
					this.$emit('close');
				}
			},
		},
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.formData = { ...this.form };
				this.$nextTick(() => {
					if (this.$refs.ipRuleForm) {
						this.$refs.ipRuleForm.clearValidate();
					}
				});
			}
		},

		// 监听列表类型变化，清空限制相关字段
		'formData.listType'(newVal) {
			if (newVal === 'ALLOW') {
				this.formData.limitMode = 0;
				this.formData.limitCount = null;
			}
		},
	},
	methods: {
		// 验证IP地址格式
		validateIpValue(rule, value, callback) {
			if (!value) {
				callback(new Error('请输入IP地址'));
				return;
			}

			// 检查是否是IP段格式（CIDR）
			const cidrPattern = /^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\/(\d{1,2})$/;
			const ipPattern = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;

			let isValid = false;

			if (cidrPattern.test(value)) {
				// CIDR格式验证
				const matches = value.match(cidrPattern);
				const ip = matches[1];
				const prefix = parseInt(matches[2]);

				if (prefix >= 0 && prefix <= 32 && this.isValidIp(ip)) {
					isValid = true;
				}
			} else if (ipPattern.test(value)) {
				// 单个IP地址验证
				isValid = this.isValidIp(value);
			}

			if (isValid) {
				callback();
			} else {
				callback(new Error('请输入有效的IP地址或IP段（如：*********** 或 ***********/24）'));
			}
		},

		// 验证IP地址各段是否有效
		isValidIp(ip) {
			const parts = ip.split('.');
			if (parts.length !== 4) return false;

			return parts.every(part => {
				const num = parseInt(part);
				return num >= 0 && num <= 255 && part === num.toString();
			});
		},

		handleClose() {
			this.$emit('close');
		},

		handleSubmit() {
			this.$refs.ipRuleForm.validate((valid) => {
				if (valid) {
					const submitData = { ...this.formData };
					
					// 黑名单模式需要设置限制数量
					if (submitData.listType === 'LIMIT' && !submitData.limitCount) {
						this.$message.error('黑名单模式需要设置限制数量');
						return;
					}
					
					// 白名单模式清空限制相关字段
					if (submitData.listType === 'ALLOW') {
						submitData.limitMode = 0;
						submitData.limitCount = null;
					}
					
					this.$emit('submit', submitData);
				} else {
					return false;
				}
			});
		},
	},
};
</script>

<style scoped>
.form-tip {
	color: #909399;
	font-size: 12px;
	margin-top: 5px;
	line-height: 1.4;
}

.unit-text {
	margin-left: 10px;
	color: #909399;
	font-size: 14px;
}

.dialog-footer {
	text-align: right;
}

.el-divider {
	margin: 20px 0;
}
</style>