package com.example.admin.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器 - 用于验证限流功能
 */
@RestController
@RequestMapping("/api")
public class TestController {

    @GetMapping("/test")
    public Map<String, Object> test(@RequestParam(value = "sleep", defaultValue = "0") Double sleepSeconds) {
        long startTime = System.currentTimeMillis();
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 如果指定了sleep参数，则模拟长时间处理
            if (sleepSeconds > 0) {
                Thread.sleep((long) (sleepSeconds * 1000));
            }
            
            long endTime = System.currentTimeMillis();
            double actualProcessingTime = (endTime - startTime) / 1000.0;
            
            result.put("message", "Test endpoint is working");
            result.put("timestamp", endTime);
            result.put("status", "success");
            result.put("sleepSeconds", sleepSeconds);
            result.put("actualProcessingTime", actualProcessingTime);
            result.put("threadId", Thread.currentThread().getId());
            result.put("threadName", Thread.currentThread().getName());
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            long endTime = System.currentTimeMillis();
            double actualProcessingTime = (endTime - startTime) / 1000.0;
            
            result.put("message", "Request was interrupted");
            result.put("timestamp", endTime);
            result.put("status", "interrupted");
            result.put("sleepSeconds", sleepSeconds);
            result.put("actualProcessingTime", actualProcessingTime);
            result.put("error", "Thread was interrupted during sleep");
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            double actualProcessingTime = (endTime - startTime) / 1000.0;
            
            result.put("message", "Request failed");
            result.put("timestamp", endTime);
            result.put("status", "error");
            result.put("sleepSeconds", sleepSeconds);
            result.put("actualProcessingTime", actualProcessingTime);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @GetMapping("/admin/users")
    public Map<String, Object> getUsers() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "Admin users endpoint");
        result.put("timestamp", System.currentTimeMillis());
        result.put("users", new String[]{"admin", "user1", "user2"});
        return result;
    }

    @GetMapping("/admin/ip-rules")
    public Map<String, Object> getIpRules() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "IP rules endpoint");
        result.put("timestamp", System.currentTimeMillis());
        result.put("rules", new String[]{"*************", "*************"});
        return result;
    }
}
