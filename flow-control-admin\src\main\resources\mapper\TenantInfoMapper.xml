<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.TenantInfoMapper">

	<!-- 结果映射 -->
	<resultMap id="BaseResultMap" type="com.example.common.entity.TenantInfo">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
		<result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="create_by" property="createBy" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="deleted" property="deleted" jdbcType="INTEGER"/>
	</resultMap>

	<!-- 基础字段 -->
	<sql id="Base_Column_List">
                id, tenant_id, tenant_name, status, create_by, create_time, update_by, update_time, deleted
	</sql>

	<!-- 分页查询租户信息 -->
	<select id="selectTenantInfoPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tenant_info
        WHERE deleted = 0
	<if test="tenantName != null and tenantName != ''">
            AND tenant_name LIKE CONCAT('%', #{tenantName}, '%')
	</if>
	<if test="status != null">
            AND status = #{status}
	</if>
        ORDER BY create_time DESC
</select>

<!-- 根据租户ID查询租户信息 -->
<select id="selectByTenantId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tenant_info
        WHERE tenant_id = #{tenantId} AND deleted = 0
        LIMIT 1
</select>

<!-- 根据租户名称查询租户信息（用于重名检查） -->
<select id="selectByTenantName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tenant_info
        WHERE tenant_name = #{tenantName} AND deleted = 0
<if test="excludeId != null">
            AND id != #{excludeId}
</if>
        LIMIT 1
</select>

<!-- 查询所有启用的租户 -->
<select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tenant_info
        WHERE status = 1 AND deleted = 0
        ORDER BY create_time DESC
</select>

<!-- 批量更新租户状态 -->
<update id="batchUpdateStatus">
        UPDATE tenant_info
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE id IN
<foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
</foreach>
        AND deleted = 0
</update>

<!-- 统计租户总数 -->
<select id="countTotal" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM tenant_info
        WHERE deleted = 0
</select>

<!-- 统计启用的租户数量 -->
<select id="countEnabled" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM tenant_info
        WHERE status = 1 AND deleted = 0
</select>

<!-- 统计各种状态的租户数量 -->
<select id="selectStatusStatistics" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count,
            CASE status
                WHEN 0 THEN '禁用'
                WHEN 1 THEN '启用'
                ELSE '未知'
            END as status_name
        FROM tenant_info
        WHERE deleted = 0
        GROUP BY status
        ORDER BY status
</select>

<!-- 查询租户概览信息（包含规则数量等统计信息） -->
<select id="selectTenantOverview" resultType="java.util.Map">
        SELECT 
            ti.tenant_id,
            ti.tenant_name,
            ti.status,
            ti.create_time,
            COALESCE(fr.flow_rule_count, 0) as flow_rule_count,
            COALESCE(ifr.ip_rule_count, 0) as ip_rule_count,
            COALESCE(ms.monitor_count, 0) as monitor_count
        FROM tenant_info ti
        LEFT JOIN (
            SELECT tenant_id, COUNT(*) as flow_rule_count
            FROM flow_rule
            WHERE deleted = 0
            GROUP BY tenant_id
        ) fr ON ti.tenant_id = fr.tenant_id
        LEFT JOIN (
            SELECT tenant_id, COUNT(*) as ip_rule_count
            FROM ip_flow_rule
            WHERE deleted = 0
            GROUP BY tenant_id
        ) ifr ON ti.tenant_id = ifr.tenant_id
        LEFT JOIN (
            SELECT tenant_id, COUNT(*) as monitor_count
            FROM monitor_statistics
            WHERE stat_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            GROUP BY tenant_id
        ) ms ON ti.tenant_id = ms.tenant_id
        WHERE ti.deleted = 0
        ORDER BY ti.create_time DESC
</select>



<!-- 查询最近创建的租户 -->
<select id="selectRecentlyCreated" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tenant_info
        WHERE deleted = 0
        ORDER BY create_time DESC
        LIMIT #{limit}
</select>

<!-- 查询最近更新的租户 -->
<select id="selectRecentlyUpdated" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tenant_info
        WHERE deleted = 0
        ORDER BY update_time DESC
        LIMIT #{limit}
</select>

<!-- 检查租户ID是否存在 -->
<select id="existsByTenantId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM tenant_info
        WHERE tenant_id = #{tenantId} AND deleted = 0
<if test="excludeId != null">
            AND id != #{excludeId}
</if>
</select>

<!-- 检查租户名称是否存在 -->
<select id="existsByTenantName" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM tenant_info
        WHERE tenant_name = #{tenantName} AND deleted = 0
<if test="excludeId != null">
            AND id != #{excludeId}
</if>
</select>

</mapper>