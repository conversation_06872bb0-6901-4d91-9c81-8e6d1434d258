{"version": "0.2.0", "configurations": [{"type": "java", "name": "Current File", "request": "launch", "mainClass": "${file}"}, {"type": "java", "name": "Gateway Service", "request": "launch", "mainClass": "com.example.gateway.GatewayApplication", "projectName": "gateway-service"}, {"type": "java", "name": "Admin Service", "request": "launch", "mainClass": "com.example.admin.AdminApplication", "projectName": "flow-control-admin"}]}