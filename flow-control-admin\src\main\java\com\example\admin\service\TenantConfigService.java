package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.TenantConfigDTO;
import com.example.common.entity.TenantConfig;
import com.example.admin.vo.TenantConfigVO;

import java.util.List;

/**
 * 租户配置服务接口
 */
public interface TenantConfigService extends IService<TenantConfig> {
    
    /**
     * 分页查询租户配置
     *
     * @param page 分页参数
     * @param tenantName 租户名称
     * @param status 状态
     * @return 租户配置VO分页结果
     */
    Page<TenantConfigVO> selectTenantConfigPage(Page<TenantConfigVO> page, String tenantName, Integer status);
    
    /**
     * 根据ID查询租户配置详情
     *
     * @param id 租户ID
     * @return 租户配置VO
     */
    TenantConfigVO getTenantConfigById(Long id);
    
    /**
     * 根据租户ID查询租户配置
     *
     * @param tenantId 租户ID
     * @return 租户配置VO
     */
    TenantConfigVO getTenantConfigByTenantId(String tenantId);
    
    /**
     * 创建租户配置
     *
     * @param tenantConfigDTO 租户配置DTO
     * @return 是否成功
     */
    boolean createTenantConfig(TenantConfigDTO tenantConfigDTO);
    
    /**
     * 更新租户配置
     *
     * @param id 租户ID
     * @param tenantConfigDTO 租户配置DTO
     * @return 是否成功
     */
    boolean updateTenantConfig(Long id, TenantConfigDTO tenantConfigDTO);
    
    /**
     * 删除租户配置
     *
     * @param id 租户ID
     * @return 是否成功
     */
    boolean deleteTenantConfig(Long id);
    
    /**
     * 批量删除租户配置
     *
     * @param ids 租户ID列表
     * @return 是否成功
     */
    boolean batchDeleteTenantConfigs(List<Long> ids);
    
    /**
     * 启用租户
     *
     * @param id 租户ID
     * @return 是否成功
     */
    boolean enableTenant(Long id);
    
    /**
     * 禁用租户
     *
     * @param id 租户ID
     * @return 是否成功
     */
    boolean disableTenant(Long id);
    
    /**
     * 批量更新租户状态
     *
     * @param ids 租户ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Long> ids, Integer status);
    
    /**
     * 检查租户ID是否存在
     *
     * @param tenantId 租户ID
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByTenantId(String tenantId, Long excludeId);
    
    /**
     * 检查租户名称是否存在
     *
     * @param tenantName 租户名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByTenantName(String tenantName, Long excludeId);
    
    /**
     * 统计租户总数
     *
     * @return 租户总数
     */
    int getTotalTenantCount();
    
    /**
     * 获取租户总数
     *
     * @return 租户总数
     */
    long getTenantCount();
    
    /**
     * 统计启用的租户数量
     *
     * @return 启用的租户数量
     */
    int getEnabledTenantCount();
    
    /**
     * 根据状态统计租户数量
     *
     * @param status 状态
     * @return 租户数量
     */
    int countByStatus(Integer status);
    
    /**
     * 根据状态获取租户数量
     *
     * @param status 状态
     * @return 租户数量
     */
    long getTenantCountByStatus(Integer status);
}