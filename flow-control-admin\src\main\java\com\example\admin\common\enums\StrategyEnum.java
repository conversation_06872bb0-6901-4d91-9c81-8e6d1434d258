package com.example.admin.common.enums;

/**
 * 流控策略枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public enum StrategyEnum {
    
    /**
     * 直接策略
     */
    DIRECT(0, "直接", "直接对当前资源进行限流"),
    
    /**
     * 关联策略
     */
    RELATE(1, "关联", "当关联资源达到阈值时，对当前资源进行限流"),
    
    /**
     * 链路策略
     */
    CHAIN(2, "链路", "只对指定链路上的请求进行限流");
    
    /**
     * 策略值
     */
    private final Integer value;
    
    /**
     * 策略名称
     */
    private final String name;
    
    /**
     * 策略描述
     */
    private final String description;
    
    StrategyEnum(Integer value, String name, String description) {
        this.value = value;
        this.name = name;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 根据值获取枚举
     * 
     * @param value 值
     * @return 枚举
     */
    public static StrategyEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (StrategyEnum strategy : values()) {
            if (strategy.getValue().equals(value)) {
                return strategy;
            }
        }
        return null;
    }
    
    /**
     * 检查值是否有效
     * 
     * @param value 值
     * @return 是否有效
     */
    public static boolean isValid(Integer value) {
        return getByValue(value) != null;
    }
}