#!/bin/bash
# 流量控制系统JMeter测试执行脚本
# 作者: 系统管理员
# 适用系统: Linux/Mac

echo "========================================"
echo "流量控制系统JMeter测试执行脚本"
echo "========================================"

# 设置JMeter路径（请根据实际安装路径修改）
JMETER_HOME="/opt/apache-jmeter-5.4.1"
JMETER_BIN="$JMETER_HOME/bin"

# 检查JMeter是否存在
if [ ! -f "$JMETER_BIN/jmeter" ]; then
    echo "错误: 未找到JMeter安装路径，请修改JMETER_HOME变量"
    echo "当前设置: $JMETER_HOME"
    exit 1
fi

# 设置测试参数
TEST_PLAN="flow-control-jmeter-test.jmx"
RESULTS_DIR="jmeter-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建结果目录
mkdir -p "$RESULTS_DIR"

# 设置结果文件路径
RESULTS_FILE="$RESULTS_DIR/flow-control-test-results_$TIMESTAMP.jtl"
LOG_FILE="$RESULTS_DIR/jmeter-test_$TIMESTAMP.log"
HTML_REPORT_DIR="$RESULTS_DIR/html-report_$TIMESTAMP"

echo "测试配置信息:"
echo "- 测试计划: $TEST_PLAN"
echo "- 结果文件: $RESULTS_FILE"
echo "- 日志文件: $LOG_FILE"
echo "- HTML报告: $HTML_REPORT_DIR"
echo

# 检查测试计划文件是否存在
if [ ! -f "$TEST_PLAN" ]; then
    echo "错误: 未找到测试计划文件 $TEST_PLAN"
    exit 1
fi

# 询问用户是否要修改测试参数
echo "默认测试参数:"
echo "- 服务器地址: localhost:8080"
echo "- 测试持续时间: 60秒"
echo "- 每个接口线程数: 20"
echo
read -p "是否要修改测试参数? (y/N): " MODIFY_PARAMS

if [[ "$MODIFY_PARAMS" =~ ^[Yy]$ ]]; then
    read -p "请输入服务器地址 (默认: localhost:8080): " BASE_URL
    read -p "请输入测试持续时间/秒 (默认: 60): " TEST_DURATION
    read -p "请输入每个接口线程数 (默认: 20): " THREAD_COUNT
    
    BASE_URL=${BASE_URL:-localhost:8080}
    TEST_DURATION=${TEST_DURATION:-60}
    THREAD_COUNT=${THREAD_COUNT:-20}
else
    BASE_URL="localhost:8080"
    TEST_DURATION="60"
    THREAD_COUNT="20"
fi

echo
echo "开始执行JMeter测试..."
echo "测试参数: 服务器=$BASE_URL, 持续时间=${TEST_DURATION}秒, 线程数=$THREAD_COUNT"
echo

# 执行JMeter测试（命令行模式）
"$JMETER_BIN/jmeter" -n -t "$TEST_PLAN" \
    -l "$RESULTS_FILE" \
    -j "$LOG_FILE" \
    -JBASE_URL="$BASE_URL" \
    -JTEST_DURATION="$TEST_DURATION" \
    -JTHREAD_COUNT="$THREAD_COUNT"

# 检查测试执行结果
if [ $? -ne 0 ]; then
    echo
    echo "错误: JMeter测试执行失败，错误代码: $?"
    echo "请检查日志文件: $LOG_FILE"
    exit $?
fi

echo
echo "测试执行完成！"
echo

# 生成HTML报告
echo "正在生成HTML测试报告..."
"$JMETER_BIN/jmeter" -g "$RESULTS_FILE" -o "$HTML_REPORT_DIR"

if [ $? -eq 0 ]; then
    echo "HTML报告生成成功: $HTML_REPORT_DIR/index.html"
else
    echo "警告: HTML报告生成失败"
fi

echo
echo "========================================"
echo "测试结果分析"
echo "========================================"

# 分析测试结果
echo "正在分析测试结果..."
echo

# 使用awk分析JTL文件
if [ -f "$RESULTS_FILE" ]; then
    # 跳过标题行，统计总数
    TOTAL_SAMPLES=$(tail -n +2 "$RESULTS_FILE" | wc -l)
    SUCCESS_SAMPLES=$(tail -n +2 "$RESULTS_FILE" | awk -F',' '$8=="true" {count++} END {print count+0}')
    ERROR_SAMPLES=$((TOTAL_SAMPLES - SUCCESS_SAMPLES))
    
    if [ $TOTAL_SAMPLES -gt 0 ]; then
        SUCCESS_RATE=$(echo "scale=2; $SUCCESS_SAMPLES * 100 / $TOTAL_SAMPLES" | bc)
        ERROR_RATE=$(echo "scale=2; $ERROR_SAMPLES * 100 / $TOTAL_SAMPLES" | bc)
        
        echo "测试结果统计:"
        echo "- 总请求数: $TOTAL_SAMPLES"
        echo "- 成功请求数: $SUCCESS_SAMPLES"
        echo "- 失败请求数: $ERROR_SAMPLES"
        echo "- 成功率: ${SUCCESS_RATE}%"
        echo "- 错误率: ${ERROR_RATE}%"
        echo
        
        # 响应码统计
        echo "响应码统计:"
        tail -n +2 "$RESULTS_FILE" | awk -F',' '{
            codes[$4]++
        } END {
            for (code in codes) {
                percentage = codes[code] * 100 / NR
                printf "- HTTP%s: %d次 (%.2f%%)\n", code, codes[code], percentage
            }
        }'
        echo
        
        # 接口请求统计
        echo "接口请求统计:"
        tail -n +2 "$RESULTS_FILE" | awk -F',' '{
            labels[$3]++
            if ($8 == "true") success[$3]++
        } END {
            for (label in labels) {
                succ = success[label] + 0
                fail = labels[label] - succ
                rate = succ * 100 / labels[label]
                printf "- %s: 总计%d, 成功%d, 失败%d, 成功率%.2f%%\n", label, labels[label], succ, fail, rate
            }
        }'
        echo
        
        # 限流效果分析
        RATE_LIMITED=$(tail -n +2 "$RESULTS_FILE" | awk -F',' '$4=="429" {count++} END {print count+0}')
        RATE_LIMIT_RATE=$(echo "scale=2; $RATE_LIMITED * 100 / $TOTAL_SAMPLES" | bc)
        
        echo "限流效果分析:"
        echo "- 被限流请求数: $RATE_LIMITED"
        echo "- 限流率: ${RATE_LIMIT_RATE}%"
        
        if (( $(echo "$RATE_LIMIT_RATE > 0" | bc -l) )); then
            echo "- 限流功能: 正常工作 ✓"
        else
            echo "- 限流功能: 可能未生效，请检查配置 ⚠️"
        fi
    else
        echo "警告: 未找到有效的测试结果数据"
    fi
else
    echo "错误: 未找到结果文件 $RESULTS_FILE"
fi

echo
echo "========================================"
echo "测试完成"
echo "========================================"
echo
echo "测试文件位置:"
echo "- 详细结果: $RESULTS_FILE"
echo "- 执行日志: $LOG_FILE"
echo "- HTML报告: $HTML_REPORT_DIR/index.html"
echo
echo "建议后续操作:"
echo "1. 查看HTML报告了解详细性能数据"
echo "2. 检查日志文件排查任何错误"
echo "3. 根据限流率调整测试参数或系统配置"
echo "4. 对比不同测试运行的结果趋势"
echo

# 询问是否打开HTML报告
read -p "是否要打开HTML测试报告? (y/N): " OPEN_REPORT
if [[ "$OPEN_REPORT" =~ ^[Yy]$ ]]; then
    if [ -f "$HTML_REPORT_DIR/index.html" ]; then
        # 尝试使用系统默认浏览器打开
        if command -v xdg-open > /dev/null; then
            xdg-open "$HTML_REPORT_DIR/index.html"
        elif command -v open > /dev/null; then
            open "$HTML_REPORT_DIR/index.html"
        else
            echo "请手动打开: $HTML_REPORT_DIR/index.html"
        fi
    else
        echo "错误: HTML报告文件不存在"
    fi
fi

echo
echo "测试脚本执行完成！"