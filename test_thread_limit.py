#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
租户线程数限流测试脚本
测试 MultiDimensionFlowFilter 的线程限流功能
"""

import requests
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any
import json

class ThreadLimitTester:
    def __init__(self, gateway_url: str = "http://localhost:8088"):
        self.gateway_url = gateway_url
        self.results = []
        self.lock = threading.Lock()
        
    def send_long_request(self, tenant_id: str, resource: str = "/api/test", duration: float = 3.0) -> Dict[str, Any]:
        """
        发送长时间运行的请求来占用线程
        
        Args:
            tenant_id: 租户ID
            resource: 请求资源路径
            duration: 请求持续时间（秒）
            
        Returns:
            dict: 请求结果
        """
        url = f"{self.gateway_url}{resource}"
        headers = {
            "X-Tenant-ID": tenant_id,
            "Content-Type": "application/json"
        }
        
        # 添加sleep参数让后端服务延迟响应，模拟长时间处理
        params = {"sleep": duration}
        
        start_time = time.time()
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            end_time = time.time()
            
            result = {
                "tenant_id": tenant_id,
                "resource": resource,
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response_time": end_time - start_time,
                "start_time": start_time,
                "end_time": end_time,
                "thread_id": threading.current_thread().ident,
                "error": None
            }
            
            if response.status_code == 429:
                result["limit_type"] = response.headers.get("X-RateLimit-Type", "UNKNOWN")
                result["error"] = "Rate limited"
            elif response.status_code != 200:
                result["error"] = f"HTTP {response.status_code}"
                
        except requests.exceptions.Timeout:
            result = {
                "tenant_id": tenant_id,
                "resource": resource,
                "status_code": 408,
                "success": False,
                "response_time": time.time() - start_time,
                "start_time": start_time,
                "end_time": time.time(),
                "thread_id": threading.current_thread().ident,
                "error": "Timeout"
            }
        except Exception as e:
            result = {
                "tenant_id": tenant_id,
                "resource": resource,
                "status_code": 500,
                "success": False,
                "response_time": time.time() - start_time,
                "start_time": start_time,
                "end_time": time.time(),
                "thread_id": threading.current_thread().ident,
                "error": str(e)
            }
            
        return result
    
    def test_thread_limit(self, tenant_id: str, thread_limit: int, concurrent_threads: int = 10, 
                         request_duration: float = 3.0) -> Dict[str, Any]:
        """
        测试线程数限流
        
        Args:
            tenant_id: 租户ID
            thread_limit: 预期的线程限制数
            concurrent_threads: 并发线程数
            request_duration: 每个请求的持续时间
            
        Returns:
            dict: 测试结果统计
        """
        print(f"\n=== 测试租户 {tenant_id} 的线程数限流 ===")
        print(f"预期线程限制: {thread_limit}")
        print(f"并发线程数: {concurrent_threads}")
        print(f"请求持续时间: {request_duration}秒")
        
        results = []
        
        # 使用线程池并发发送请求
        with ThreadPoolExecutor(max_workers=concurrent_threads) as executor:
            # 提交所有任务
            futures = []
            for i in range(concurrent_threads):
                future = executor.submit(self.send_long_request, tenant_id, "/api/test", request_duration)
                futures.append(future)
            
            # 收集结果
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                    
                    status = "✅ 成功" if result["success"] else f"❌ 失败 ({result['error']})"
                    print(f"线程 {result['thread_id']}: {status}, 响应时间: {result['response_time']:.2f}s")
                    
                except Exception as e:
                    print(f"获取结果时出错: {e}")
        
        # 统计结果
        success_count = sum(1 for r in results if r["success"])
        failed_count = len(results) - success_count
        rate_limited_count = sum(1 for r in results if r.get("status_code") == 429)
        
        stats = {
            "tenant_id": tenant_id,
            "thread_limit": thread_limit,
            "concurrent_threads": concurrent_threads,
            "total_requests": len(results),
            "success_requests": success_count,
            "failed_requests": failed_count,
            "rate_limited_requests": rate_limited_count,
            "success_rate": success_count / len(results) if results else 0,
            "results": results
        }
        
        print(f"\n📊 测试结果统计:")
        print(f"总请求数: {stats['total_requests']}")
        print(f"成功请求: {stats['success_requests']}")
        print(f"失败请求: {stats['failed_requests']}")
        print(f"被限流请求: {stats['rate_limited_requests']}")
        print(f"成功率: {stats['success_rate']:.2%}")
        
        # 验证线程限流是否生效
        if self.validate_thread_limit(stats, thread_limit):
            print("✅ 线程限流验证通过")
        else:
            print("❌ 线程限流验证失败")
            
        return stats
    
    def validate_thread_limit(self, stats: Dict[str, Any], expected_limit: int) -> bool:
        """
        验证线程限流是否按预期工作
        
        Args:
            stats: 测试统计结果
            expected_limit: 预期的线程限制
            
        Returns:
            bool: 验证是否通过
        """
        success_count = stats["success_requests"]
        total_count = stats["total_requests"]
        
        # 对于线程数限流，成功的请求数应该接近限制值
        # 允许一定的误差范围
        tolerance = max(1, expected_limit * 0.2)  # 20%的容差
        
        if abs(success_count - expected_limit) <= tolerance:
            return True
        else:
            print(f"  验证失败: 期望成功请求约{expected_limit}个，实际{success_count}个")
            return False

def main():
    """主测试函数"""
    tester = ThreadLimitTester()
    
    print("🚀 开始租户线程数限流测试")
    print("=" * 60)
    
    # 测试配置：基于数据库中的租户规则
    test_cases = [
        {
            "tenant_id": "tenant1",
            "thread_limit": 5,
            "concurrent_threads": 10,
            "request_duration": 2.0
        },
        {
            "tenant_id": "tenant2", 
            "thread_limit": 5,
            "concurrent_threads": 8,
            "request_duration": 2.5
        }
    ]
    
    all_results = []
    
    for test_case in test_cases:
        try:
            result = tester.test_thread_limit(**test_case)
            all_results.append(result)
            time.sleep(2)  # 测试间隔
        except Exception as e:
            print(f"测试 {test_case['tenant_id']} 时出错: {e}")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    
    for result in all_results:
        tenant_id = result["tenant_id"]
        success_rate = result["success_rate"]
        status = "✅ 通过" if success_rate > 0 else "❌ 失败"
        print(f"租户 {tenant_id}: 成功率 {success_rate:.2%} - {status}")

if __name__ == "__main__":
    main()
