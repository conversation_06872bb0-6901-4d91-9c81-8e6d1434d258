{"test_config": {"concurrent_threads": 10, "test_duration": 10, "expected_thread_limit": 5}, "summary": {"total_tests": 5, "passed_tests": 0, "failed_tests": 5, "pass_rate": 0.0}, "detailed_results": {"tenant1": {"result": {"success": 967, "blocked": 0, "error": 0, "total": 967}, "pass": false, "message": "线程限流可能未生效，限流率过低: 0.00% (期望 >= 20%)"}, "tenant2": {"result": {"success": 967, "blocked": 0, "error": 0, "total": 967}, "pass": false, "message": "线程限流可能未生效，限流率过低: 0.00% (期望 >= 20%)"}, "tenant3": {"result": {"success": 966, "blocked": 0, "error": 0, "total": 966}, "pass": false, "message": "线程限流可能未生效，限流率过低: 0.00% (期望 >= 20%)"}, "tenant4": {"result": {"success": 965, "blocked": 0, "error": 0, "total": 965}, "pass": false, "message": "线程限流可能未生效，限流率过低: 0.00% (期望 >= 20%)"}, "tenant5": {"result": {"success": 966, "blocked": 0, "error": 0, "total": 966}, "pass": false, "message": "线程限流可能未生效，限流率过低: 0.00% (期望 >= 20%)"}}, "timestamp": "2025-08-31 15:29:19"}