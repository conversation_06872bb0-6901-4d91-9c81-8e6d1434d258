<template>
	<div class="tenant-management">
		<layout>
			<div class="tenant-management-content">
				<div class="page-header">
					<h1>租户管理</h1>
					<p>管理系统中的租户信息和总QPS限制配置</p>
				</div>

				<!-- 操作栏 -->
				<div class="action-bar">
					<el-button
						type="primary"
						icon="el-icon-plus"
						@click="showAddDialog"
					>
						新增租户
					</el-button>
					<el-button icon="el-icon-refresh" @click="loadTenants">
						刷新
					</el-button>
				</div>

				<!-- 租户列表 -->
				<el-table
					:data="tenants"
					v-loading="loading"
					stripe
					style="width: 100%"
					@selection-change="handleSelectionChange"
				>
					<el-table-column
						type="selection"
						width="55"
					></el-table-column>

					<el-table-column prop="tenantId" label="租户ID" width="120">
						<template slot-scope="scope">
							<el-tag size="small">{{
								scope.row.tenantId
							}}</el-tag>
						</template>
					</el-table-column>

					<el-table-column
						prop="tenantName"
						label="租户名称"
						width="150"
					></el-table-column>

					<el-table-column
						prop="description"
						label="描述"
						min-width="150"
						show-overflow-tooltip
					></el-table-column>

					<el-table-column
						prop="contactPerson"
						label="联系人"
						width="100"
					></el-table-column>

					<el-table-column
						prop="contactEmail"
						label="联系邮箱"
						min-width="180"
					></el-table-column>



					<el-table-column prop="status" label="状态" width="80">
						<template slot-scope="scope">
							<el-tag
								:type="
									scope.row.status === 1
										? 'success'
										: 'danger'
								"
								size="small"
							>
								{{ scope.row.status === 1 ? '启用' : '禁用' }}
							</el-tag>
						</template>
					</el-table-column>

					<el-table-column
						prop="createTime"
						label="创建时间"
						width="160"
					>
						<template slot-scope="scope">
							{{ formatDate(scope.row.createTime) }}
						</template>
					</el-table-column>

					<el-table-column label="操作" width="300">
						<template slot-scope="scope">
							<el-button
								size="mini"
								type="primary"
								icon="el-icon-edit"
								@click="editTenant(scope.row)"
							>
								编辑
							</el-button>
							<el-button
								size="mini"
								type="danger"
								icon="el-icon-delete"
								@click="deleteTenant(scope.row)"
							>
								删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>

				<!-- 分页 -->
				<div class="pagination-wrapper">
					<el-pagination
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						:current-page="pagination.current"
						:page-sizes="[10, 20, 50, 100]"
						:page-size="pagination.size"
						layout="total, sizes, prev, pager, next, jumper"
						:total="pagination.total"
					>
					</el-pagination>
				</div>

				<!-- 新增/编辑对话框 -->
				<el-dialog
					:title="dialogTitle"
					:visible.sync="dialogVisible"
					width="600px"
					@close="resetForm"
				>
					<el-form
						:model="form"
						:rules="rules"
						ref="tenantForm"
						label-width="120px"
					>
						<el-form-item label="租户ID" prop="tenantId">
							<el-input
								v-model="form.tenantId"
								placeholder="请输入租户ID"
								:disabled="isEdit"
							></el-input>
							<div class="form-tip">
								租户ID只能包含字母、数字、下划线和横线
							</div>
						</el-form-item>

						<el-form-item label="租户名称" prop="tenantName">
							<el-input
								v-model="form.tenantName"
								placeholder="请输入租户名称"
							></el-input>
						</el-form-item>

						<el-form-item label="描述">
							<el-input
								type="textarea"
								v-model="form.description"
								placeholder="请输入租户描述"
								:rows="3"
							></el-input>
						</el-form-item>
						<el-form-item label="流控行为">
							<el-select
								v-model="form.controlBehavior"
								placeholder="请选择流控行为"
								style="width: 100%"
							>
								<el-option label="直接拒绝" value="0"></el-option>
								<el-option label="Warm Up" value="1"></el-option>
								<el-option label="匀速排队" value="2"></el-option>
								<el-option label="Warm Up + 匀速排队" value="3"></el-option>
							</el-select>
							<div class="form-tip">
								选择触发限流时的处理行为
							</div>
						</el-form-item>

						<el-form-item label="排队超时时间" v-if="form.controlBehavior === '2' || form.controlBehavior === '3'">
							<el-input-number
								v-model="form.maxQueueingTimeMs"
								:min="0"
								:max="60000"
								placeholder="请输入排队超时时间"
								style="width: 100%"
							></el-input-number>
							<div class="form-tip">
								匀速排队模式下的最大排队等待时间（毫秒）
							</div>
						</el-form-item>

						<el-form-item label="预热时长" v-if="form.controlBehavior === '1' || form.controlBehavior === '3'">
							<el-input-number
								v-model="form.warmUpPeriodSec"
								:min="1"
								:max="3600"
								placeholder="请输入预热时长"
								style="width: 100%"
							></el-input-number>
							<div class="form-tip">
								Warm Up模式下的预热时长（秒）
							</div>
						</el-form-item>

						<el-form-item label="联系人">
							<el-input
								v-model="form.contactPerson"
								placeholder="请输入联系人"
							></el-input>
						</el-form-item>

						<el-form-item label="联系邮箱" prop="contactEmail">
							<el-input
								v-model="form.contactEmail"
								placeholder="请输入联系邮箱"
							></el-input>
						</el-form-item>

						<el-form-item label="联系电话">
							<el-input
								v-model="form.contactPhone"
								placeholder="请输入联系电话"
							></el-input>
						</el-form-item>

						<el-form-item label="状态" prop="status">
							<el-radio-group v-model="form.status">
								<el-radio :label="1">启用</el-radio>
								<el-radio :label="0">禁用</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-form>

					<div slot="footer" class="dialog-footer">
						<el-button @click="dialogVisible = false"
							>取消</el-button
						>
						<el-button
							type="primary"
							@click="submitForm"
							:loading="submitting"
						>
							{{ isEdit ? '更新' : '创建' }}
						</el-button>
					</div>
				</el-dialog>
			</div>
		</layout>
	</div>
</template>

<script>
import Layout from '../components/Layout.vue';

export default {
	name: 'TenantManagement',
	components: {
		Layout,
	},
	data() {
		return {
			tenants: [],
			loading: false,
			submitting: false,
			dialogVisible: false,
			isEdit: false,
			selectedTenants: [],

			// 分页
			pagination: {
				current: 1,
				size: 20,
				total: 0,
			},

			// 表单数据
			form: {
				tenantId: '',
				tenantName: '',
				description: '',
				contactPerson: '',
				contactEmail: '',
				contactPhone: '',
				status: 1,
			},

			// 表单验证规则
			rules: {
				tenantId: [
					{
						required: true,
						message: '请输入租户ID',
						trigger: 'blur',
					},
					{
						pattern: /^[a-zA-Z0-9_-]+$/,
						message: '租户ID只能包含字母、数字、下划线和横线',
						trigger: 'blur',
					},
				],
				tenantName: [
					{
						required: true,
						message: '请输入租户名称',
						trigger: 'blur',
					},
				],
				contactEmail: [
					{
						type: 'email',
						message: '请输入有效的邮箱地址',
						trigger: 'blur',
					},
				],
				status: [
					{
						required: true,
						message: '请选择状态',
						trigger: 'change',
					},
				],
			},
		};
	},

	computed: {
		dialogTitle() {
			return this.isEdit ? '编辑租户' : '新增租户';
		},
	},

	mounted() {
		this.loadTenants();
	},

	methods: {
		// 加载租户列表
		async loadTenants() {
			this.loading = true;
			try {
				const response = await this.$api.tenants.getList({
					current: this.pagination.current,
					size: this.pagination.size,
				});

				if (response.data.success) {
					this.tenants = response.data.data.records || [];
					this.pagination.total = response.data.data.total || 0;
				} else {
					this.$message.error('获取租户列表失败');
				}
			} catch (error) {
				console.error('Error loading tenants:', error);
				this.$message.error('获取租户列表失败');
			} finally {
				this.loading = false;
			}
		},

		// 显示新增对话框
		showAddDialog() {
			this.isEdit = false;
			this.dialogVisible = true;
		},

		// 编辑租户
		editTenant(tenant) {
			this.isEdit = true;
			this.form = { ...tenant };
			this.dialogVisible = true;
		},

		// 删除租户
		deleteTenant(tenant) {
			this.$confirm(
				`确定要删除租户 "${tenant.tenantName}" 吗？`,
				'确认删除',
				{
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				}
			).then(async () => {
				try {
					const response = await this.$api.tenants.delete(tenant.id);
					if (response.data.success) {
						this.$message.success('删除成功');
						this.loadTenants();
					} else {
						this.$message.error('删除失败');
					}
				} catch (error) {
					console.error('Error deleting tenant:', error);
					this.$message.error('删除失败');
				}
			});
		},

		// 提交表单
		submitForm() {
			this.$refs.tenantForm.validate(async (valid) => {
				if (valid) {
					this.submitting = true;
					try {
						let response;
						if (this.isEdit) {
							response = await this.$api.tenants.update(this.form.id, this.form);
						} else {
							response = await this.$api.tenants.create(this.form);
						}

						if (response.data.success) {
							this.$message.success(
								this.isEdit ? '更新成功' : '创建成功'
							);
							this.dialogVisible = false;
							this.loadTenants();
						} else {
							this.$message.error(
								response.data.message || '操作失败'
							);
						}
					} catch (error) {
						console.error('Error saving tenant:', error);
						this.$message.error('操作失败');
					} finally {
						this.submitting = false;
					}
				}
			});
		},

		// 重置表单
		resetForm() {
			this.$refs.tenantForm && this.$refs.tenantForm.resetFields();
			this.form = {
				tenantId: '',
				tenantName: '',
				description: '',
				contactPerson: '',
				contactEmail: '',
				contactPhone: '',
				status: 1,
			};
		},

		// 处理选择变化
		handleSelectionChange(selection) {
			this.selectedTenants = selection;
		},

		// 处理分页大小变化
		handleSizeChange(size) {
			this.pagination.size = size;
			this.pagination.current = 1;
			this.loadTenants();
		},

		// 处理当前页变化
		handleCurrentChange(current) {
			this.pagination.current = current;
			this.loadTenants();
		},

		// 格式化日期
		formatDate(dateString) {
			if (!dateString) return '-';
			// 统一使用 YYYY-MM-DD HH:mm:ss 格式
			const date = new Date(dateString);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},
	},
};
</script>

<style scoped>
.tenant-management-content {
	padding: 20px;
}

.page-header {
	margin-bottom: 20px;
}

.page-header h1 {
	margin: 0 0 8px 0;
	font-size: 24px;
	color: #303133;
}

.page-header p {
	margin: 0;
	color: #909399;
	font-size: 14px;
}

.action-bar {
	margin-bottom: 20px;
}

.pagination-wrapper {
	margin-top: 20px;
	text-align: right;
}

.form-tip {
	font-size: 12px;
	color: #909399;
	margin-top: 4px;
}

.dialog-footer {
	text-align: right;
}
</style>
