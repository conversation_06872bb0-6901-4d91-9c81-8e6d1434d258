package com.example.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.dto.MonitorStatisticsDTO;
import com.example.common.entity.MonitorStatistics;
import com.example.admin.mapper.MonitorStatisticsMapper;
import com.example.admin.service.MonitorStatisticsService;
import com.example.admin.vo.MonitorVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监控统计服务实现类
 */
@Service
public class MonitorStatisticsServiceImpl extends ServiceImpl<MonitorStatisticsMapper, MonitorStatistics> implements MonitorStatisticsService {
    
    @Resource
    private MonitorStatisticsMapper monitorStatisticsMapper;
    
    @Override
    public Page<MonitorVO> selectMonitorStatisticsPage(Page<MonitorVO> page, String tenantId, String resourceName,
                                                       LocalDateTime startTime, LocalDateTime endTime) {
        return monitorStatisticsMapper.selectMonitorVOPage(page, tenantId, resourceName, null, startTime, endTime);
    }
    
    @Override
    public MonitorVO getMonitorStatisticsById(Long id) {
        MonitorStatistics monitorStatistics = this.getById(id);
        if (monitorStatistics == null) {
            return null;
        }
        MonitorVO monitorVO = new MonitorVO();
        BeanUtils.copyProperties(monitorStatistics, monitorVO);
        return monitorVO;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMonitorStatistics(MonitorStatisticsDTO monitorStatisticsDTO) {
        MonitorStatistics monitorStatistics = new MonitorStatistics();
        BeanUtils.copyProperties(monitorStatisticsDTO, monitorStatistics);
        monitorStatistics.setCreateTime(LocalDateTime.now());
        
        return this.save(monitorStatistics);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMonitorStatistics(Long id, MonitorStatisticsDTO monitorStatisticsDTO) {
        MonitorStatistics existingStatistics = this.getById(id);
        if (existingStatistics == null) {
            throw new RuntimeException("监控统计记录不存在");
        }
        
        BeanUtils.copyProperties(monitorStatisticsDTO, existingStatistics);
        existingStatistics.setId(id);
        
        return this.updateById(existingStatistics);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMonitorStatistics(Long id) {
        return this.removeById(id);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteMonitorStatistics(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        return this.removeByIds(ids);
    }
    
    @Override
    public List<MonitorVO> getMonitorStatisticsByTimeRange(LocalDateTime startTime, LocalDateTime endTime,
                                                            String tenantId, String resourceName, Integer limit) {
        List<MonitorStatistics> statistics = monitorStatisticsMapper.selectByTimeRange(startTime, endTime, tenantId, resourceName, null);
        return statistics.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<MonitorVO> getMonitorStatisticsByTenantId(String tenantId, LocalDateTime startTime,
                                                          LocalDateTime endTime, Integer limit) {
        List<MonitorStatistics> statistics = monitorStatisticsMapper.selectByTenantId(tenantId, null, limit);
        return statistics.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<MonitorVO> getMonitorStatisticsByResourceName(String resourceName, String tenantId,
                                                              LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        List<MonitorStatistics> statistics = monitorStatisticsMapper.selectByResourceName(resourceName, null, limit);
        return statistics.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<MonitorVO> getRealtimeMonitorData(String tenantId, String resourceName, Integer minutes, Integer limit) {
        List<MonitorStatistics> statistics = monitorStatisticsMapper.selectRealtimeData(minutes, tenantId, resourceName);
        return statistics.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<Map<String, Object>> getTrendData(String tenantId, String resourceName, LocalDateTime startTime,
                                                  LocalDateTime endTime, Integer interval) {
        return monitorStatisticsMapper.selectTrendData(startTime, endTime, tenantId, resourceName, null);
    }
    
    @Override
    public Long getTotalRequestCount(String tenantId, String resourceName, LocalDateTime startTime, LocalDateTime endTime) {
        return monitorStatisticsMapper.sumTotalRequests(startTime, endTime, tenantId);
    }
    
    @Override
    public Long getBlockedRequestCount(String tenantId, String resourceName, LocalDateTime startTime, LocalDateTime endTime) {
        return monitorStatisticsMapper.sumBlockRequests(startTime, endTime, tenantId);
    }
    
    @Override
    public Double getAverageResponseTime(String tenantId, String resourceName, LocalDateTime startTime, LocalDateTime endTime) {
        return monitorStatisticsMapper.avgResponseTime(startTime, endTime, tenantId, resourceName);
    }
    
    private MonitorVO convertToVO(MonitorStatistics statistics) {
        MonitorVO vo = new MonitorVO();
        vo.setId(statistics.getId());
        vo.setTenantId(statistics.getTenantId());
        vo.setResourceName(statistics.getResourceName());
        vo.setTotalRequests(statistics.getTotalRequests());
        vo.setBlockRequests(statistics.getBlockRequests());
        vo.setPassRequests(statistics.getPassRequests());
        vo.setAvgRt(statistics.getAvgRt() != null ? statistics.getAvgRt().doubleValue() : null);
        vo.setMaxRt(statistics.getMaxRt());
        vo.setMinRt(statistics.getMinRt());
        vo.setStatTime(statistics.getStatTime());
        vo.setCreateTime(statistics.getCreateTime());
        vo.setUpdateTime(statistics.getUpdateTime());
        return vo;
    }
    
    @Override
    public List<Map<String, Object>> getResourceQpsRanking(String tenantId, LocalDateTime startTime,
                                                            LocalDateTime endTime, Integer limit) {
        return monitorStatisticsMapper.selectResourceRankByQps(startTime, endTime, limit);
    }
    
    @Override
    public List<Map<String, Object>> getResourceResponseTimeRanking(String tenantId, LocalDateTime startTime,
                                                                     LocalDateTime endTime, Integer limit) {
        return monitorStatisticsMapper.selectResourceRankByRt(startTime, endTime, limit);
    }
    
    @Override
    public List<Map<String, Object>> getTenantStatistics(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        return monitorStatisticsMapper.selectTenantStatistics(startTime, endTime);
    }
    
    @Override
    public Map<String, Object> getSystemOverview(LocalDateTime startTime, LocalDateTime endTime) {
        return monitorStatisticsMapper.selectSystemOverview(startTime, endTime);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsertMonitorStatistics(List<MonitorStatistics> monitorStatisticsList) {
        if (CollectionUtils.isEmpty(monitorStatisticsList)) {
            return false;
        }
        
        // 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        monitorStatisticsList.forEach(statistics -> {
            if (statistics.getCreateTime() == null) {
                statistics.setCreateTime(now);
            }
        });
        
        return monitorStatisticsMapper.batchInsert(monitorStatisticsList) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteExpiredData(LocalDateTime beforeTime) {
        return monitorStatisticsMapper.deleteExpiredData(beforeTime, null);
    }
    
    @Override
    public List<MonitorVO> getAbnormalData(String tenantId, String resourceName, LocalDateTime startTime,
                                           LocalDateTime endTime, Double threshold, Integer limit) {
        // 将threshold作为maxBlockRate，limit作为maxRt（转换为Integer）
        Integer maxRt = limit != null ? limit : 1000; // 默认1000ms
        Double maxBlockRate = threshold != null ? threshold : 0.1; // 默认10%
        List<MonitorStatistics> abnormalData = monitorStatisticsMapper.selectAbnormalData(maxRt, maxBlockRate, startTime, endTime);
        return abnormalData.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<Map<String, Object>> getMonitorSummary(String tenantId, LocalDateTime startTime,
                                                        LocalDateTime endTime, String groupBy) {
        Map<String, Object> result = new HashMap<>();
        result.put("tenantId", tenantId);
        result.put("startTime", startTime);
        result.put("endTime", endTime);
        result.put("groupBy", groupBy);
        
        // 根据分组类型获取汇总数据
        switch (groupBy.toLowerCase()) {
            case "hour":
                return getHourlySummary(tenantId, startTime, endTime);
            case "day":
                return getDailySummary(tenantId, startTime, endTime);
            case "month":
                return getMonthlySummary(tenantId, startTime, endTime);
            default:
                return new ArrayList<>();
        }
    }
    
    @Override
    public List<MonitorVO> exportMonitorStatistics(String tenantId, String resourceName,
                                                    LocalDateTime startTime, LocalDateTime endTime) {
        List<MonitorStatistics> statistics = monitorStatisticsMapper.selectByTimeRange(startTime, endTime, tenantId, resourceName, null);
        return statistics.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<Map<String, Object>> getHotResources(String tenantId, LocalDateTime startTime,
                                                      LocalDateTime endTime, Integer limit) {
        // 使用QPS排行榜作为热点资源
        return monitorStatisticsMapper.selectResourceRankByQps(startTime, endTime, limit != null ? limit : 10);
    }
    
    @Override
    public Map<String, Object> getPerformanceMetrics(String tenantId, String resourceName,
                                                      LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> metrics = new HashMap<>();
        
        // 获取基本指标
        Long totalRequests = getTotalRequestCount(tenantId, resourceName, startTime, endTime);
        Long blockedRequests = getBlockedRequestCount(tenantId, resourceName, startTime, endTime);
        Double avgResponseTime = getAverageResponseTime(tenantId, resourceName, startTime, endTime);
        
        metrics.put("totalRequests", totalRequests != null ? totalRequests : 0L);
        metrics.put("blockedRequests", blockedRequests != null ? blockedRequests : 0L);
        metrics.put("avgResponseTime", avgResponseTime != null ? avgResponseTime : 0.0);
        
        // 计算通过率
        if (totalRequests != null && totalRequests > 0) {
            double passRate = ((double) (totalRequests - (blockedRequests != null ? blockedRequests : 0)) / totalRequests) * 100;
            metrics.put("passRate", Math.round(passRate * 100.0) / 100.0);
        } else {
            metrics.put("passRate", 100.0);
        }
        
        // 计算QPS（假设时间范围为分钟）
        if (startTime != null && endTime != null) {
            long minutes = java.time.Duration.between(startTime, endTime).toMinutes();
            if (minutes > 0 && totalRequests != null) {
                double qps = (double) totalRequests / (minutes * 60);
                metrics.put("qps", Math.round(qps * 100.0) / 100.0);
            } else {
                metrics.put("qps", 0.0);
            }
        }
        
        return metrics;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupMonitorData(Integer retentionDays) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);
        return deleteExpiredData(cutoffTime);
    }
    
    @Override
    public Map<String, Object> getStorageStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取总记录数
        long totalCount = this.count();
        statistics.put("totalRecords", totalCount);
        
        // 获取最早和最新记录时间
        MonitorStatistics earliest = this.lambdaQuery()
                .orderByAsc(MonitorStatistics::getCreateTime)
                .last("LIMIT 1")
                .one();
        MonitorStatistics latest = this.lambdaQuery()
                .orderByDesc(MonitorStatistics::getCreateTime)
                .last("LIMIT 1")
                .one();
        
        if (earliest != null) {
            statistics.put("earliestRecord", earliest.getCreateTime());
        }
        if (latest != null) {
            statistics.put("latestRecord", latest.getCreateTime());
        }
        
        // 计算数据跨度（天数）
        if (earliest != null && latest != null) {
            long days = java.time.Duration.between(earliest.getCreateTime(), latest.getCreateTime()).toDays();
            statistics.put("dataSpanDays", days);
        }
        
        return statistics;
    }
    
    /**
     * 获取小时级汇总数据
     */
    private List<Map<String, Object>> getHourlySummary(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        // TODO: 实现小时级汇总逻辑
        return new ArrayList<>();
    }
    
    /**
     * 获取日级汇总数据
     */
    private List<Map<String, Object>> getDailySummary(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        // TODO: 实现日级汇总逻辑
        return new ArrayList<>();
    }
    
    /**
     * 获取月级汇总数据
     */
    private List<Map<String, Object>> getMonthlySummary(String tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        // TODO: 实现月级汇总逻辑
        return new ArrayList<>();
    }
    
    @Override
    public Map<String, Object> getDashboardData(String tenantId) {
        Map<String, Object> dashboardData = new HashMap<>();
        
        // 设置时间范围（最近24小时）
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusHours(24);
        
        // 获取基本统计数据
        Long totalRequests = getTotalRequestCount(tenantId, null, startTime, endTime);
        Long blockedRequests = getBlockedRequestCount(tenantId, null, startTime, endTime);
        Double avgResponseTime = getAverageResponseTime(tenantId, null, startTime, endTime);
        
        dashboardData.put("totalRequests", totalRequests != null ? totalRequests : 0L);
        dashboardData.put("blockedRequests", blockedRequests != null ? blockedRequests : 0L);
        dashboardData.put("avgResponseTime", avgResponseTime != null ? avgResponseTime : 0.0);
        
        // 计算通过率
        if (totalRequests != null && totalRequests > 0) {
            double passRate = ((double) (totalRequests - (blockedRequests != null ? blockedRequests : 0)) / totalRequests) * 100;
            dashboardData.put("passRate", Math.round(passRate * 100.0) / 100.0);
        } else {
            dashboardData.put("passRate", 100.0);
        }
        
        // 获取资源QPS排行（前10）
        List<Map<String, Object>> qpsRanking = getResourceQpsRanking(tenantId, startTime, endTime, 10);
        dashboardData.put("qpsRanking", qpsRanking);
        
        // 获取响应时间排行（前10）
        List<Map<String, Object>> rtRanking = getResourceResponseTimeRanking(tenantId, startTime, endTime, 10);
        dashboardData.put("rtRanking", rtRanking);
        
        // 获取趋势数据（最近24小时，每小时一个点）
        List<Map<String, Object>> trendData = getTrendData(tenantId, null, startTime, endTime, 60);
        dashboardData.put("trendData", trendData);
        
        // 获取热点资源
        List<Map<String, Object>> hotResources = getHotResources(tenantId, startTime, endTime, 5);
        dashboardData.put("hotResources", hotResources);
        
        return dashboardData;
    }
}