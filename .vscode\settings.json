{"java.configuration.updateBuildConfiguration": "automatic", "java.format.enabled": true, "editor.formatOnSave": true, "java.format.settings.url": ".vscode/java-formatter.xml", "java.compile.nullAnalysis.mode": "automatic", "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Djava.awt.headless=true -javaagent:C:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.32/lombok-1.18.32.jar", "java.configuration.runtimes": [{"name": "JavaSE-21", "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Trae\\User\\globalStorage\\pleiades.java-extension-pack-jdk\\java\\21", "default": true}], "terminal.integrated.defaultProfile.windows": "JavaSE-21 LTS", "maven.terminal.useJavaHome": true, "maven.terminal.customEnv": [{"environmentVariable": "JAVA_HOME", "value": "C:\\Users\\<USER>\\AppData\\Roaming\\Trae\\User\\globalStorage\\pleiades.java-extension-pack-jdk\\java\\21"}], "terminal.integrated.env.windows": {"JAVA_HOME": "C:\\Users\\<USER>\\AppData\\Roaming\\Trae\\User\\globalStorage\\pleiades.java-extension-pack-jdk\\java\\21", "PATH": "C:\\Users\\<USER>\\AppData\\Roaming\\Trae\\User\\globalStorage\\pleiades.java-extension-pack-jdk\\java\\21\\bin;${env:PATH}"}}