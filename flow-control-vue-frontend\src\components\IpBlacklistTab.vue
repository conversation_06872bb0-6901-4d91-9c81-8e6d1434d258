<template>
	<div class="ip-blacklist-tab">
		<!-- 操作栏 -->
		<div class="header-actions">
			<el-button
				type="danger"
				:disabled="!hasSelectedLists"
				@click="handleBatchDelete"
			>
				批量删除 ({{ selectedListsCount }})
			</el-button>
			<el-button type="warning" @click="showImportDialog">
				批量导入
			</el-button>
			<el-button type="info" @click="handleExport">
				导出列表
			</el-button>
			<el-button
				type="primary"
				icon="el-icon-plus"
				@click="showCreateDialog"
			>
				新增黑名单
			</el-button>
			<el-button icon="el-icon-refresh" @click="fetchLists">
				刷新
			</el-button>
		</div>

		<!-- 搜索和筛选 -->
		<div class="search-filters">
			<el-row :gutter="20">
				<el-col :span="6">
					<el-input
						v-model="searchKeyword"
						placeholder="搜索IP地址或描述"
						prefix-icon="el-icon-search"
						clearable
						@input="handleSearch"
					/>
				</el-col>
				<el-col :span="4">
					<el-select
						v-model="filters.status"
						placeholder="状态筛选"
						clearable
						@change="handleFilterChange"
					>
						<el-option label="启用" value="enabled" />
						<el-option label="禁用" value="disabled" />
					</el-select>
				</el-col>
				<el-col :span="4">
					<el-select
						v-model="filters.ipType"
						placeholder="IP类型"
						clearable
						@change="handleFilterChange"
					>
						<el-option label="单个IP" value="single" />
						<el-option label="IP段" value="range" />
						<el-option label="CIDR" value="cidr" />
					</el-select>
				</el-col>
				<el-col :span="6">
					<div class="stats-info">
						<span>启用: {{ enabledListsCount }}</span>
						<span>禁用: {{ disabledListsCount }}</span>
						<span>总计: {{ pagination.total }}</span>
					</div>
				</el-col>
			</el-row>
		</div>

		<!-- 列表表格 -->
		<div class="lists-table">
			<el-table
				:data="paginatedLists"
				:loading="isLoading"
				style="width: 100%"
				stripe
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="55"></el-table-column>
				<el-table-column prop="id" label="ID" width="80"></el-table-column>
				<el-table-column
					prop="ipAddress"
					label="IP地址"
					min-width="150"
					show-overflow-tooltip
				></el-table-column>
				<el-table-column
					prop="description"
					label="描述"
					min-width="200"
					show-overflow-tooltip
				></el-table-column>
				<el-table-column prop="priority" label="优先级" width="100">
					<template slot-scope="scope">
						<el-tag
							size="mini"
							:type="getPriorityColor(scope.row.priority)"
						>
							{{ scope.row.priority }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="enabled" label="状态" width="100">
					<template slot-scope="scope">
						<el-switch
							v-model="scope.row.enabled"
							@change="handleStatusChange(scope.row)"
						></el-switch>
					</template>
				</el-table-column>
				<el-table-column
					prop="createTime"
					label="创建时间"
					width="180"
				>
					<template slot-scope="scope">
						{{ formatTime(scope.row.createTime) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="200">
					<template slot-scope="scope">
						<el-button
							size="mini"
							type="primary"
							@click="editList(scope.row)"
						>
							编辑
						</el-button>
						<el-button
							size="mini"
							type="info"
							@click="copyList(scope.row)"
						>
							复制
						</el-button>
						<el-button
							size="mini"
							type="danger"
							@click="deleteList(scope.row)"
						>
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 分页 -->
		<div class="pagination">
			<el-pagination
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				:current-page="pagination.currentPage"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="pagination.pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="pagination.total"
			>
			</el-pagination>
		</div>

		<!-- 新增/编辑对话框 -->
		<el-dialog
			:title="dialogTitle"
			:visible.sync="dialogVisible"
			width="600px"
			@close="resetForm"
		>
			<el-form
				ref="listForm"
				:model="listForm"
				:rules="computedFormRules"
				label-width="120px"
			>
				<el-form-item label="IP地址" prop="ipAddress">
					<el-input
						v-model="listForm.ipAddress"
						placeholder="支持单个IP、IP段、CIDR格式"
					></el-input>
				</el-form-item>
				<el-form-item label="描述" prop="description">
					<el-input
						v-model="listForm.description"
						type="textarea"
						placeholder="请输入描述信息"
					></el-input>
				</el-form-item>
				<el-form-item label="优先级" prop="priority">
					<el-input-number
						v-model="listForm.priority"
						:min="1"
						:max="100"
					></el-input-number>
				</el-form-item>
				<el-form-item label="状态">
					<el-switch
						v-model="listForm.enabled"
						active-text="启用"
						inactive-text="禁用"
					></el-switch>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="saveList">确定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import validationRules from '@/utils/validation'

export default {
	name: 'IpBlacklistTab',
  beforeCreate() {
    // 注册验证规则到Vue实例
    this.$validation = validationRules;
  },
  data() {
		return {
			searchKeyword: '',
			filters: {
				status: '',
				ipType: ''
			},
			selectedLists: [],
			dialogVisible: false,
			isEdit: false,
			listForm: {
				id: null,
				ipAddress: '',
				description: '',
				priority: 10,
				enabled: true
			},
			formRules: {}
		}
	},
	computed: {
		...mapGetters('ipBlacklist', [
			'allLists',
			'isLoading',
			'pagination',
			'selectedListIds',
			'hasSelectedLists',
			'selectedListsCount',
			'enabledListsCount',
			'disabledListsCount',
			'filteredLists'
		]),
		
		// 动态计算表单验证规则
		computedFormRules() {
			const rules = {
				listName: this.$validation.ipBlacklistRules.listName,
				tenantId: this.$validation.ipBlacklistRules.tenantId,
				ipType: this.$validation.ipBlacklistRules.ipType,
				singleIp: this.$validation.ipBlacklistRules.singleIp,
				startIp: this.$validation.ipBlacklistRules.startIp,
				endIp: this.$validation.ipBlacklistRules.endIp,
				cidrIp: this.$validation.ipBlacklistRules.cidrIp,
				priority: this.$validation.ipBlacklistRules.priority,
				enabled: this.$validation.ipBlacklistRules.enabled,
				startTime: this.$validation.ipBlacklistRules.startTime,
				endTime: this.$validation.ipBlacklistRules.endTime,
				description: this.$validation.ipBlacklistRules.description
			};
			
			// 根据IP类型动态调整验证规则
			if (this.listForm.ipType === 'SINGLE_IP') {
				rules.singleIp = this.$validation.ipBlacklistRules.singleIp;
				rules.startIp = [];
				rules.endIp = [];
				rules.cidrIp = [];
			} else if (this.listForm.ipType === 'IP_RANGE') {
				rules.singleIp = [];
				rules.startIp = this.$validation.ipBlacklistRules.startIp;
				rules.endIp = this.$validation.ipBlacklistRules.endIp;
				rules.cidrIp = [];
			} else if (this.listForm.ipType === 'IP_CIDR') {
				rules.singleIp = [];
				rules.startIp = [];
				rules.endIp = [];
				rules.cidrIp = this.$validation.ipBlacklistRules.cidrIp;
			}
			
			return rules;
		},
		paginatedLists() {
			const start = (this.pagination.currentPage - 1) * this.pagination.pageSize
			const end = start + this.pagination.pageSize
			return this.filteredLists.slice(start, end)
		},
		dialogTitle() {
			return this.isEdit ? '编辑IP黑名单' : '新增IP黑名单'
		}
	},
	mounted() {
		this.fetchLists()
	},
	methods: {
		...mapActions('ipBlacklist', [
			'fetchLists',
			'createList',
			'updateList',
			'deleteList',
			'batchDeleteLists',
			'updateStatus',
			'copyList',
			'exportLists'
		]),
		showCreateDialog() {
			this.isEdit = false
			this.dialogVisible = true
		},
		editList(list) {
			this.isEdit = true
			this.listForm = { ...list }
			this.dialogVisible = true
		},
		async saveList() {
			this.$refs.listForm.validate(async (valid) => {
				if (valid) {
					try {
						if (this.isEdit) {
							await this.updateList(this.listForm)
							this.$message.success('更新成功')
						} else {
							await this.createList(this.listForm)
							this.$message.success('创建成功')
						}
						this.dialogVisible = false
						this.fetchLists()
					} catch (error) {
						this.$message.error('操作失败')
					}
				}
			})
		},
		async deleteList(list) {
			try {
				await this.$confirm('确定要删除这条记录吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
				await this.deleteList(list.id)
				this.$message.success('删除成功')
				this.fetchLists()
			} catch (error) {
				if (error !== 'cancel') {
					this.$message.error('删除失败')
				}
			}
		},
		async copyList(list) {
			try {
				await this.copyList(list.id)
				this.$message.success('复制成功')
				this.fetchLists()
			} catch (error) {
				this.$message.error('复制失败')
			}
		},
		async handleStatusChange(list) {
			try {
				await this.updateStatus({ id: list.id, enabled: list.enabled })
				this.$message.success(`已${list.enabled ? '启用' : '禁用'}`)
			} catch (error) {
				list.enabled = !list.enabled
				this.$message.error('状态切换失败')
			}
		},
		handleSearch() {
			// 搜索逻辑在computed中处理
		},
		handleFilterChange() {
			// 筛选逻辑在computed中处理
		},
		handleSelectionChange(selection) {
			this.selectedLists = selection
		},
		async handleBatchDelete() {
			if (this.selectedLists.length === 0) {
				this.$message.warning('请选择要删除的记录')
				return
			}
			try {
				await this.$confirm(`确定要删除选中的 ${this.selectedLists.length} 条记录吗？`, '批量删除', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
				const ids = this.selectedLists.map(list => list.id)
				await this.batchDeleteLists(ids)
				this.$message.success(`成功删除 ${ids.length} 条记录`)
				this.selectedLists = []
				this.fetchLists()
			} catch (error) {
				if (error !== 'cancel') {
					this.$message.error('批量删除失败')
				}
			}
		},
		showImportDialog() {
			// TODO: 实现导入功能
			this.$message.info('导入功能开发中')
		},
		async handleExport() {
			try {
				await this.exportLists()
				this.$message.success('导出成功')
			} catch (error) {
				this.$message.error('导出失败')
			}
		},
		handleSizeChange(val) {
			this.pagination.pageSize = val
			this.pagination.currentPage = 1
		},
		handleCurrentChange(val) {
			this.pagination.currentPage = val
		},
		resetForm() {
			this.listForm = {
				id: null,
				ipAddress: '',
				description: '',
				priority: 10,
				enabled: true
			}
			if (this.$refs.listForm) {
				this.$refs.listForm.resetFields()
			}
		},
		getPriorityColor(priority) {
			if (priority >= 80) return 'danger'
			if (priority >= 50) return 'warning'
			return 'success'
		},
		formatTime(time) {
			if (!time) return '-'
			return new Date(time).toLocaleString()
		}
	}
}
</script>

<style scoped>
.ip-blacklist-tab {
	padding: 20px;
}

.header-actions {
	margin-bottom: 20px;
}

.header-actions .el-button {
	margin-right: 10px;
}

.search-filters {
	margin-bottom: 20px;
	padding: 20px;
	background: #f5f7fa;
	border-radius: 4px;
}

.stats-info {
	display: flex;
	justify-content: space-around;
	align-items: center;
	height: 32px;
}

.stats-info span {
	font-size: 12px;
	color: #606266;
}

.pagination {
	margin-top: 20px;
	text-align: right;
}
</style>