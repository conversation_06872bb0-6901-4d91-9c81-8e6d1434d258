import java.sql.*;

public class DatabaseQuery {
    private static final String URL = "**********************************************************************************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "aids520a";
    
    public static void main(String[] args) {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            
            // 查询租户规则表
            System.out.println("=== 查询tenant_flow_rules表 ===");
            String sql = "SELECT * FROM tenant_flow_rules ORDER BY id DESC LIMIT 10";
            
            // 先查看表结构
            System.out.println("\n=== tenant_flow_rules表结构 ===");
            String descSql = "DESCRIBE tenant_flow_rules";
            PreparedStatement descStmt = conn.prepareStatement(descSql);
            ResultSet descRs = descStmt.executeQuery();
            while (descRs.next()) {
                System.out.println(descRs.getString("Field") + " - " + descRs.getString("Type"));
            }
            descRs.close();
            descStmt.close();
            System.out.println();
            PreparedStatement stmt = conn.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            
            System.out.printf("%-5s %-10s %-15s %-10s %-10s %-10s %-15s %-10s%n", 
                "ID", "TenantID", "RuleName", "Count", "Grade", "Strategy", "ControlBehavior", "Enabled");
            System.out.println("=".repeat(100));
            
            while (rs.next()) {
                System.out.printf("%-5d %-10s %-15s %-10.1f %-10d %-10d %-15d %-10d%n",
                    rs.getLong("id"),
                    rs.getString("tenant_id"),
                    rs.getString("rule_name"),
                    rs.getDouble("count"),
                    rs.getInt("grade"),
                    rs.getInt("strategy"),
                    rs.getInt("control_behavior"),
                    rs.getInt("enabled")
                );
            }
            
            rs.close();
            stmt.close();
            
            // 查询流量规则表
            System.out.println("\n=== 查询flow_rules表 ===");
            sql = "SELECT * FROM flow_rules ORDER BY id DESC LIMIT 10";
            
            // 先查看表结构
            System.out.println("\n=== flow_rules表结构 ===");
            descSql = "DESCRIBE flow_rules";
            descStmt = conn.prepareStatement(descSql);
            descRs = descStmt.executeQuery();
            while (descRs.next()) {
                System.out.println(descRs.getString("Field") + " - " + descRs.getString("Type"));
            }
            descRs.close();
            descStmt.close();
            System.out.println();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            
            System.out.printf("%-5s %-20s %-10s %-10s %-15s %-10s %-20s%n", 
                "ID", "Resource", "Count", "Grade", "LimitApp", "Strategy", "ControlBehavior");
            System.out.println("=".repeat(100));
            
            while (rs.next()) {
                System.out.printf("%-5d %-20s %-10d %-10d %-15s %-10d %-20d%n",
                    rs.getLong("id"),
                    rs.getString("resource"),
                    rs.getInt("count"),
                    rs.getInt("grade"),
                    rs.getString("limit_app"),
                    rs.getInt("strategy"),
                    rs.getInt("control_behavior")
                );
            }
            
            rs.close();
            stmt.close();
            conn.close();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}