package com.example.common.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

/**
 * IP流量控制规则实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@TableName("ip_flow_rule")
public class IPFlowRule {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;
    
    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
    
    /**
     * 规则类型：SINGLE_IP-单个IP，IP_RANGE-IP范围，CIDR-IP段
     */
    @TableField("rule_type")
    private String ruleType;
    
    /**
     * IP值
     */
    @TableField("ip_value")
    private String ipValue;
    
    /**
     * QPS限制
     */
    @TableField("qps_limit")
    private Integer qpsLimit;
    
    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;
    
    /**
     * 规则描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 规则状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableField("deleted")
    private Integer deleted;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public String getIpValue() {
        return ipValue;
    }

    public void setIpValue(String ipValue) {
        this.ipValue = ipValue;
    }

    public Integer getQpsLimit() {
        return qpsLimit;
    }

    public void setQpsLimit(Integer qpsLimit) {
        this.qpsLimit = qpsLimit;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IPFlowRule that = (IPFlowRule) o;
        return java.util.Objects.equals(id, that.id) &&
                java.util.Objects.equals(ruleName, that.ruleName) &&
                java.util.Objects.equals(tenantId, that.tenantId) &&
                java.util.Objects.equals(ruleType, that.ruleType) &&
                java.util.Objects.equals(ipValue, that.ipValue) &&
                java.util.Objects.equals(qpsLimit, that.qpsLimit) &&
                java.util.Objects.equals(priority, that.priority) &&
                java.util.Objects.equals(description, that.description) &&
                java.util.Objects.equals(createTime, that.createTime) &&
                java.util.Objects.equals(updateTime, that.updateTime) &&
                java.util.Objects.equals(status, that.status) &&
				java.util.Objects.equals(createBy, that.createBy) &&
				java.util.Objects.equals(updateBy, that.updateBy) &&
				java.util.Objects.equals(deleted, that.deleted);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(id, ruleName, tenantId, ruleType, ipValue, qpsLimit, priority, description, createTime, updateTime, status, createBy, updateBy, deleted);
    }

    @Override
    public String toString() {
        return "IPFlowRule{"
                + "id=" + id +
                ", ruleName='" + ruleName + "'" +
                ", tenantId='" + tenantId + "'" +
                ", ruleType='" + ruleType + "'" +
                ", ipValue='" + ipValue + "'" +
                ", qpsLimit=" + qpsLimit +
                ", priority=" + priority +
                ", description='" + description + "'" +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", status=" + status +
                ", createBy='" + createBy + "'" +
                ", updateBy='" + updateBy + "'" +
                ", deleted=" + deleted +
                '}';
    }
}
