package com.example.gateway.rule;

import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.example.common.constant.FlowControlConstants;
import com.example.gateway.service.DatabaseRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 流量规则管理器 负责规则的动态管理和热更新
 */
@Slf4j
@Component
public class FlowRuleManager {

	@Autowired
	private DatabaseRuleService databaseRuleService;

	@Autowired(required = false)
	private StringRedisTemplate redisTemplate;

	private static final String RULE_CACHE_PREFIX = "flow:rules:";
	private static final String RULE_VERSION_KEY = "flow:rules:version";
	private static final int CACHE_EXPIRE_SECONDS = 3600; // 1小时

	private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

	/**
	 * 初始化规则管理器
	 */
	@PostConstruct
	public void init() {
		// 启动时加载规则
		loadRulesFromDatabase();

		// 定时刷新规则（每30秒检查一次）
		scheduler.scheduleWithFixedDelay(this::loadRulesFromDatabase, 30, 30, TimeUnit.SECONDS);

		log.info("FlowRuleManager initialized with database rule loading");
	}

	/**
	 * 从数据库加载规则
	 */
	public void loadRulesFromDatabase() {
		try {
			List<FlowRule> rules = databaseRuleService.loadFlowRulesFromDatabase();
			com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.loadRules(rules);
			updateRuleVersion();
			log.debug("Loaded {} rules from database", rules.size());

			// 调试：打印加载的规则
			for (FlowRule rule : rules) {
				log.debug("Loaded rule: resource={}, count={}, grade={}", rule.getResource(), rule.getCount(),
						rule.getGrade());
			}

			// 验证Sentinel中的规则
			List<FlowRule> sentinelRules = com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.getRules();
			log.debug("Sentinel now has {} rules", sentinelRules.size());
		} catch (Exception e) {
			log.error("Failed to load rules from database", e);
		}
	}

	/**
	 * 创建流量规则
	 */
	public FlowRule createRule(FlowRule rule) {
		try {
			// 验证规则
			validateRule(rule);

			// 设置默认值
			setDefaultValues(rule);

			// 添加到Sentinel规则管理器
			List<FlowRule> currentRules = new ArrayList<>(
					com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.getRules());
			currentRules.add(rule);
			com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.loadRules(currentRules);

			// 缓存规则
			cacheRule(rule);

			// 更新版本号
			updateRuleVersion();

			log.info("Flow rule created: resource={}, count={}, controlBehavior={}", rule.getResource(),
					rule.getCount(), rule.getControlBehavior());

			return rule;

		} catch (Exception e) {
			log.error("Failed to create flow rule: {}", rule, e);
			throw new RuntimeException("创建流量规则失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 更新流量规则
	 */
	public FlowRule updateRule(FlowRule rule) {
		try {
			// 验证规则
			validateRule(rule);

			// 获取当前规则列表
			List<FlowRule> currentRules = new ArrayList<>(
					com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.getRules());

			// 查找并更新规则
			boolean found = false;
			for (int i = 0; i < currentRules.size(); i++) {
				FlowRule existingRule = currentRules.get(i);
				if (isSameRule(existingRule, rule)) {
					currentRules.set(i, rule);
					found = true;
					break;
				}
			}

			if (!found) {
				throw new RuntimeException("规则不存在");
			}

			// 重新加载规则
			com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.loadRules(currentRules);

			// 更新缓存
			cacheRule(rule);

			// 更新版本号
			updateRuleVersion();

			log.info("Flow rule updated: resource={}, count={}, controlBehavior={}", rule.getResource(),
					rule.getCount(), rule.getControlBehavior());

			return rule;

		} catch (Exception e) {
			log.error("Failed to update flow rule: {}", rule, e);
			throw new RuntimeException("更新流量规则失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 删除流量规则
	 */
	public void deleteRule(String resource, String limitApp) {
		try {
			// 获取当前规则列表
			List<FlowRule> currentRules = new ArrayList<>(
					com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.getRules());

			// 查找并删除规则
			boolean removed = currentRules.removeIf(rule -> resource.equals(rule.getResource())
					&& (limitApp == null || limitApp.equals(rule.getLimitApp())));

			if (!removed) {
				throw new RuntimeException("规则不存在");
			}

			// 重新加载规则
			com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.loadRules(currentRules);

			// 删除缓存
			deleteCachedRule(resource, limitApp);

			// 更新版本号
			updateRuleVersion();

			log.info("Flow rule deleted: resource={}, limitApp={}", resource, limitApp);

		} catch (Exception e) {
			log.error("Failed to delete flow rule: resource={}, limitApp={}", resource, limitApp, e);
			throw new RuntimeException("删除流量规则失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 获取所有流量规则
	 */
	public List<FlowRule> getAllRules() {
		return new ArrayList<>(com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.getRules());
	}

	/**
	 * 根据租户获取规则
	 */
	public List<FlowRule> getRulesByTenant(String tenantId) {
		List<FlowRule> allRules = getAllRules();
		List<FlowRule> tenantRules = new ArrayList<>();

		for (FlowRule rule : allRules) {
			String resource = rule.getResource();
			if (resource.contains(FlowControlConstants.ResourcePrefix.TENANT + tenantId)) {
				tenantRules.add(rule);
			}
		}

		return tenantRules;
	}

	/**
	 * 批量加载规则
	 */
	public void loadRules(List<FlowRule> rules) {
		try {
			// 验证所有规则
			for (FlowRule rule : rules) {
				validateRule(rule);
				setDefaultValues(rule);
			}

			// 加载到Sentinel
			com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.loadRules(rules);

			// 批量缓存规则
			batchCacheRules(rules);

			// 更新版本号
			updateRuleVersion();

			log.info("Batch loaded {} flow rules", rules.size());

		} catch (Exception e) {
			log.error("Failed to batch load flow rules", e);
			throw new RuntimeException("批量加载流量规则失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 清空所有规则
	 */
	public void clearAllRules() {
		try {
			com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.loadRules(new ArrayList<>());

			// 清空缓存
			clearRuleCache();

			// 更新版本号
			updateRuleVersion();

			log.info("All flow rules cleared");

		} catch (Exception e) {
			log.error("Failed to clear all flow rules", e);
			throw new RuntimeException("清空流量规则失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 获取规则版本号
	 */
	public long getRuleVersion() {
		if (redisTemplate != null) {
			try {
				String version = redisTemplate.opsForValue().get(RULE_VERSION_KEY);
				return version != null ? Long.parseLong(version) : 0L;
			} catch (Exception e) {
				log.warn("Failed to get rule version from Redis", e);
			}
		}
		return System.currentTimeMillis();
	}

	/**
	 * 验证规则
	 */
	private void validateRule(FlowRule rule) {
		if (rule == null) {
			throw new IllegalArgumentException("规则不能为空");
		}

		if (!StringUtils.hasText(rule.getResource())) {
			throw new IllegalArgumentException("资源标识不能为空");
		}

		if (rule.getCount() < 0) {
			throw new IllegalArgumentException("限流阈值不能为负数");
		}

		if (rule.getGrade() != FlowControlConstants.Grade.THREAD && rule.getGrade() != FlowControlConstants.Grade.QPS) {
			throw new IllegalArgumentException("限流模式无效");
		}

		if (rule.getControlBehavior() == FlowControlConstants.ControlBehavior.QUEUE
				&& rule.getMaxQueueingTimeMs() <= 0) {
			throw new IllegalArgumentException("排队等待模式下超时时间必须大于0");
		}
	}

	/**
	 * 设置默认值
	 */
	private void setDefaultValues(FlowRule rule) {
		if (!StringUtils.hasText(rule.getLimitApp())) {
			rule.setLimitApp(FlowControlConstants.Defaults.DEFAULT_LIMIT_APP);
		}

		if (rule.getGrade() == 0) {
			rule.setGrade(FlowControlConstants.Grade.QPS);
		}

		if (rule.getStrategy() == 0) {
			rule.setStrategy(FlowControlConstants.Strategy.DIRECT);
		}

		if (rule.getControlBehavior() == FlowControlConstants.ControlBehavior.WARM_UP
				&& rule.getWarmUpPeriodSec() <= 0) {
			rule.setWarmUpPeriodSec(FlowControlConstants.Defaults.DEFAULT_WARM_UP_PERIOD);
		}
	}

	/**
	 * 判断是否为同一个规则
	 */
	private boolean isSameRule(FlowRule rule1, FlowRule rule2) {
		return rule1.getResource().equals(rule2.getResource()) && rule1.getLimitApp().equals(rule2.getLimitApp());
	}

	/**
	 * 缓存规则
	 */
	private void cacheRule(FlowRule rule) {
		if (redisTemplate != null) {
			try {
				String key = RULE_CACHE_PREFIX + rule.getResource() + ":" + rule.getLimitApp();
				// 简化缓存，只存储基本信息
				String value = String.format("{\"resource\":\"%s\",\"count\":%s,\"grade\":%d}", rule.getResource(),
						rule.getCount(), rule.getGrade());
				redisTemplate.opsForValue().set(key, value, CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
			} catch (Exception e) {
				log.warn("Failed to cache rule: {}", rule.getResource(), e);
			}
		}
	}

	/**
	 * 批量缓存规则
	 */
	private void batchCacheRules(List<FlowRule> rules) {
		if (redisTemplate != null) {
			try {
				for (FlowRule rule : rules) {
					cacheRule(rule);
				}
			} catch (Exception e) {
				log.warn("Failed to batch cache rules", e);
			}
		}
	}

	/**
	 * 删除缓存的规则
	 */
	private void deleteCachedRule(String resource, String limitApp) {
		if (redisTemplate != null) {
			try {
				String key = RULE_CACHE_PREFIX + resource + ":" + limitApp;
				redisTemplate.delete(key);
			} catch (Exception e) {
				log.warn("Failed to delete cached rule: {}", resource, e);
			}
		}
	}

	/**
	 * 清空规则缓存
	 */
	private void clearRuleCache() {
		if (redisTemplate != null) {
			try {
				redisTemplate.delete(redisTemplate.keys(RULE_CACHE_PREFIX + "*"));
			} catch (Exception e) {
				log.warn("Failed to clear rule cache", e);
			}
		}
	}

	/**
	 * 更新规则版本号
	 */
	private void updateRuleVersion() {
		if (redisTemplate != null) {
			try {
				long version = System.currentTimeMillis();
				redisTemplate.opsForValue().set(RULE_VERSION_KEY, String.valueOf(version));
			} catch (Exception e) {
				log.warn("Failed to update rule version", e);
			}
		}
	}
}