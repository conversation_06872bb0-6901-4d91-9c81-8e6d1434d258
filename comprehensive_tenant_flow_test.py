#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合租户限流测试脚本
用于验证重构后的两层租户限流架构：
1. 第一层：租户级别限流（控制整个租户的流量）
2. 第二层：租户+接口级别限流（在租户限流通过后进行）

支持QPS和线程数两种限流模式的测试
"""

import requests
import time
import threading
import argparse
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
import mysql.connector
from mysql.connector import Error
import statistics


class ComprehensiveTenantFlowTester:
    """
    综合租户限流测试器
    """
    
    def __init__(self, base_url='http://localhost:8088', db_config=None):
        self.base_url = base_url.rstrip('/')
        self.db_config = db_config
        self.results = []
        self.lock = threading.Lock()
    
    def send_request(self, tenant_id, resource_name=None, duration=0.1):
        """
        发送HTTP请求到网关
        
        Args:
            tenant_id: 租户ID
            resource_name: 资源名称（接口名）
            duration: 请求持续时间（秒）
        
        Returns:
            dict: 请求结果
        """
        url = f"{self.base_url}/api/test"
        if resource_name:
            url = f"{self.base_url}/api/{resource_name}"
        
        headers = {
            'X-Tenant-ID': tenant_id,
            'Content-Type': 'application/json'
        }
        
        params = {}
        if duration > 0:
            params['duration'] = duration
        
        start_time = time.time()
        try:
            response = requests.get(url, headers=headers, params=params, timeout=30)
            end_time = time.time()
            
            return {
                'success': True,
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'blocked': response.status_code == 429,
                'tenant_id': tenant_id,
                'resource_name': resource_name,
                'timestamp': start_time,
                'response_text': response.text[:200] if response.text else ''
            }
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'status_code': 408,
                'response_time': time.time() - start_time,
                'blocked': False,
                'tenant_id': tenant_id,
                'resource_name': resource_name,
                'timestamp': start_time,
                'error': 'Timeout'
            }
        except Exception as e:
            return {
                'success': False,
                'status_code': 0,
                'response_time': time.time() - start_time,
                'blocked': False,
                'tenant_id': tenant_id,
                'resource_name': resource_name,
                'timestamp': start_time,
                'error': str(e)
            }
    
    def test_tenant_level_flow_control(self, tenant_id, rule_config, concurrent_threads=10, test_duration=30):
        """
        测试租户级别限流
        
        Args:
            tenant_id: 租户ID
            rule_config: 规则配置
            concurrent_threads: 并发线程数
            test_duration: 测试持续时间（秒）
        
        Returns:
            dict: 测试统计结果
        """
        print(f"\n开始测试租户级别限流 - 租户: {tenant_id}")
        print(f"规则配置: {rule_config}")
        print(f"并发线程数: {concurrent_threads}, 测试时长: {test_duration}秒")
        
        results = []
        start_time = time.time()
        
        def worker():
            while time.time() - start_time < test_duration:
                # 对于线程限流，使用较长的请求持续时间来占用线程
                duration = 2.0 if rule_config['grade'] == 0 else 0.1
                result = self.send_request(tenant_id, duration=duration)
                with self.lock:
                    results.append(result)
                
                # 对于QPS限流，控制请求频率
                if rule_config['grade'] == 1:  # QPS限流
                    time.sleep(0.1)  # 100ms间隔
        
        # 启动并发线程
        with ThreadPoolExecutor(max_workers=concurrent_threads) as executor:
            futures = [executor.submit(worker) for _ in range(concurrent_threads)]
            
            # 等待所有线程完成
            for future in as_completed(futures, timeout=test_duration + 10):
                try:
                    future.result()
                except Exception as e:
                    print(f"线程执行错误: {e}")
        
        return self.analyze_results(results, rule_config, 'tenant_level')
    
    def test_tenant_resource_flow_control(self, tenant_id, resource_name, rule_config, concurrent_threads=10, test_duration=30):
        """
        测试租户+接口级别限流
        
        Args:
            tenant_id: 租户ID
            resource_name: 资源名称
            rule_config: 规则配置
            concurrent_threads: 并发线程数
            test_duration: 测试持续时间（秒）
        
        Returns:
            dict: 测试统计结果
        """
        print(f"\n开始测试租户+接口级别限流 - 租户: {tenant_id}, 接口: {resource_name}")
        print(f"规则配置: {rule_config}")
        print(f"并发线程数: {concurrent_threads}, 测试时长: {test_duration}秒")
        
        results = []
        start_time = time.time()
        
        def worker():
            while time.time() - start_time < test_duration:
                # 对于线程限流，使用较长的请求持续时间来占用线程
                duration = 2.0 if rule_config['grade'] == 0 else 0.1
                result = self.send_request(tenant_id, resource_name, duration=duration)
                with self.lock:
                    results.append(result)
                
                # 对于QPS限流，控制请求频率
                if rule_config['grade'] == 1:  # QPS限流
                    time.sleep(0.1)  # 100ms间隔
        
        # 启动并发线程
        with ThreadPoolExecutor(max_workers=concurrent_threads) as executor:
            futures = [executor.submit(worker) for _ in range(concurrent_threads)]
            
            # 等待所有线程完成
            for future in as_completed(futures, timeout=test_duration + 10):
                try:
                    future.result()
                except Exception as e:
                    print(f"线程执行错误: {e}")
        
        return self.analyze_results(results, rule_config, 'tenant_resource_level')
    
    def analyze_results(self, results, rule_config, test_type):
        """
        分析测试结果
        
        Args:
            results: 测试结果列表
            rule_config: 规则配置
            test_type: 测试类型
        
        Returns:
            dict: 分析统计结果
        """
        if not results:
            return {
                'test_type': test_type,
                'rule_config': rule_config,
                'total_requests': 0,
                'success_requests': 0,
                'blocked_requests': 0,
                'error_requests': 0,
                'avg_response_time': 0,
                'test_passed': False,
                'message': '没有测试结果'
            }
        
        total_requests = len(results)
        success_requests = len([r for r in results if r['success'] and not r['blocked']])
        blocked_requests = len([r for r in results if r['blocked']])
        error_requests = len([r for r in results if not r['success']])
        
        response_times = [r['response_time'] for r in results if r['success']]
        avg_response_time = statistics.mean(response_times) if response_times else 0
        
        # 判断测试是否通过
        test_passed = self.evaluate_test_result(rule_config, success_requests, blocked_requests, total_requests)
        
        stats = {
            'test_type': test_type,
            'rule_config': rule_config,
            'total_requests': total_requests,
            'success_requests': success_requests,
            'blocked_requests': blocked_requests,
            'error_requests': error_requests,
            'avg_response_time': avg_response_time,
            'test_passed': test_passed,
            'success_rate': success_requests / total_requests if total_requests > 0 else 0,
            'block_rate': blocked_requests / total_requests if total_requests > 0 else 0
        }
        
        # 打印结果
        print(f"\n测试结果 ({test_type}):")
        print(f"  总请求数: {total_requests}")
        print(f"  成功请求数: {success_requests}")
        print(f"  被阻止请求数: {blocked_requests}")
        print(f"  错误请求数: {error_requests}")
        print(f"  平均响应时间: {avg_response_time:.3f}秒")
        print(f"  成功率: {stats['success_rate']:.2%}")
        print(f"  阻止率: {stats['block_rate']:.2%}")
        print(f"  测试结果: {'通过' if test_passed else '失败'}")
        
        return stats
    
    def evaluate_test_result(self, rule_config, success_requests, blocked_requests, total_requests):
        """
        评估测试结果是否符合预期
        
        Args:
            rule_config: 规则配置
            success_requests: 成功请求数
            blocked_requests: 被阻止请求数
            total_requests: 总请求数
        
        Returns:
            bool: 测试是否通过
        """
        if total_requests == 0:
            return False
        
        limit_count = rule_config['count']
        grade = rule_config['grade']  # 0=线程数限流, 1=QPS限流
        control_behavior = rule_config.get('control_behavior', 0)
        
        # 基本检查：应该有被阻止的请求
        if blocked_requests == 0:
            print(f"  警告: 没有请求被阻止，可能限流规则未生效")
            return False
        
        # 对于线程数限流
        if grade == 0:
            # 线程数限流的成功请求数应该接近限制值
            expected_success = limit_count
            tolerance = max(2, limit_count * 0.3)  # 30%的容差
            
            if abs(success_requests - expected_success) <= tolerance:
                return True
            else:
                print(f"  线程限流验证失败: 期望成功请求约{expected_success}个，实际{success_requests}个")
                return False
        
        # 对于QPS限流
        elif grade == 1:
            # QPS限流需要考虑时间窗口
            success_rate = success_requests / total_requests
            
            # 如果阻止率在合理范围内，认为测试通过
            if 0.1 <= success_rate <= 0.9:  # 10%-90%的成功率都是合理的
                return True
            else:
                print(f"  QPS限流验证: 成功率{success_rate:.2%}")
                return success_rate > 0.05  # 至少5%的成功率
        
        return False
    
    def get_tenant_rules_from_db(self):
        """
        从数据库查询租户限流规则
        
        Returns:
            list: 租户规则列表
        """
        if not self.db_config:
            print("未配置数据库连接，使用默认规则")
            return self.create_default_rules()
        
        try:
            connection = mysql.connector.connect(**self.db_config)
            cursor = connection.cursor(dictionary=True)
            
            # 查询启用的租户限流规则
            query = """
            SELECT id, rule_name, tenant_id, ref_resource, grade, count, 
                   limit_mode, control_behavior, warm_up_period_sec, 
                   max_queueing_time_ms, priority, enabled, description
            FROM tenant_flow_rules 
            WHERE enabled = 1 
            ORDER BY tenant_id, grade, priority
            """
            
            cursor.execute(query)
            rules = cursor.fetchall()
            
            print(f"从数据库查询到 {len(rules)} 条租户限流规则")
            
            if not rules:
                print("数据库中没有找到启用的租户限流规则，使用默认规则")
                return self.create_default_rules()
            
            # 按租户分组规则
            tenant_rules = defaultdict(list)
            for rule in rules:
                tenant_rules[rule['tenant_id']].append(rule)
            
            print(f"涉及租户数: {len(tenant_rules)}")
            for tenant_id, tenant_rule_list in tenant_rules.items():
                thread_rules = [r for r in tenant_rule_list if r['grade'] == 0]
                qps_rules = [r for r in tenant_rule_list if r['grade'] == 1]
                print(f"  租户 {tenant_id}: 线程限流规则 {len(thread_rules)} 条, QPS限流规则 {len(qps_rules)} 条")
            
            return list(rules)
            
        except Error as e:
            print(f"数据库查询错误: {e}")
            print("使用默认规则进行测试")
            return self.create_default_rules()
            
        finally:
            if 'connection' in locals() and connection.is_connected():
                cursor.close()
                connection.close()
    
    def create_default_rules(self):
        """
        创建默认测试规则
        
        Returns:
            list: 默认规则列表
        """
        return [
            {
                'id': 1,
                'tenant_id': 'tenant1',
                'rule_name': '租户1线程限流',
                'ref_resource': None,
                'grade': 0,  # 线程数限流
                'count': 5,
                'control_behavior': 0,  # 快速失败
                'enabled': 1,
                'description': '租户1的线程数限流规则'
            },
            {
                'id': 2,
                'tenant_id': 'tenant1',
                'rule_name': '租户1接口限流',
                'ref_resource': 'userInfo',
                'grade': 1,  # QPS限流
                'count': 10,
                'control_behavior': 0,  # 快速失败
                'enabled': 1,
                'description': '租户1的接口QPS限流规则'
            },
            {
                'id': 3,
                'tenant_id': 'tenant2',
                'rule_name': '租户2QPS限流',
                'ref_resource': None,
                'grade': 1,  # QPS限流
                'count': 20,
                'control_behavior': 0,  # 快速失败
                'enabled': 1,
                'description': '租户2的QPS限流规则'
            },
            {
                'id': 4,
                'tenant_id': 'tenant3',
                'rule_name': '租户3线程限流',
                'ref_resource': None,
                'grade': 0,  # 线程数限流
                'count': 3,
                'control_behavior': 0,  # 快速失败
                'enabled': 1,
                'description': '租户3的线程数限流规则'
            }
        ]
    
    def run_comprehensive_test(self, rules, concurrent_threads=10, test_duration=30):
        """
        运行综合测试
        
        Args:
            rules: 规则列表
            concurrent_threads: 并发线程数
            test_duration: 测试持续时间
        
        Returns:
            list: 所有测试结果
        """
        all_results = []
        
        # 按租户分组规则
        tenant_rules = defaultdict(list)
        for rule in rules:
            tenant_rules[rule['tenant_id']].append(rule)
        
        for tenant_id, tenant_rule_list in tenant_rules.items():
            print(f"\n{'='*60}")
            print(f"开始测试租户: {tenant_id}")
            print(f"{'='*60}")
            
            # 找到租户级别的规则（ref_resource为空或None）
            tenant_level_rules = [r for r in tenant_rule_list if not r.get('ref_resource')]
            resource_level_rules = [r for r in tenant_rule_list if r.get('ref_resource')]
            
            # 测试租户级别限流
            for rule in tenant_level_rules:
                try:
                    result = self.test_tenant_level_flow_control(
                        tenant_id, rule, concurrent_threads, test_duration
                    )
                    all_results.append(result)
                except Exception as e:
                    print(f"租户级别限流测试失败: {e}")
            
            # 测试租户+接口级别限流
            for rule in resource_level_rules:
                try:
                    result = self.test_tenant_resource_flow_control(
                        tenant_id, rule['ref_resource'], rule, concurrent_threads, test_duration
                    )
                    all_results.append(result)
                except Exception as e:
                    print(f"租户+接口级别限流测试失败: {e}")
            
            # 在租户测试之间稍作停顿
            time.sleep(2)
        
        return all_results
    
    def save_report(self, results, filename=None):
        """
        保存测试报告
        
        Args:
            results: 测试结果列表
            filename: 报告文件名
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'comprehensive_flow_test_report_{timestamp}.json'
        
        report = {
            'test_time': datetime.now().isoformat(),
            'total_tests': len(results),
            'passed_tests': len([r for r in results if r['test_passed']]),
            'failed_tests': len([r for r in results if not r['test_passed']]),
            'results': results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n测试报告已保存到: {filename}")
    
    def print_summary(self, results):
        """
        打印测试摘要
        
        Args:
            results: 测试结果列表
        """
        if not results:
            print("\n没有测试结果")
            return
        
        print(f"\n{'='*80}")
        print(f"综合租户限流测试摘要")
        print(f"{'='*80}")
        
        total_tests = len(results)
        passed_tests = len([r for r in results if r['test_passed']])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"通过率: {passed_tests/total_tests:.1%}")
        
        # 按测试类型统计
        tenant_level_tests = [r for r in results if r['test_type'] == 'tenant_level']
        resource_level_tests = [r for r in results if r['test_type'] == 'tenant_resource_level']
        
        print(f"\n按测试类型统计:")
        print(f"  租户级别限流测试: {len(tenant_level_tests)} 个")
        print(f"  租户+接口级别限流测试: {len(resource_level_tests)} 个")
        
        # 按限流类型统计
        thread_limit_tests = [r for r in results if r['rule_config']['grade'] == 0]
        qps_limit_tests = [r for r in results if r['rule_config']['grade'] == 1]
        
        print(f"\n按限流类型统计:")
        print(f"  线程数限流测试: {len(thread_limit_tests)} 个 (通过: {len([r for r in thread_limit_tests if r['test_passed']])} 个)")
        print(f"  QPS限流测试: {len(qps_limit_tests)} 个 (通过: {len([r for r in qps_limit_tests if r['test_passed']])} 个)")
        
        # 显示失败的测试
        failed_results = [r for r in results if not r['test_passed']]
        if failed_results:
            print(f"\n失败的测试详情:")
            for i, result in enumerate(failed_results, 1):
                rule = result['rule_config']
                limit_type = '线程数限流' if rule['grade'] == 0 else 'QPS限流'
                print(f"  {i}. 租户: {rule['tenant_id']}, 类型: {limit_type}, 测试类型: {result['test_type']}")
                print(f"     成功请求: {result['success_requests']}, 阻止请求: {result['blocked_requests']}")


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='综合租户限流测试工具')
    parser.add_argument('--url', default='http://localhost:8088', help='测试目标URL')
    parser.add_argument('--concurrent', type=int, default=10, help='并发线程数')
    parser.add_argument('--test-duration', type=int, default=30, help='每个测试的持续时间（秒）')
    parser.add_argument('--tenant', help='只测试指定租户')
    parser.add_argument('--output', help='输出报告文件名')
    parser.add_argument('--use-db', action='store_true', help='从数据库查询租户规则')
    parser.add_argument('--db-host', default='localhost', help='数据库主机')
    parser.add_argument('--db-port', type=int, default=33016, help='数据库端口')
    parser.add_argument('--db-user', default='root', help='数据库用户名')
    parser.add_argument('--db-password', default='123456', help='数据库密码')
    parser.add_argument('--db-name', default='flow_control', help='数据库名称')
    parser.add_argument('--list-rules', action='store_true', help='显示所有规则')
    
    args = parser.parse_args()
    
    # 创建数据库配置
    db_config = None
    if args.use_db:
        db_config = {
            'host': args.db_host,
            'port': args.db_port,
            'user': args.db_user,
            'password': args.db_password,
            'database': args.db_name
        }
    
    # 创建测试器
    tester = ComprehensiveTenantFlowTester(base_url=args.url, db_config=db_config)
    
    # 获取规则
    if args.use_db:
        print("从数据库查询租户限流规则...")
        rules = tester.get_tenant_rules_from_db()
    else:
        print("使用默认租户限流规则...")
        rules = tester.create_default_rules()
    
    # 显示规则列表
    if args.list_rules:
        print("\n租户限流规则列表:")
        print("-" * 100)
        for rule in rules:
            limit_type = '线程数限流' if rule['grade'] == 0 else 'QPS限流'
            level_type = '租户级别' if not rule.get('ref_resource') else f"接口级别({rule['ref_resource']})"
            print(f"租户: {rule['tenant_id']}, {level_type}, {limit_type}, 限制: {rule['count']}")
        return
    
    # 过滤租户
    if args.tenant:
        rules = [rule for rule in rules if rule['tenant_id'] == args.tenant]
        if not rules:
            print(f"错误: 未找到租户 '{args.tenant}' 的规则")
            return
    
    print(f"\n开始综合租户限流测试...")
    print(f"目标URL: {args.url}")
    print(f"并发线程数: {args.concurrent}")
    print(f"每个测试持续时间: {args.test_duration}秒")
    print(f"测试规则数: {len(rules)}")
    
    # 执行测试
    try:
        results = tester.run_comprehensive_test(rules, args.concurrent, args.test_duration)
        
        # 保存和显示结果
        if results:
            tester.save_report(results, args.output)
            tester.print_summary(results)
        else:
            print("没有成功完成的测试")
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试执行错误: {e}")


if __name__ == '__main__':
    main()