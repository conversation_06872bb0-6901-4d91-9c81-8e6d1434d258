package com.example.admin.service;

import com.example.admin.vo.IPFlowRuleVO;

import java.util.List;

/**
 * IP规则匹配服务接口
 */
public interface IPRuleMatchService {

	/**
	 * 检查IP是否匹配规则
	 * 
	 * @param ipAddress 客户端IP地址
	 * @param tenantId  租户ID
	 * @return 匹配结果
	 */
	IPRuleMatchResult matchIPRule(String ipAddress, String tenantId);

	/**
	 * 获取匹配的规则列表
	 * 
	 * @param ipAddress 客户端IP地址
	 * @param tenantId  租户ID
	 * @return 匹配的规则列表
	 */
	List<IPFlowRuleVO> getMatchingRules(String ipAddress, String tenantId);

	/**
	 * 检查IP是否在黑名单中
	 * 
	 * @param ipAddress 客户端IP地址
	 * @param tenantId  租户ID
	 * @return 是否在黑名单中
	 */
	boolean isIPInBlacklist(String ipAddress, String tenantId);

	/**
	 * 检查IP是否在白名单中
	 * 
	 * @param ipAddress 客户端IP地址
	 * @param tenantId  租户ID
	 * @return 是否在白名单中
	 */
	boolean isIPInWhitelist(String ipAddress, String tenantId);

	/**
	 * 获取IP的限流规则
	 * 
	 * @param ipAddress 客户端IP地址
	 * @param tenantId  租户ID
	 * @return 限流规则，如果没有则返回null
	 */
	IPFlowRuleVO getIPLimitRule(String ipAddress, String tenantId);

	/**
	 * 刷新规则缓存
	 * 
	 * @param tenantId 租户ID，如果为null则刷新所有租户的缓存
	 */
	void refreshRuleCache(String tenantId);

	/**
	 * IP规则匹配结果
	 */
	class IPRuleMatchResult {
		private IPFlowRuleVO rule;
		private boolean matched;
		private String matchType; // BLACKLIST, WHITELIST, LIMIT

		public IPRuleMatchResult(IPFlowRuleVO rule, boolean matched) {
			this.rule = rule;
			this.matched = matched;
			if (rule != null) {
				this.matchType = rule.getListType();
			}
		}

		public IPRuleMatchResult(IPFlowRuleVO rule, boolean matched, String matchType) {
			this.rule = rule;
			this.matched = matched;
			this.matchType = matchType;
		}

		// Getters and setters
		public IPFlowRuleVO getRule() {
			return rule;
		}

		public void setRule(IPFlowRuleVO rule) {
			this.rule = rule;
		}

		public boolean isMatched() {
			return matched;
		}

		public void setMatched(boolean matched) {
			this.matched = matched;
		}

		public String getMatchType() {
			return matchType;
		}

		public void setMatchType(String matchType) {
			this.matchType = matchType;
		}
	}
}
