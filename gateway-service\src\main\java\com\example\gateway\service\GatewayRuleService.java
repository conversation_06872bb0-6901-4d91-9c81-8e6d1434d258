package com.example.gateway.service;

import com.example.common.entity.IPFlowRule;
import com.example.common.entity.IPBlacklist;
import com.example.common.entity.IPWhitelist;
import com.example.common.entity.TenantFlowRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GatewayRuleService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public List<IPFlowRule> getAllIPFlowRules() {
        String sql = "SELECT * FROM ip_flow_rule WHERE status = 1";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(IPFlowRule.class));
    }

    public List<IPWhitelist> getAllIPWhitelists() {
        String sql = "SELECT * FROM ip_whitelist WHERE enabled = 1";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(IPWhitelist.class));
    }

    public List<IPBlacklist> getAllIPBlacklists() {
        String sql = "SELECT * FROM ip_blacklist WHERE enabled = 1";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(IPBlacklist.class));
    }

    public List<TenantFlowRule> getAllTenantFlowRules() {
        String sql = "SELECT * FROM tenant_flow_rules WHERE enabled = 1";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(TenantFlowRule.class));
    }

    /**
     * 获取所有启用的流量规则（包括通用流量规则）
     */
    public List<com.example.common.entity.FlowRuleEntity> getAllFlowRules() {
        String sql = "SELECT * FROM flow_rule WHERE status = 1";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(com.example.common.entity.FlowRuleEntity.class));
    }

    /**
     * 获取所有启用的租户规则
     */
    public List<com.example.common.entity.TenantRuleEntity> getAllTenantRules() {
        String sql = "SELECT * FROM tenant_flow_rules WHERE enabled = 1 AND deleted = 0";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(com.example.common.entity.TenantRuleEntity.class));
    }
}