// 主题颜色（使用CSS变量）
$primary-color: var(--primary-color);
$success-color: var(--success-color);
$warning-color: var(--warning-color);
$danger-color: var(--danger-color);
$info-color: var(--info-color);

// 文本颜色（使用CSS变量）
$text-primary: var(--text-primary);
$text-regular: var(--text-regular);
$text-secondary: var(--text-secondary);
$text-placeholder: var(--text-placeholder);

// 边框颜色（使用CSS变量）
$border-base: var(--border-base);
$border-light: var(--border-light);
$border-lighter: var(--border-lighter);
$border-extra-light: var(--border-extra-light);

// 背景颜色（使用CSS变量）
$background-base: var(--background-base);
$background-light: var(--background-light);
$background-white: var(--background-white);

// 布局尺寸
$header-height: 60px;
$sidebar-width: 200px;
$sidebar-collapsed-width: 64px;

// 字体大小
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

// 圆角
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-circle: 50%;

// 阴影（使用CSS变量）
$box-shadow-base: var(--box-shadow-base);
$box-shadow-dark: var(--box-shadow-dark);
$box-shadow-light: var(--box-shadow-light);

// 动画时间
$transition-base: all .3s;
$transition-fade: opacity .3s;
$transition-md-fade: transform .3s, opacity .3s;
$transition-border: border-color .2s;
$transition-color: color .2s;