package com.example.admin.dto;



import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * 流控日志数据传输对象
 * 用于接收和传输流控日志数据
 */

public class FlowControlLogDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则类型：1-流控规则，2-IP流控规则
     */
    @NotNull(message = "规则类型不能为空")
    @Min(value = 1, message = "规则类型最小值为1")
    @Max(value = 2, message = "规则类型最大值为2")
    private Integer ruleType;

    /**
     * 资源名称
     */
    @NotBlank(message = "资源名称不能为空")
    private String resource;

    /**
     * 请求IP
     */
    private String requestIp;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 触发动作：1-通过，2-限流，3-拒绝
     */
    @NotNull(message = "触发动作不能为空")
    @Min(value = 1, message = "触发动作最小值为1")
    @Max(value = 3, message = "触发动作最大值为3")
    private Integer action;

    /**
     * 动作描述
     */
    private String actionDesc;

    /**
     * 限流阈值
     */
    private Double threshold;

    /**
     * 当前QPS
     */
    private Double currentQps;

    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;

    /**
     * 异常信息
     */
    private String exceptionMsg;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 响应结果
     */
    private String responseResult;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 服务器IP
     */
    private String serverIp;

    /**
     * 服务器端口
     */
    private Integer serverPort;

    /**
     * 日志级别：1-INFO，2-WARN，3-ERROR
     */
    @Min(value = 1, message = "日志级别最小值为1")
    @Max(value = 3, message = "日志级别最大值为3")
    private Integer logLevel;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 日期（用于分表）
     */
    private String logDate;

    /**
     * 扩展字段1
     */
    private String ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 备注
     */
    private String remark;
}