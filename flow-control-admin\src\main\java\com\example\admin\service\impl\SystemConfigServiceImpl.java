package com.example.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.dto.SystemConfigDTO;
import com.example.common.entity.SystemConfig;
import com.example.admin.mapper.SystemConfigMapper;
import com.example.admin.service.SystemConfigService;
import com.example.admin.vo.SystemConfigVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.Objects;

/**
 * 系统配置服务实现类
 */
@Service
public class SystemConfigServiceImpl extends ServiceImpl<SystemConfigMapper, SystemConfig> implements SystemConfigService {
    
    @Resource
    private SystemConfigMapper systemConfigMapper;
    
    // 邮箱格式验证正则
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$");
    
    // URL格式验证正则
    private static final Pattern URL_PATTERN = Pattern.compile(
            "^(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]");
    
    @Override
    public Page<SystemConfigVO> selectSystemConfigPage(Page<SystemConfigVO> page, String configKey, String configType,
                                                       String tenantId, Integer status) {
        return systemConfigMapper.selectSystemConfigPage(page, configKey, configType, tenantId, status, null);
    }
    
    @Override
    public SystemConfigVO getSystemConfigById(Long id) {
        SystemConfig systemConfig = this.getById(id);
        if (systemConfig == null) {
            return null;
        }
        SystemConfigVO systemConfigVO = new SystemConfigVO();
        BeanUtils.copyProperties(systemConfig, systemConfigVO);
        return systemConfigVO;
    }
    
    @Override
    public SystemConfigVO getSystemConfigByKey(String configKey, String tenantId) {
        return systemConfigMapper.selectByConfigKeyAndTenantId(configKey, tenantId);
    }
    
    @Override
    public List<SystemConfigVO> getSystemConfigsByType(String configType, String tenantId, Integer status, Integer limit) {
        return systemConfigMapper.selectByConfigType(configType, tenantId, status);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createSystemConfig(SystemConfigDTO systemConfigDTO) {
        // 检查配置键是否已存在
        if (existsByConfigKey(systemConfigDTO.getConfigKey(), systemConfigDTO.getTenantId(), null)) {
            throw new RuntimeException("配置键已存在: " + systemConfigDTO.getConfigKey());
        }
        
        // 验证配置值格式
        if (!validateConfigValue(systemConfigDTO.getConfigKey(), systemConfigDTO.getConfigValue())) {
            throw new RuntimeException("配置值格式不正确");
        }
        
        SystemConfig systemConfig = new SystemConfig();
        BeanUtils.copyProperties(systemConfigDTO, systemConfig);
        systemConfig.setCreateTime(LocalDateTime.now());
        systemConfig.setUpdateTime(LocalDateTime.now());
        
        return this.save(systemConfig);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSystemConfig(Long id, SystemConfigDTO systemConfigDTO) {
        SystemConfig existingConfig = this.getById(id);
        if (existingConfig == null) {
            throw new RuntimeException("系统配置不存在");
        }
        
        // 如果修改了配置键，检查新键是否已存在
        if (!existingConfig.getConfigKey().equals(systemConfigDTO.getConfigKey())) {
            if (existsByConfigKey(systemConfigDTO.getConfigKey(), systemConfigDTO.getTenantId(), id)) {
                throw new RuntimeException("配置键已存在: " + systemConfigDTO.getConfigKey());
            }
        }
        
        // 验证配置值格式
        if (!validateConfigValue(systemConfigDTO.getConfigKey(), systemConfigDTO.getConfigValue())) {
            throw new RuntimeException("配置值格式不正确");
        }
        
        BeanUtils.copyProperties(systemConfigDTO, existingConfig);
        existingConfig.setId(id);
        existingConfig.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(existingConfig);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSystemConfig(Long id) {
        return this.removeById(id);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteSystemConfigs(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        return this.removeByIds(ids);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConfigStatus(Long id, Integer status) {
        return systemConfigMapper.updateConfigStatus(id, status, LocalDateTime.now()) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateConfigStatus(List<Long> ids, Integer status) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        return systemConfigMapper.batchUpdateConfigStatus(ids, status, LocalDateTime.now()) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableSystemConfig(Long id) {
        return updateConfigStatus(id, 1);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableSystemConfig(Long id) {
        return updateConfigStatus(id, 0);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> ids, Integer status) {
        return batchUpdateConfigStatus(ids, status);
    }
    
    @Override
    public boolean existsByConfigKey(String configKey, String tenantId, Long excludeId) {
        return systemConfigMapper.existsByConfigKey(configKey, tenantId, excludeId) > 0;
    }
    
    @Override
    public Long countSystemConfigs(String configType, String tenantId, Integer status) {
        // 使用现有的查询方法来计算数量
        List<SystemConfigVO> configs = systemConfigMapper.selectByConfigType(configType, tenantId, status);
        return (long) configs.size();
    }
    
    @Override
    public Map<String, Object> batchOperateSystemConfigs(List<SystemConfigDTO> systemConfigDTOList, String operation) {
        Map<String, Object> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(systemConfigDTOList)) {
            result.put("success", false);
            result.put("message", "操作列表不能为空");
            return result;
        }
        
        try {
            boolean success = false;
            String message = "操作完成";
            
            switch (operation.toLowerCase()) {
                case "create":
                    success = batchCreateSystemConfigs(systemConfigDTOList);
                    message = success ? "批量创建成功" : "批量创建失败";
                    break;
                case "update":
                    success = batchUpdateSystemConfigs(systemConfigDTOList);
                    message = success ? "批量更新成功" : "批量更新失败";
                    break;
                case "delete":
                    List<Long> ids = systemConfigDTOList.stream()
                            .map(SystemConfigDTO::getId)
                            .filter(Objects::nonNull)
                            .collect(java.util.stream.Collectors.toList());
                    success = batchDeleteSystemConfigs(ids);
                    message = success ? "批量删除成功" : "批量删除失败";
                    break;
                default:
                    result.put("success", false);
                    result.put("message", "不支持的操作类型: " + operation);
                    return result;
            }
            
            result.put("success", success);
            result.put("message", message);
            result.put("count", systemConfigDTOList.size());
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public List<Map<String, Object>> getConfigTypeStatistics(String tenantId) {
        return systemConfigMapper.selectTypeStatistics();
    }
    
    @Override
    public List<Map<String, Object>> getConfigStatusStatistics(String tenantId, String configType) {
        return systemConfigMapper.selectStatusStatistics();
    }
    
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateSystemConfigs(List<SystemConfigDTO> systemConfigDTOList) {
        if (CollectionUtils.isEmpty(systemConfigDTOList)) {
            return false;
        }
        
        List<SystemConfig> systemConfigList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (SystemConfigDTO dto : systemConfigDTOList) {
            // 检查配置键是否已存在
            if (existsByConfigKey(dto.getConfigKey(), dto.getTenantId(), null)) {
                throw new RuntimeException("配置键已存在: " + dto.getConfigKey());
            }
            
            // 验证配置值格式
            Map<String, Object> validationResult = validateConfigValue(dto.getConfigKey(), dto.getConfigValue(), dto.getConfigType());
            if (!(Boolean) validationResult.get("valid")) {
                throw new RuntimeException("配置值格式不正确: " + dto.getConfigKey());
            }
            
            SystemConfig systemConfig = new SystemConfig();
            BeanUtils.copyProperties(dto, systemConfig);
            systemConfig.setCreateTime(now);
            systemConfig.setUpdateTime(now);
            systemConfigList.add(systemConfig);
        }
        
        return systemConfigMapper.batchInsertOrUpdate(systemConfigList) > 0;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateSystemConfigs(List<SystemConfigDTO> systemConfigDTOList) {
        if (CollectionUtils.isEmpty(systemConfigDTOList)) {
            return false;
        }
        
        List<SystemConfig> systemConfigList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (SystemConfigDTO dto : systemConfigDTOList) {
            // 验证配置值格式
            Map<String, Object> validationResult = validateConfigValue(dto.getConfigKey(), dto.getConfigValue(), dto.getConfigType());
            if (!(Boolean) validationResult.get("valid")) {
                throw new RuntimeException("配置值格式不正确: " + dto.getConfigKey());
            }
            
            SystemConfig systemConfig = new SystemConfig();
            BeanUtils.copyProperties(dto, systemConfig);
            systemConfig.setUpdateTime(now);
            systemConfigList.add(systemConfig);
        }
        
        return systemConfigMapper.batchInsertOrUpdate(systemConfigList) > 0;
    }
    
    @Override
    public List<SystemConfigVO> getSystemDefaultConfigs(String configType, Integer limit) {
        List<SystemConfig> configs = systemConfigMapper.selectSystemDefaults();
        return configs.stream()
                .filter(config -> configType == null || configType.equals(config.getConfigType()))
                .limit(limit != null ? limit : Long.MAX_VALUE)
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<SystemConfigVO> getTenantSpecificConfigs(String tenantId, String configType, Integer limit) {
        List<SystemConfig> configs = systemConfigMapper.selectTenantConfigs(tenantId);
        return configs.stream()
                .filter(config -> configType == null || configType.equals(config.getConfigType()))
                .limit(limit != null ? limit : Long.MAX_VALUE)
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<SystemConfigVO> getSystemConfigsByTenantId(String tenantId, String configType, Integer status, Integer limit) {
        List<SystemConfigVO> configs = systemConfigMapper.selectByConfigType(configType, tenantId, status);
        if (limit != null && limit > 0) {
            return configs.stream().limit(limit).collect(java.util.stream.Collectors.toList());
        }
        return configs;
    }

    @Override
    public List<SystemConfigVO> getFlowControlConfigs(String tenantId, Integer limit) {
        List<SystemConfig> configs = systemConfigMapper.selectFlowControlConfigs();
        return configs.stream()
                .filter(config -> tenantId == null || tenantId.equals(config.getTenantId()))
                .limit(limit != null ? limit : Long.MAX_VALUE)
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<SystemConfigVO> getMonitorConfigs(String tenantId, Integer limit) {
        List<SystemConfig> configs = systemConfigMapper.selectMonitorConfigs();
        return configs.stream()
                .filter(config -> tenantId == null || tenantId.equals(config.getTenantId()))
                .limit(limit != null ? limit : Long.MAX_VALUE)
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<SystemConfigVO> getAlarmConfigs(String tenantId, Integer limit) {
        List<SystemConfig> configs = systemConfigMapper.selectAlarmConfigs();
        return configs.stream()
                .filter(config -> tenantId == null || tenantId.equals(config.getTenantId()))
                .limit(limit != null ? limit : Long.MAX_VALUE)
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<SystemConfigVO> exportSystemConfigs(String configType, String tenantId, Integer status) {
        // 注意：selectForExport只接受configType和status参数
        List<SystemConfig> configs = systemConfigMapper.selectForExport(configType, status);
        // 转换为VO并根据tenantId过滤（如果需要）
        return configs.stream()
                .filter(config -> tenantId == null || tenantId.equals(config.getTenantId()))
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Transactional(rollbackFor = Exception.class)
    public boolean resetConfigsToDefault(String tenantId, String configType) {
        // 查询需要重置的配置键
        List<SystemConfigVO> configs = getSystemConfigsByTenantId(tenantId, configType, null, null);
        List<String> configKeys = configs.stream()
                .map(SystemConfigVO::getConfigKey)
                .collect(java.util.stream.Collectors.toList());
        return systemConfigMapper.resetToDefault(configKeys, "system") > 0;
    }
    

    
    @Override
    public Map<String, Object> validateConfigValue(String configKey, String configValue, String configType) {
        Map<String, Object> result = new HashMap<>();
        
        if (!StringUtils.hasText(configValue)) {
            result.put("valid", false);
            result.put("message", "配置值不能为空");
            return result;
        }
        
        boolean isValid = true;
        String message = "验证通过";
        
        // 根据配置键的类型进行不同的验证
        if (configKey.toLowerCase().contains("email")) {
            isValid = EMAIL_PATTERN.matcher(configValue).matches();
            if (!isValid) message = "邮箱格式不正确";
        } else if (configKey.toLowerCase().contains("url") || configKey.toLowerCase().contains("endpoint")) {
            isValid = URL_PATTERN.matcher(configValue).matches();
            if (!isValid) message = "URL格式不正确";
        } else if (configKey.toLowerCase().contains("port")) {
            try {
                int port = Integer.parseInt(configValue);
                isValid = port > 0 && port <= 65535;
                if (!isValid) message = "端口号必须在1-65535之间";
            } catch (NumberFormatException e) {
                isValid = false;
                message = "端口号必须是数字";
            }
        } else if (configKey.toLowerCase().contains("timeout") || configKey.toLowerCase().contains("interval")) {
            try {
                long value = Long.parseLong(configValue);
                isValid = value > 0;
                if (!isValid) message = "超时时间必须大于0";
            } catch (NumberFormatException e) {
                isValid = false;
                message = "超时时间必须是数字";
            }
        } else if (configKey.toLowerCase().contains("size") || configKey.toLowerCase().contains("limit")) {
            try {
                int value = Integer.parseInt(configValue);
                isValid = value >= 0;
                if (!isValid) message = "大小限制不能为负数";
            } catch (NumberFormatException e) {
                isValid = false;
                message = "大小限制必须是数字";
            }
        } else if (configKey.toLowerCase().contains("enable") || configKey.toLowerCase().contains("flag")) {
            isValid = "true".equalsIgnoreCase(configValue) || "false".equalsIgnoreCase(configValue);
            if (!isValid) message = "布尔值必须是true或false";
        }
        
        result.put("valid", isValid);
        result.put("message", message);
        result.put("configKey", configKey);
        result.put("configValue", configValue);
        result.put("configType", configType);
        
        return result;
    }
    
    /**
     * 简化版本的验证方法，用于内部调用
     */
    private boolean validateConfigValue(String configKey, String configValue) {
        Map<String, Object> result = validateConfigValue(configKey, configValue, null);
        return (Boolean) result.get("valid");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetConfigToDefault(String configKey, String tenantId) {
        List<String> configKeys = new ArrayList<>();
        configKeys.add(configKey);
        return systemConfigMapper.resetToDefault(configKeys, "system") > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsertOrUpdateConfigs(List<SystemConfigDTO> systemConfigDTOList) {
        if (CollectionUtils.isEmpty(systemConfigDTOList)) {
            return false;
        }
        
        List<SystemConfig> systemConfigList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (SystemConfigDTO dto : systemConfigDTOList) {
            SystemConfig config = new SystemConfig();
            BeanUtils.copyProperties(dto, config);
            config.setCreateTime(now);
            config.setUpdateTime(now);
            config.setDeleted(0);
            systemConfigList.add(config);
        }
        
        return systemConfigMapper.batchInsertOrUpdate(systemConfigList) > 0;
    }
    
    @Override
    public <T> T getConfigValue(String configKey, String tenantId, T defaultValue, Class<T> targetType) {
        try {
            SystemConfigVO config = getSystemConfigByKey(configKey, tenantId);
            if (config == null || config.getConfigValue() == null) {
                return defaultValue;
            }
            
            String configValue = config.getConfigValue();
            
            // 根据目标类型进行转换
            if (targetType == String.class) {
                return targetType.cast(configValue);
            } else if (targetType == Integer.class) {
                return targetType.cast(Integer.valueOf(configValue));
            } else if (targetType == Long.class) {
                return targetType.cast(Long.valueOf(configValue));
            } else if (targetType == Boolean.class) {
                return targetType.cast(Boolean.valueOf(configValue));
            } else if (targetType == Double.class) {
                return targetType.cast(Double.valueOf(configValue));
            } else if (targetType == Float.class) {
                return targetType.cast(Float.valueOf(configValue));
            } else {
                // 对于其他类型，尝试直接转换
                return targetType.cast(configValue);
            }
        } catch (Exception e) {
            return defaultValue;
        }
    }
    
    @Override
    public boolean setConfigValue(String configKey, String configValue, String tenantId, String configType) {
        try {
            // 验证配置值
            if (!validateConfigValue(configKey, configValue)) {
                return false;
            }
            
            // 查找现有配置
            SystemConfigVO existingConfig = getSystemConfigByKey(configKey, tenantId);
            
            if (existingConfig != null) {
                // 更新现有配置
                SystemConfigDTO updateDTO = new SystemConfigDTO();
                updateDTO.setConfigKey(configKey);
                updateDTO.setConfigValue(configValue);
                updateDTO.setConfigType(configType);
                updateDTO.setTenantId(tenantId);
                return updateSystemConfig(existingConfig.getId(), updateDTO);
            } else {
                // 创建新配置
                SystemConfigDTO createDTO = new SystemConfigDTO();
                createDTO.setConfigKey(configKey);
                createDTO.setConfigValue(configValue);
                createDTO.setConfigType(configType != null ? configType : "SYSTEM");
                createDTO.setTenantId(tenantId);
                createDTO.setStatus(1); // 启用状态
                return createSystemConfig(createDTO);
            }
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public boolean refreshConfigCache(String configKey, String tenantId) {
        try {
            // TODO: 实现配置缓存刷新逻辑
            // 可以使用Redis或本地缓存
            SystemConfigVO config = getSystemConfigByKey(configKey, tenantId);
            if (config != null) {
                // 更新缓存
                updateCache(configKey, tenantId, config);
            } else {
                // 删除缓存
                removeFromCache(configKey, tenantId);
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    public void clearConfigCache(String tenantId) {
        // TODO: 实现清除租户配置缓存逻辑
        clearTenantCache(tenantId);
    }
    
    @Override
    public Map<String, Object> getConfigCacheStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        try {
            // TODO: 实现获取配置缓存统计的逻辑
            statistics.put("totalCacheSize", 1000);
            statistics.put("hitCount", 850);
            statistics.put("missCount", 150);
            statistics.put("hitRate", 0.85);
            statistics.put("evictionCount", 10);
            statistics.put("averageLoadTime", 50.5);
            statistics.put("cacheEntries", 100);
            statistics.put("memoryUsage", "2.5MB");
        } catch (Exception e) {
            statistics.put("error", "获取缓存统计失败: " + e.getMessage());
        }
        return statistics;
    }
    
    public boolean syncConfigToRemote(String configKey, String tenantId, String targetSystem) {
        // TODO: 实现同步配置到远程系统的逻辑
        SystemConfigVO config = getSystemConfigByKey(configKey, tenantId);
        if (config == null) {
            return false;
        }
        
        switch (targetSystem.toLowerCase()) {
            case "nacos":
                return syncToNacos(config);
            case "apollo":
                return syncToApollo(config);
            case "consul":
                return syncToConsul(config);
            default:
                return false;
        }
    }
    
    @Override
    public Map<String, Object> syncConfigToNodes(String configKey, String tenantId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // TODO: 实现同步配置到其他节点的逻辑
            SystemConfigVO config = getSystemConfigByKey(configKey, tenantId);
            if (config == null) {
                result.put("success", false);
                result.put("message", "配置不存在");
                return result;
            }
            
            // 模拟同步到多个节点
            List<String> nodes = Arrays.asList("node1", "node2", "node3");
            List<String> successNodes = new ArrayList<>();
            List<String> failedNodes = new ArrayList<>();
            
            for (String node : nodes) {
                boolean syncResult = syncConfigToNode(config, node);
                if (syncResult) {
                    successNodes.add(node);
                } else {
                    failedNodes.add(node);
                }
            }
            
            result.put("success", failedNodes.isEmpty());
            result.put("message", failedNodes.isEmpty() ? "配置同步成功" : "部分节点同步失败");
            result.put("successNodes", successNodes);
            result.put("failedNodes", failedNodes);
            result.put("totalNodes", nodes.size());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "配置同步异常: " + e.getMessage());
        }
        return result;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public boolean backupConfigs(String tenantId, String backupName) {
        // TODO: 实现配置备份逻辑
        List<SystemConfig> configs = this.lambdaQuery()
                .eq(StringUtils.hasText(tenantId), SystemConfig::getTenantId, tenantId)
                .list();
        
        // 创建备份记录
        return createBackupRecord(tenantId, backupName, configs);
    }
    
    @Override
    public Map<String, Object> backupSystemConfigs(String tenantId, String configType) {
        Map<String, Object> result = new HashMap<>();
        try {
            // TODO: 实现系统配置备份逻辑
            List<SystemConfig> configs = this.lambdaQuery()
                    .eq(StringUtils.hasText(tenantId), SystemConfig::getTenantId, tenantId)
                    .eq(StringUtils.hasText(configType), SystemConfig::getConfigType, configType)
                    .list();
            
            if (CollectionUtils.isEmpty(configs)) {
                result.put("success", false);
                result.put("message", "没有找到需要备份的配置");
                return result;
            }
            
            // 生成备份ID
            String backupId = "backup_" + System.currentTimeMillis();
            
            // 创建备份记录
            boolean success = createBackupRecordWithId(backupId, tenantId, configType, configs);
            result.put("success", success);
            result.put("message", success ? "配置备份成功" : "配置备份失败");
            result.put("backupId", backupId);
            result.put("backupCount", configs.size());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "配置备份异常: " + e.getMessage());
        }
        return result;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public boolean restoreConfigs(String tenantId, String backupName) {
        // TODO: 实现配置恢复逻辑
        List<SystemConfig> backupConfigs = getBackupConfigs(tenantId, backupName);
        if (CollectionUtils.isEmpty(backupConfigs)) {
            return false;
        }
        
        // 删除现有配置
        this.lambdaUpdate()
                .eq(StringUtils.hasText(tenantId), SystemConfig::getTenantId, tenantId)
                .remove();
        
        // 恢复备份配置
        return this.saveBatch(backupConfigs);
    }
    
    @Override
    public Map<String, Object> restoreSystemConfigs(String backupId, String tenantId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // TODO: 实现系统配置恢复逻辑
            List<SystemConfig> backupConfigs = getBackupConfigsById(backupId, tenantId);
            if (CollectionUtils.isEmpty(backupConfigs)) {
                result.put("success", false);
                result.put("message", "备份配置不存在");
                return result;
            }
            
            // 删除现有配置
            this.lambdaUpdate()
                    .eq(StringUtils.hasText(tenantId), SystemConfig::getTenantId, tenantId)
                    .remove();
            
            // 恢复备份配置
            boolean success = this.saveBatch(backupConfigs);
            result.put("success", success);
            result.put("message", success ? "配置恢复成功" : "配置恢复失败");
            result.put("restoredCount", backupConfigs.size());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "配置恢复异常: " + e.getMessage());
        }
        return result;
    }
    
    @Override
    public boolean subscribeConfigChange(String configKey, String tenantId, Runnable callback) {
        // TODO: 实现配置变更订阅逻辑
        // 可以使用观察者模式或消息队列
        registerConfigChangeCallback(configKey, tenantId, callback);
        return true;
    }
    
    @Override
    public boolean unsubscribeConfigChange(String configKey, String tenantId) {
        // TODO: 实现取消配置变更订阅逻辑
        unregisterConfigChangeListener(configKey, tenantId);
        return true;
    }
    
    /**
     * 更新缓存
     */
    private void updateCache(String configKey, String tenantId, SystemConfigVO config) {
        // TODO: 实现缓存更新逻辑
        String cacheKey = buildCacheKey(configKey, tenantId);
        // redisTemplate.opsForValue().set(cacheKey, config, Duration.ofMinutes(30));
    }
    
    /**
     * 从缓存中删除
     */
    private void removeFromCache(String configKey, String tenantId) {
        // TODO: 实现缓存删除逻辑
        String cacheKey = buildCacheKey(configKey, tenantId);
        // redisTemplate.delete(cacheKey);
    }
    
    /**
     * 清除租户缓存
     */
    private void clearTenantCache(String tenantId) {
        // TODO: 实现清除租户缓存逻辑
        String pattern = "config:" + tenantId + ":*";
        // Set<String> keys = redisTemplate.keys(pattern);
        // if (!CollectionUtils.isEmpty(keys)) {
        //     redisTemplate.delete(keys);
        // }
    }
    
    /**
     * 构建缓存键
     */
    private String buildCacheKey(String configKey, String tenantId) {
        return "config:" + (StringUtils.hasText(tenantId) ? tenantId : "system") + ":" + configKey;
    }
    
    /**
     * 同步到Nacos
     */
    private boolean syncToNacos(SystemConfigVO config) {
        // TODO: 实现同步到Nacos的逻辑
        return true;
    }
    
    /**
     * 同步到Apollo
     */
    private boolean syncToApollo(SystemConfigVO config) {
        // TODO: 实现同步到Apollo的逻辑
        return true;
    }
    
    /**
     * 同步到Consul
     */
    private boolean syncToConsul(SystemConfigVO config) {
        // TODO: 实现同步到Consul的逻辑
        return true;
    }
    
    /**
     * 同步配置到单个节点
     */
    private boolean syncConfigToNode(SystemConfigVO config, String node) {
        // TODO: 实现同步配置到单个节点的逻辑
        try {
            // 模拟网络调用
            Thread.sleep(100);
            // 模拟成功率
            return Math.random() > 0.1;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }
    
    /**
     * 创建备份记录
     */
    private boolean createBackupRecord(String tenantId, String backupName, List<SystemConfig> configs) {
        // TODO: 实现创建备份记录的逻辑
        return true;
    }
    
    /**
     * 根据备份ID创建备份记录
     */
    private boolean createBackupRecordWithId(String backupId, String tenantId, String configType, List<SystemConfig> configs) {
        // TODO: 实现根据备份ID创建备份记录的逻辑
        return true;
    }
    
    /**
     * 获取备份配置
     */
    private List<SystemConfig> getBackupConfigs(String tenantId, String backupName) {
        // TODO: 实现获取备份配置的逻辑
        return new ArrayList<>();
    }
    
    /**
     * 根据备份ID获取备份配置
     */
    private List<SystemConfig> getBackupConfigsById(String backupId, String tenantId) {
        // TODO: 实现根据备份ID获取备份配置的逻辑
        return new ArrayList<>();
    }
    
    /**
     * 注册配置变更回调
     */
    private void registerConfigChangeCallback(String configKey, String tenantId, Runnable callback) {
        // TODO: 实现注册配置变更回调的逻辑
    }
    
    /**
     * 取消注册配置变更监听器
     */
    private void unregisterConfigChangeListener(String configKey, String tenantId) {
        // TODO: 实现取消注册配置变更监听器的逻辑
    }
    
    @Override
    public List<Map<String, Object>> getConfigHistory(String configKey, String tenantId, Integer limit) {
        return systemConfigMapper.selectConfigHistory(configKey, limit != null ? limit : 10);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importSystemConfigs(List<SystemConfigDTO> systemConfigDTOList, boolean overwrite) {
        Map<String, Object> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(systemConfigDTOList)) {
            result.put("success", false);
            result.put("message", "导入配置列表为空");
            return result;
        }
        
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();
        
        try {
            for (SystemConfigDTO dto : systemConfigDTOList) {
                try {
                    // 检查配置键是否已存在
                    boolean exists = existsByConfigKey(dto.getConfigKey(), dto.getTenantId(), null);
                    
                    if (exists && !overwrite) {
                        failCount++;
                        errorMessages.add("配置键已存在且不允许覆盖: " + dto.getConfigKey());
                        continue;
                    }
                    
                    // 验证配置值格式
                    Map<String, Object> validationResult = validateConfigValue(dto.getConfigKey(), dto.getConfigValue(), dto.getConfigType());
                    if (!(Boolean) validationResult.get("valid")) {
                        failCount++;
                        errorMessages.add("配置值格式不正确: " + dto.getConfigKey() + " - " + validationResult.get("message"));
                        continue;
                    }
                    
                    SystemConfig systemConfig = new SystemConfig();
                    BeanUtils.copyProperties(dto, systemConfig);
                    systemConfig.setCreateTime(LocalDateTime.now());
                    systemConfig.setUpdateTime(LocalDateTime.now());
                    
                    if (exists && overwrite) {
                        // 更新现有配置
                        SystemConfigVO existingConfig = getSystemConfigByKey(dto.getConfigKey(), dto.getTenantId());
                        if (existingConfig != null) {
                            systemConfig.setId(existingConfig.getId());
                            this.updateById(systemConfig);
                        }
                    } else {
                        // 创建新配置
                        this.save(systemConfig);
                    }
                    
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errorMessages.add("导入配置失败: " + dto.getConfigKey() + " - " + e.getMessage());
                }
            }
            
            result.put("success", successCount > 0);
            result.put("message", String.format("导入完成，成功: %d，失败: %d", successCount, failCount));
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errorMessages", errorMessages);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "导入配置异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 将SystemConfig转换为SystemConfigVO
     */
    private SystemConfigVO convertToVO(SystemConfig config) {
        SystemConfigVO vo = new SystemConfigVO();
        vo.setId(config.getId());
        vo.setConfigKey(config.getConfigKey());
        vo.setConfigValue(config.getConfigValue());
        vo.setConfigType(config.getConfigType());
        vo.setDescription(config.getDescription());
        vo.setTenantId(config.getTenantId());
        vo.setStatus(config.getStatus());
        vo.setCreateTime(config.getCreateTime());
        vo.setUpdateTime(config.getUpdateTime());
        vo.setCreateBy(config.getCreateBy());
        vo.setUpdateBy(config.getUpdateBy());
        vo.setDeleted(config.getDeleted());
        return vo;
    }

    /**
     * 配置变更监听器接口
     */
    public interface ConfigChangeListener {
        void onConfigChanged(String configKey, String tenantId, String oldValue, String newValue);
    }
}