# Sentinel流量控制系统技术架构文档

## 1. Architecture design

```mermaid
graph TD
    A[Vue前端应用] --> B[Spring Boot后端服务]
    B --> C[MySQL数据库]
    B --> D[Redis缓存]
    B --> E[Nacos配置中心]
    B --> F[Sentinel核心]
    
    subgraph "前端层"
        A
    end
    
    subgraph "应用层"
        B
        G[Controller层]
        H[Service层]
        I[Repository层]
    end
    
    subgraph "数据层"
        C
        D
    end
    
    subgraph "配置层"
        E
    end
    
    subgraph "流控核心"
        F
    end
```

## 2. Technology Description

- Frontend: Vue@2 + Element UI + TypeScript + Vuex + Vue Router
- Backend: Spring Boot@2.7 + MyBatis Plus + Druid + Sentinel
- Database: MySQL@8.0
- Cache: Redis@6.0
- Configuration: Nacos@2.0
- Build Tool: Maven@3.8

## 3. Route definitions

| Route | Purpose |
|-------|----------|
| / | 首页大屏，显示系统整体监控数据和关键指标 |
| /rules | 流量规则管理页面，支持规则的CRUD操作 |
| /ip-rules | IP规则管理页面，管理IP黑白名单和地理位置控制 |
| /monitor | 监控统计页面，实时监控和历史数据分析 |
| /config | 系统配置页面，全局配置和租户配置管理 |
| /tenant | 租户管理页面，租户信息和权限管理 |
| /logs | 日志管理页面，操作日志和流控事件日志 |
| /login | 用户登录页面 |

## 4. API definitions

### 4.1 Core API

#### 流量规则管理API

```
GET /api/flow-rules
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|--------------|
| tenantId | string | false | 租户ID筛选 |
| resourceName | string | false | 资源名称筛选 |
| status | integer | false | 规则状态筛选 |
| page | integer | false | 页码，默认1 |
| size | integer | false | 页大小，默认10 |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| code | integer | 响应状态码 |
| message | string | 响应消息 |
| data | object | 分页数据 |
| data.records | array | 规则列表 |
| data.total | integer | 总记录数 |

```
POST /api/flow-rules
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|--------------|
| ruleName | string | true | 规则名称 |
| resourceName | string | true | 资源名称 |
| tenantId | string | true | 租户ID |
| limitMode | integer | true | 限流模式：0-QPS，1-线程数 |
| threshold | integer | true | 阈值 |
| strategy | integer | true | 流控策略：0-直接，1-关联，2-链路 |
| behavior | integer | true | 流控行为：0-快速失败，1-预热，2-排队等待 |
| warmUpPeriod | integer | false | 预热时长（秒） |
| queueTimeout | integer | false | 排队超时时间（毫秒） |
| description | string | false | 规则描述 |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| code | integer | 响应状态码 |
| message | string | 响应消息 |
| data | object | 创建的规则信息 |

#### IP规则管理API

```
GET /api/ip-rules
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|--------------|
| tenantId | string | false | 租户ID筛选 |
| ruleType | string | false | 规则类型：SINGLE_IP, IP_RANGE, CIDR |
| listType | string | false | 列表类型：WHITELIST, BLACKLIST |
| page | integer | false | 页码，默认1 |
| size | integer | false | 页大小，默认10 |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| code | integer | 响应状态码 |
| message | string | 响应消息 |
| data | object | 分页数据 |

```
POST /api/ip-rules
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|--------------|
| ruleName | string | true | 规则名称 |
| tenantId | string | true | 租户ID |
| ruleType | string | true | 规则类型 |
| ipValue | string | true | IP值 |
| listType | string | true | 列表类型 |
| qpsLimit | integer | false | QPS限制 |
| priority | integer | false | 优先级 |
| description | string | false | 规则描述 |

#### 监控统计API

```
GET /api/monitor/realtime
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|--------------|
| tenantId | string | false | 租户ID |
| resourceName | string | false | 资源名称 |
| timeRange | integer | false | 时间范围（分钟），默认30 |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| code | integer | 响应状态码 |
| message | string | 响应消息 |
| data | array | 实时监控数据 |

```
GET /api/monitor/statistics
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|--------------|
| tenantId | string | false | 租户ID |
| startDate | string | true | 开始日期 |
| endDate | string | true | 结束日期 |
| granularity | string | false | 粒度：HOURLY, DAILY |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| code | integer | 响应状态码 |
| message | string | 响应消息 |
| data | object | 统计数据 |

#### 租户管理API

```
GET /api/tenants
```

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| code | integer | 响应状态码 |
| message | string | 响应消息 |
| data | array | 租户列表 |

```
POST /api/tenants
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|--------------|
| tenantId | string | true | 租户ID |
| tenantName | string | true | 租户名称 |
| description | string | false | 租户描述 |
| contactPerson | string | false | 联系人 |
| contactPhone | string | false | 联系电话 |
| contactEmail | string | false | 联系邮箱 |

## 5. Server architecture diagram

```mermaid
graph TD
    A[前端请求] --> B[Controller层]
    B --> C[Service层]
    C --> D[Repository层]
    D --> E[(MySQL数据库)]
    
    C --> F[Redis缓存]
    C --> G[Nacos配置]
    C --> H[Sentinel流控]
    
    subgraph "应用服务器"
        B
        C
        D
        I[配置管理]
        J[缓存管理]
        K[流控管理]
    end
```

## 6. Data model

### 6.1 Data model definition

```mermaid
erDiagram
    TENANT_INFO ||--o{ FLOW_RULE : has
    TENANT_INFO ||--o{ IP_FLOW_RULE : has
    TENANT_INFO ||--o{ MONITOR_STATISTICS_DAILY : generates
    TENANT_INFO ||--o{ MONITOR_STATISTICS_HOURLY : generates
    TENANT_INFO ||--o{ FLOW_CONTROL_LOG : generates
    
    FLOW_RULE {
        bigint id PK
        varchar rule_name
        varchar resource_name
        varchar tenant_id FK
        tinyint limit_mode
        int threshold
        tinyint strategy
        varchar related_resource
        tinyint behavior
        int warm_up_period
        int queue_timeout
        tinyint cluster_mode
        tinyint status
        int priority
        text description
        datetime create_time
        datetime update_time
        varchar create_by
        varchar update_by
        tinyint deleted
    }
    
    IP_FLOW_RULE {
        bigint id PK
        varchar rule_name
        varchar tenant_id FK
        varchar rule_type
        varchar ip_value
        varchar list_type
        int qps_limit
        int priority
        text description
        datetime create_time
        datetime update_time
        tinyint status
    }
    
    TENANT_INFO {
        bigint id PK
        varchar tenant_id
        varchar tenant_name
        varchar description
        varchar contact_person
        varchar contact_phone
        varchar contact_email
        tinyint status
        datetime create_time
        datetime update_time
        varchar create_by
        varchar update_by
    }
    
    MONITOR_STATISTICS_DAILY {
        bigint id PK
        date date
        varchar resource_name
        varchar tenant_id FK
        bigint total_requests
        bigint pass_requests
        bigint block_requests
        bigint queue_requests
        bigint total_rt
        decimal avg_rt
        int max_rt
        int min_rt
        datetime create_time
        datetime update_time
    }
    
    MONITOR_STATISTICS_HOURLY {
        bigint id PK
        datetime datetime
        varchar resource_name
        varchar tenant_id FK
        bigint total_requests
        bigint pass_requests
        bigint block_requests
        bigint queue_requests
        bigint total_rt
        decimal avg_rt
        int max_rt
        int min_rt
        datetime create_time
        datetime update_time
    }
    
    FLOW_CONTROL_LOG {
        bigint id PK
        varchar resource_name
        varchar tenant_id FK
        varchar rule_name
        varchar event_type
        varchar request_id
        varchar user_id
        varchar ip_address
        varchar user_agent
        text request_params
        int response_time
        text error_message
        datetime create_time
    }
    
    SYSTEM_CONFIG {
        bigint id PK
        varchar config_key
        text config_value
        varchar config_type
        varchar description
        datetime create_time
        datetime update_time
        varchar create_by
        varchar update_by
    }
```

### 6.2 Data Definition Language

#### 流量控制规则表 (flow_rule)
```sql
CREATE TABLE `flow_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `resource_name` varchar(200) NOT NULL COMMENT '资源名称',
  `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
  `limit_mode` tinyint(1) NOT NULL DEFAULT '0' COMMENT '限流模式：0-QPS，1-线程数',
  `threshold` int(11) NOT NULL COMMENT '阈值',
  `strategy` tinyint(1) NOT NULL DEFAULT '0' COMMENT '流控策略：0-直接，1-关联，2-链路',
  `related_resource` varchar(200) DEFAULT NULL COMMENT '关联资源',
  `behavior` tinyint(1) NOT NULL DEFAULT '0' COMMENT '流控行为：0-快速失败，1-预热，2-排队等待',
  `warm_up_period` int(11) DEFAULT NULL COMMENT '预热时长（秒）',
  `queue_timeout` int(11) DEFAULT NULL COMMENT '排队超时时间（毫秒）',
  `cluster_mode` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否集群模式：0-否，1-是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '规则状态：0-禁用，1-启用',
  `priority` int(11) NOT NULL DEFAULT '1' COMMENT '优先级',
  `description` text COMMENT '规则描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_name` (`rule_name`),
  KEY `idx_resource_name` (`resource_name`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='流量控制规则表';
```

#### IP流量控制规则表 (ip_flow_rule)
```sql
CREATE TABLE `ip_flow_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
  `rule_type` varchar(20) NOT NULL COMMENT '规则类型：SINGLE_IP-单个IP，IP_RANGE-IP范围，CIDR-IP段',
  `ip_value` varchar(200) NOT NULL COMMENT 'IP值',
  `list_type` varchar(20) NOT NULL COMMENT '列表类型：WHITELIST-白名单，BLACKLIST-黑名单',
  `qps_limit` int(11) DEFAULT NULL COMMENT 'QPS限制',
  `priority` int(11) NOT NULL DEFAULT '1' COMMENT '优先级',
  `description` text COMMENT '规则描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '规则状态：0-禁用，1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_name` (`rule_name`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_rule_type` (`rule_type`),
  KEY `idx_list_type` (`list_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IP流量控制规则表';
```

#### 租户信息表 (tenant_info)
```sql
CREATE TABLE `tenant_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
  `tenant_name` varchar(100) NOT NULL COMMENT '租户名称',
  `description` varchar(500) DEFAULT NULL COMMENT '租户描述',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户信息表';
```

#### 初始化数据
```sql
-- 插入默认租户
INSERT INTO `tenant_info` (`tenant_id`, `tenant_name`, `description`, `contact_person`, `contact_email`, `status`) VALUES
('default', '默认租户', '系统默认租户', '系统管理员', '<EMAIL>', 1),
('tenant1', '租户1', '测试租户1', '张三', '<EMAIL>', 1),
('tenant2', '租户2', '测试租户2', '李四', '<EMAIL>', 1);

-- 插入示例流量控制规则
INSERT INTO `flow_rule` (`rule_name`, `resource_name`, `tenant_id`, `limit_mode`, `threshold`, `strategy`, `behavior`, `status`, `priority`, `description`) VALUES
('默认API限流', '/api/**', 'default', 0, 1000, 0, 0, 1, 1, '默认API接口限流规则'),
('用户服务限流', '/api/users/**', 'tenant1', 0, 500, 0, 0, 1, 2, '用户服务接口限流'),
('订单服务限流', '/api/orders/**', 'tenant1', 0, 200, 0, 2, 1, 3, '订单服务接口限流，使用排队等待'),
('支付服务限流', '/api/payments/**', 'tenant2', 0, 100, 0, 0, 1, 4, '支付服务接口限流'),
('报表服务限流', '/api/reports/**', 'tenant2', 0, 50, 0, 1, 1, 5, '报表服务接口限流，使用预热模式');

-- 插入示例IP规则
INSERT INTO `ip_flow_rule` (`rule_name`, `tenant_id`, `rule_type`, `ip_value`, `list_type`, `qps_limit`, `priority`, `description`, `status`) VALUES
('内网IP白名单', 'default', 'CIDR', '***********/16', 'WHITELIST', NULL, 1, '内网IP段白名单', 1),
('恶意IP黑名单', 'default', 'SINGLE_IP', '*******', 'BLACKLIST', NULL, 1, '恶意IP黑名单', 1),
('高频访问IP限制', 'tenant1', 'SINGLE_IP', '**********', 'BLACKLIST', 10, 2, '高频访问IP限制', 1);
```