package com.example.gateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.common.entity.TenantInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 租户信息Mapper接口
 */
@Mapper
public interface TenantInfoMapper extends BaseMapper<TenantInfo> {

    /**
     * 查询所有启用的租户
     *
     * @return 启用的租户列表
     */
    @Select("SELECT * FROM tenant_info WHERE status = 1 AND deleted = 0")
    List<TenantInfo> selectAllEnabled();

    /**
     * 根据租户ID查询租户信息
     *
     * @param tenantId 租户ID
     * @return 租户信息
     */
    @Select("SELECT * FROM tenant_info WHERE tenant_id = #{tenantId} AND deleted = 0")
    TenantInfo selectByTenantId(@Param("tenantId") String tenantId);

    /**
     * 查询有QPS限制的启用租户
     *
     * @return 有QPS限制的租户列表
     */
    @Select("SELECT * FROM tenant_info WHERE status = 1 AND deleted = 0 AND (total_qps_limit IS NOT NULL AND total_qps_limit > 0)")
    List<TenantInfo> selectEnabledWithQpsLimit();

    /**
     * 查询有并发限制的启用租户
     *
     * @return 有并发限制的租户列表
     */
    @Select("SELECT * FROM tenant_info WHERE status = 1 AND deleted = 0 AND (total_concurrent_limit IS NOT NULL AND total_concurrent_limit > 0)")
    List<TenantInfo> selectEnabledWithConcurrentLimit();

    /**
     * 查询有任何限制的启用租户
     *
     * @return 有限制的租户列表
     */
    @Select("SELECT * FROM tenant_info WHERE status = 1 AND deleted = 0 AND ((total_qps_limit IS NOT NULL AND total_qps_limit > 0) OR (total_concurrent_limit IS NOT NULL AND total_concurrent_limit > 0))")
    List<TenantInfo> selectEnabledWithAnyLimit();
}