# 流量控制系统架构图

## 1. 系统整体架构图

```mermaid
graph TB
    %% 客户端层
    Client[客户端应用]
    WebUI[管理后台界面]
  
    %% 网关层
    subgraph "网关服务层"
        Gateway[Gateway Service<br/>Spring Cloud Gateway]
        Filter[MultiDimensionFlowFilter<br/>多维度流量过滤器]
    end
  
    %% 流量控制核心
    subgraph "流量控制核心"
        Sentinel[Sentinel<br/>流量控制引擎]
        FlowManager[FlowRuleManager<br/>规则管理器]
        Monitor[FlowControlMonitor<br/>监控组件]
    end
  
    %% 管理服务层
    subgraph "管理服务层"
        AdminService[Flow Control Admin<br/>管理服务]
        RuleService[规则管理服务]
        MonitorService[监控统计服务]
        TenantService[租户管理服务]
    end
  
    %% 数据存储层
    subgraph "数据存储层"
        MySQL[(MySQL <br/>数据库)]
        Redis[(Redis <br/>缓存数据库)]
    end
  

  

  
    %% 连接关系
    Client --> Gateway
    WebUI --> AdminService
  
    Gateway --> Filter
    Filter --> Sentinel
    Filter --> FlowManager
    Filter --> Monitor
  
    AdminService --> RuleService
    AdminService --> MonitorService
    AdminService --> TenantService
  
    RuleService --> MySQL
    MonitorService --> MySQL
    TenantService --> MySQL
  
    FlowManager --> Redis
    Monitor --> Redis
    Sentinel --> Redis
  
    %% Gateway启动时/定时从数据库加载规则
    MySQL -.->|"启动时加载规则"| FlowManager
  
    %% Gateway定期从数据库同步规则
    MySQL -.->|"定期同步规则"| FlowManager
  

  
    %% 样式定义
    classDef clientStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gatewayStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef coreStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef serviceStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dataStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef configStyle fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    class Client,WebUI clientStyle
    class Gateway,Filter gatewayStyle
    class Sentinel,FlowManager,Monitor coreStyle
    class AdminService,RuleService,MonitorService,TenantService serviceStyle
    class MySQL,Redis dataStyle
```

## 2. 流量控制时序图

### 2.1 请求流量控制处理时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as Gateway Service
    participant Filter as MultiDimensionFlowFilter
    participant Sentinel as Sentinel引擎
    participant Redis as Redis缓存
    participant Monitor as FlowControlMonitor
    participant MySQL as MySQL数据库
  
    Client->>Gateway: 1. 发送HTTP请求
    Gateway->>Filter: 2. 请求进入流量控制过滤器
    Filter->>Filter: 3. 提取请求上下文信息
  
    %% 多维度流量控制检查
    Filter->>Redis: 4. 查询流量控制规则
    Redis-->>Filter: 5. 返回规则配置
    Filter->>Sentinel: 6. 执行多维度限流检查
  
    alt 流量控制通过
        Sentinel-->>Filter: 7a. 检查通过
        Filter-->>Gateway: 8a. 放行请求
        Gateway->>Client: 9a. 转发到后端服务
        Filter->>Monitor: 10a. 记录PASS事件
    else 流量控制阻断
        Sentinel-->>Filter: 7b. 检查被阻断
        Filter->>Monitor: 8b. 记录BLOCK事件
        Filter-->>Gateway: 9b. 返回限流响应
        Gateway->>Client: 10b. 返回429/403状态码
    end
  
    %% 异步监控数据处理
    Monitor->>Redis: 11. 更新统计计数器
    Monitor->>MySQL: 12. 异步写入日志
```

### 2.2 规则管理时序图

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant WebUI as 管理界面
    participant AdminService as Admin Service
    participant RuleService as 规则管理服务
    participant MySQL as MySQL数据库
    participant Redis as Redis缓存
    participant Gateway as Gateway Service
    participant RuleManager as FlowRuleManager
  
    Admin->>WebUI: 1. 创建/修改流量规则
    WebUI->>AdminService: 2. 提交规则配置请求
    AdminService->>RuleService: 3. 调用规则管理服务
  
    RuleService->>MySQL: 4. 保存规则到数据库
    MySQL-->>RuleService: 5. 返回保存结果
  
    RuleService->>Redis: 6. 更新Redis缓存
    Redis-->>RuleService: 7. 返回缓存更新结果
  
    RuleService-->>AdminService: 8. 返回操作结果
    AdminService-->>WebUI: 9. 返回操作响应
    WebUI-->>Admin: 10. 显示操作结果
  
    %% Gateway定期同步规则
    Note over Gateway,MySQL: Gateway定期从数据库同步规则
    Gateway->>RuleManager: 11. 触发规则同步
    RuleManager->>MySQL: 12. 查询最新规则
    MySQL-->>RuleManager: 13. 返回规则数据
    RuleManager->>Redis: 14. 更新本地缓存
    Redis-->>RuleManager: 15. 缓存更新完成
```

## 3. 请求处理数据流图

```mermaid
graph TD
    subgraph "客户端请求层"
        A[客户端请求] --> B[Gateway Service]
    end

    subgraph "流量控制处理层"
        B --> C[MultiDimensionFlowFilter]
        C --> D[RequestContextExtractor]
        D --> E["提取上下文信息(tenantId, resource, clientIP)"]
    end

    subgraph "多维度流量控制决策"
        E --> F{IP限流检查}
        F -->|通过| G{租户总限流检查}
        F -->|阻断| H[返回429 IP限流]
        G -->|通过| I{租户+接口限流检查}
        G -->|阻断| J[返回429 租户限流]
        I -->|通过| K[请求放行到后端]
        I -->|阻断| L[返回429 接口限流]
    end

    subgraph "规则数据源"
        M[(MySQL数据库)] --> N[FlowRuleManager]
        N --> O[(Redis规则缓存)]
        N -.->|启动加载/定期同步| C
        O -.->|实时查询| C
    end


    style A fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#ccf,stroke:#333,stroke-width:2px
    style M fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    style O fill:#fce4ec,stroke:#880e4f,stroke-width:2px
```

## 4. 管理后台数据流图

```mermaid
graph TD
    subgraph "管理界面层"
        A[管理员] --> B[Vue管理界面]
    end

    subgraph "管理服务层"
        B --> C[Admin Service]
        C --> D[规则管理服务]
        C --> E[监控统计服务]
        C --> F[租户管理服务]
    end

    subgraph "业务处理层"
        D --> G[流量规则CRUD]
        D --> H[IP规则CRUD]
        D --> I[租户规则CRUD]
        E --> J[统计数据查询]
        F --> L[租户信息管理]
    end

    subgraph "数据存储层"
        G --> M[(MySQL数据库)]
        H --> M
        I --> M
        J --> M
        L --> M
    
        G --> N[(Redis缓存)]
        H --> N
        I --> N
    end

    subgraph "规则同步通知"
        M -.->|规则变更通知| O[Gateway规则同步]
        N -.->|缓存更新| O
    end

    style A fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style B fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style M fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    style N fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    style O fill:#fff3e0,stroke:#e65100,stroke-width:2px
```

---

**说明**：

1. **系统整体架构图**：展示了流量控制系统的完整架构，包括各个组件及其关系
2. **流量控制时序图**：详细描述了请求处理的完整流程，包括多维度限流检查
3. **规则管理时序图**：展示了规则配置的管理和同步流程
4. **数据流图**：简化展示了数据在系统中的流转过程，包括请求处理和管理后台的数据流

这些图表可以帮助理解系统的流量控制设计和运行机制。
