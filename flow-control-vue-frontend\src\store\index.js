import Vue from 'vue'
import Vuex from 'vuex'
import auth from './modules/auth'
import tenantFlowRules from './modules/tenantFlowRules'
import ipWhitelist from './modules/ipWhitelist'
import ipBlacklist from './modules/ipBlacklist'
import ipFlowRules from './modules/ipFlowRules'
import monitor from './modules/monitor'
import config from './modules/config'
import theme from './modules/theme'
import language from './modules/language'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    loading: false,
    error: null
  },
  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading
    },
    SET_ERROR(state, error) {
      state.error = error
    },
    CLEAR_ERROR(state) {
      state.error = null
    }
  },
  actions: {
    setLoading({ commit }, loading) {
      commit('SET_LOADING', loading)
    },
    setError({ commit }, error) {
      commit('SET_ERROR', error)
    },
    clearError({ commit }) {
      commit('CLEAR_ERROR')
    }
  },
  modules: {
    auth,
    tenantFlowRules,
    ipWhitelist,
    ipBlacklist,
    ipFlowRules,
    monitor,
    config,
    theme,
    language
  }
})