import Vue from 'vue'
import VueRouter from 'vue-router'
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'
import TenantFlowRules from '../views/TenantFlowRules.vue'
import InterfaceFlowRules from '../views/InterfaceFlowRules.vue'
import IpFlowRules from '../views/IpFlowRules.vue'
import IpBlackWhiteList from '../views/IpBlackWhiteList.vue'
import Statistics from '../views/Statistics.vue'
import Config from '../views/Config.vue'

Vue.use(VueRouter)

const routes = [
	{
		path: '/',
		redirect: '/dashboard'
	},
	{
		path: '/login',
		name: 'Login',
		component: Login
	},
	{
		path: '/dashboard',
		name: 'Dashboard',
		component: Dashboard,
		meta: { requiresAuth: true }
	},
	{
		path: '/tenant-flow-rules',
		name: 'TenantFlowRules',
		component: TenantFlowRules,
		meta: { requiresAuth: true }
	},
	{
		path: '/interface-flow-rules',
		name: 'InterfaceFlowRules',
		component: InterfaceFlowRules,
		meta: { requiresAuth: true }
	},
	{
		path: '/ip-flow-rules',
		name: 'IpFlowRules',
		component: IpFlowRules,
		meta: { requiresAuth: true }
	},
	{
		path: '/ip-blackwhite-list',
		name: 'IpBlackWhiteList',
		component: IpBlackWhiteList,
		meta: { requiresAuth: true }
	},
	{
		path: '/statistics',
		name: 'Statistics',
		component: Statistics,
		meta: { requiresAuth: true }
	},
	{
		path: '/config',
		name: 'Config',
		component: Config,
		meta: { requiresAuth: true }
	}
]

const router = new VueRouter({
	mode: 'history',
	base: process.env.BASE_URL,
	routes
})

// 路由守卫 - 已禁用认证检查，允许直接访问所有页面
router.beforeEach(async (to, from, next) => {
	// 跳过所有认证检查，直接允许访问
	next()
})

export default router