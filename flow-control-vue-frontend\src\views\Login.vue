<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>Sentinel 流量控制系统</h2>
        <p>请登录您的账户</p>
      </div>
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.native.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名"
            prefix-icon="el-icon-user"
            size="large"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            prefix-icon="el-icon-lock"
            size="large"
            @keyup.enter.native="handleLogin"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            class="login-button"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import api from '../api/index.js'

export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  created() {
    // 注入API到组件实例
    this.$api = api
  },
  methods: {
    ...mapActions('auth', ['login']),
    async handleLogin() {
      if (!this.validateForm()) {
        return
      }
      
      this.loading = true
      try {
        // 调用真实的登录API
        const response = await this.$api.auth.login({
          username: this.loginForm.username,
          password: this.loginForm.password
        })
        
        // 调用store的login action
        await this.$store.dispatch('auth/login', {
          token: response.data.token,
          user: response.data.user
        })
        
        this.$message.success('登录成功')
         // 登录成功后重定向到原始页面或仪表板
         const redirect = this.$route.query.redirect || '/dashboard'
         this.$router.push(redirect)
      } catch (error) {
        this.$message.error(error.message || '登录失败，请检查用户名和密码')
      } finally {
        this.loading = false
      }
    },
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.loginForm.validate((valid) => {
          resolve(valid)
        })
      })
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.login-header p {
  color: #666;
  font-size: 14px;
}

.login-form {
  margin-top: 20px;
}

.login-button {
  width: 100%;
  height: 45px;
  font-size: 16px;
  font-weight: 600;
}

.el-form-item {
  margin-bottom: 20px;
}
</style>