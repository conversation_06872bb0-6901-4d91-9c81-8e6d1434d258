package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.Result;
import com.example.admin.dto.FlowRuleDTO;
import com.example.admin.service.FlowRuleService;
import com.example.admin.vo.FlowRuleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 流量规则控制器
 * 提供流量规则的CRUD操作、批量管理、统计分析等功能
 */
@Tag(name = "流量规则管理")
@RestController
@RequestMapping("/api/flow-rules")
@Validated
public class FlowRuleController {

    private static final Logger log = LoggerFactory.getLogger(FlowRuleController.class);

    @Autowired
    private FlowRuleService flowRuleService;

    /**
     * 分页查询流量规则
     */
    @Operation(summary = "分页查询流量规则")
    @GetMapping
    public Result<Page<FlowRuleVO>> pageFlowRules(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
        @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
        @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
        @Parameter(description = "资源名称") @RequestParam(required = false) String resourceName,
        @Parameter(description = "规则状态") @RequestParam(required = false) Integer status) {
        try {
            Page<FlowRuleVO> pageParam = new Page<>(page, size);
            Page<FlowRuleVO> result = flowRuleService.selectFlowRulePage(pageParam, tenantId, resourceName, null, status, null);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询流量规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询流量规则
     */
    @Operation(summary = "根据ID查询流量规则")
    @GetMapping("/{id}")
    public Result<FlowRuleVO> getFlowRuleById(
        @Parameter(description = "规则ID") @PathVariable @NotNull Long id) {
        try {
            FlowRuleVO result = flowRuleService.getFlowRuleById(id);
            if (result == null) {
                return Result.error("流量规则不存在");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询流量规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建流量规则
     */
    @Operation(summary = "创建流量规则")
    @PostMapping
    public Result<String> createFlowRule(
        @Parameter(description = "流量规则信息") @RequestBody @Valid FlowRuleDTO flowRuleDTO) {
        try {
            boolean result = flowRuleService.createFlowRule(flowRuleDTO);
            if (result) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建流量规则失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新流量规则
     */
    @Operation(summary = "更新流量规则")
    @PutMapping("/{id}")
    public Result<String> updateFlowRule(
        @Parameter(description = "规则ID") @PathVariable @NotNull Long id,
        @Parameter(description = "流量规则信息") @RequestBody @Valid FlowRuleDTO flowRuleDTO) {
        try {
            boolean result = flowRuleService.updateFlowRule(id, flowRuleDTO);
            if (result) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新流量规则失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除流量规则
     */
    @Operation(summary = "删除流量规则")
    @DeleteMapping("/{id}")
    public Result<String> deleteFlowRule(
        @Parameter(description = "规则ID") @PathVariable @NotNull Long id) {
        try {
            boolean result = flowRuleService.deleteFlowRule(id);
            if (result) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除流量规则失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除流量规则
     */
    @Operation(summary = "批量删除流量规则")
    @DeleteMapping("/batch")
    public Result<String> batchDeleteFlowRules(
        @Parameter(description = "规则ID列表") @RequestBody @NotEmpty List<Long> ids) {
        try {
            boolean result = flowRuleService.batchDeleteFlowRules(ids);
            if (result) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除流量规则失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用流量规则
     */
    @Operation(summary = "启用/禁用流量规则")
    @PutMapping("/{id}/status")
    public Result<String> updateFlowRuleStatus(
        @Parameter(description = "规则ID") @PathVariable @NotNull Long id,
        @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer status) {
        try {
            boolean result;
            if (status == 1) {
                result = flowRuleService.enableFlowRule(id);
            } else {
                result = flowRuleService.disableFlowRule(id);
            }
            if (result) {
                String message = status == 1 ? "启用成功" : "禁用成功";
                return Result.success(message);
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("更新流量规则状态失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 批量启用/禁用流量规则
     */
    @Operation(summary = "批量启用/禁用流量规则")
    @PutMapping("/batch/status")
    public Result<String> batchUpdateFlowRuleStatus(
        @Parameter(description = "规则ID列表") @RequestBody @NotEmpty List<Long> ids,
        @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer status) {
        try {
            boolean result = flowRuleService.batchUpdateStatus(ids, status);
            if (result) {
                String message = status == 1 ? "批量启用成功" : "批量禁用成功";
                return Result.success(message);
            } else {
                return Result.error("批量操作失败");
            }
        } catch (Exception e) {
            log.error("批量更新流量规则状态失败", e);
            return Result.error("批量操作失败: " + e.getMessage());
        }
    }

    /**
     * 根据租户ID查询流量规则
     */
    @Operation(summary = "根据租户ID查询流量规则")
    @GetMapping("/tenant/{tenantId}")
    public Result<List<FlowRuleVO>> getFlowRulesByTenantId(
        @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId) {
        try {
            List<FlowRuleVO> result = flowRuleService.getFlowRulesByTenantId(tenantId, null, null);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据租户ID查询流量规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 同步规则到Sentinel
     */
    @Operation(summary = "同步规则到Sentinel")
    @PostMapping("/sync/{tenantId}")
    public Result<String> syncRulesToSentinel(
        @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId) {
        try {
            // 获取该租户的所有启用规则并同步到Sentinel
            List<FlowRuleVO> rules = flowRuleService.getFlowRulesByTenantId(tenantId, 1, null);
            // 这里应该调用Sentinel的规则同步接口
            return Result.success("同步成功，共同步" + rules.size() + "条规则");
        } catch (Exception e) {
            log.error("同步规则到Sentinel失败", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 导入流量规则
     */
    @Operation(summary = "导入流量规则")
    @PostMapping("/import")
    public Result<String> importFlowRules(
        @Parameter(description = "流量规则列表") @RequestBody @NotEmpty List<FlowRuleDTO> flowRuleDTOs) {
        try {
            int successCount = 0;
            for (FlowRuleDTO dto : flowRuleDTOs) {
                if (flowRuleService.createFlowRule(dto)) {
                    successCount++;
                }
            }
            return Result.success("导入成功，共导入" + successCount + "条规则");
        } catch (Exception e) {
            log.error("导入流量规则失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出流量规则
     */
    @Operation(summary = "导出流量规则")
    @GetMapping("/export")
    public Result<List<FlowRuleVO>> exportFlowRules(
        @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        try {
            List<FlowRuleVO> result = flowRuleService.getFlowRulesByTenantId(tenantId, null, null);
            return Result.success(result);
        } catch (Exception e) {
            log.error("导出流量规则失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取流量规则统计
     */
    @Operation(summary = "获取流量规则统计")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getFlowRuleStatistics(
        @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        try {
            // 统计规则数量
            List<FlowRuleVO> rules = flowRuleService.getFlowRulesByTenantId(tenantId, null, null);
            long totalCount = rules.size();
            long enabledCount = rules.stream().filter(rule -> rule.getStatus() != null && rule.getStatus() == 1).count();
            long disabledCount = totalCount - enabledCount;
            
            Map<String, Object> result = Map.of(
                "totalCount", totalCount,
                "enabledCount", enabledCount,
                "disabledCount", disabledCount
            );
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取流量规则统计失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 验证流量规则
     */
    @Operation(summary = "验证流量规则")
    @PostMapping("/validate")
    public Result<String> validateFlowRule(
        @Parameter(description = "流量规则信息") @RequestBody @Valid FlowRuleDTO flowRuleDTO) {
        try {
            // 基础验证
            if (flowRuleDTO.getResourceName() == null || flowRuleDTO.getResourceName().trim().isEmpty()) {
                return Result.error("验证失败: 资源名称不能为空");
            }
            if (flowRuleDTO.getThreshold() == null || flowRuleDTO.getThreshold() <= 0) {
                return Result.error("验证失败: 阈值必须大于0");
            }
            return Result.success("验证通过");
        } catch (Exception e) {
            log.error("验证流量规则失败", e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }
}