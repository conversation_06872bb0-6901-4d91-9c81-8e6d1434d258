<template>
  <div class="monitor">
    <layout>
      <div class="monitor-content">
        <div class="page-header">
          <div class="header-left">
            <h1>实时监控</h1>
            <p>实时监控系统流量和性能指标</p>
          </div>
          <div class="header-right">
            <el-button 
              :icon="isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'" 
              @click="toggleFullscreen"
              size="small"
            >
              {{ isFullscreen ? '退出全屏' : '全屏显示' }}
            </el-button>
            <el-dropdown @command="handleExport" size="small">
              <el-button type="success" size="small">
                <i class="el-icon-download"></i>
                数据导出<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="excel">导出Excel</el-dropdown-item>
                <el-dropdown-item command="csv">导出CSV</el-dropdown-item>
                <el-dropdown-item command="json">导出JSON</el-dropdown-item>
                <el-dropdown-item command="logs">导出日志</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        
        <!-- 实时指标卡片 -->
        <el-row :gutter="20" class="metrics-row">
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-header">
                <span class="metric-title">当前QPS</span>
                <i class="el-icon-data-line metric-icon"></i>
              </div>
              <div class="metric-value">{{ realTimeData.qps }}</div>
              <div class="metric-trend">
                <span class="trend-text">较上分钟</span>
                <span class="trend-value positive">+12%</span>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-header">
                <span class="metric-title">平均响应时间</span>
                <i class="el-icon-timer metric-icon"></i>
              </div>
              <div class="metric-value">{{ realTimeData.rt }}ms</div>
              <div class="metric-trend">
                <span class="trend-text">较上分钟</span>
                <span class="trend-value negative">-5%</span>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-header">
                <span class="metric-title">成功率</span>
                <i class="el-icon-success metric-icon"></i>
              </div>
              <div class="metric-value">{{ realTimeData.successRate.toFixed(1) }}%</div>
              <div class="metric-trend">
                <span class="trend-text">较上分钟</span>
                <span class="trend-value positive">+0.2%</span>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-header">
                <span class="metric-title">错误率</span>
                <i class="el-icon-warning metric-icon"></i>
              </div>
              <div class="metric-value">{{ realTimeData.errorRate.toFixed(1) }}%</div>
              <div class="metric-trend">
                <span class="trend-text">较上分钟</span>
                <span class="trend-value negative">-0.1%</span>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <!-- 图表区域 -->
        <el-row :gutter="20" class="charts-row">
          <el-col :span="24">
            <div class="chart-card">
              <div class="chart-header">
                <h3>实时流量趋势</h3>
                <div class="chart-controls">
                  <el-radio-group v-model="timeRange" size="small" @change="onTimeRangeChange">
                    <el-radio-button label="1m">1分钟</el-radio-button>
                    <el-radio-button label="5m">5分钟</el-radio-button>
                    <el-radio-button label="15m">15分钟</el-radio-button>
                    <el-radio-button label="1h">1小时</el-radio-button>
                  </el-radio-group>
                  <el-button 
                    :icon="autoRefresh ? 'el-icon-video-pause' : 'el-icon-video-play'" 
                    size="small" 
                    @click="toggleAutoRefresh"
                  >
                    {{ autoRefresh ? '暂停' : '开始' }}
                  </el-button>
                </div>
              </div>
              <div id="traffic-chart" class="chart-container"></div>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" class="charts-row">
          <el-col :span="12">
            <div class="chart-card">
              <h3>响应时间分布</h3>
              <div id="rt-distribution-chart" class="chart-container"></div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-card">
              <h3>状态码分布</h3>
              <div id="status-chart" class="chart-container"></div>
            </div>
          </el-col>
        </el-row>
        
        <!-- 实时日志 -->
        <div class="logs-section">
          <div class="logs-header">
            <h3>实时访问日志</h3>
            <div class="logs-controls">
              <el-switch 
                v-model="showLogs" 
                active-text="显示日志" 
                inactive-text="隐藏日志"
              ></el-switch>
              <el-button size="small" @click="clearLogs">清空</el-button>
            </div>
          </div>
          <div v-if="showLogs" class="logs-content">
            <div class="log-item" v-for="(log, index) in recentLogs" :key="index">
              <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
              <span class="log-ip">{{ log.ip }}</span>
              <span class="log-method" :class="log.method.toLowerCase()">{{ log.method }}</span>
              <span class="log-path">{{ log.path }}</span>
              <span class="log-status" :class="getStatusClass(log.status)">{{ log.status }}</span>
              <span class="log-rt">{{ log.responseTime }}ms</span>
            </div>
          </div>
        </div>
        
        <!-- 告警列表 -->
        <div class="alerts-section">
          <div class="alerts-header">
            <h3>实时告警</h3>
            <el-button size="small" type="danger" @click="clearAlerts">清空告警</el-button>
          </div>
          <el-table 
            :data="alerts" 
            style="width: 100%" 
            max-height="300"
            :empty-text="$t('table.emptyText')"
          >
            <el-table-column prop="level" :label="$t('common.status')" width="80">
              <template slot-scope="scope">
                <el-tag :type="getAlertType(scope.row.level)" size="small">
                  {{ scope.row.level }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="告警信息"></el-table-column>
            <el-table-column prop="source" label="来源" width="120"></el-table-column>
            <el-table-column prop="time" label="时间" width="150">
              <template slot-scope="scope">
                {{ formatTime(scope.row.time) }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('common.operation')" width="120">
              <template>
                <el-button size="mini" type="text">{{ $t('common.delete') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </layout>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import Layout from '../components/Layout.vue'
import * as echarts from 'echarts'
import wsService from '../utils/websocket'
import { 
  exportMonitorDataToExcel, 
  exportLogsToExcel, 
  exportToCSV, 
  exportToJSON 
} from '../utils/export'

export default {
  name: 'Monitor',
  components: {
    Layout
  },
  data() {
    return {
      timeRange: '5m',
      autoRefresh: true,
      showLogs: true,
      refreshTimer: null,
      recentLogs: [],
      trafficChart: null,
      rtChart: null,
      statusChart: null,
      isFullscreen: false
    }
  },
  computed: {
    ...mapGetters('monitor', ['realTimeData', 'alerts'])
  },
  mounted() {
    this.startMonitoring()
    this.generateMockLogs()
    this.initWebSocket()
    this.$nextTick(() => {
      this.initCharts()
    })
  },
  beforeDestroy() {
    this.stopMonitoring()
    this.destroyCharts()
    this.disconnectWebSocket()
  },
  methods: {
    ...mapActions('monitor', ['fetchRealTimeData', 'fetchAlerts', 'clearAlerts']),
    // 获取告警列表
    async loadAlerts() {
      try {
        const response = await this.$api.alerts.getList({
          page: 1,
          size: 20,
          status: 'active'
        })
        this.$store.commit('monitor/SET_ALERTS', response.data || [])
      } catch (error) {
        console.error('获取告警列表失败:', error)
      }
    },
    // 确认告警
    async acknowledgeAlert(alertId) {
      try {
        await this.$api.alerts.acknowledge(alertId, {
          acknowledgedBy: 'current_user',
          acknowledgedAt: new Date().toISOString(),
          comment: '已确认'
        })
        this.$message.success('告警已确认')
        this.loadAlerts()
      } catch (error) {
        this.$message.error('确认告警失败')
        console.error('确认告警失败:', error)
      }
    },
    // 关闭告警
    async closeAlert(alertId) {
      try {
        await this.$api.alerts.close(alertId, {
          closedBy: 'current_user',
          closedAt: new Date().toISOString(),
          comment: '已处理'
        })
        this.$message.success('告警已关闭')
        this.loadAlerts()
      } catch (error) {
        this.$message.error('关闭告警失败')
        console.error('关闭告警失败:', error)
      }
    },
    // 获取监控统计数据
    async loadMonitorStatistics() {
      try {
        const response = await this.$api.monitor.getStatistics({
          timeRange: this.timeRange,
          tenantId: this.$store.getters.currentTenantId
        })
        this.$store.commit('monitor/SET_STATISTICS', response.data || {})
      } catch (error) {
        console.error('获取监控统计失败:', error)
      }
    },
    startMonitoring() {
      this.fetchRealTimeData()
      this.loadAlerts()
      this.loadMonitorStatistics()
      
      if (this.autoRefresh) {
        this.refreshTimer = setInterval(() => {
          this.fetchRealTimeData()
          this.loadAlerts()
          this.loadMonitorStatistics()
          this.generateMockLog()
          this.updateCharts()
        }, 2000) // 每2秒更新一次
      }
    },
    stopMonitoring() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    toggleAutoRefresh() {
      this.autoRefresh = !this.autoRefresh
      if (this.autoRefresh) {
        this.startMonitoring()
      } else {
        this.stopMonitoring()
      }
    },
    onTimeRangeChange() {
      // 根据时间范围重新加载数据
      this.fetchRealTimeData()
    },
    generateMockLogs() {
      // 生成一些初始的模拟日志
      for (let i = 0; i < 10; i++) {
        this.generateMockLog()
      }
    },
    generateMockLog() {
      const methods = ['GET', 'POST', 'PUT', 'DELETE']
      const paths = ['/api/users', '/api/orders', '/api/products', '/api/auth/login']
      const statuses = [200, 201, 400, 401, 404, 500]
      const ips = ['*************', '*********', '***********', '************']
      
      const log = {
        timestamp: Date.now(),
        ip: ips[Math.floor(Math.random() * ips.length)],
        method: methods[Math.floor(Math.random() * methods.length)],
        path: paths[Math.floor(Math.random() * paths.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        responseTime: Math.floor(Math.random() * 500) + 10
      }
      
      this.recentLogs.unshift(log)
      if (this.recentLogs.length > 50) {
        this.recentLogs.pop()
      }
    },
    clearLogs() {
      this.recentLogs = []
    },
    formatLogTime(timestamp) {
      const date = new Date(timestamp)
      return date.toLocaleTimeString()
    },
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString()
    },
    getStatusClass(status) {
      if (status >= 200 && status < 300) return 'success'
      if (status >= 300 && status < 400) return 'info'
      if (status >= 400 && status < 500) return 'warning'
      return 'error'
    },
    getAlertType(level) {
      const types = {
        '严重': 'danger',
        '警告': 'warning',
        '信息': 'info'
      }
      return types[level] || 'info'
    },
    initCharts() {
      this.initTrafficChart()
      this.initRtChart()
      this.initStatusChart()
    },
    initTrafficChart() {
      const chartDom = document.getElementById('traffic-chart')
      if (!chartDom) return
      
      this.trafficChart = echarts.init(chartDom)
      const option = {
        title: {
          text: '实时流量趋势',
          left: 'center',
          textStyle: {
            fontSize: 16,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['QPS', '响应时间'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.generateTimeLabels()
        },
        yAxis: [
          {
            type: 'value',
            name: 'QPS',
            position: 'left',
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: '响应时间(ms)',
            position: 'right',
            axisLabel: {
              formatter: '{value} ms'
            }
          }
        ],
        series: [
          {
            name: 'QPS',
            type: 'line',
            yAxisIndex: 0,
            data: this.generateQpsData(),
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '响应时间',
            type: 'line',
            yAxisIndex: 1,
            data: this.generateRtData(),
            smooth: true,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      this.trafficChart.setOption(option)
    },
    initRtChart() {
      const chartDom = document.getElementById('rt-distribution-chart')
      if (!chartDom) return
      
      this.rtChart = echarts.init(chartDom)
      const option = {
        title: {
          text: '响应时间分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle'
        },
        series: [
          {
            name: '响应时间分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            data: [
              { value: 335, name: '0-50ms' },
              { value: 310, name: '50-100ms' },
              { value: 234, name: '100-200ms' },
              { value: 135, name: '200-500ms' },
              { value: 48, name: '>500ms' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.rtChart.setOption(option)
    },
    initStatusChart() {
      const chartDom = document.getElementById('status-chart')
      if (!chartDom) return
      
      this.statusChart = echarts.init(chartDom)
      const option = {
        title: {
          text: '状态码分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['200', '201', '400', '401', '404', '500']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '请求数量',
            type: 'bar',
            data: [820, 120, 45, 23, 67, 12],
            itemStyle: {
              color: function(params) {
                const colors = ['#67C23A', '#67C23A', '#E6A23C', '#E6A23C', '#E6A23C', '#F56C6C']
                return colors[params.dataIndex]
              }
            }
          }
        ]
      }
      this.statusChart.setOption(option)
    },
    generateTimeLabels() {
      const labels = []
      const now = new Date()
      for (let i = 29; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 10000) // 每10秒一个点
        labels.push(time.toLocaleTimeString().slice(0, 8))
      }
      return labels
    },
    generateQpsData() {
      const data = []
      for (let i = 0; i < 30; i++) {
        data.push(Math.floor(Math.random() * 1000) + 100)
      }
      return data
    },
    generateRtData() {
      const data = []
      for (let i = 0; i < 30; i++) {
        data.push(Math.floor(Math.random() * 200) + 20)
      }
      return data
    },
    updateCharts() {
      if (this.trafficChart) {
        const option = this.trafficChart.getOption()
        option.xAxis[0].data = this.generateTimeLabels()
        option.series[0].data = this.generateQpsData()
        option.series[1].data = this.generateRtData()
        this.trafficChart.setOption(option)
      }
    },
    destroyCharts() {
      if (this.trafficChart) {
        this.trafficChart.dispose()
        this.trafficChart = null
      }
      if (this.rtChart) {
        this.rtChart.dispose()
        this.rtChart = null
      }
      if (this.statusChart) {
        this.statusChart.dispose()
        this.statusChart = null
      }
    },
    initWebSocket() {
      // 订阅实时监控数据
      wsService.subscribe('realtime-data', this.handleRealtimeData)
      wsService.subscribe('alert', this.handleAlert)
      wsService.subscribe('log', this.handleLog)
      wsService.subscribe('connected', this.handleWebSocketConnected)
      wsService.subscribe('disconnected', this.handleWebSocketDisconnected)
      
      // 尝试连接WebSocket
      wsService.connect()
    },
    disconnectWebSocket() {
      wsService.disconnect()
    },
    handleRealtimeData(data) {
      // 更新实时数据到Vuex store
      this.$store.commit('monitor/SET_REAL_TIME_DATA', data)
      
      // 更新图表
      this.updateCharts()
    },
    handleAlert(alert) {
      // 添加新告警到store
      this.$store.commit('monitor/ADD_ALERT', {
        id: Date.now(),
        level: alert.level || '信息',
        message: alert.message,
        source: alert.source || 'System',
        time: Date.now()
      })
      
      // 显示通知
      this.$message({
        type: alert.level === '严重' ? 'error' : alert.level === '警告' ? 'warning' : 'info',
        message: alert.message,
        duration: 5000
      })
    },
    handleLog(log) {
      // 添加新日志
      this.recentLogs.unshift({
        timestamp: log.timestamp || Date.now(),
        ip: log.ip,
        method: log.method,
        path: log.path,
        status: log.status,
        responseTime: log.responseTime
      })
      
      // 保持最近50条日志
      if (this.recentLogs.length > 50) {
        this.recentLogs.pop()
      }
    },
    handleWebSocketConnected() {
      this.$message.success('实时数据连接已建立')
      
      // 发送订阅消息
      wsService.send({
        type: 'subscribe',
        topics: ['realtime-data', 'alerts', 'logs']
      })
    },
    handleWebSocketDisconnected() {
      this.$message.warning('实时数据连接已断开，正在尝试重连...')
    },
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      
      if (this.isFullscreen) {
        this.enterFullscreen()
      } else {
        this.exitFullscreen()
      }
    },
    enterFullscreen() {
      const element = this.$el
      if (element.requestFullscreen) {
        element.requestFullscreen()
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen()
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen()
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen()
      }
      
      // 监听全屏状态变化
      document.addEventListener('fullscreenchange', this.handleFullscreenChange)
      document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
      document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
      document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)
    },
    exitFullscreen() {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    },
    handleFullscreenChange() {
      const isFullscreen = !!(document.fullscreenElement || 
                             document.webkitFullscreenElement || 
                             document.mozFullScreenElement || 
                             document.msFullscreenElement)
      
      this.isFullscreen = isFullscreen
      
      // 全屏状态改变时重新调整图表大小
      this.$nextTick(() => {
        this.resizeCharts()
      })
    },
    resizeCharts() {
      if (this.trafficChart) {
        this.trafficChart.resize()
      }
      if (this.rtChart) {
        this.rtChart.resize()
      }
      if (this.statusChart) {
        this.statusChart.resize()
      }
    },

    // 数据导出处理
    handleExport(command) {
      switch (command) {
        case 'excel':
          this.exportToExcel()
          break
        case 'csv':
          this.exportToCSV()
          break
        case 'json':
          this.exportToJSON()
          break
        case 'logs':
          this.exportLogs()
          break
      }
    },

    // 导出Excel格式
    exportToExcel() {
      const monitorData = {
        realTimeData: this.realTimeData,
        historicalData: this.historicalData,
        alerts: this.alerts
      }
      
      const success = exportMonitorDataToExcel(monitorData, '监控数据报告')
      if (success) {
        this.$message.success('Excel导出成功')
      } else {
        this.$message.error('Excel导出失败')
      }
    },

    // 导出CSV格式
    exportToCSV() {
      const csvData = this.historicalData.map(item => ({
        '时间': new Date(item.timestamp).toLocaleString(),
        'QPS': item.qps,
        '响应时间(ms)': item.rt,
        '成功率(%)': item.successRate.toFixed(2),
        '错误率(%)': item.errorRate.toFixed(2)
      }))
      
      const success = exportToCSV(csvData, '监控历史数据')
      if (success) {
        this.$message.success('CSV导出成功')
      } else {
        this.$message.error('CSV导出失败')
      }
    },

    // 导出JSON格式
    exportToJSON() {
      const jsonData = {
        exportTime: new Date().toISOString(),
        realTimeData: this.realTimeData,
        historicalData: this.historicalData,
        alerts: this.alerts,
        logs: this.logs
      }
      
      const success = exportToJSON(jsonData, '监控数据完整报告')
      if (success) {
        this.$message.success('JSON导出成功')
      } else {
        this.$message.error('JSON导出失败')
      }
    },

    // 导出日志
    exportLogs() {
      if (this.logs.length === 0) {
        this.$message.warning('暂无日志数据可导出')
        return
      }
      
      const success = exportLogsToExcel(this.logs, '系统访问日志')
      if (success) {
        this.$message.success('日志导出成功')
      } else {
        this.$message.error('日志导出失败')
      }
    }
  }
}
</script>

<style scoped>
.monitor-content {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  color: #333;
  margin-bottom: 5px;
}

.header-left p {
  color: #666;
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 全屏模式样式 */
.monitor:-webkit-full-screen {
  background: #f5f5f5;
}

.monitor:-moz-full-screen {
  background: #f5f5f5;
}

.monitor:-ms-fullscreen {
  background: #f5f5f5;
}

.monitor:fullscreen {
  background: #f5f5f5;
}

.monitor:-webkit-full-screen .monitor-content {
  padding: 10px;
}

.monitor:-moz-full-screen .monitor-content {
  padding: 10px;
}

.monitor:-ms-fullscreen .monitor-content {
  padding: 10px;
}

.monitor:fullscreen .monitor-content {
  padding: 10px;
}

.monitor:-webkit-full-screen .chart-container {
  height: 400px;
}

.monitor:-moz-full-screen .chart-container {
  height: 400px;
}

.monitor:-ms-fullscreen .chart-container {
  height: 400px;
}

.monitor:fullscreen .chart-container {
  height: 400px;
}

.metrics-row {
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.metric-title {
  font-size: 14px;
  color: #666;
}

.metric-icon {
  font-size: 20px;
  color: #409EFF;
}

.metric-value {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.metric-trend {
  font-size: 12px;
  color: #666;
}

.trend-value {
  margin-left: 5px;
  font-weight: 600;
}

.trend-value.positive {
  color: #67C23A;
}

.trend-value.negative {
  color: #F56C6C;
}

.charts-row {
  margin-bottom: 30px;
}

.chart-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-header h3 {
  color: #333;
  margin: 0;
  font-size: 16px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chart-container {
  height: 300px;
  border-radius: 4px;
}

.logs-section, .alerts-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.logs-header, .alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.logs-header h3, .alerts-header h3 {
  color: #333;
  margin: 0;
  font-size: 16px;
}

.logs-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logs-content {
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.log-time {
  color: #666;
  width: 80px;
}

.log-ip {
  color: #409EFF;
  width: 120px;
}

.log-method {
  width: 60px;
  font-weight: 600;
}

.log-method.get { color: #67C23A; }
.log-method.post { color: #409EFF; }
.log-method.put { color: #E6A23C; }
.log-method.delete { color: #F56C6C; }

.log-path {
  flex: 1;
  color: #333;
}

.log-status {
  width: 40px;
  font-weight: 600;
}

.log-status.success { color: #67C23A; }
.log-status.info { color: #409EFF; }
.log-status.warning { color: #E6A23C; }
.log-status.error { color: #F56C6C; }

.log-rt {
  color: #666;
  width: 60px;
}
</style>