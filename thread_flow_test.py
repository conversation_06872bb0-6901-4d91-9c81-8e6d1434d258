#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程限流测试脚本
测试所有租户的线程限流功能（grade=0, count=5）
"""

import requests
import threading
import time
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
import json

class ThreadFlowTester:
    def __init__(self, base_url, concurrent_threads=10, test_duration=10):
        self.base_url = base_url
        self.concurrent_threads = concurrent_threads
        self.test_duration = test_duration
        self.results = defaultdict(lambda: {'success': 0, 'blocked': 0, 'error': 0, 'total': 0})
        self.lock = threading.Lock()
        
    def make_request(self, tenant_id, thread_id):
        """发送单个请求"""
        try:
            headers = {'X-Tenant-ID': tenant_id}
            response = requests.get(self.base_url, headers=headers, timeout=5)
            
            with self.lock:
                self.results[tenant_id]['total'] += 1
                if response.status_code == 200:
                    self.results[tenant_id]['success'] += 1
                elif response.status_code == 429:  # 限流状态码
                    self.results[tenant_id]['blocked'] += 1
                else:
                    self.results[tenant_id]['error'] += 1
                    
            return {
                'tenant_id': tenant_id,
                'thread_id': thread_id,
                'status_code': response.status_code,
                'success': response.status_code == 200
            }
        except Exception as e:
            with self.lock:
                self.results[tenant_id]['total'] += 1
                self.results[tenant_id]['error'] += 1
            return {
                'tenant_id': tenant_id,
                'thread_id': thread_id,
                'error': str(e),
                'success': False
            }
    
    def test_tenant_thread_limit(self, tenant_id):
        """测试单个租户的线程限流"""
        print(f"\n开始测试 {tenant_id} 的线程限流（预期限制：5个并发线程）")
        
        start_time = time.time()
        futures = []
        
        with ThreadPoolExecutor(max_workers=self.concurrent_threads) as executor:
            # 持续提交任务直到测试时间结束
            thread_counter = 0
            while time.time() - start_time < self.test_duration:
                future = executor.submit(self.make_request, tenant_id, thread_counter)
                futures.append(future)
                thread_counter += 1
                time.sleep(0.01)  # 短暂间隔避免过快提交
            
            # 等待所有任务完成
            for future in as_completed(futures, timeout=30):
                try:
                    result = future.result()
                except Exception as e:
                    print(f"任务执行异常: {e}")
        
        return self.results[tenant_id]
    
    def evaluate_thread_limit_result(self, tenant_id, result):
        """评估线程限流测试结果"""
        total = result['total']
        success = result['success']
        blocked = result['blocked']
        error = result['error']
        
        if total == 0:
            return False, "没有发送任何请求"
        
        success_rate = (success / total) * 100
        block_rate = (blocked / total) * 100
        
        print(f"\n{tenant_id} 线程限流测试结果:")
        print(f"  总请求数: {total}")
        print(f"  成功请求: {success} ({success_rate:.2f}%)")
        print(f"  被限流: {blocked} ({block_rate:.2f}%)")
        print(f"  错误请求: {error}")
        
        # 线程限流的评估标准：
        # 1. 应该有一定比例的请求被限流（因为并发超过了5个线程的限制）
        # 2. 成功率不应该太高（如果没有限流，成功率会接近100%）
        # 3. 被限流的比例应该合理（表明限流机制在工作）
        
        expected_block_rate_min = 20  # 至少20%的请求应该被限流
        expected_success_rate_max = 80  # 成功率不应超过80%
        
        if block_rate >= expected_block_rate_min and success_rate <= expected_success_rate_max:
            return True, f"线程限流正常工作，限流率: {block_rate:.2f}%"
        elif block_rate < expected_block_rate_min:
            return False, f"线程限流可能未生效，限流率过低: {block_rate:.2f}% (期望 >= {expected_block_rate_min}%)"
        else:
            return False, f"线程限流异常，成功率过高: {success_rate:.2f}% (期望 <= {expected_success_rate_max}%)"
    
    def run_all_tests(self):
        """运行所有租户的线程限流测试"""
        tenants = ['tenant1', 'tenant2', 'tenant3', 'tenant4', 'tenant5']
        test_results = {}
        
        print(f"开始线程限流测试")
        print(f"测试配置: 并发线程数={self.concurrent_threads}, 测试时长={self.test_duration}秒")
        print(f"预期限制: 每个租户最多5个并发线程")
        print("="*60)
        
        for tenant_id in tenants:
            # 重置结果
            self.results[tenant_id] = {'success': 0, 'blocked': 0, 'error': 0, 'total': 0}
            
            # 测试租户线程限流
            result = self.test_tenant_thread_limit(tenant_id)
            is_pass, message = self.evaluate_thread_limit_result(tenant_id, result)
            
            test_results[tenant_id] = {
                'result': result,
                'pass': is_pass,
                'message': message
            }
            
            print(f"  结果: {'✓ 通过' if is_pass else '✗ 失败'} - {message}")
            
            # 测试间隔
            time.sleep(1)
        
        # 生成测试报告
        self.generate_report(test_results)
        
        return test_results
    
    def generate_report(self, test_results):
        """生成测试报告"""
        print("\n" + "="*60)
        print("线程限流测试报告")
        print("="*60)
        
        passed_tests = sum(1 for result in test_results.values() if result['pass'])
        total_tests = len(test_results)
        
        print(f"测试概览:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过测试: {passed_tests}")
        print(f"  失败测试: {total_tests - passed_tests}")
        print(f"  通过率: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n详细结果:")
        for tenant_id, test_result in test_results.items():
            result = test_result['result']
            status = "✓ 通过" if test_result['pass'] else "✗ 失败"
            print(f"  {tenant_id}: {status}")
            print(f"    请求统计: 总数={result['total']}, 成功={result['success']}, 限流={result['blocked']}, 错误={result['error']}")
            print(f"    评估结果: {test_result['message']}")
        
        # 保存详细报告到文件
        report_data = {
            'test_config': {
                'concurrent_threads': self.concurrent_threads,
                'test_duration': self.test_duration,
                'expected_thread_limit': 5
            },
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'pass_rate': (passed_tests/total_tests)*100
            },
            'detailed_results': test_results,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        with open('thread_flow_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细报告已保存到: thread_flow_test_report.json")

def main():
    parser = argparse.ArgumentParser(description='线程限流测试脚本')
    parser.add_argument('--url', default='http://localhost:8088/api/test', 
                       help='测试API基础URL (默认: http://localhost:8088/api/test)')
    parser.add_argument('--concurrent', type=int, default=10, 
                       help='并发线程数 (默认: 10)')
    parser.add_argument('--test-duration', type=int, default=10, 
                       help='每个租户的测试持续时间(秒) (默认: 10)')
    
    args = parser.parse_args()
    
    # 验证服务是否可用
    try:
        response = requests.get(args.url, timeout=5)
        print(f"服务连接成功: {args.url}")
    except Exception as e:
        print(f"无法连接到服务 {args.url}: {e}")
        return
    
    # 创建测试器并运行测试
    tester = ThreadFlowTester(
        base_url=args.url,
        concurrent_threads=args.concurrent,
        test_duration=args.test_duration
    )
    
    test_results = tester.run_all_tests()
    
    # 返回退出码
    passed_tests = sum(1 for result in test_results.values() if result['pass'])
    total_tests = len(test_results)
    
    if passed_tests == total_tests:
        print("\n🎉 所有线程限流测试通过！")
        exit(0)
    else:
        print(f"\n❌ {total_tests - passed_tests} 个测试失败")
        exit(1)

if __name__ == '__main__':
    main()