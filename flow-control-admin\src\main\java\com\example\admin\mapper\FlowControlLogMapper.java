package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.entity.FlowControlLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 流控日志Mapper接口
 */
@Mapper
public interface FlowControlLogMapper extends BaseMapper<FlowControlLog> {
    
    /**
     * 分页查询流控日志
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 流控日志列表
     */
    Page<FlowControlLog> selectLogPage(Page<FlowControlLog> page,
                                       @Param("tenantId") String tenantId,
                                       @Param("resourceName") String resourceName,
                                       @Param("eventType") String eventType,
                                       @Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据时间范围查询流控日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @return 流控日志列表
     */
    List<FlowControlLog> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime,
                                           @Param("tenantId") String tenantId,
                                           @Param("resourceName") String resourceName);
    
    /**
     * 根据租户ID查询流控日志
     *
     * @param tenantId 租户ID
     * @param eventType 事件类型
     * @param limit 限制数量
     * @return 流控日志列表
     */
    List<FlowControlLog> selectByTenantId(@Param("tenantId") String tenantId,
                                          @Param("eventType") String eventType,
                                          @Param("limit") Integer limit);
    
    /**
     * 根据资源名称查询流控日志
     *
     * @param resourceName 资源名称
     * @param eventType 事件类型
     * @param limit 限制数量
     * @return 流控日志列表
     */
    List<FlowControlLog> selectByResourceName(@Param("resourceName") String resourceName,
                                              @Param("eventType") String eventType,
                                              @Param("limit") Integer limit);
    
    /**
     * 根据事件类型查询流控日志
     *
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 流控日志列表
     */
    List<FlowControlLog> selectByEventType(@Param("eventType") String eventType,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime,
                                           @Param("limit") Integer limit);
    
    /**
     * 查询最近的流控日志
     *
     * @param minutes 分钟数
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @return 最近的流控日志列表
     */
    List<FlowControlLog> selectRecentLogs(@Param("minutes") int minutes,
                                          @Param("tenantId") String tenantId,
                                          @Param("resourceName") String resourceName);
    
    /**
     * 统计事件类型分布
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @return 事件类型统计结果
     */
    List<java.util.Map<String, Object>> selectEventTypeStatistics(@Param("startTime") LocalDateTime startTime,
                                                                   @Param("endTime") LocalDateTime endTime,
                                                                   @Param("tenantId") String tenantId);
    
    /**
     * 统计资源访问频次
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 资源访问频次统计结果
     */
    List<java.util.Map<String, Object>> selectResourceAccessStatistics(@Param("startTime") LocalDateTime startTime,
                                                                        @Param("endTime") LocalDateTime endTime,
                                                                        @Param("tenantId") String tenantId,
                                                                        @Param("limit") int limit);
    
    /**
     * 统计租户日志数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 租户日志数量统计结果
     */
    List<java.util.Map<String, Object>> selectTenantLogStatistics(@Param("startTime") LocalDateTime startTime,
                                                                   @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询异常日志（错误、阻塞等）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 异常日志列表
     */
    List<FlowControlLog> selectAbnormalLogs(@Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime,
                                            @Param("tenantId") String tenantId,
                                            @Param("limit") int limit);
    
    /**
     * 查询告警日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 告警日志列表
     */
    List<FlowControlLog> selectAlarmLogs(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime,
                                         @Param("tenantId") String tenantId,
                                         @Param("limit") int limit);
    
    /**
     * 批量插入流控日志
     *
     * @param logList 流控日志列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<FlowControlLog> logList);
    
    /**
     * 删除过期的日志数据
     *
     * @param beforeTime 过期时间点
     * @param eventType 事件类型（可选）
     * @return 删除数量
     */
    int deleteExpiredLogs(@Param("beforeTime") LocalDateTime beforeTime,
                          @Param("eventType") String eventType);
    
    /**
     * 统计日志总数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @return 日志总数
     */
    Long countLogs(@Param("startTime") LocalDateTime startTime,
                   @Param("endTime") LocalDateTime endTime,
                   @Param("tenantId") String tenantId);
    
    /**
     * 统计错误日志数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @return 错误日志数量
     */
    Long countErrorLogs(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        @Param("tenantId") String tenantId);
    
    /**
     * 查询日志趋势数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @param eventType 事件类型
     * @param granularity 粒度（hour、day）
     * @return 日志趋势数据
     */
    List<java.util.Map<String, Object>> selectLogTrendData(@Param("startTime") LocalDateTime startTime,
                                                            @Param("endTime") LocalDateTime endTime,
                                                            @Param("tenantId") String tenantId,
                                                            @Param("eventType") String eventType,
                                                            @Param("granularity") String granularity);
    
    /**
     * 查询IP访问统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return IP访问统计结果
     */
    List<java.util.Map<String, Object>> selectIpAccessStatistics(@Param("startTime") LocalDateTime startTime,
                                                                  @Param("endTime") LocalDateTime endTime,
                                                                  @Param("tenantId") String tenantId,
                                                                  @Param("limit") int limit);

    /**
     * 查询热点IP列表
     *
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 热点IP列表
     */
    List<java.util.Map<String, Object>> selectHotIpList(@Param("tenantId") String tenantId,
                                                         @Param("resourceName") String resourceName,
                                                         @Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime,
                                                         @Param("limit") Integer limit);

    /**
     * 查询攻击检测结果
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param threshold 阈值
     * @param limit 限制数量
     * @return 攻击检测结果
     */
    List<java.util.Map<String, Object>> selectAttackDetectionResults(@Param("tenantId") String tenantId,
                                                                     @Param("startTime") LocalDateTime startTime,
                                                                     @Param("endTime") LocalDateTime endTime,
                                                                     @Param("threshold") Integer threshold,
                                                                     @Param("limit") Integer limit);
}