# Sentinel流量控制系统本地部署说明

## 环境要求

### 必需软件
1. **Java 17+** - 运行Spring Boot应用
2. **Maven 3.6+** - 构建Java项目
3. **Node.js 16+** - 运行React前端
4. **MySQL 8.0+** - 数据库
5. **Redis 6.0+** - 缓存
6. **Nacos 2.0+** - 服务注册与配置中心

## 数据库配置

### MySQL数据库初始化
1. 确保MySQL服务已启动
2. 创建数据库：
```sql
CREATE DATABASE sentinel_flow_control CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. 执行初始化脚本：
```bash
mysql -u root -p sentinel_flow_control < sql/init.sql
```

### 数据库连接配置
默认配置：
- 主机：localhost
- 端口：3306
- 数据库：sentinel_flow_control
- 用户名：root
- 密码：root

如需修改，请编辑以下文件中的环境变量：
- `start-local.bat` (Windows)
- `flow-control-admin/src/main/resources/application.yml`
- `gateway-service/src/main/resources/application.yml`

## Redis配置

### Redis服务启动
1. 启动Redis服务：
```bash
redis-server
```

默认配置：
- 主机：localhost
- 端口：6379
- 密码：无

## Nacos配置

### Nacos服务启动
1. 下载Nacos：https://github.com/alibaba/nacos/releases
2. 解压并启动：
```bash
# Windows
startup.cmd -m standalone

# Linux/Mac
sh startup.sh -m standalone
```

3. 访问Nacos控制台：http://localhost:8848/nacos
   - 用户名：nacos
   - 密码：nacos

### Nacos配置导入
在Nacos控制台中创建以下配置：

1. **common-config.yml** (Group: DEFAULT_GROUP)
```yaml
# 通用配置
logging:
  level:
    com.example: DEBUG
    org.springframework.cloud.gateway: DEBUG
```

2. **database-config.yml** (Group: DEFAULT_GROUP)
```yaml
# 数据库配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

3. **sentinel-gateway-flow-control-common.yml** (Group: DEFAULT_GROUP)
```yaml
# Sentinel配置
sentinel:
  transport:
    dashboard: localhost:8080
    port: 8719
  datasource:
    flow:
      nacos:
        server-addr: localhost:8848
        dataId: sentinel-gateway-flow-rules
        groupId: SENTINEL_GROUP
        rule-type: flow
```

## 启动步骤

### 方式一：使用启动脚本（推荐）
1. 双击运行 `start-local.bat`
2. 脚本会自动：
   - 检查环境
   - 编译Java项目
   - 安装前端依赖
   - 启动所有服务

### 方式二：手动启动

#### 1. 编译Java项目
```bash
mvn clean compile -DskipTests
```

#### 2. 启动Gateway服务
```bash
mvn spring-boot:run -pl gateway-service
```

#### 3. 启动Admin服务
```bash
mvn spring-boot:run -pl flow-control-admin
```

#### 4. 启动前端服务
```bash
cd flow-control-frontend
npm install
npm start
```

## 访问地址

- **前端管理界面**: http://localhost:3000
- **Gateway网关**: http://localhost:8080
- **Admin后台API**: http://localhost:8081
- **Nacos控制台**: http://localhost:8848/nacos

## 环境变量配置

如需修改默认配置，可在 `start-local.bat` 中修改以下环境变量：

```batch
set MYSQL_HOST=localhost
set MYSQL_PORT=3306
set MYSQL_DATABASE=sentinel_flow_control
set MYSQL_USERNAME=root
set MYSQL_PASSWORD=your_password
set REDIS_HOST=localhost
set REDIS_PORT=6379
set NACOS_NAMESPACE=
```

## 常见问题

### 1. 端口冲突
如果端口被占用，可修改以下配置文件中的端口：
- Gateway: `gateway-service/src/main/resources/application.yml` (server.port)
- Admin: `flow-control-admin/src/main/resources/application.yml` (server.port)
- Frontend: `flow-control-frontend/package.json` (scripts.start)

### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库用户名密码正确
- 确认数据库已创建

### 3. Redis连接失败
- 检查Redis服务是否启动
- 确认Redis端口配置正确

### 4. Nacos连接失败
- 检查Nacos服务是否启动
- 确认Nacos端口配置正确
- 检查配置是否已导入

## 测试验证

### 1. 健康检查
```bash
# Gateway健康检查
curl http://localhost:8080/actuator/health

# Admin健康检查
curl http://localhost:8081/actuator/health
```

### 2. 功能测试
1. 访问前端管理界面：http://localhost:3000
2. 创建流量控制规则
3. 通过Gateway发送测试请求
4. 观察流量控制效果

## 停止服务

关闭所有启动的命令行窗口即可停止服务。