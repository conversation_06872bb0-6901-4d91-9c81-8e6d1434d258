// 中文语言包
export default {
	// 通用
	common: {
		confirm: '确认',
		cancel: '取消',
		save: '保存',
		delete: '删除',
		edit: '编辑',
		add: '添加',
		search: '搜索',
		reset: '重置',
		submit: '提交',
		back: '返回',
		close: '关闭',
		loading: '加载中...',
		noData: '暂无数据',
		operation: '操作',
		status: '状态',
		createTime: '创建时间',
		updateTime: '更新时间',
		description: '描述',
		name: '名称',
		type: '类型',
		value: '值',
		enabled: '启用',
		disabled: '禁用',
		success: '成功',
		failed: '失败',
		warning: '警告',
		info: '信息',
		error: '错误'
	},

	// 表格和分页
	table: {
		emptyText: '暂无数据',
		loading: '加载中...',
		noDataText: '没有找到相关数据'
	},

	pagination: {
		total: '共 {total} 条',
		goto: '前往',
		pageClassifier: '页',
		page: '页',
		prevPage: '上一页',
		nextPage: '下一页',
		itemsPerPage: '条/页'
	},

	// Element UI 分页组件标准翻译键
	el: {
		pagination: {
			goto: '前往',
			pagesize: '条/页',
			total: '共 {total} 条',
			pageClassifier: '页',
			page: '页',
			prev: '上一页',
			next: '下一页',
			currentPage: '第 {pager} 页',
			prevPages: '向前 {pager} 页',
			nextPages: '向后 {pager} 页'
		},
		table: {
			emptyText: '暂无数据',
			confirmFilter: '筛选',
			resetFilter: '重置',
			clearFilter: '全部',
			sumText: '合计'
		}
	},

	// 导航菜单
	menu: {
		dashboard: '仪表板',
		tenants: '租户管理',
		flowControl: '流量控制',
		tenantFlowRules: '租户流量规则',
		ipFlowRules: 'IP流量规则',
		ipManagement: 'IP黑白名单',
		monitor: '实时监控',
		interfaceFlowRules: '接口流量规则',
		ipBlackWhiteList: 'IP黑白名单',
		statistics: '统计分析',
		config: '系统配置'
	},

	// 用户相关
	user: {
		login: '登录',
		logout: '退出登录',
		username: '用户名',
		password: '密码',
		profile: '个人资料',
		settings: '设置',
		themeSettings: '主题设置',
		languageSettings: '语言设置'
	},

	// 仪表板
	dashboard: {
		title: '仪表板',
		totalRequests: '总请求数',
		blockedRequests: '拦截请求数',
		passedRequests: '通过请求数',
		systemLoad: '系统负载',
		memoryUsage: '内存使用率',
		cpuUsage: 'CPU使用率',
		requestTrend: '请求趋势',
		topBlockedIPs: '拦截IP排行',
		recentAlerts: '最近告警'
	},

	// 租户流量规则
	tenantFlowRules: {
		title: '流量规则',
		ruleName: '规则名称',
		resource: '资源名称',
		limitApp: '限流应用',
		grade: '阈值类型',
		count: '阈值',
		strategy: '流控模式',
		controlBehavior: '流控效果',
		warmUpPeriodSec: '预热时长',
		maxQueueingTimeMs: '排队超时时间',
		clusterMode: '集群模式',
		addRule: '添加规则',
		editRule: '编辑规则',
		deleteRule: '删除规则',
		enableRule: '启用规则',
		disableRule: '禁用规则'
	},

	// IP流量规则
	ipFlowRules: {
		title: 'IP规则',
		ipAddress: 'IP地址',
		ipRange: 'IP范围',
		ruleType: '规则类型',
		whitelist: '白名单',
		blacklist: '黑名单',
		addRule: '添加IP规则',
		editRule: '编辑IP规则',
		deleteRule: '删除IP规则',
		importRules: '导入规则',
		exportRules: '导出规则'
	},

	// 实时监控
	monitor: {
		title: '实时监控',
		realTimeData: '实时数据',
		requestRate: '请求速率',
		responseTime: '响应时间',
		errorRate: '错误率',
		activeConnections: '活跃连接数',
		systemMetrics: '系统指标',
		alertRules: '告警规则',
		alertHistory: '告警历史'
	},

	// 统计分析
	statistics: {
		title: '统计分析',
		timeRange: '时间范围',
		today: '今天',
		yesterday: '昨天',
		lastWeek: '最近7天',
		lastMonth: '最近30天',
		custom: '自定义',
		requestStatistics: '请求统计',
		ipStatistics: 'IP统计',
		errorStatistics: '错误统计',
		performanceAnalysis: '性能分析'
	},

	// 系统配置
	config: {
		title: '系统配置',
		globalConfig: '全局配置',
		alertConfig: '告警配置',
		notificationConfig: '通知配置',
		systemLogs: '系统日志',

		// 全局参数
		globalParams: {
			title: '全局参数',
			maxConnections: '最大连接数',
			requestTimeout: '请求超时时间',
			enableLogging: '启用日志记录',
			logLevel: '日志级别',
			enableMetrics: '启用指标收集',
			metricsInterval: '指标收集间隔'
		},

		// 告警设置
		alertSettings: {
			title: '告警设置',
			enableAlert: '启用告警',
			cpuThreshold: 'CPU阈值',
			memoryThreshold: '内存阈值',
			diskThreshold: '磁盘阈值',
			errorRateThreshold: '错误率阈值',
			responseTimeThreshold: '响应时间阈值'
		},

		// 通知配置
		notification: {
			title: '通知配置',
			emailNotification: '邮件通知',
			smsNotification: '短信通知',
			webhookNotification: 'Webhook通知',
			emailSettings: '邮件设置',
			smtpServer: 'SMTP服务器',
			smtpPort: 'SMTP端口',
			emailAccount: '邮箱账号',
			emailPassword: '邮箱密码',
			recipients: '收件人',
			smsSettings: '短信设置',
			smsProvider: '短信服务商',
			apiKey: 'API密钥',
			apiSecret: 'API密钥',
			phoneNumbers: '手机号码',
			webhookSettings: 'Webhook设置',
			webhookUrl: 'Webhook地址',
			webhookMethod: '请求方法',
			webhookHeaders: '请求头',
			webhookSecret: '签名密钥'
		}
	},

	// 主题
	theme: {
		light: '明亮模式',
		dark: '暗黑模式',
		switchToLight: '已切换到明亮模式',
		switchToDark: '已切换到暗黑模式',
		themeSettings: '主题设置',
		selectTheme: '选择您喜欢的主题模式'
	},

	// 语言
	language: {
		chinese: '简体中文',
		english: 'English',
		switchLanguage: '切换语言',
		languageChanged: '语言已切换'
	},

	// 消息提示
	message: {
		saveSuccess: '保存成功',
		saveFailed: '保存失败',
		deleteSuccess: '删除成功',
		deleteFailed: '删除失败',
		operationSuccess: '操作成功',
		operationFailed: '操作失败',
		loginSuccess: '登录成功',
		loginFailed: '登录失败',
		logoutSuccess: '退出登录成功',
		logoutFailed: '退出登录失败',
		networkError: '网络错误',
		serverError: '服务器错误',
		parameterError: '参数错误',
		permissionDenied: '权限不足',
		confirmDelete: '确认删除此项？',
		confirmOperation: '确认执行此操作？'
	},

	// 表单验证
	validation: {
		required: '此字段为必填项',
		email: '请输入有效的邮箱地址',
		phone: '请输入有效的手机号码',
		url: '请输入有效的URL地址',
		number: '请输入有效的数字',
		integer: '请输入有效的整数',
		minLength: '长度不能少于{min}个字符',
		maxLength: '长度不能超过{max}个字符',
		min: '值不能小于{min}',
		max: '值不能大于{max}'
	},

	// 操作向导
	guide: {
		previous: '上一步',
		next: '下一步',
		skip: '跳过',
		finish: '完成',
		completed: '引导完成！',

		welcome: {
			title: '欢迎使用流量控制系统',
			content: '这是一个功能强大的网络流量管理平台。让我们通过简单的引导来了解主要功能。'
		},

		sidebar: {
			title: '导航菜单',
			content: '左侧是主要的导航菜单，您可以通过这里访问系统的各个功能模块。'
		},

		dashboard: {
			title: '仪表盘',
			content: '仪表盘是系统的首页，显示重要的统计信息和系统状态概览。'
		},

		theme: {
			title: '主题切换',
			content: '点击这里可以在明亮和暗黑主题之间切换，选择您喜欢的界面风格。'
		},

		language: {
			title: '语言切换',
			content: '点击这里可以在中文和英文之间切换界面语言。'
		},

		complete: {
			title: '引导完成',
			content: '恭喜！您已经了解了系统的基本功能。如需再次查看引导，请点击右下角的帮助按钮。'
		}
	},

	// 帮助文档
	help: {
		title: '帮助文档',
		search: '搜索帮助内容',
		categories: {
			getting_started: '快速开始',
			tenant_flow_rules: '租户流量规则',
			ip_flow_rules: 'IP流量规则',
			monitoring: '监控统计',
			configuration: '系统配置',
			troubleshooting: '故障排除'
		},

		getting_started: {
			title: '快速开始',
			overview: '系统概述',
			first_login: '首次登录',
			basic_setup: '基础设置'
		},

		tenant_flow_rules: {
			title: '租户流量规则管理',
			create_rule: '创建规则',
			edit_rule: '编辑规则',
			rule_priority: '规则优先级'
		},

		ip_flow_rules: {
			title: 'IP流量规则管理',
			whitelist: '白名单设置',
			blacklist: '黑名单设置',
			ip_range: 'IP范围配置'
		}
	}
}