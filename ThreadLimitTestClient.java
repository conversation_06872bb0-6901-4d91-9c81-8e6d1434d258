import java.io.*;
import java.net.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadLimitTestClient {
    private static final String TEST_URL = "http://localhost:8088/api/test";
    private static final String TENANT_ID = "thread_test";
    private static final int THREAD_COUNT = 5;
    private static final int DELAY_MS = 2000; // 每个请求持续2秒
    
    private static AtomicInteger successCount = new AtomicInteger(0);
    private static AtomicInteger failCount = new AtomicInteger(0);
    
    public static void main(String[] args) {
        System.out.println("开始线程限流测试...");
        System.out.println("测试配置：");
        System.out.println("- 租户ID: " + TENANT_ID);
        System.out.println("- 并发线程数: " + THREAD_COUNT);
        System.out.println("- 每个请求持续时间: " + DELAY_MS + "ms");
        System.out.println("- 预期线程限流阈值: 2个线程");
        System.out.println("- 预期结果: 最多2个请求成功，其余被限流\n");
        
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
        
        // 同时启动多个线程发送请求
        for (int i = 0; i < THREAD_COUNT; i++) {
            final int threadId = i + 1;
            executor.submit(() -> {
                try {
                    sendRequest(threadId);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            // 等待所有请求完成
            latch.await();
            
            System.out.println("\n=== 测试结果 ===");
            System.out.println("成功请求数: " + successCount.get());
            System.out.println("失败请求数: " + failCount.get());
            System.out.println("总请求数: " + (successCount.get() + failCount.get()));
            
            // 分析结果
            if (successCount.get() <= 2 && failCount.get() > 0) {
                System.out.println("\n✓ 线程限流功能正常工作！");
                System.out.println("  - 最多允许2个并发线程通过");
                System.out.println("  - 超出阈值的请求被正确限流");
            } else if (successCount.get() == THREAD_COUNT) {
                System.out.println("\n✗ 线程限流功能未生效！");
                System.out.println("  - 所有请求都成功了，没有被限流");
                System.out.println("  - 可能的原因：规则未加载或线程限流逻辑有问题");
            } else {
                System.out.println("\n? 结果异常，需要进一步分析");
            }
            
        } catch (InterruptedException e) {
            System.err.println("测试被中断: " + e.getMessage());
        } finally {
            executor.shutdown();
        }
    }
    
    private static void sendRequest(int threadId) {
        try {
            System.out.println("[线程" + threadId + "] 开始发送请求...");
            
            URL url = new URL(TEST_URL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            
            // 设置请求属性
            conn.setRequestMethod("GET");
            conn.setRequestProperty("X-Tenant-ID", TENANT_ID);
            conn.setRequestProperty("User-Agent", "ThreadLimitTest/1.0");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(10000);
            
            // 发送请求
            long startTime = System.currentTimeMillis();
            int responseCode = conn.getResponseCode();
            long endTime = System.currentTimeMillis();
            
            if (responseCode == 200) {
                // 读取响应内容
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                successCount.incrementAndGet();
                System.out.println("[线程" + threadId + "] ✓ 请求成功 (" + responseCode + ") - 耗时: " + (endTime - startTime) + "ms");
                System.out.println("[线程" + threadId + "] 响应: " + response.toString());
                
                // 模拟长时间处理
                Thread.sleep(DELAY_MS);
                
            } else {
                // 读取错误响应
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
                StringBuilder errorResponse = new StringBuilder();
                String line;
                while ((line = errorReader.readLine()) != null) {
                    errorResponse.append(line);
                }
                errorReader.close();
                
                failCount.incrementAndGet();
                System.out.println("[线程" + threadId + "] ✗ 请求失败 (" + responseCode + ") - 耗时: " + (endTime - startTime) + "ms");
                System.out.println("[线程" + threadId + "] 错误: " + errorResponse.toString());
            }
            
            conn.disconnect();
            
        } catch (Exception e) {
            failCount.incrementAndGet();
            System.err.println("[线程" + threadId + "] 请求异常: " + e.getMessage());
        }
    }
}