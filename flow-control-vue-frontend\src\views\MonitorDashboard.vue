<template>
  <div class="monitor-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>监控统计</h1>
      <p>实时监控流量控制系统的运行状态和统计数据</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <i class="el-icon-data-line"></i>
              </div>
              <div class="stats-info">
                <h3>{{ dashboardData.totalRequests || 0 }}</h3>
                <p>总请求数</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <i class="el-icon-warning"></i>
              </div>
              <div class="stats-info">
                <h3>{{ dashboardData.blockedRequests || 0 }}</h3>
                <p>拦截请求数</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <i class="el-icon-success"></i>
              </div>
              <div class="stats-info">
                <h3>{{ dashboardData.passedRequests || 0 }}</h3>
                <p>通过请求数</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <i class="el-icon-time"></i>
              </div>
              <div class="stats-info">
                <h3>{{ dashboardData.avgResponseTime || 0 }}ms</h3>
                <p>平均响应时间</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 实时QPS图表 -->
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>实时QPS监控</span>
              <el-button-group style="float: right;">
                <el-button size="mini" @click="setTimeRange('1h')" :type="timeRange === '1h' ? 'primary' : ''">1小时</el-button>
                <el-button size="mini" @click="setTimeRange('6h')" :type="timeRange === '6h' ? 'primary' : ''">6小时</el-button>
                <el-button size="mini" @click="setTimeRange('24h')" :type="timeRange === '24h' ? 'primary' : ''">24小时</el-button>
              </el-button-group>
            </div>
            <div ref="qpsChart" style="height: 300px;"></div>
          </el-card>
        </el-col>

        <!-- 响应时间图表 -->
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>响应时间趋势</span>
            </div>
            <div ref="responseTimeChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 租户流量排行 -->
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>租户流量排行</span>
            </div>
            <div ref="tenantRankingChart" style="height: 300px;"></div>
          </el-card>
        </el-col>

        <!-- 规则命中统计 -->
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>规则命中统计</span>
            </div>
            <div ref="ruleHitChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 实时日志 -->
    <div class="realtime-logs">
      <el-card>
        <div slot="header">
          <span>实时日志</span>
          <el-switch
            v-model="autoRefresh"
            active-text="自动刷新"
            style="float: right;"
            @change="toggleAutoRefresh">
          </el-switch>
        </div>
        <el-table
          :data="realtimeLogs"
          style="width: 100%"
          max-height="300"
          stripe>
          <el-table-column prop="timestamp" label="时间" width="180">
            <template slot-scope="scope">
              {{ formatTime(scope.row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column prop="tenantId" label="租户ID" width="120"></el-table-column>
          <el-table-column prop="ipAddress" label="IP地址" width="150"></el-table-column>
          <el-table-column prop="resource" label="资源" width="200"></el-table-column>
          <el-table-column prop="action" label="动作" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.action === 'PASS' ? 'success' : 'danger'">
                {{ scope.row.action }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="rule" label="命中规则"></el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import api from '@/api'

export default {
  name: 'MonitorDashboard',
  data() {
    return {
      // 仪表盘数据
      dashboardData: {
        totalRequests: 0,
        blockedRequests: 0,
        passedRequests: 0,
        avgResponseTime: 0
      },
      // 时间范围
      timeRange: '1h',
      // 自动刷新
      autoRefresh: true,
      refreshTimer: null,
      // 实时日志
      realtimeLogs: [],
      // 图表实例
      qpsChartInstance: null,
      responseTimeChartInstance: null,
      tenantRankingChartInstance: null,
      ruleHitChartInstance: null
    }
  },
  mounted() {
    this.initCharts()
    this.loadDashboardData()
    this.loadRealtimeLogs()
    this.startAutoRefresh()
  },
  beforeDestroy() {
    this.stopAutoRefresh()
    this.destroyCharts()
  },
  methods: {
    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        this.initQpsChart()
        this.initResponseTimeChart()
        this.initTenantRankingChart()
        this.initRuleHitChart()
      })
    },

    // 初始化QPS图表
    initQpsChart() {
      this.qpsChartInstance = echarts.init(this.$refs.qpsChart)
      const option = {
        title: {
          text: 'QPS监控',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          name: 'QPS'
        },
        series: [{
          name: 'QPS',
          type: 'line',
          data: [],
          smooth: true,
          lineStyle: {
            color: '#409EFF'
          }
        }]
      }
      this.qpsChartInstance.setOption(option)
    },

    // 初始化响应时间图表
    initResponseTimeChart() {
      this.responseTimeChartInstance = echarts.init(this.$refs.responseTimeChart)
      const option = {
        title: {
          text: '响应时间趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          name: '响应时间(ms)'
        },
        series: [{
          name: '响应时间',
          type: 'line',
          data: [],
          smooth: true,
          lineStyle: {
            color: '#67C23A'
          }
        }]
      }
      this.responseTimeChartInstance.setOption(option)
    },

    // 初始化租户排行图表
    initTenantRankingChart() {
      this.tenantRankingChartInstance = echarts.init(this.$refs.tenantRankingChart)
      const option = {
        title: {
          text: '租户流量排行',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: []
        },
        series: [{
          name: '请求数',
          type: 'bar',
          data: [],
          itemStyle: {
            color: '#E6A23C'
          }
        }]
      }
      this.tenantRankingChartInstance.setOption(option)
    },

    // 初始化规则命中图表
    initRuleHitChart() {
      this.ruleHitChartInstance = echarts.init(this.$refs.ruleHitChart)
      const option = {
        title: {
          text: '规则命中统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [{
          name: '规则命中',
          type: 'pie',
          radius: '50%',
          data: [],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      this.ruleHitChartInstance.setOption(option)
    },

    // 加载仪表盘数据
    async loadDashboardData() {
      try {
        const response = await api.monitor.getDashboard()
        this.dashboardData = response.data || {}
        this.updateCharts()
      } catch (error) {
        console.error('加载仪表盘数据失败:', error)
        this.$message.error('加载仪表盘数据失败')
      }
    },

    // 更新图表数据
    async updateCharts() {
      try {
        // 更新QPS图表
        const qpsData = await api.monitor.getRealtime(null, null, this.timeRange)
        if (qpsData.data) {
          this.updateQpsChart(qpsData.data)
        }

        // 更新响应时间图表
        const rtData = await api.monitor.getTrend({ type: 'response_time', timeRange: this.timeRange })
        if (rtData.data) {
          this.updateResponseTimeChart(rtData.data)
        }

        // 更新租户排行图表
        const rankingData = await api.monitor.getQpsRanking({ limit: 10 })
        if (rankingData.data) {
          this.updateTenantRankingChart(rankingData.data)
        }

        // 更新规则命中图表
        const ruleHitData = await api.monitor.getStatistics({ type: 'rule_hit' })
        if (ruleHitData.data) {
          this.updateRuleHitChart(ruleHitData.data)
        }
      } catch (error) {
        console.error('更新图表数据失败:', error)
      }
    },

    // 更新QPS图表
    updateQpsChart(data) {
      const option = {
        xAxis: {
          data: data.map(item => this.formatTime(item.timestamp))
        },
        series: [{
          data: data.map(item => item.qps)
        }]
      }
      this.qpsChartInstance.setOption(option)
    },

    // 更新响应时间图表
    updateResponseTimeChart(data) {
      const option = {
        xAxis: {
          data: data.map(item => this.formatTime(item.timestamp))
        },
        series: [{
          data: data.map(item => item.responseTime)
        }]
      }
      this.responseTimeChartInstance.setOption(option)
    },

    // 更新租户排行图表
    updateTenantRankingChart(data) {
      const option = {
        yAxis: {
          data: data.map(item => item.tenantId)
        },
        series: [{
          data: data.map(item => item.requestCount)
        }]
      }
      this.tenantRankingChartInstance.setOption(option)
    },

    // 更新规则命中图表
    updateRuleHitChart(data) {
      const option = {
        series: [{
          data: data.map(item => ({
            name: item.ruleName,
            value: item.hitCount
          }))
        }]
      }
      this.ruleHitChartInstance.setOption(option)
    },

    // 设置时间范围
    setTimeRange(range) {
      this.timeRange = range
      this.updateCharts()
    },

    // 加载实时日志
    async loadRealtimeLogs() {
      try {
        const response = await api.monitor.getRealtime(null, null, '5m')
        this.realtimeLogs = response.data || []
      } catch (error) {
        console.error('加载实时日志失败:', error)
      }
    },

    // 切换自动刷新
    toggleAutoRefresh(enabled) {
      if (enabled) {
        this.startAutoRefresh()
      } else {
        this.stopAutoRefresh()
      }
    },

    // 开始自动刷新
    startAutoRefresh() {
      if (this.autoRefresh) {
        this.refreshTimer = setInterval(() => {
          this.loadDashboardData()
          this.loadRealtimeLogs()
        }, 30000) // 30秒刷新一次
      }
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 销毁图表
    destroyCharts() {
      if (this.qpsChartInstance) {
        this.qpsChartInstance.dispose()
      }
      if (this.responseTimeChartInstance) {
        this.responseTimeChartInstance.dispose()
      }
      if (this.tenantRankingChartInstance) {
        this.tenantRankingChartInstance.dispose()
      }
      if (this.ruleHitChartInstance) {
        this.ruleHitChartInstance.dispose()
      }
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return '-'
      // 统一使用 YYYY-MM-DD HH:mm:ss 格式
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
     }
  }
}
</script>

<style scoped>
.monitor-dashboard {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 500;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 30px;
}

.stats-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  font-size: 24px;
}

.stats-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stats-info p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.charts-section {
  margin-bottom: 30px;
}

.realtime-logs {
  margin-top: 20px;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-button-group {
  margin-left: 10px;
}
</style>