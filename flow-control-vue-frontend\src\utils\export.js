import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

/**
 * 导出数据到Excel
 * @param {Array} data - 要导出的数据数组
 * @param {String} filename - 文件名（不含扩展名）
 * @param {String} sheetName - 工作表名称
 */
export function exportToExcel(data, filename = 'export', sheetName = 'Sheet1') {
  try {
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    
    // 将数据转换为工作表
    const ws = XLSX.utils.json_to_sheet(data)
    
    // 设置列宽
    const colWidths = []
    if (data.length > 0) {
      Object.keys(data[0]).forEach((key, index) => {
        const maxLength = Math.max(
          key.length,
          ...data.map(row => String(row[key] || '').length)
        )
        colWidths[index] = { wch: Math.min(maxLength + 2, 50) }
      })
    }
    ws['!cols'] = colWidths
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, sheetName)
    
    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    saveAs(blob, `${filename}.xlsx`)
    
    return true
  } catch (error) {
    console.error('导出Excel失败:', error)
    return false
  }
}

/**
 * 导出监控数据到Excel
 * @param {Object} monitorData - 监控数据对象
 * @param {String} filename - 文件名
 */
export function exportMonitorDataToExcel(monitorData, filename = 'monitor_data') {
  try {
    const wb = XLSX.utils.book_new()
    
    // 实时指标数据
    if (monitorData.realTimeData) {
      const realTimeSheet = [
        {
          '指标名称': 'QPS',
          '当前值': monitorData.realTimeData.qps,
          '单位': '次/秒',
          '更新时间': new Date().toLocaleString()
        },
        {
          '指标名称': '平均响应时间',
          '当前值': monitorData.realTimeData.rt,
          '单位': '毫秒',
          '更新时间': new Date().toLocaleString()
        },
        {
          '指标名称': '成功率',
          '当前值': monitorData.realTimeData.successRate.toFixed(2),
          '单位': '%',
          '更新时间': new Date().toLocaleString()
        },
        {
          '指标名称': '错误率',
          '当前值': monitorData.realTimeData.errorRate.toFixed(2),
          '单位': '%',
          '更新时间': new Date().toLocaleString()
        }
      ]
      const ws1 = XLSX.utils.json_to_sheet(realTimeSheet)
      XLSX.utils.book_append_sheet(wb, ws1, '实时指标')
    }
    
    // 历史数据
    if (monitorData.historicalData && monitorData.historicalData.length > 0) {
      const historicalSheet = monitorData.historicalData.map(item => ({
        '时间': new Date(item.timestamp).toLocaleString(),
        'QPS': item.qps,
        '响应时间(ms)': item.rt,
        '成功率(%)': item.successRate.toFixed(2),
        '错误率(%)': item.errorRate.toFixed(2)
      }))
      const ws2 = XLSX.utils.json_to_sheet(historicalSheet)
      XLSX.utils.book_append_sheet(wb, ws2, '历史数据')
    }
    
    // 告警数据
    if (monitorData.alerts && monitorData.alerts.length > 0) {
      const alertsSheet = monitorData.alerts.map(alert => ({
        '告警级别': alert.level,
        '告警信息': alert.message,
        '来源': alert.source,
        '时间': new Date(alert.time).toLocaleString()
      }))
      const ws3 = XLSX.utils.json_to_sheet(alertsSheet)
      XLSX.utils.book_append_sheet(wb, ws3, '告警记录')
    }
    
    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    saveAs(blob, `${filename}_${new Date().toISOString().slice(0, 10)}.xlsx`)
    
    return true
  } catch (error) {
    console.error('导出监控数据失败:', error)
    return false
  }
}

/**
 * 导出日志数据到Excel
 * @param {Array} logs - 日志数据数组
 * @param {String} filename - 文件名
 */
export function exportLogsToExcel(logs, filename = 'system_logs') {
  try {
    const logsData = logs.map(log => ({
      '时间': new Date(log.timestamp).toLocaleString(),
      'IP地址': log.ip,
      '请求方法': log.method,
      '请求路径': log.path,
      '状态码': log.status,
      '响应时间(ms)': log.responseTime
    }))
    
    return exportToExcel(logsData, filename, '访问日志')
  } catch (error) {
    console.error('导出日志失败:', error)
    return false
  }
}

/**
 * 导出系统日志到Excel
 * @param {Array} systemLogs - 系统日志数组
 * @param {String} filename - 文件名
 */
export function exportSystemLogsToExcel(systemLogs, filename = 'system_logs') {
  try {
    const logsData = systemLogs.map(log => ({
      '时间': new Date(log.timestamp).toLocaleString(),
      '日志级别': log.level,
      '模块': log.logger,
      '消息': log.message
    }))
    
    return exportToExcel(logsData, filename, '系统日志')
  } catch (error) {
    console.error('导出系统日志失败:', error)
    return false
  }
}

/**
 * 导出租户流量规则到Excel
 * @param {Array} rules - 租户流量规则数组
 * @param {String} filename - 文件名
 */
export function exportTenantFlowRulesToExcel(rules, filename = 'tenant_flow_rules') {
  try {
    const rulesData = rules.map(rule => ({
      '规则名称': rule.name,
      '资源名称': rule.resource,
      '限流阈值': rule.count,
      '阈值类型': rule.grade === 1 ? 'QPS' : 'RT',
      '流控模式': rule.strategy === 0 ? '直接' : rule.strategy === 1 ? '关联' : '链路',
      '流控效果': rule.controlBehavior === 0 ? '快速失败' : rule.controlBehavior === 1 ? 'Warm Up' : '排队等待',
      '状态': rule.enabled ? '启用' : '禁用',
      '创建时间': new Date(rule.createTime).toLocaleString(),
      '更新时间': new Date(rule.updateTime).toLocaleString()
    }))
    
    return exportToExcel(rulesData, filename, '租户流量规则')
  } catch (error) {
    console.error('导出租户流量规则失败:', error)
    return false
  }
}

/**
 * 导出IP流量规则到Excel
 * @param {Array} ipRules - IP流量规则数组
 * @param {String} filename - 文件名
 */
export function exportIpFlowRulesToExcel(ipRules, filename = 'ip_flow_rules') {
  try {
    const rulesData = ipRules.map(rule => ({
      'IP地址/CIDR': rule.ip,
      '规则类型': rule.type === 'whitelist' ? '白名单' : '黑名单',
      '描述': rule.description,
      '状态': rule.enabled ? '启用' : '禁用',
      '创建时间': new Date(rule.createTime).toLocaleString(),
      '更新时间': new Date(rule.updateTime).toLocaleString()
    }))
    
    return exportToExcel(rulesData, filename, 'IP流量规则')
  } catch (error) {
    console.error('导出IP流量规则失败:', error)
    return false
  }
}

/**
 * 导出数据到CSV
 * @param {Array} data - 要导出的数据数组
 * @param {String} filename - 文件名（不含扩展名）
 */
export function exportToCSV(data, filename = 'export') {
  try {
    if (!data || data.length === 0) {
      throw new Error('没有数据可导出')
    }
    
    // 获取表头
    const headers = Object.keys(data[0])
    
    // 构建CSV内容
    let csvContent = headers.join(',') + '\n'
    
    data.forEach(row => {
      const values = headers.map(header => {
        const value = row[header]
        // 处理包含逗号或引号的值
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value
      })
      csvContent += values.join(',') + '\n'
    })
    
    // 添加BOM以支持中文
    const BOM = '\uFEFF'
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8' })
    saveAs(blob, `${filename}.csv`)
    
    return true
  } catch (error) {
    console.error('导出CSV失败:', error)
    return false
  }
}

/**
 * 导出数据到JSON
 * @param {Array|Object} data - 要导出的数据
 * @param {String} filename - 文件名（不含扩展名）
 */
export function exportToJSON(data, filename = 'export') {
  try {
    const jsonContent = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8' })
    saveAs(blob, `${filename}.json`)
    
    return true
  } catch (error) {
    console.error('导出JSON失败:', error)
    return false
  }
}

/**
 * 批量导出多个数据集
 * @param {Array} datasets - 数据集数组，每个元素包含 {data, filename, sheetName}
 * @param {String} filename - 主文件名
 */
export function exportMultipleDatasets(datasets, filename = 'export') {
  try {
    const wb = XLSX.utils.book_new()
    
    datasets.forEach(dataset => {
      if (dataset.data && dataset.data.length > 0) {
        const ws = XLSX.utils.json_to_sheet(dataset.data)
        XLSX.utils.book_append_sheet(wb, ws, dataset.sheetName || 'Sheet')
      }
    })
    
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    saveAs(blob, `${filename}.xlsx`)
    
    return true
  } catch (error) {
    console.error('批量导出失败:', error)
    return false
  }
}