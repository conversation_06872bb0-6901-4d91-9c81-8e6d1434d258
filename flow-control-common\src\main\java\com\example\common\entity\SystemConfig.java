package com.example.common.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

/**
 * 系统配置实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@TableName("system_config")
public class SystemConfig {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 配置键
     */
    @TableField("config_key")
    private String configKey;
    
    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;
    
    /**
     * 配置类型
     */
    @TableField("config_type")
    private String configType;
    
    /**
     * 配置描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;
    
    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否系统配置：0-否，1-是
     */
    @TableField("is_system")
    private Integer isSystem;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 版本号
     */
    @TableField("version")
    private Integer version;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Integer isSystem) {
        this.isSystem = isSystem;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SystemConfig that = (SystemConfig) o;
        return java.util.Objects.equals(id, that.id) &&
                java.util.Objects.equals(configKey, that.configKey) &&
                java.util.Objects.equals(configValue, that.configValue) &&
                java.util.Objects.equals(configType, that.configType) &&
                java.util.Objects.equals(description, that.description) &&
                java.util.Objects.equals(createTime, that.createTime) &&
                java.util.Objects.equals(updateTime, that.updateTime) &&
                java.util.Objects.equals(createBy, that.createBy) &&
                java.util.Objects.equals(updateBy, that.updateBy) &&
                java.util.Objects.equals(tenantId, that.tenantId) &&
                java.util.Objects.equals(status, that.status) &&
                java.util.Objects.equals(isSystem, that.isSystem) &&
                java.util.Objects.equals(sortOrder, that.sortOrder) &&
                java.util.Objects.equals(remark, that.remark) &&
                java.util.Objects.equals(deleted, that.deleted) &&
                java.util.Objects.equals(version, that.version);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(id, configKey, configValue, configType, description, createTime, updateTime, createBy, updateBy, tenantId, status, isSystem, sortOrder, remark, deleted, version);
    }

    @Override
    public String toString() {
        return "SystemConfig{" +
                "id=" + id +
                ", configKey='" + configKey + '\'' +
                ", configValue='" + configValue + '\'' +
                ", configType='" + configType + '\'' +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", status=" + status +
                ", isSystem=" + isSystem +
                ", sortOrder=" + sortOrder +
                ", remark='" + remark + '\'' +
                ", deleted=" + deleted +
                ", version=" + version +
                '}';
    }
}