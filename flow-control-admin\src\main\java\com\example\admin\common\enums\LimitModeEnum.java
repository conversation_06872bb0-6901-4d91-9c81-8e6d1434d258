package com.example.admin.common.enums;

/**
 * 限流模式枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public enum LimitModeEnum {
    
    /**
     * QPS限流
     */
    QPS(0, "QPS限流", "基于每秒查询率进行限流"),
    
    /**
     * 线程数限流
     */
    THREAD(1, "线程数限流", "基于并发线程数进行限流");
    
    /**
     * 模式值
     */
    private final Integer value;
    
    /**
     * 模式名称
     */
    private final String name;
    
    /**
     * 模式描述
     */
    private final String description;
    
    LimitModeEnum(Integer value, String name, String description) {
        this.value = value;
        this.name = name;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 根据值获取枚举
     * 
     * @param value 值
     * @return 枚举
     */
    public static LimitModeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (LimitModeEnum mode : values()) {
            if (mode.value.equals(value)) {
                return mode;
            }
        }
        return null;
    }
    
    /**
     * 检查值是否有效
     * 
     * @param value 值
     * @return 是否有效
     */
    public static boolean isValid(Integer value) {
        return getByValue(value) != null;
    }
}