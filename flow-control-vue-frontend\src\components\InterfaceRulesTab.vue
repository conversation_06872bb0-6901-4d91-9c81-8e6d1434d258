<template>
	<div class="interface-rules-tab">
		<!-- 操作栏 -->
		<div class="operation-bar">
			<el-button type="primary" @click="$emit('add-rule')">
				<i class="el-icon-plus"></i>
				新增接口规则
			</el-button>
			<el-button @click="$emit('load-rules')">
				<i class="el-icon-refresh"></i>
				刷新
			</el-button>
		</div>

		<!-- 接口规则表格 -->
		<el-table
			:data="rules"
			v-loading="loading"
			border
			style="width: 100%"
			height="600"
		>
			<el-table-column prop="ruleName" label="规则名称" width="150" />
			<el-table-column prop="tenantId" label="租户ID" width="120" />
			<el-table-column prop="resourceName" label="资源名称" width="200" show-overflow-tooltip />
			<el-table-column label="限流模式" width="100">
				<template slot-scope="scope">
					<el-tag :type="scope.row.limitMode === 0 ? 'primary' : 'success'">
						{{ scope.row.limitMode === 0 ? 'QPS' : '并发数' }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="threshold" label="阈值" width="80" />
			<el-table-column label="流控行为" width="100">
				<template slot-scope="scope">
					<span>{{ getBehaviorText(scope.row.behavior) }}</span>
				</template>
			</el-table-column>
			<el-table-column label="预热时长" width="100">
				<template slot-scope="scope">
					<span v-if="scope.row.behavior === 1 && scope.row.warmUpPeriod">
						{{ scope.row.warmUpPeriod }}s
					</span>
					<span v-else>-</span>
				</template>
			</el-table-column>
			<el-table-column label="排队超时" width="100">
				<template slot-scope="scope">
					<span v-if="scope.row.behavior === 2 && scope.row.queueTimeout">
						{{ scope.row.queueTimeout }}ms
					</span>
					<span v-else>-</span>
				</template>
			</el-table-column>
			<el-table-column label="状态" width="80">
				<template slot-scope="scope">
					<el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
						{{ scope.row.status === 1 ? '启用' : '禁用' }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="createTime" label="创建时间" width="160">
				<template slot-scope="scope">
					{{ formatDate(scope.row.createTime) }}
				</template>
			</el-table-column>
			<el-table-column label="操作" width="280" min-width="280">
				<template slot-scope="scope">
					<div class="action-buttons">
						<el-button
							type="text"
							size="small"
							@click="$emit('edit-rule', scope.row)"
						>
							编辑
						</el-button>
						<el-button
							type="text"
							size="small"
							class="delete-btn"
							@click="$emit('delete-rule', scope.row)"
						>
							删除
						</el-button>
					</div>
				</template>
			</el-table-column>
		</el-table>

		<!-- 分页 -->
		<div class="pagination-wrapper">
			<el-pagination
				@size-change="$emit('size-change', $event)"
				@current-change="$emit('current-change', $event)"
				:current-page="pagination.current"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="pagination.size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="pagination.total"
			>
			</el-pagination>
		</div>
	</div>
</template>

<script>
export default {
	name: 'InterfaceRulesTab',
	props: {
		rules: {
			type: Array,
			default: () => [],
		},
		loading: {
			type: Boolean,
			default: false,
		},
		pagination: {
			type: Object,
			default: () => ({
				current: 1,
				size: 20,
				total: 0,
			}),
		},
	},
	methods: {
		formatDate(dateString) {
			if (!dateString) return '-';
			const date = new Date(dateString);
			return date.toLocaleString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit',
			});
		},

		getBehaviorText(behavior) {
			const behaviorMap = {
				0: '快速失败',
				1: '预热',
				2: '排队等待',
			};
			return behaviorMap[behavior] || '未知';
		},
	},
};
</script>

<style scoped>
.interface-rules-tab {
	padding: 0;
}

.operation-bar {
	margin-bottom: 20px;
	display: flex;
	gap: 10px;
}

.action-buttons {
	display: flex;
	gap: 8px;
	align-items: center;
	justify-content: flex-start;
	flex-wrap: nowrap;
}

.delete-btn {
	color: #f56c6c;
}

.delete-btn:hover {
	color: #f56c6c;
}

.pagination-wrapper {
	margin-top: 20px;
	display: flex;
	justify-content: center;
}
</style>