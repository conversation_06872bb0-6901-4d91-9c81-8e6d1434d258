package com.example.admin.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 租户规则VO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Schema(description = "租户规则VO")
public class TenantFlowRuleVO {
    
    @Schema(description = "规则ID")
    private Long id;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "规则名称")
    private String ruleName;



    @Schema(description = "限流阈值类型：0-线程数，1-QPS")
    private Integer grade;

    @Schema(description = "限流类型名称")
    private String gradeName;

    @Schema(description = "限流阈值")
    private Double count;

    @Schema(description = "流控针对的调用来源策略：0-直接，1-关联，2-链路")
    private Integer strategy;

    @Schema(description = "关联资源")
    private String refResource;

    @Schema(description = "流控效果：0-快速失败，1-Warm Up，2-排队等待，3-Warm Up + 排队等待")
    private Integer controlBehavior;

    @Schema(description = "流控效果名称")
    private String controlBehaviorName;

    @Schema(description = "Warm Up预热时间（秒）")
    private Integer warmUpPeriodSec;

    @Schema(description = "排队等待超时时间（毫秒）")
    private Integer maxQueueingTimeMs;

    @Schema(description = "是否集群模式")
    private Boolean clusterMode;

    @Schema(description = "集群配置信息")
    private String clusterConfig;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "是否启用：0-禁用，1-启用")
    private Integer enabled;

    @Schema(description = "启用状态名称")
    private String enabledName;

    @Schema(description = "生效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "生效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "规则描述")
    private String description;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "是否有效（当前时间在生效时间范围内且启用）")
    private Boolean isValid;

    @Schema(description = "是否即将过期")
    private Boolean isExpiring;

    @Schema(description = "剩余有效时间（小时）")
    private Long remainingHours;
    
    // Constructors
    public TenantFlowRuleVO() {}
    
    public TenantFlowRuleVO(Long id, String tenantId, String ruleName, Integer grade, Double count) {
        this.id = id;
        this.tenantId = tenantId;
        this.ruleName = ruleName;
        this.grade = grade;
        this.count = count;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    public String getTenantName() {
        return tenantName;
    }
    
    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }
    
    public String getRuleName() {
        return ruleName;
    }
    
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }
    

    
    public Integer getGrade() {
        return grade;
    }
    
    public void setGrade(Integer grade) {
        this.grade = grade;
        // 自动设置限流类型名称
        this.gradeName = getGradeDesc(grade);
    }
    
    public String getGradeName() {
        return gradeName;
    }
    
    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }
    
    public Double getCount() {
        return count;
    }
    
    public void setCount(Double count) {
        this.count = count;
    }
    
    public Integer getStrategy() {
        return strategy;
    }
    
    public void setStrategy(Integer strategy) {
        this.strategy = strategy;
    }
    
    public String getRefResource() {
        return refResource;
    }
    
    public void setRefResource(String refResource) {
        this.refResource = refResource;
    }
    
    public Integer getControlBehavior() {
        return controlBehavior;
    }
    
    public void setControlBehavior(Integer controlBehavior) {
        this.controlBehavior = controlBehavior;
        // 自动设置流控效果名称
        this.controlBehaviorName = getControlBehaviorDesc(controlBehavior);
    }
    
    public String getControlBehaviorName() {
        return controlBehaviorName;
    }
    
    public void setControlBehaviorName(String controlBehaviorName) {
        this.controlBehaviorName = controlBehaviorName;
    }
    
    public Integer getWarmUpPeriodSec() {
        return warmUpPeriodSec;
    }
    
    public void setWarmUpPeriodSec(Integer warmUpPeriodSec) {
        this.warmUpPeriodSec = warmUpPeriodSec;
    }
    
    public Integer getMaxQueueingTimeMs() {
        return maxQueueingTimeMs;
    }
    
    public void setMaxQueueingTimeMs(Integer maxQueueingTimeMs) {
        this.maxQueueingTimeMs = maxQueueingTimeMs;
    }
    
    public Boolean getClusterMode() {
        return clusterMode;
    }
    
    public void setClusterMode(Boolean clusterMode) {
        this.clusterMode = clusterMode;
    }
    
    public String getClusterConfig() {
        return clusterConfig;
    }
    
    public void setClusterConfig(String clusterConfig) {
        this.clusterConfig = clusterConfig;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public Integer getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
        // 自动设置启用状态名称
        this.enabledName = getEnabledDesc(enabled);
    }
    
    public String getEnabledName() {
        return enabledName;
    }
    
    public void setEnabledName(String enabledName) {
        this.enabledName = enabledName;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getCreateBy() {
        return createBy;
    }
    
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    
    public String getUpdateBy() {
        return updateBy;
    }
    
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    
    public Boolean getIsValid() {
        return isValid;
    }
    
    public void setIsValid(Boolean isValid) {
        this.isValid = isValid;
    }
    
    public Boolean getIsExpiring() {
        return isExpiring;
    }
    
    public void setIsExpiring(Boolean isExpiring) {
        this.isExpiring = isExpiring;
    }
    
    public Long getRemainingHours() {
        return remainingHours;
    }
    
    public void setRemainingHours(Long remainingHours) {
        this.remainingHours = remainingHours;
    }
    
    // Utility methods
    
    /**
     * 获取流控效果描述
     */
    private String getControlBehaviorDesc(Integer controlBehavior) {
        if (controlBehavior == null) {
            return "未知";
        }
        switch (controlBehavior) {
            case 0:
                return "快速失败";
            case 1:
                return "Warm Up";
            case 2:
                return "排队等待";
            case 3:
                return "Warm Up + 排队等待";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取限流类型描述
     */
    private String getGradeDesc(Integer grade) {
        if (grade == null) {
            return "未知";
        }
        switch (grade) {
            case 0:
                return "线程数";
            case 1:
                return "QPS";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取启用状态描述
     */
    private String getEnabledDesc(Integer enabled) {
        if (enabled == null) {
            return "未知";
        }
        return enabled == 1 ? "启用" : "禁用";
    }
    
    /**
     * 检查规则是否当前有效
     */
    public boolean isCurrentlyValid() {
        if (enabled == null || enabled != 1) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 检查开始时间
        if (startTime != null && now.isBefore(startTime)) {
            return false;
        }
        
        // 检查结束时间
        if (endTime != null && now.isAfter(endTime)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查规则是否即将过期
     */
    public boolean isExpiringWithin(int hours) {
        if (endTime == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threshold = now.plusHours(hours);
        
        return endTime.isBefore(threshold) && endTime.isAfter(now);
    }
    
    /**
     * 计算剩余有效时间（小时）
     */
    public long calculateRemainingHours() {
        if (endTime == null) {
            return -1; // 表示永久有效
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(endTime)) {
            return 0; // 已过期
        }
        
        return java.time.Duration.between(now, endTime).toHours();
    }
    
    /**
     * 获取流控效果配置描述
     */
    public String getControlBehaviorConfig() {
        if (controlBehavior == null) {
            return "未配置";
        }
        
        StringBuilder config = new StringBuilder(getControlBehaviorDesc(controlBehavior));
        
        switch (controlBehavior) {
            case 1: // Warm Up
                if (warmUpPeriodSec != null) {
                    config.append("(").append(warmUpPeriodSec).append("秒)");
                }
                break;
            case 2: // 排队等待
                if (maxQueueingTimeMs != null) {
                    config.append("(").append(maxQueueingTimeMs).append("毫秒)");
                }
                break;
            case 3: // Warm Up + 排队等待
                if (warmUpPeriodSec != null || maxQueueingTimeMs != null) {
                    config.append("(");
                    if (warmUpPeriodSec != null) {
                        config.append("预热:").append(warmUpPeriodSec).append("秒");
                    }
                    if (maxQueueingTimeMs != null) {
                        if (warmUpPeriodSec != null) {
                            config.append(", ");
                        }
                        config.append("排队:").append(maxQueueingTimeMs).append("毫秒");
                    }
                    config.append(")");
                }
                break;
        }
        
        return config.toString();
    }
    
    /**
     * 获取时间范围描述
     */
    public String getTimeRangeDesc() {
        if (startTime == null && endTime == null) {
            return "永久有效";
        }
        
        StringBuilder desc = new StringBuilder();
        if (startTime != null) {
            desc.append("从 ").append(startTime.toString().replace("T", " "));
        }
        if (endTime != null) {
            if (desc.length() > 0) {
                desc.append(" 到 ");
            } else {
                desc.append("到 ");
            }
            desc.append(endTime.toString().replace("T", " "));
        }
        
        return desc.toString();
    }
    
    /**
     * 获取流控配置描述
     */
    public String getFlowConfig() {
        StringBuilder config = new StringBuilder();
        
        // 限流类型和阈值
        if (grade != null && count != null) {
            config.append(getGradeDesc(grade)).append(": ").append(count);
        }
        
        // 调用来源策略
        if (strategy != null && strategy > 0) {
            if (config.length() > 0) {
                config.append(", ");
            }
            String strategyDesc = "";
            switch (strategy) {
                case 1:
                    strategyDesc = "关联";
                    break;
                case 2:
                    strategyDesc = "链路";
                    break;
                default:
                    strategyDesc = "直接";
            }
            config.append("策略: ").append(strategyDesc);
            
            if (refResource != null && !refResource.isEmpty()) {
                config.append("(").append(refResource).append(")");
            }
        }
        
        // 集群模式
        if (clusterMode != null && clusterMode) {
            if (config.length() > 0) {
                config.append(", ");
            }
            config.append("集群模式");
        }
        
        return config.toString();
    }
    
    @Override
    public String toString() {
        return "TenantFlowRuleVO{" +
                "id=" + id +
                ", tenantId='" + tenantId + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", ruleName='" + ruleName + '\'' +

                ", grade=" + grade +
                ", gradeName='" + gradeName + '\'' +
                ", count=" + count +
                ", strategy=" + strategy +
                ", refResource='" + refResource + '\'' +
                ", controlBehavior=" + controlBehavior +
                ", controlBehaviorName='" + controlBehaviorName + '\'' +
                ", warmUpPeriodSec=" + warmUpPeriodSec +
                ", maxQueueingTimeMs=" + maxQueueingTimeMs +
                ", clusterMode=" + clusterMode +
                ", clusterConfig='" + clusterConfig + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                ", enabledName='" + enabledName + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", isValid=" + isValid +
                ", isExpiring=" + isExpiring +
                ", remainingHours=" + remainingHours +
                '}';
    }
}