const state = {
	rules: [],
	currentRule: null,
	loading: false,
	searchKeyword: '',
	filters: {
		status: '',
		thresholdType: '',
		controlBehavior: ''
	},
	pagination: {
		current: 1,
		pageSize: 10,
		total: 0
	},
	selectedRuleIds: []
}

const mutations = {
	SET_RULES (state, { rules, total }) {
		state.rules = rules
		state.pagination.total = total
	},
	SET_CURRENT_RULE (state, rule) {
		state.currentRule = rule
	},
	ADD_RULE (state, rule) {
		state.rules.unshift(rule)
		state.pagination.total += 1
	},
	UPDATE_RULE (state, updatedRule) {
		const index = state.rules.findIndex(rule => rule.id === updatedRule.id)
		if (index !== -1) {
			state.rules.splice(index, 1, updatedRule)
		}
	},
	DELETE_RULE (state, ruleId) {
		state.rules = state.rules.filter(rule => rule.id !== ruleId)
		state.pagination.total -= 1
	},
	BATCH_DELETE_RULES (state, ruleIds) {
		state.rules = state.rules.filter(rule => !ruleIds.includes(rule.id))
		state.pagination.total -= ruleIds.length
		state.selectedRuleIds = []
	},
	SET_LOADING (state, loading) {
		state.loading = loading
	},
	SET_SEARCH_KEYWORD (state, keyword) {
		state.searchKeyword = keyword
	},
	SET_FILTERS (state, filters) {
		state.filters = { ...state.filters, ...filters }
	},
	SET_PAGINATION (state, pagination) {
		state.pagination = { ...state.pagination, ...pagination }
	},
	SET_SELECTED_RULE_IDS (state, ids) {
		state.selectedRuleIds = ids
	},
	TOGGLE_RULE_SELECTION (state, ruleId) {
		const index = state.selectedRuleIds.indexOf(ruleId)
		if (index > -1) {
			state.selectedRuleIds.splice(index, 1)
		} else {
			state.selectedRuleIds.push(ruleId)
		}
	},
	CLEAR_SELECTION (state) {
		state.selectedRuleIds = []
	}
}

const actions = {
	async fetchRules ({ commit, state }) {
		commit('SET_LOADING', true)
		try {
			const { api } = await import('@/api')
			const params = {
				page: state.pagination.current,
				pageSize: state.pagination.pageSize,
				keyword: state.searchKeyword,
				...state.filters
			}
			const response = await api.tenantFlowRules.getList(params)
			// 转换后端数据格式到前端期望格式
			const transformedRules = (response.data.records || []).map(rule => ({
				id: rule.id,
				name: rule.ruleName,
				resource: rule.resourceName,  // 前端期望的字段名
				resourceName: rule.resourceName,
				limitApp: rule.tenantName || '',  // 前端搜索过滤需要的字段
				tenantId: rule.tenantId,
				tenantName: rule.tenantName,
				limitMode: rule.limitMode,
				limitModeName: rule.limitModeName,
				grade: rule.limitMode,  // 阈值类型，前端期望的字段名
				count: rule.threshold,  // 阈值，前端期望的字段名
				threshold: rule.threshold,
				strategy: rule.strategy,
				strategyName: rule.strategyName,
				relatedResource: rule.relatedResource,
				controlBehavior: rule.behavior,  // 前端期望的字段名
				behavior: rule.behavior,
				behaviorName: rule.behaviorName,
				warmUpPeriod: rule.warmUpPeriod,
				queueTimeout: rule.queueTimeout,
				clusterMode: rule.clusterMode,
				clusterModeName: rule.clusterModeName,
				status: rule.status,
				statusName: rule.statusName,
				priority: rule.priority,
				description: rule.description,
				createTime: rule.createTime,
				updateTime: rule.updateTime,
				enabled: rule.status === 1  // 将后端的status转换为前端的enabled布尔值
			}))
			commit('SET_RULES', {
				rules: transformedRules,
				total: response.data.total || 0
			})
		} catch (error) {
			console.error('获取流量规则失败:', error)
			commit('SET_RULES', { rules: [], total: 0 })
		} finally {
			commit('SET_LOADING', false)
		}
	},

	async createRule ({ commit, dispatch }, rule) {
		try {
			const { api } = await import('@/api')
			const response = await api.tenantFlowRules.create(rule)
			commit('ADD_RULE', response.data)
			return response.data
		} catch (error) {
			console.error('创建流量规则失败:', error)
			throw error
		}
	},

	async updateRule ({ commit }, rule) {
		try {
			const { api } = await import('@/api')
			const response = await api.tenantFlowRules.update(rule.id, rule)
			commit('UPDATE_RULE', response.data)
			return response.data
		} catch (error) {
			console.error('更新流量规则失败:', error)
			throw error
		}
	},

	async deleteRule ({ commit }, ruleId) {
		try {
			const { api } = await import('@/api')
			await api.tenantFlowRules.delete(ruleId)
			commit('DELETE_RULE', ruleId)
		} catch (error) {
			console.error('删除流量规则失败:', error)
			throw error
		}
	},

	async batchDeleteRules ({ commit, state }) {
		try {
			const { api } = await import('@/api')
			await api.tenantFlowRules.batchDelete(state.selectedRuleIds)
			commit('BATCH_DELETE_RULES', state.selectedRuleIds)
		} catch (error) {
			console.error('批量删除流量规则失败:', error)
			throw error
		}
	},

	async enableRule ({ commit }, ruleId) {
		try {
			const { api } = await import('@/api')
			const response = await api.tenantFlowRules.enable(ruleId)
			commit('UPDATE_RULE', response.data)
			return response.data
		} catch (error) {
			console.error('启用流量规则失败:', error)
			throw error
		}
	},

	async disableRule ({ commit }, ruleId) {
		try {
			const { api } = await import('@/api')
			const response = await api.tenantFlowRules.disable(ruleId)
			commit('UPDATE_RULE', response.data)
			return response.data
		} catch (error) {
			console.error('禁用流量规则失败:', error)
			throw error
		}
	},

	setSearchKeyword ({ commit, dispatch }, keyword) {
		commit('SET_SEARCH_KEYWORD', keyword)
		commit('SET_PAGINATION', { current: 1 })
		dispatch('fetchRules')
	},

	setFilters ({ commit, dispatch }, filters) {
		commit('SET_FILTERS', filters)
		commit('SET_PAGINATION', { current: 1 })
		dispatch('fetchRules')
	},

	setPagination ({ commit, dispatch }, pagination) {
		commit('SET_PAGINATION', pagination)
		dispatch('fetchRules')
	},

	setSelectedRuleIds ({ commit }, ids) {
		commit('SET_SELECTED_RULE_IDS', ids)
	},

	toggleRuleSelection ({ commit }, ruleId) {
		commit('TOGGLE_RULE_SELECTION', ruleId)
	},

	clearSelection ({ commit }) {
		commit('CLEAR_SELECTION')
	}
}

const getters = {
	allRules: state => state.rules,
	currentRule: state => state.currentRule,
	isLoading: state => state.loading,
	searchKeyword: state => state.searchKeyword,
	filters: state => state.filters,
	pagination: state => state.pagination,
	selectedRuleIds: state => state.selectedRuleIds,
	selectedRulesCount: state => state.selectedRuleIds.length,
	hasSelectedRules: state => state.selectedRuleIds.length > 0,
	filteredRules: state => {
		let filtered = state.rules

		// 搜索关键词过滤
		if (state.searchKeyword) {
			const keyword = state.searchKeyword.toLowerCase()
			filtered = filtered.filter(rule =>
				rule.resource?.toLowerCase().includes(keyword) ||
				rule.limitApp?.toLowerCase().includes(keyword)
			)
		}

		return filtered
	},
	enabledRulesCount: state => state.rules.filter(rule => rule.status === 'enabled').length,
	disabledRulesCount: state => state.rules.filter(rule => rule.status === 'disabled').length
}

export default {
	namespaced: true,
	state,
	mutations,
	actions,
	getters
}