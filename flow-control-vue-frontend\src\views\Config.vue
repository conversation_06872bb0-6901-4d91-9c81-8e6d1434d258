<template>
	<div class="config">
		<layout>
			<div class="config-content">
				<div class="page-header">
					<h1>系统配置</h1>
					<p>配置系统全局参数和告警设置</p>
				</div>

				<el-tabs v-model="activeTab" type="card">
					<!-- 全局参数配置 -->
					<el-tab-pane label="全局参数" name="global">
						<div class="config-section">
							<h3>流量控制参数</h3>
							<el-form
								:model="localGlobalConfig"
								:rules="computedGlobalRules"
								ref="globalForm"
								label-width="150px"
							>
								<el-row :gutter="20">
									<el-col :span="12">
										<el-form-item
											label="默认QPS阈值"
											prop="defaultQpsThreshold"
										>
											<el-input-number
												v-model="
													localGlobalConfig.defaultQpsThreshold
												"
												:min="1"
												:max="10000"
												placeholder="请输入默认QPS阈值"
											></el-input-number>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item
											label="默认响应时间阈值"
											prop="defaultRtThreshold"
										>
											<el-input-number
												v-model="
													localGlobalConfig.defaultRtThreshold
												"
												:min="1"
												:max="5000"
												placeholder="请输入响应时间阈值(ms)"
											></el-input-number>
										</el-form-item>
									</el-col>
								</el-row>

								<el-row :gutter="20">
									<el-col :span="12">
										<el-form-item
											label="统计窗口时间"
											prop="statisticIntervalMs"
										>
											<el-select
												v-model="
													localGlobalConfig.statisticIntervalMs
												"
												placeholder="请选择统计窗口时间"
											>
												<el-option
													label="1秒"
													:value="1000"
												></el-option>
												<el-option
													label="5秒"
													:value="5000"
												></el-option>
												<el-option
													label="10秒"
													:value="10000"
												></el-option>
												<el-option
													label="30秒"
													:value="30000"
												></el-option>
												<el-option
													label="1分钟"
													:value="60000"
												></el-option>
											</el-select>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item
											label="熔断恢复时间"
											prop="timeoutMs"
										>
											<el-input-number
												v-model="
													localGlobalConfig.timeoutMs
												"
												:min="1000"
												:max="300000"
												placeholder="请输入熔断恢复时间(ms)"
											></el-input-number>
										</el-form-item>
									</el-col>
								</el-row>

								<el-form-item
									label="启用预热模式"
									prop="warmUpEnabled"
								>
									<el-switch
										v-model="
											localGlobalConfig.warmUpEnabled
										"
									></el-switch>
									<span class="form-tip"
										>启用后系统将在启动时进行预热</span
									>
								</el-form-item>

								<el-form-item
									label="预热时间"
									prop="warmUpPeriodSec"
									v-if="localGlobalConfig.warmUpEnabled"
								>
									<el-input-number
										v-model="
											localGlobalConfig.warmUpPeriodSec
										"
										:min="10"
										:max="3600"
										placeholder="请输入预热时间(秒)"
									></el-input-number>
								</el-form-item>

								<el-form-item>
									<el-button
										type="primary"
										@click="handleSaveGlobalConfig"
										:loading="loading.saving"
										>保存配置</el-button
									>
									<el-button @click="resetGlobalConfig"
										>重置</el-button
									>
								</el-form-item>
							</el-form>
						</div>
					</el-tab-pane>

					<!-- 告警阈值设置 -->
					<el-tab-pane label="告警设置" name="alert">
						<div class="config-section">
							<h3>告警阈值配置</h3>
							<el-form
								:model="localAlertConfig"
								:rules="computedAlertRules"
								ref="alertForm"
								label-width="150px"
							>
								<el-row :gutter="20">
									<el-col :span="12">
										<el-form-item
											label="QPS告警阈值"
											prop="qpsWarningThreshold"
										>
											<el-input-number
												v-model="
													localAlertConfig.qpsWarningThreshold
												"
												:min="1"
												:max="10000"
												placeholder="QPS警告阈值"
											></el-input-number>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item
											label="QPS严重告警阈值"
											prop="qpsCriticalThreshold"
										>
											<el-input-number
												v-model="
													localAlertConfig.qpsCriticalThreshold
												"
												:min="1"
												:max="10000"
												placeholder="QPS严重告警阈值"
											></el-input-number>
										</el-form-item>
									</el-col>
								</el-row>

								<el-row :gutter="20">
									<el-col :span="12">
										<el-form-item
											label="响应时间告警阈值"
											prop="rtWarningThreshold"
										>
											<el-input-number
												v-model="
													localAlertConfig.rtWarningThreshold
												"
												:min="1"
												:max="5000"
												placeholder="响应时间警告阈值(ms)"
											></el-input-number>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item
											label="响应时间严重告警阈值"
											prop="rtCriticalThreshold"
										>
											<el-input-number
												v-model="
													localAlertConfig.rtCriticalThreshold
												"
												:min="1"
												:max="5000"
												placeholder="响应时间严重告警阈值(ms)"
											></el-input-number>
										</el-form-item>
									</el-col>
								</el-row>

								<el-row :gutter="20">
									<el-col :span="12">
										<el-form-item
											label="错误率告警阈值"
											prop="errorRateWarningThreshold"
										>
											<el-input-number
												v-model="
													localAlertConfig.errorRateWarningThreshold
												"
												:min="0"
												:max="100"
												:precision="2"
												placeholder="错误率警告阈值(%)"
											></el-input-number>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item
											label="错误率严重告警阈值"
											prop="errorRateCriticalThreshold"
										>
											<el-input-number
												v-model="
													localAlertConfig.errorRateCriticalThreshold
												"
												:min="0"
												:max="100"
												:precision="2"
												placeholder="错误率严重告警阈值(%)"
											></el-input-number>
										</el-form-item>
									</el-col>
								</el-row>

								<el-form-item>
									<el-button
										type="primary"
										@click="handleSaveAlertConfig"
										:loading="loading.saving"
										>保存配置</el-button
									>
									<el-button @click="resetAlertConfig"
										>重置</el-button
									>
								</el-form-item>
							</el-form>
						</div>
					</el-tab-pane>

					<!-- 通知方式配置 -->
					<el-tab-pane label="通知配置" name="notification">
						<div class="config-section">
							<h3>通知方式配置</h3>
							<el-form
								:model="localNotificationConfig"
								ref="notificationForm"
								label-width="150px"
							>
								<el-form-item label="启用邮件通知">
									<el-switch
										v-model="
											localNotificationConfig.emailEnabled
										"
									></el-switch>
								</el-form-item>

								<div
									v-if="localNotificationConfig.emailEnabled"
									class="notification-detail"
								>
									<el-form-item
										label="SMTP服务器"
										prop="smtpHost"
									>
										<el-input
											v-model="
												localNotificationConfig.smtpHost
											"
											placeholder="请输入SMTP服务器地址"
										></el-input>
									</el-form-item>

									<el-row :gutter="20">
										<el-col :span="12">
											<el-form-item
												label="SMTP端口"
												prop="smtpPort"
											>
												<el-input-number
													v-model="
														localNotificationConfig.smtpPort
													"
													:min="1"
													:max="65535"
												></el-input-number>
											</el-form-item>
										</el-col>
										<el-col :span="12">
											<el-form-item label="启用SSL">
												<el-switch
													v-model="
														localNotificationConfig.smtpSsl
													"
												></el-switch>
											</el-form-item>
										</el-col>
									</el-row>

									<el-form-item
										label="发送邮箱"
										prop="smtpUsername"
									>
										<el-input
											v-model="
												localNotificationConfig.smtpUsername
											"
											placeholder="请输入发送邮箱"
										></el-input>
									</el-form-item>

									<el-form-item
										label="邮箱密码"
										prop="smtpPassword"
									>
										<el-input
											v-model="
												localNotificationConfig.smtpPassword
											"
											type="password"
											placeholder="请输入邮箱密码"
										></el-input>
									</el-form-item>

									<el-form-item
										label="接收邮箱"
										prop="recipients"
									>
										<el-input
											v-model="
												localNotificationConfig.recipients
											"
											placeholder="多个邮箱用逗号分隔"
										></el-input>
									</el-form-item>
								</div>

								<el-form-item label="启用短信通知">
									<el-switch
										v-model="
											localNotificationConfig.smsEnabled
										"
									></el-switch>
								</el-form-item>

								<div
									v-if="localNotificationConfig.smsEnabled"
									class="notification-detail"
								>
									<el-form-item label="短信服务商">
										<el-select
											v-model="
												localNotificationConfig.smsProvider
											"
											placeholder="请选择短信服务商"
										>
											<el-option
												label="阿里云"
												value="aliyun"
											></el-option>
											<el-option
												label="腾讯云"
												value="tencent"
											></el-option>
											<el-option
												label="华为云"
												value="huawei"
											></el-option>
										</el-select>
									</el-form-item>

									<el-form-item label="AccessKey">
										<el-input
											v-model="
												localNotificationConfig.smsAccessKey
											"
											placeholder="请输入AccessKey"
										></el-input>
									</el-form-item>

									<el-form-item label="SecretKey">
										<el-input
											v-model="
												localNotificationConfig.smsSecretKey
											"
											type="password"
											placeholder="请输入SecretKey"
										></el-input>
									</el-form-item>

									<el-form-item label="短信模板ID">
										<el-input
											v-model="
												localNotificationConfig.smsTemplateId
											"
											placeholder="请输入短信模板ID"
										></el-input>
									</el-form-item>

									<el-form-item label="接收手机号">
										<el-input
											v-model="
												localNotificationConfig.phoneNumbers
											"
											placeholder="多个手机号用逗号分隔"
										></el-input>
									</el-form-item>
								</div>

								<el-form-item label="启用Webhook通知">
									<el-switch
										v-model="
											localNotificationConfig.webhookEnabled
										"
									></el-switch>
								</el-form-item>

								<div
									v-if="
										localNotificationConfig.webhookEnabled
									"
									class="notification-detail"
								>
									<el-form-item label="Webhook URL">
										<el-input
											v-model="
												localNotificationConfig.webhookUrl
											"
											placeholder="请输入Webhook URL"
										></el-input>
									</el-form-item>

									<el-form-item label="请求方法">
										<el-select
											v-model="
												localNotificationConfig.webhookMethod
											"
										>
											<el-option
												label="POST"
												value="POST"
											></el-option>
											<el-option
												label="PUT"
												value="PUT"
											></el-option>
										</el-select>
									</el-form-item>

									<el-form-item label="请求头">
										<el-input
											v-model="
												localNotificationConfig.webhookHeaders
											"
											type="textarea"
											:rows="3"
											placeholder='JSON格式，如：{"Content-Type": "application/json"}'
										></el-input>
									</el-form-item>
								</div>

								<el-form-item>
									<el-button
										type="primary"
										@click="handleSaveNotificationConfig"
										:loading="loading.saving"
										>保存配置</el-button
									>
									<el-button @click="testNotification"
										>测试通知</el-button
									>
									<el-button @click="resetNotificationConfig"
										>重置</el-button
									>
								</el-form-item>
							</el-form>
						</div>
					</el-tab-pane>

					<!-- 系统日志 -->
					<el-tab-pane label="系统日志" name="logs">
						<div class="config-section">
							<div class="logs-header">
								<h3>系统日志查看</h3>
								<div class="logs-controls">
									<el-select
										v-model="logLevel"
										placeholder="日志级别"
										size="small"
										style="width: 120px; margin-right: 10px"
									>
										<el-option
											label="全部"
											value=""
										></el-option>
										<el-option
											label="DEBUG"
											value="DEBUG"
										></el-option>
										<el-option
											label="INFO"
											value="INFO"
										></el-option>
										<el-option
											label="WARN"
											value="WARN"
										></el-option>
										<el-option
											label="ERROR"
											value="ERROR"
										></el-option>
									</el-select>

									<el-date-picker
										v-model="logDateRange"
										type="datetimerange"
										range-separator="至"
										start-placeholder="开始日期"
										end-placeholder="结束日期"
										size="small"
										style="margin-right: 10px"
									></el-date-picker>

									<el-button size="small" @click="searchLogs"
										>查询</el-button
									>
									<el-button
										size="small"
										@click="handleClearLogs"
										>清空</el-button
									>
									<el-button size="small" @click="exportLogs"
										>导出</el-button
									>
								</div>
							</div>

							<div class="logs-content">
								<el-table
									:data="systemLogs"
									style="width: 100%"
									max-height="400"
								>
									<el-table-column
										prop="timestamp"
										label="时间"
										width="180"
									>
										<template slot-scope="scope">
											{{
												formatTime(scope.row.timestamp)
											}}
										</template>
									</el-table-column>
									<el-table-column
										prop="level"
										label="级别"
										width="80"
									>
										<template slot-scope="scope">
											<el-tag
												:type="
													getLogLevelType(
														scope.row.level
													)
												"
												size="small"
											>
												{{ scope.row.level }}
											</el-tag>
										</template>
									</el-table-column>
									<el-table-column
										prop="logger"
										label="模块"
										width="150"
									></el-table-column>
									<el-table-column
										prop="message"
										label="消息"
										show-overflow-tooltip
									></el-table-column>
								</el-table>
							</div>
						</div>
					</el-tab-pane>

					<!-- Nacos配置管理 -->
					<el-tab-pane label="Nacos配置" name="nacos">
						<div class="config-section">
							<div class="nacos-header">
								<h3>Nacos配置中心管理</h3>
								<div class="nacos-controls">
									<el-button
										type="primary"
										icon="el-icon-connection"
										@click="testNacosConnection"
										:loading="connectionTesting"
										size="small"
									>
										测试连接
									</el-button>
									<el-button
										type="success"
										icon="el-icon-upload2"
										@click="publishAllRulesToNacos"
										:loading="publishLoading"
										size="small"
									>
										发布所有规则
									</el-button>
									<el-button
										type="info"
										icon="el-icon-refresh"
										@click="refreshConfigStatus"
										:loading="statusLoading"
										size="small"
									>
										刷新状态
									</el-button>
								</div>
							</div>

							<!-- 连接状态 -->
							<el-card class="status-card" shadow="never">
								<div slot="header" class="card-header">
									<span>连接状态</span>
								</div>
								<div class="status-info">
									<el-row :gutter="20">
										<el-col :span="8">
											<div class="status-item">
												<span class="status-label"
													>连接状态:</span
												>
												<el-tag
													:type="
														nacosStatus.connected
															? 'success'
															: 'danger'
													"
													size="small"
												>
													{{
														nacosStatus.connected
															? '已连接'
															: '未连接'
													}}
												</el-tag>
											</div>
										</el-col>
										<el-col :span="8">
											<div class="status-item">
												<span class="status-label"
													>服务地址:</span
												>
												<span class="status-value">{{
													nacosStatus.serverAddr ||
													'-'
												}}</span>
											</div>
										</el-col>
										<el-col :span="8">
											<div class="status-item">
												<span class="status-label"
													>最后测试:</span
												>
												<span class="status-value">{{
													formatTime(
														nacosStatus.testTime
													)
												}}</span>
											</div>
										</el-col>
									</el-row>
								</div>
							</el-card>

							<!-- 配置状态 -->
							<el-card class="config-card" shadow="never">
								<div slot="header" class="card-header">
									<span>配置状态</span>
								</div>
								<div class="config-info">
									<el-row :gutter="20">
										<el-col :span="12">
											<div class="config-item">
												<h4>流量规则配置</h4>
												<div class="config-details">
													<p>
														<span
															class="detail-label"
															>规则数量:</span
														>
														<span
															class="detail-value"
															>{{
																configStatus.tenantFlowRulesCount ||
																0
															}}条</span
														>
													</p>
													<p>
														<span
															class="detail-label"
															>最后发布:</span
														>
														<span
															class="detail-value"
															>{{
																formatTime(
																	configStatus.tenantFlowRulesLastPublish
																)
															}}</span
														>
													</p>
													<p>
														<span
															class="detail-label"
															>发布状态:</span
														>
														<el-tag
															:type="
																configStatus.tenantFlowRulesSuccess
																	? 'success'
																	: 'warning'
															"
															size="mini"
														>
															{{
																configStatus.tenantFlowRulesSuccess
																	? '成功'
																	: '待发布'
															}}
														</el-tag>
													</p>
												</div>
												<div class="config-actions">
													<el-button
														type="text"
														size="small"
														@click="
															viewTenantFlowRulesConfig
														"
														:loading="
															viewConfigLoading.flow
														"
													>
														查看配置
													</el-button>
												</div>
											</div>
										</el-col>
										<el-col :span="12">
											<div class="config-item">
												<h4>IP规则配置</h4>
												<div class="config-details">
													<p>
														<span
															class="detail-label"
															>规则数量:</span
														>
														<span
															class="detail-value"
															>{{
																configStatus.ipFlowRulesCount ||
																0
															}}条</span
														>
													</p>
													<p>
														<span
															class="detail-label"
															>最后发布:</span
														>
														<span
															class="detail-value"
															>{{
																formatTime(
																	configStatus.ipFlowRulesLastPublish
																)
															}}</span
														>
													</p>
													<p>
														<span
															class="detail-label"
															>发布状态:</span
														>
														<el-tag
															:type="
																configStatus.ipFlowRulesSuccess
																	? 'success'
																	: 'warning'
															"
															size="mini"
														>
															{{
																configStatus.ipFlowRulesSuccess
																	? '成功'
																	: '待发布'
															}}
														</el-tag>
													</p>
												</div>
												<div class="config-actions">
													<el-button
														type="text"
														size="small"
														@click="
															viewIpFlowRulesConfig
														"
														:loading="
															viewConfigLoading.ip
														"
													>
														查看配置
													</el-button>
												</div>
											</div>
										</el-col>
									</el-row>
								</div>
							</el-card>
						</div>
					</el-tab-pane>
				</el-tabs>
			</div>
		</layout>
	</div>
</template>

<script>
import Layout from '../components/Layout.vue';
import { mapState, mapActions, mapGetters } from 'vuex';
import { exportSystemLogsToExcel, exportToCSV } from '../utils/export';
import validationRules from '../utils/validation';

export default {
	name: 'Config',
	components: {
		Layout,
	},
	beforeCreate() {
		// 注册验证规则到Vue实例
		this.$validation = validationRules;
	},
	data() {
		return {
			activeTab: 'global',

			// 本地表单数据（用于编辑）
			localGlobalConfig: {},
			localAlertConfig: {},
			localNotificationConfig: {},

			// 系统日志过滤器
			logLevel: '',
			logDateRange: [],

			// Nacos配置相关
			connectionTesting: false,
			publishLoading: false,
			statusLoading: false,
			viewConfigLoading: {
				flow: false,
				ip: false,
			},
			nacosStatus: {
				connected: false,
				serverAddr: '',
				testTime: null,
			},
			configStatus: {
				tenantFlowRulesCount: 0,
			tenantFlowRulesSuccess: false,
			tenantFlowRulesLastPublish: null,
			ipFlowRulesCount: 0,
			ipFlowRulesSuccess: false,
			ipFlowRulesLastPublish: null,
			},

			// 表单验证规则
			globalRules: {},
			alertRules: {},
			notificationRules: {},
		};
	},

	computed: {
		...mapState('config', [
			'globalConfig',
			'alertThresholds',
			'notificationConfig',
			'systemLogs',
			'loading',
		]),

		...mapGetters('config', ['filteredSystemLogs']),

		// 动态计算全局配置验证规则
		computedGlobalRules() {
			return {
				defaultQpsThreshold: this.$validation.alertConfigRules.qpsWarningThreshold,
				defaultRtThreshold: this.$validation.alertConfigRules.rtWarningThreshold,
				defaultConcurrentThreshold: this.$validation.alertConfigRules.concurrentWarningThreshold,
				warmUpPeriodSec: this.$validation.alertConfigRules.warmUpPeriod,
				maxQueueingTimeMs: this.$validation.alertConfigRules.queueTimeout
			};
		},

		// 动态计算告警配置验证规则
		computedAlertRules() {
			return {
				qpsWarningThreshold: this.$validation.alertConfigRules.qpsWarningThreshold,
				qpsCriticalThreshold: this.$validation.alertConfigRules.qpsCriticalThreshold,
				rtWarningThreshold: this.$validation.alertConfigRules.rtWarningThreshold,
				rtCriticalThreshold: this.$validation.alertConfigRules.rtCriticalThreshold,
				errorRateWarningThreshold: this.$validation.alertConfigRules.errorRateWarningThreshold,
				errorRateCriticalThreshold: this.$validation.alertConfigRules.errorRateCriticalThreshold,
				concurrentWarningThreshold: this.$validation.alertConfigRules.concurrentWarningThreshold,
				concurrentCriticalThreshold: this.$validation.alertConfigRules.concurrentCriticalThreshold
			};
		},

		// 动态计算通知配置验证规则
		computedNotificationRules() {
			return {
				emailEnabled: this.$validation.notificationConfigRules.emailEnabled,
				emailRecipients: this.$validation.notificationConfigRules.emailRecipients,
				smsEnabled: this.$validation.notificationConfigRules.smsEnabled,
				smsRecipients: this.$validation.notificationConfigRules.smsRecipients,
				webhookEnabled: this.$validation.notificationConfigRules.webhookEnabled,
				webhookUrl: this.$validation.notificationConfigRules.webhookUrl,
				alertInterval: this.$validation.notificationConfigRules.alertInterval,
				maxAlertsPerHour: this.$validation.notificationConfigRules.maxAlertsPerHour
			};
		},

		// 日志过滤器
		logFilters() {
			return {
				level: this.logLevel,
				startDate:
					this.logDateRange && this.logDateRange[0]
						? this.logDateRange[0]
						: null,
				endDate:
					this.logDateRange && this.logDateRange[1]
						? this.logDateRange[1]
						: null,
				keyword: '',
			};
		},

		// 过滤后的系统日志
		filteredLogs() {
			return this.filteredSystemLogs(this.logFilters);
		},
	},

	async mounted() {
		await this.loadConfig();
		await this.loadSystemLogs();

		// 初始化本地表单数据
		this.localGlobalConfig = { ...this.globalConfig };
		this.localAlertConfig = { ...this.alertThresholds };
		this.localNotificationConfig = JSON.parse(
			JSON.stringify(this.notificationConfig)
		);

		// 如果当前标签是Nacos，则初始化Nacos状态
		if (this.activeTab === 'nacos') {
			await this.initNacosStatus();
		}
	},

	methods: {
		...mapActions('config', [
			'loadConfig',
			'saveGlobalConfig',
			'saveAlertThresholds',
			'saveNotificationConfig',
			'testEmailNotification',
			'testSmsNotification',
			'testWebhookNotification',
			'loadSystemLogs',
			'clearSystemLogs',
		]),
		// 加载配置
		loadConfigs() {
			// 这里应该调用API加载配置
			// 暂时使用本地存储
			const savedGlobalConfig = localStorage.getItem('globalConfig');
			if (savedGlobalConfig) {
				this.globalConfig = {
					...this.globalConfig,
					...JSON.parse(savedGlobalConfig),
				};
			}

			const savedAlertConfig = localStorage.getItem('alertConfig');
			if (savedAlertConfig) {
				this.alertConfig = {
					...this.alertConfig,
					...JSON.parse(savedAlertConfig),
				};
			}

			const savedNotificationConfig =
				localStorage.getItem('notificationConfig');
			if (savedNotificationConfig) {
				this.notificationConfig = {
					...this.notificationConfig,
					...JSON.parse(savedNotificationConfig),
				};
			}
		},

		// 保存全局配置
		async handleSaveGlobalConfig() {
			this.$refs.globalForm.validate(async (valid) => {
				if (valid) {
					const result = await this.saveGlobalConfig(
						this.localGlobalConfig
					);
					if (result.success) {
						this.$message.success(result.message);
					} else {
						this.$message.error(result.message);
					}
				}
			});
		},

		// 重置全局配置
		resetGlobalConfig() {
			this.localGlobalConfig = {
				defaultQpsThreshold: 1000,
				defaultRtThreshold: 3000,
				statisticIntervalMs: 1000,
				timeoutMs: 5000,
				warmUpEnabled: false,
				warmUpPeriodSec: 10,
			};
			this.$message.info('全局配置已重置');
		},

		// 保存告警配置
		async handleSaveAlertConfig() {
			this.$refs.alertForm.validate(async (valid) => {
				if (valid) {
					try {
						// 调用API保存告警阈值配置
						await this.$api.alertRules.create(this.localAlertConfig);
						// 同时调用Vuex action
						const result = await this.saveAlertThresholds(
							this.localAlertConfig
						);
						if (result.success) {
							this.$message.success(result.message);
						} else {
							this.$message.error(result.message);
						}
					} catch (error) {
						console.error('保存告警配置失败:', error);
						this.$message.error('保存告警配置失败: ' + (error.message || '未知错误'));
					}
				}
			});
		},

		// 重置告警配置
		resetAlertConfig() {
			this.localAlertConfig = {
				qpsWarningThreshold: 800,
				qpsCriticalThreshold: 1000,
				rtWarningThreshold: 2000,
				rtCriticalThreshold: 3000,
				errorRateWarningThreshold: 5.0,
				errorRateCriticalThreshold: 10.0,
			};
			this.$message.info('告警配置已重置');
		},

		// 保存通知配置
		async handleSaveNotificationConfig() {
			try {
				// 调用API保存通知配置
				await this.$api.alertNotifications.create(this.localNotificationConfig);
				// 同时调用Vuex action
				const result = await this.saveNotificationConfig(
					this.localNotificationConfig
				);
				if (result.success) {
					this.$message.success(result.message);
				} else {
					this.$message.error(result.message);
				}
			} catch (error) {
				console.error('保存通知配置失败:', error);
				this.$message.error('保存通知配置失败: ' + (error.message || '未知错误'));
			}
		},

		// 测试通知
		async testNotification() {
			this.$message.info('正在发送测试通知...');

			try {
				const promises = [];
				if (this.localNotificationConfig.emailEnabled) {
					// 调用API测试邮件通知
					promises.push(this.$api.alertNotifications.testEmail(this.localNotificationConfig.email));
					// 同时调用Vuex action
					promises.push(this.testEmailNotification());
				}
				if (this.localNotificationConfig.smsEnabled) {
					// 调用API测试短信通知
					promises.push(this.$api.alertNotifications.testSms(this.localNotificationConfig.sms));
					// 同时调用Vuex action
					promises.push(this.testSmsNotification());
				}
				if (this.localNotificationConfig.webhookEnabled) {
					// 调用API测试Webhook通知
					promises.push(this.$api.alertNotifications.testWebhook(this.localNotificationConfig.webhook));
					// 同时调用Vuex action
					promises.push(this.testWebhookNotification());
				}

				await Promise.all(promises);
				this.$message.success('测试通知发送成功');
			} catch (error) {
				console.error('测试通知失败:', error);
				this.$message.error('测试通知发送失败: ' + (error.message || '未知错误'));
			}
		},

		// 重置通知配置
		resetNotificationConfig() {
			this.localNotificationConfig = {
				emailEnabled: false,
				smtpHost: '',
				smtpPort: 587,
				smtpSsl: true,
				smtpUsername: '',
				smtpPassword: '',
				recipients: '',
				smsEnabled: false,
				smsProvider: 'aliyun',
				smsAccessKey: '',
				smsSecretKey: '',
				smsTemplateId: '',
				phoneNumbers: '',
				webhookEnabled: false,
				webhookUrl: '',
				webhookMethod: 'POST',
				webhookHeaders: '',
			};
			this.$message.info('通知配置已重置');
		},

		// 搜索日志
		async searchLogs() {
			try {
				// 调用API搜索系统日志
				const response = await this.$api.systemLogs.list(this.logFilters);
				// 同时调用Vuex action
				await this.loadSystemLogs(this.logFilters);
				this.$message.success('日志搜索完成');
			} catch (error) {
				console.error('搜索日志失败:', error);
				this.$message.error('日志搜索失败: ' + (error.message || '未知错误'));
			}
		},

		// 清空日志
		async handleClearLogs() {
			this.$confirm('确定要清空所有日志吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(async () => {
				try {
					// 调用API清空系统日志
					await this.$api.systemLogs.clear();
					// 同时调用Vuex action
					const result = await this.clearSystemLogs();
					if (result.success) {
						this.$message.success(result.message);
					} else {
						this.$message.error(result.message);
					}
				} catch (error) {
					console.error('清空日志失败:', error);
					this.$message.error('清空日志失败: ' + (error.message || '未知错误'));
				}
			});
		},

		// 导出日志
		async exportLogs() {
			if (this.filteredLogs.length === 0) {
				this.$message.warning('暂无日志数据可导出');
				return;
			}

			try {
				// 调用API导出系统日志
				const response = await this.$api.systemLogs.export(this.logFilters);
				// 处理文件下载
				const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
				const url = window.URL.createObjectURL(blob);
				const link = document.createElement('a');
				link.href = url;
				link.download = `系统日志_${new Date().toISOString().slice(0, 10)}.xlsx`;
				link.click();
				window.URL.revokeObjectURL(url);
				this.$message.success('日志导出成功');
			} catch (error) {
				console.error('导出日志失败:', error);
				// 降级到本地导出
				const success = exportSystemLogsToExcel(
					this.filteredLogs,
					'系统日志'
				);
				if (success) {
					this.$message.success('日志导出成功（本地导出）');
				} else {
					this.$message.error('日志导出失败');
				}
			}
		},

		// 格式化时间
		formatTime(timestamp) {
			if (!timestamp) return '-';
			// 统一使用 YYYY-MM-DD HH:mm:ss 格式
			const date = new Date(timestamp);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},

		// 获取日志级别类型
		getLogLevelType(level) {
			const types = {
				DEBUG: '',
				INFO: 'success',
				WARN: 'warning',
				ERROR: 'danger',
			};
			return types[level] || '';
		},

		// 添加邮件接收者
		addEmailRecipient() {
			this.localNotificationConfig.email.recipients.push('');
		},

		// 删除邮件接收者
		removeEmailRecipient(index) {
			this.localNotificationConfig.email.recipients.splice(index, 1);
		},

		// 添加短信号码
		addPhoneNumber() {
			this.localNotificationConfig.sms.phoneNumbers.push('');
		},

		// 删除短信号码
		removePhoneNumber(index) {
			this.localNotificationConfig.sms.phoneNumbers.splice(index, 1);
		},

		// Nacos配置相关方法
		async testNacosConnection() {
			try {
				this.connectionTesting = true;
				const result = await this.$api.nacosConfig.testConnection();

				this.nacosStatus = {
					connected: result.data.connected,
					serverAddr: result.data.serverAddr,
					testTime: result.data.testTime,
				};

				if (result.data.connected) {
					this.$message.success('Nacos连接测试成功');
				} else {
					this.$message.error('Nacos连接测试失败');
				}
			} catch (error) {
				console.error('测试Nacos连接失败:', error);
				this.$message.error(
					'测试Nacos连接失败: ' + (error.message || '未知错误')
				);
				this.nacosStatus.connected = false;
			} finally {
				this.connectionTesting = false;
			}
		},

		async publishAllRulesToNacos() {
			try {
				this.publishLoading = true;

				// 先测试连接
				const connectionResult =
					await this.$api.nacosConfig.testConnection();
				if (!connectionResult.data.connected) {
					this.$message.error('Nacos连接失败，请检查配置');
					return;
				}

				// 发布规则
				const result = await this.$api.nacosConfig.publishAllRules();

				// 更新配置状态
				this.configStatus = {
					tenantFlowRulesCount: result.data.tenantFlowRulesCount,
				tenantFlowRulesSuccess: result.data.tenantFlowRulesSuccess,
				tenantFlowRulesLastPublish: new Date().toISOString(),
				ipFlowRulesCount: result.data.ipFlowRulesCount,
				ipFlowRulesSuccess: result.data.ipFlowRulesSuccess,
				ipFlowRulesLastPublish: new Date().toISOString(),
				};

				if (result.data.allSuccess) {
					this.$message.success(
						`发布成功！租户流量规则: ${result.data.tenantFlowRulesCount}条，IP流量规则: ${result.data.ipFlowRulesCount}条`
					);
				} else {
					this.$message.warning('部分规则发布失败，请查看详细信息');
				}

				// 显示详细结果
				this.$notify({
					title: 'Nacos发布结果',
					message: `
						<div>
							<p>租户流量规则: ${result.data.tenantFlowRulesCount}条 ${
						result.data.tenantFlowRulesSuccess ? '✓' : '✗'
					}</p>
					<p>IP流量规则: ${result.data.ipFlowRulesCount}条 ${
						result.data.ipFlowRulesSuccess ? '✓' : '✗'
					}</p>
						</div>
					`,
					dangerouslyUseHTMLString: true,
					type: result.data.allSuccess ? 'success' : 'warning',
					duration: 5000,
				});
			} catch (error) {
				console.error('发布到Nacos失败:', error);
				this.$message.error(
					'发布到Nacos失败: ' + (error.message || '未知错误')
				);
			} finally {
				this.publishLoading = false;
			}
		},

		async refreshConfigStatus() {
			try {
				this.statusLoading = true;
				const result = await this.$api.nacosConfig.getConfigStatus();

				this.configStatus = {
					...this.configStatus,
					...result.data,
				};

				this.$message.success('配置状态已刷新');
			} catch (error) {
				console.error('刷新配置状态失败:', error);
				this.$message.error(
					'刷新配置状态失败: ' + (error.message || '未知错误')
				);
			} finally {
				this.statusLoading = false;
			}
		},

		async viewTenantFlowRulesConfig() {
			try {
				this.viewConfigLoading.flow = true;
				const result = await this.$api.nacosConfig.getTenantFlowRulesConfig();

				// 显示配置内容对话框
				this.$alert(result.data, '流量规则配置', {
					confirmButtonText: '确定',
					type: 'info',
					customClass: 'config-dialog',
				});
			} catch (error) {
				console.error('获取流量规则配置失败:', error);
				this.$message.error(
					'获取流量规则配置失败: ' + (error.message || '未知错误')
				);
			} finally {
				this.viewConfigLoading.flow = false;
			}
		},

		async viewIpFlowRulesConfig() {
			try {
				this.viewConfigLoading.ip = true;
				const result = await this.$api.nacosConfig.getIpFlowRulesConfig();

				// 显示配置内容对话框
				this.$alert(result.data, 'IP规则配置', {
					confirmButtonText: '确定',
					type: 'info',
					customClass: 'config-dialog',
				});
			} catch (error) {
				console.error('获取IP规则配置失败:', error);
				this.$message.error(
					'获取IP规则配置失败: ' + (error.message || '未知错误')
				);
			} finally {
				this.viewConfigLoading.ip = false;
			}
		},

		async initNacosStatus() {
			// 初始化时自动测试连接和获取状态
			await this.testNacosConnection();
			await this.refreshConfigStatus();
		},
	},

	watch: {
		activeTab(newTab) {
			if (newTab === 'nacos') {
				this.initNacosStatus();
			}
		},
	},
};
</script>

<style scoped>
.config-content {
	padding: 20px;
}

.page-header {
	margin-bottom: 30px;
}

.page-header h1 {
	color: #333;
	margin-bottom: 5px;
}

.page-header p {
	color: #666;
	font-size: 14px;
}

.config-section {
	background: white;
	padding: 20px;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-section h3 {
	color: #333;
	margin-bottom: 20px;
	font-size: 16px;
}

.form-tip {
	color: #666;
	font-size: 12px;
	margin-left: 10px;
}

.notification-detail {
	background: #f8f9fa;
	padding: 15px;
	border-radius: 4px;
	margin: 15px 0;
}

.logs-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
}

.logs-header h3 {
	color: #333;
	margin: 0;
	font-size: 16px;
}

.logs-controls {
	display: flex;
	align-items: center;
}

.logs-content {
	background: #f8f9fa;
	border-radius: 4px;
	padding: 10px;
}

/* Nacos配置相关样式 */
.nacos-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
}

.nacos-controls {
	display: flex;
	gap: 10px;
}

.status-card,
.config-card {
	margin-bottom: 20px;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.status-info,
.config-info {
	padding: 10px 0;
}

.status-item {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.status-label {
	font-weight: 500;
	margin-right: 8px;
	color: #666;
}

.status-value {
	color: #333;
}

.config-item {
	border: 1px solid #e6e6e6;
	border-radius: 6px;
	padding: 16px;
	background: #fafafa;
}

.config-item h4 {
	margin: 0 0 12px 0;
	color: #333;
	font-size: 16px;
}

.config-details p {
	margin: 8px 0;
	display: flex;
	align-items: center;
}

.detail-label {
	font-weight: 500;
	margin-right: 8px;
	color: #666;
	min-width: 80px;
}

.detail-value {
	color: #333;
}

.config-actions {
	margin-top: 12px;
	text-align: right;
}

/* 配置对话框样式 */
.config-dialog .el-message-box__content {
	max-height: 400px;
	overflow-y: auto;
	white-space: pre-wrap;
	font-family: 'Courier New', monospace;
	font-size: 12px;
	background: #f5f5f5;
	padding: 15px;
	border-radius: 4px;
}
</style>
