#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程限流测试脚本
根据用户提供的SQL数据，所有租户(tenant1-tenant5)都配置了线程限流(grade=0)，count值为5
"""

import requests
import threading
import time
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict

def test_single_request(url, tenant_id, sleep_seconds=2, timeout=10):
    """
    发送单个请求
    """
    headers = {'X-Tenant-ID': tenant_id}
    params = {'sleep': sleep_seconds}
    
    try:
        start_time = time.time()
        response = requests.get(url, headers=headers, params=params, timeout=timeout)
        end_time = time.time()
        
        return {
            'tenant_id': tenant_id,
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'response_time': end_time - start_time,
            'thread_name': threading.current_thread().name,
            'timestamp': time.time()
        }
    except requests.exceptions.Timeout:
        return {
            'tenant_id': tenant_id,
            'status_code': 408,
            'success': False,
            'response_time': timeout,
            'thread_name': threading.current_thread().name,
            'timestamp': time.time(),
            'error': 'Timeout'
        }
    except Exception as e:
        return {
            'tenant_id': tenant_id,
            'status_code': 0,
            'success': False,
            'response_time': 0,
            'thread_name': threading.current_thread().name,
            'timestamp': time.time(),
            'error': str(e)
        }

def test_tenant_thread_limit(url, tenant_id, concurrent_requests=10, sleep_seconds=2):
    """
    测试单个租户的线程限流
    根据配置，每个租户的线程限制为5，所以10个并发请求中应该有5个被限流
    """
    print(f"\n=== 测试租户 {tenant_id} 的线程限流 ===")
    print(f"并发请求数: {concurrent_requests}, 线程限制: 5, 请求延迟: {sleep_seconds}秒")
    
    results = []
    
    # 使用线程池发送并发请求
    with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
        # 提交所有任务
        futures = []
        for i in range(concurrent_requests):
            future = executor.submit(test_single_request, url, tenant_id, sleep_seconds)
            futures.append(future)
        
        # 收集结果
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
            status = "成功" if result['success'] else f"失败({result.get('status_code', 'N/A')})"
            print(f"请求 {len(results):2d}: {status} - 响应时间: {result['response_time']:.2f}s - 线程: {result['thread_name']}")
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    failed_count = len(results) - success_count
    
    print(f"\n--- 租户 {tenant_id} 测试结果 ---")
    print(f"总请求数: {len(results)}")
    print(f"成功请求: {success_count}")
    print(f"失败请求: {failed_count}")
    
    # 根据线程限流规则，应该只有5个请求成功（线程限制为5）
    expected_success = 5
    if success_count <= expected_success:
        print(f"✅ 线程限流正常工作！预期最多成功 {expected_success} 个，实际成功 {success_count} 个")
    else:
        print(f"❌ 线程限流异常！预期最多成功 {expected_success} 个，实际成功 {success_count} 个")
    
    return {
        'tenant_id': tenant_id,
        'total_requests': len(results),
        'success_count': success_count,
        'failed_count': failed_count,
        'thread_limit_working': success_count <= expected_success,
        'results': results
    }

def main():
    parser = argparse.ArgumentParser(description='测试租户线程限流功能')
    parser.add_argument('--url', default='http://localhost:8088/api/test', help='测试接口URL')
    parser.add_argument('--concurrent', type=int, default=10, help='并发请求数')
    parser.add_argument('--sleep', type=int, default=2, help='请求延迟秒数')
    parser.add_argument('--tenants', nargs='+', default=['tenant1', 'tenant2', 'tenant3', 'tenant4', 'tenant5'], help='要测试的租户列表')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("租户线程限流测试")
    print("=" * 60)
    print(f"测试URL: {args.url}")
    print(f"并发请求数: {args.concurrent}")
    print(f"请求延迟: {args.sleep}秒")
    print(f"测试租户: {', '.join(args.tenants)}")
    print(f"预期行为: 每个租户最多允许5个并发线程，超出的请求应该被限流")
    
    all_results = []
    
    # 测试每个租户
    for tenant_id in args.tenants:
        result = test_tenant_thread_limit(args.url, tenant_id, args.concurrent, args.sleep)
        all_results.append(result)
        time.sleep(1)  # 租户间间隔1秒
    
    # 生成总结报告
    print("\n" + "=" * 60)
    print("测试总结报告")
    print("=" * 60)
    
    working_count = 0
    for result in all_results:
        status = "✅ 正常" if result['thread_limit_working'] else "❌ 异常"
        print(f"租户 {result['tenant_id']}: {status} (成功: {result['success_count']}/{result['total_requests']})")
        if result['thread_limit_working']:
            working_count += 1
    
    print(f"\n线程限流功能状态: {working_count}/{len(all_results)} 个租户正常")
    
    if working_count == len(all_results):
        print("🎉 所有租户的线程限流功能都正常工作！")
    else:
        print("⚠️  部分或全部租户的线程限流功能异常，请检查配置！")

if __name__ == '__main__':
    main()