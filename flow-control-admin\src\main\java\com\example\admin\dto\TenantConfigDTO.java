package com.example.admin.dto;

import jakarta.validation.constraints.*;

/**
 * 租户配置DTO
 */
public class TenantConfigDTO {

	// 手动添加getter方法以解决lombok编译问题
	public String getTenantId() {
		return tenantId;
	}

	public String getTenantName() {
		return tenantName;
	}

	public Integer getStatus() {
		return status;
	}

	public String getCreateBy() {
		return createBy;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	/**
	 * 租户ID
	 */
	@NotBlank(message = "租户ID不能为空")
	@Size(max = 50, message = "租户ID长度不能超过50个字符")
	@Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "租户ID只能包含字母、数字、下划线和横线")
	private String tenantId;

	/**
	 * 租户名称
	 */
	@NotBlank(message = "租户名称不能为空")
	@Size(max = 100, message = "租户名称长度不能超过100个字符")
	private String tenantName;

	/**
	 * 状态：0-禁用，1-启用
	 */
	@Min(value = 0, message = "状态值必须为0或1")
	@Max(value = 1, message = "状态值必须为0或1")
	private Integer status = 1;

	/**
	 * 创建人
	 */
	@Size(max = 50, message = "创建人长度不能超过50个字符")
	private String createBy;

	/**
	 * 更新人
	 */
	@Size(max = 50, message = "更新人长度不能超过50个字符")
	private String updateBy;
}