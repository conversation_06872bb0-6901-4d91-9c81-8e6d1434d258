<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.FlowControlLogMapper">

    <!-- Result mapping -->
    <resultMap id="BaseResultMap" type="com.example.common.entity.FlowControlLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="resource_name" property="resourceName" jdbcType="VARCHAR"/>
        <result column="event_type" property="eventType" jdbcType="VARCHAR"/>
        <result column="event_time" property="eventTime" jdbcType="TIMESTAMP"/>
        <result column="client_ip" property="clientIp" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="request_url" property="requestUrl" jdbcType="VARCHAR"/>
        <result column="request_method" property="requestMethod" jdbcType="VARCHAR"/>
        <result column="response_time" property="responseTime" jdbcType="INTEGER"/>
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
        <result column="rule_type" property="ruleType" jdbcType="VARCHAR"/>
        <result column="threshold_value" property="thresholdValue" jdbcType="DOUBLE"/>
        <result column="current_value" property="currentValue" jdbcType="DOUBLE"/>
        <result column="block_reason" property="blockReason" jdbcType="VARCHAR"/>
        <result column="additional_info" property="additionalInfo" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- Base columns -->
    <sql id="Base_Column_List">
        id, tenant_id, resource_name, event_type, event_time, client_ip, user_agent,
        request_url, request_method, response_time, rule_name, rule_type, threshold_value,
        current_value, block_reason, additional_info, create_time
    </sql>

    <!-- Query flow control logs with pagination -->
    <select id="selectLogPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_control_log
        WHERE 1=1
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="resourceName != null and resourceName != ''">
            AND resource_name LIKE CONCAT('%', #{resourceName}, '%')
        </if>
        <if test="eventType != null and eventType != ''">
            AND event_type = #{eventType}
        </if>
        <if test="startTime != null">
            AND event_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND event_time &lt;= #{endTime}
        </if>
        ORDER BY event_time DESC
    </select>

    <!-- Query flow control logs by time range -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_control_log
        WHERE event_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="resourceName != null and resourceName != ''">
            AND resource_name = #{resourceName}
        </if>
        ORDER BY event_time DESC
    </select>

    <!-- Query flow control logs by tenant ID -->
    <select id="selectByTenantId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_control_log
        WHERE tenant_id = #{tenantId}
        <if test="eventType != null and eventType != ''">
            AND event_type = #{eventType}
        </if>
        ORDER BY event_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- Query flow control logs by resource name -->
    <select id="selectByResourceName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_control_log
        WHERE resource_name = #{resourceName}
        <if test="eventType != null and eventType != ''">
            AND event_type = #{eventType}
        </if>
        ORDER BY event_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- Query flow control logs by event type -->
    <select id="selectByEventType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_control_log
        WHERE event_type = #{eventType}
        <if test="startTime != null">
            AND event_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND event_time &lt;= #{endTime}
        </if>
        ORDER BY event_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- Query recent flow control logs -->
    <select id="selectRecentLogs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_control_log
        WHERE event_time &gt;= DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="resourceName != null and resourceName != ''">
            AND resource_name = #{resourceName}
        </if>
        ORDER BY event_time DESC
    </select>

    <!-- 统计事件类型分布 -->
    <select id="selectEventTypeStatistics" resultType="java.util.Map">
        SELECT 
            event_type,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM flow_control_log 
                WHERE event_time BETWEEN #{startTime} AND #{endTime}
                <if test="tenantId != null and tenantId != ''">AND tenant_id = #{tenantId}</if>
            ), 2) as percentage
        FROM flow_control_log
        WHERE event_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        GROUP BY event_type
        ORDER BY count DESC
    </select>

    <!-- 统计资源访问频次 -->
    <select id="selectResourceAccessStatistics" resultType="java.util.Map">
        SELECT 
            resource_name,
            COUNT(*) as access_count,
            COUNT(DISTINCT client_ip) as unique_ip_count,
            SUM(CASE WHEN event_type = 'BLOCK' THEN 1 ELSE 0 END) as block_count,
            ROUND(SUM(CASE WHEN event_type = 'BLOCK' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as block_rate,
            AVG(response_time) as avg_response_time
        FROM flow_control_log
        WHERE event_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        GROUP BY resource_name
        ORDER BY access_count DESC
        LIMIT #{limit}
    </select>

    <!-- 统计租户日志数量 -->
    <select id="selectTenantLogStatistics" resultType="java.util.Map">
        SELECT 
            fcl.tenant_id,
            ti.tenant_name,
            COUNT(*) as log_count,
            SUM(CASE WHEN fcl.event_type = 'BLOCK' THEN 1 ELSE 0 END) as block_count,
            SUM(CASE WHEN fcl.event_type = 'PASS' THEN 1 ELSE 0 END) as pass_count,
            SUM(CASE WHEN fcl.event_type = 'ERROR' THEN 1 ELSE 0 END) as error_count,
            COUNT(DISTINCT fcl.resource_name) as resource_count
        FROM flow_control_log fcl
        LEFT JOIN tenant_info ti ON fcl.tenant_id = ti.tenant_id
        WHERE fcl.event_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY fcl.tenant_id, ti.tenant_name
        ORDER BY log_count DESC
    </select>

    <!-- 查询异常日志（错误、阻塞等） -->
    <select id="selectAbnormalLogs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_control_log
        WHERE event_time BETWEEN #{startTime} AND #{endTime}
        AND event_type IN ('BLOCK', 'ERROR', 'EXCEPTION')
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY event_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询告警日志 -->
    <select id="selectAlarmLogs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM flow_control_log
        WHERE event_time BETWEEN #{startTime} AND #{endTime}
        AND (event_type = 'ALARM' OR block_reason IS NOT NULL)
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY event_time DESC
        LIMIT #{limit}
    </select>

    <!-- 批量插入流控日志 -->
    <insert id="batchInsert">
        INSERT INTO flow_control_log (
            tenant_id, resource_name, event_type, event_time, client_ip, user_agent,
            request_url, request_method, response_time, rule_name, rule_type, threshold_value,
            current_value, block_reason, additional_info, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.tenantId}, #{item.resourceName}, #{item.eventType}, #{item.eventTime},
                #{item.clientIp}, #{item.userAgent}, #{item.requestUrl}, #{item.requestMethod},
                #{item.responseTime}, #{item.ruleName}, #{item.ruleType}, #{item.thresholdValue},
                #{item.currentValue}, #{item.blockReason}, #{item.additionalInfo}, NOW()
            )
        </foreach>
    </insert>

    <!-- 删除过期的日志数据 -->
    <delete id="deleteExpiredLogs">
        DELETE FROM flow_control_log
        WHERE event_time &lt; #{beforeTime}
        <if test="eventType != null and eventType != ''">
            AND event_type = #{eventType}
        </if>
    </delete>

    <!-- 统计日志总数 -->
    <select id="countLogs" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM flow_control_log
        WHERE event_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 统计错误日志数量 -->
    <select id="countErrorLogs" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM flow_control_log
        WHERE event_time BETWEEN #{startTime} AND #{endTime}
        AND event_type IN ('ERROR', 'EXCEPTION')
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 查询日志趋势数据 -->
    <select id="selectLogTrendData" resultType="java.util.Map">
        SELECT 
            <choose>
                <when test="granularity == 'hour'">
                    DATE_FORMAT(event_time, '%Y-%m-%d %H:00:00') as time_point
                </when>
                <otherwise>
                    DATE_FORMAT(event_time, '%Y-%m-%d') as time_point
                </otherwise>
            </choose>,
            COUNT(*) as log_count,
            SUM(CASE WHEN event_type = 'BLOCK' THEN 1 ELSE 0 END) as block_count,
            SUM(CASE WHEN event_type = 'PASS' THEN 1 ELSE 0 END) as pass_count,
            SUM(CASE WHEN event_type = 'ERROR' THEN 1 ELSE 0 END) as error_count
        FROM flow_control_log
        WHERE event_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="eventType != null and eventType != ''">
            AND event_type = #{eventType}
        </if>
        GROUP BY 
            <choose>
                <when test="granularity == 'hour'">
                    DATE_FORMAT(event_time, '%Y-%m-%d %H:00:00')
                </when>
                <otherwise>
                    DATE_FORMAT(event_time, '%Y-%m-%d')
                </otherwise>
            </choose>
        ORDER BY time_point
    </select>

    <!-- 查询IP访问统计 -->
    <select id="selectIpAccessStatistics" resultType="java.util.Map">
        SELECT 
            client_ip,
            COUNT(*) as access_count,
            COUNT(DISTINCT resource_name) as resource_count,
            SUM(CASE WHEN event_type = 'BLOCK' THEN 1 ELSE 0 END) as block_count,
            ROUND(SUM(CASE WHEN event_type = 'BLOCK' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as block_rate,
            MIN(event_time) as first_access_time,
            MAX(event_time) as last_access_time
        FROM flow_control_log
        WHERE event_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        AND client_ip IS NOT NULL
        GROUP BY client_ip
        ORDER BY access_count DESC
        LIMIT #{limit}
    </select>

</mapper>