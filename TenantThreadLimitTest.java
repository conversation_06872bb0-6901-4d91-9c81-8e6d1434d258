import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

public class TenantThreadLimitTest {
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "123456";
    private static final String GATEWAY_URL = "http://localhost:8088";
    
    public static void main(String[] args) {
        try {
            // 1. 清理并创建测试规则
            setupTestRule();
            
            // 2. 等待规则生效
            Thread.sleep(2000);
            
            // 3. 执行并发测试
            testConcurrentRequests();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static void setupTestRule() throws SQLException {
        Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
        
        // 清理现有规则
        String deleteSQL = "DELETE FROM tenant_flow_rules WHERE tenant_id = 'test-tenant-001'";
        PreparedStatement deleteStmt = conn.prepareStatement(deleteSQL);
        deleteStmt.executeUpdate();
        deleteStmt.close();
        
        // 创建线程数限流规则：限制租户test-tenant-001最多2个并发线程
        String insertSQL = "INSERT INTO tenant_flow_rules (rule_name, tenant_id, ref_resource, grade, count, strategy, control_behavior, warm_up_period_sec, max_queueing_time_ms, cluster_mode, cluster_config, cluster_threshold_type, cluster_fallback_threshold, created_time, updated_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        PreparedStatement insertStmt = conn.prepareStatement(insertSQL);
        insertStmt.setString(1, "tenant-thread-limit-rule");
        insertStmt.setString(2, "test-tenant-001");
        insertStmt.setString(3, "tenant:total");
        insertStmt.setInt(4, 0); // grade=0表示线程数限流
        insertStmt.setDouble(5, 2.0); // 限制2个并发线程
        insertStmt.setInt(6, 0); // 直接拒绝策略
        insertStmt.setInt(7, 0); // 快速失败
        insertStmt.setInt(8, 0);
        insertStmt.setInt(9, 0);
        insertStmt.setBoolean(10, false);
        insertStmt.setString(11, null);
        insertStmt.setInt(12, 0);
        insertStmt.setDouble(13, 0);
        
        insertStmt.executeUpdate();
        insertStmt.close();
        conn.close();
        
        System.out.println("✓ 创建租户线程数限流规则：tenant=test-tenant-001, 线程数限制=2");
    }
    
    private static void testConcurrentRequests() {
        int threadCount = 5; // 启动5个并发线程，超过限制的2个
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger blockedCount = new AtomicInteger(0);
        
        System.out.println("\n开始并发测试：启动" + threadCount + "个线程同时请求...");
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i + 1;
            executor.submit(() -> {
                try {
                    // 模拟长时间请求，保持线程占用
                    String result = sendRequest("/api/test?sleep=3000", "test-tenant-001");
                    if (result.contains("blocked") || result.contains("限流")) {
                        blockedCount.incrementAndGet();
                        System.out.println("线程" + threadId + ": 被限流 - " + result);
                    } else {
                        successCount.incrementAndGet();
                        System.out.println("线程" + threadId + ": 请求成功 - " + result);
                    }
                } catch (Exception e) {
                    System.out.println("线程" + threadId + ": 请求异常 - " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await(); // 等待所有线程完成
            executor.shutdown();
            
            System.out.println("\n=== 测试结果 ===");
            System.out.println("成功请求数: " + successCount.get());
            System.out.println("被限流数: " + blockedCount.get());
            System.out.println("总请求数: " + threadCount);
            
            if (blockedCount.get() > 0) {
                System.out.println("✓ 线程数限流生效！");
            } else {
                System.out.println("✗ 线程数限流未生效，需要检查配置");
            }
            
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
    
    private static String sendRequest(String path, String tenantId) throws Exception {
        URL url = new URL(GATEWAY_URL + path);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setRequestProperty("X-Tenant-ID", tenantId);
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(10000);
        
        int responseCode = conn.getResponseCode();
        BufferedReader reader;
        
        if (responseCode >= 200 && responseCode < 300) {
            reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        } else {
            reader = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
        }
        
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();
        
        return "HTTP " + responseCode + ": " + response.toString();
    }
}