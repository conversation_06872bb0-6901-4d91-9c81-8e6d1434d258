package com.example.admin.vo;


import java.time.LocalDateTime;

/**
 * 流量规则VO
 */

public class FlowRuleVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 资源名称
     */
    private String resourceName;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 租户名称
     */
    private String tenantName;
    
    /**
     * 限流模式：0-QPS限流，1-并发限流（线程数）
     */
    private Integer limitMode;
    
    /**
     * 限流模式名称
     */
    private String limitModeName;
    
    /**
     * 阈值
     */
    private Integer threshold;
    
    /**
     * 流控策略：0-直接，1-关联，2-链路
     */
    private Integer strategy;
    
    /**
     * 流控策略名称
     */
    private String strategyName;
    
    /**
     * 关联资源
     */
    private String relatedResource;
    
    /**
     * 流控行为：0-快速失败，1-预热，2-排队等待
     */
    private Integer behavior;
    
    /**
     * 流控行为名称
     */
    private String behaviorName;
    
    /**
     * 预热时长（秒）
     */
    private Integer warmUpPeriod;
    
    /**
     * 排队超时时间（毫秒）
     */
    private Integer queueTimeout;
    
    /**
     * 是否集群模式：0-否，1-是
     */
    private Integer clusterMode;
    
    /**
     * 集群模式名称
     */
    private String clusterModeName;
    
    /**
     * 规则状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 规则状态名称
     */
    private String statusName;
    
    /**
     * 优先级
     */
    private Integer priority;
    
    /**
     * 规则描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public Integer getLimitMode() {
        return limitMode;
    }

    public void setLimitMode(Integer limitMode) {
        this.limitMode = limitMode;
    }

    public String getLimitModeName() {
        return limitModeName;
    }

    public void setLimitModeName(String limitModeName) {
        this.limitModeName = limitModeName;
    }

    public Integer getThreshold() {
        return threshold;
    }

    public void setThreshold(Integer threshold) {
        this.threshold = threshold;
    }

    public Integer getStrategy() {
        return strategy;
    }

    public void setStrategy(Integer strategy) {
        this.strategy = strategy;
    }

    public String getStrategyName() {
        return strategyName;
    }

    public void setStrategyName(String strategyName) {
        this.strategyName = strategyName;
    }

    public String getRelatedResource() {
        return relatedResource;
    }

    public void setRelatedResource(String relatedResource) {
        this.relatedResource = relatedResource;
    }

    public Integer getBehavior() {
        return behavior;
    }

    public void setBehavior(Integer behavior) {
        this.behavior = behavior;
    }

    public String getBehaviorName() {
        return behaviorName;
    }

    public void setBehaviorName(String behaviorName) {
        this.behaviorName = behaviorName;
    }

    public Integer getWarmUpPeriod() {
        return warmUpPeriod;
    }

    public void setWarmUpPeriod(Integer warmUpPeriod) {
        this.warmUpPeriod = warmUpPeriod;
    }

    public Integer getQueueTimeout() {
        return queueTimeout;
    }

    public void setQueueTimeout(Integer queueTimeout) {
        this.queueTimeout = queueTimeout;
    }

    public Integer getClusterMode() {
        return clusterMode;
    }

    public void setClusterMode(Integer clusterMode) {
        this.clusterMode = clusterMode;
    }

    public String getClusterModeName() {
        return clusterModeName;
    }

    public void setClusterModeName(String clusterModeName) {
        this.clusterModeName = clusterModeName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }
    
    /**
     * 是否集群模式
     */
    public boolean isClusterMode() {
        return clusterMode != null && clusterMode == 1;
    }
    
    /**
     * 获取完整的规则配置描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("资源: ").append(resourceName);
        sb.append(", 模式: ").append(limitModeName);
        sb.append(", 阈值: ").append(threshold);
        sb.append(", 策略: ").append(strategyName);
        sb.append(", 行为: ").append(behaviorName);
        
        if (warmUpPeriod != null && warmUpPeriod > 0) {
            sb.append(", 预热: ").append(warmUpPeriod).append("秒");
        }
        
        if (queueTimeout != null && queueTimeout > 0) {
            sb.append(", 排队超时: ").append(queueTimeout).append("毫秒");
        }
        
        return sb.toString();
    }
}