package com.example.gateway.controller;

import com.example.common.entity.TenantRuleEntity;
import com.example.gateway.mapper.TenantRuleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 测试租户规则控制器 用于测试TenantRuleMapper的查询功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/rule")
public class TestTenantRuleController {

	@Autowired
	private TenantRuleMapper tenantRuleMapper;

	/**
	 * 测试查询指定租户的启用规则
	 * 
	 * @param tenantId 租户ID
	 * @return 租户规则列表
	 */
	@GetMapping("/tenant-rules/{tenantId}")
	public List<TenantRuleEntity> getTenantRules(@PathVariable String tenantId) {
		return tenantRuleMapper.selectEnabledRulesByTenant(tenantId);
	}

	/**
	 * 测试查询指定租户和资源的启用规则
	 * 
	 * @param tenantId 租户ID
	 * @param resource 资源名称
	 * @return 租户规则列表
	 */
	@GetMapping("/tenant-rules/{tenantId}/resource/{resource}")
	public List<TenantRuleEntity> getTenantRulesByResource(@PathVariable String tenantId,
			@PathVariable String resource) {
		return tenantRuleMapper.selectEnabledRulesByTenantAndResource(tenantId, resource);
	}

	/**
	 * 测试查询所有启用的规则
	 * 
	 * @return 所有启用的租户规则列表
	 */
	@GetMapping("/tenant-rules/all")
	public List<TenantRuleEntity> getAllEnabledRules() {
		return tenantRuleMapper.selectEnabledRules();
	}

	/**
	 * 测试统计租户规则数量
	 * 
	 * @param tenantId 租户ID
	 * @return 规则数量
	 */
	@GetMapping("/tenant-rules/{tenantId}/count")
	public Integer countTenantRules(@PathVariable String tenantId) {
		return tenantRuleMapper.countRulesByTenant(tenantId, 1); // 只统计启用的规则
	}
}