# 流量控制系统设计文档

## 1. 系统概述

### 1.1 项目背景
本流量控制系统是基于Spring Cloud Gateway和Alibaba Sentinel构建的多维度智能流量控制平台，旨在为微服务架构提供全面的流量管控能力。系统采用分层限流策略，支持IP级、租户级和接口级的精细化流量控制。

### 1.2 设计目标
- **多维度流量控制**：支持IP、租户、接口三个维度的流量限制
- **高可用性**：确保系统在高并发场景下的稳定运行
- **可扩展性**：支持集群部署和水平扩展
- **实时监控**：提供实时的流量监控和统计分析
- **灵活配置**：支持动态规则配置和热更新

### 1.3 核心特性
- 基于Sentinel的流量控制引擎
- 多层级流量限制策略
- 实时规则同步和配置管理
- 完善的监控和统计功能
- RESTful API管理界面
- 支持黑白名单机制

## 2. 系统架构

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    流量控制系统架构                          │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Vue.js)                                           │
│  ┌─────────────────────────────────────────────────────┐   │
│  │  流量控制管理界面                                    │   │
│  │  - 规则配置管理                                     │   │
│  │  - 监控数据展示                                     │   │
│  │  - 统计报表分析                                     │   │
│  └─────────────────────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  网关层 (Spring Cloud Gateway)                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │  MultiDimensionFlowFilter                          │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │  IP级限流   │ │  租户级限流  │ │  接口级限流  │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  └─────────────────────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  服务层 (Spring Boot)                                      │
│  ┌─────────────────────────────────────────────────────┐   │
│  │  flow-control-admin                                │   │
│  │  - 规则管理服务                                     │   │
│  │  - 监控统计服务                                     │   │
│  │  - 配置同步服务                                     │   │
│  └─────────────────────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  数据层                                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │
│  │    MySQL    │ │    Redis    │ │    Nacos    │         │
│  │  持久化存储  │ │  缓存/计数   │ │  配置中心    │         │
│  └─────────────┘ └─────────────┘ └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 技术栈

#### 2.2.1 核心框架
- **Spring Boot**: 3.2.1
- **Spring Cloud**: 2023.0.0
- **Spring Cloud Alibaba**: 2023.0.1.0
- **Spring Cloud Gateway**: 网关服务
- **Alibaba Sentinel**: 1.8.6 (流量控制核心)

#### 2.2.2 数据存储
- **MySQL**: 8.0.33 (主数据库)
- **Redis**: 7.0 (缓存和计数器)
- **MyBatis Plus**: 3.5.5 (ORM框架)

#### 2.2.3 配置管理
- **Nacos**: 2.2.4 (配置中心和服务发现)
- **Apollo**: 配置管理

#### 2.2.4 监控运维
- **Prometheus**: 指标收集
- **Grafana**: 监控面板
- **Docker**: 容器化部署

#### 2.2.5 前端技术
- **Vue.js**: 前端框架
- **Element UI**: UI组件库
- **Axios**: HTTP客户端

## 3. 核心功能模块

### 3.1 流量控制引擎

#### 3.1.1 多维度限流策略
系统实现三层限流检查机制：

1. **IP级限流**（第一层）
   - 基于客户端IP地址进行流量控制
   - 支持单个IP、IP范围、CIDR网段
   - 独立于租户的全局IP控制

2. **租户级限流**（第二层）
   - 基于租户ID的总体QPS限制
   - 防止单个租户占用过多系统资源
   - 支持租户级别的流量配额管理

3. **接口级限流**（第三层）
   - 基于租户+接口的精细化控制
   - 支持不同接口的差异化限流策略
   - 可配置预热、排队等流控行为

#### 3.1.2 流控算法
- **QPS限流**：基于每秒请求数的限制
- **并发线程数限流**：基于同时处理请求数的限制
- **预热模式**：系统启动时的渐进式限流
- **排队等待**：请求排队处理机制

### 3.2 规则管理模块

#### 3.2.1 流量规则管理
- **基础流量规则**：通用的流量控制规则
- **IP流量规则**：基于IP的流量控制规则
- **租户流量规则**：基于租户的流量控制规则
- **规则优先级**：支持规则优先级排序
- **规则生效时间**：支持定时生效和失效

#### 3.2.2 规则操作功能
- **CRUD操作**：创建、查询、更新、删除规则
- **批量管理**：批量创建、删除、启用/禁用规则
- **规则复制**：规则复制到其他租户
- **规则导入导出**：支持规则的批量导入导出
- **规则验证**：规则配置的有效性验证

### 3.3 IP管理模块

#### 3.3.1 IP黑白名单
- **IP白名单**：允许访问的IP列表
- **IP黑名单**：禁止访问的IP列表
- **IP类型支持**：
  - 单个IP地址
  - IP地址范围
  - CIDR网段格式

#### 3.3.2 IP规则管理
- **规则类型**：白名单、黑名单、限流
- **QPS限制**：针对特定IP的QPS限制
- **时间窗口**：限流统计的时间窗口
- **优先级控制**：IP规则的优先级管理

### 3.4 租户管理模块

#### 3.4.1 租户信息管理
- **租户基础信息**：租户ID、名称、描述
- **联系信息**：联系人、电话、邮箱
- **配额管理**：租户总QPS限制
- **状态管理**：租户启用/禁用状态

#### 3.4.2 租户流量规则
- **全局租户限流**：租户级别的总体限流
- **流控效果配置**：快速失败、预热、排队等待
- **集群模式支持**：单机和集群模式切换
- **时间范围控制**：规则生效的时间范围

### 3.5 监控统计模块

#### 3.5.1 实时监控
- **流量监控**：实时请求量、QPS统计
- **限流监控**：被限流请求的统计
- **响应时间监控**：接口响应时间统计
- **错误率监控**：系统错误率统计

#### 3.5.2 统计分析
- **租户维度统计**：各租户的流量使用情况
- **接口维度统计**：各接口的访问统计
- **IP维度统计**：IP访问模式分析
- **时间维度统计**：按时间段的流量趋势

### 3.6 配置管理模块

#### 3.6.1 系统配置
- **流控开关**：全局流量控制开关
- **默认参数**：默认QPS、超时时间等
- **监控配置**：数据保留天数、采集频率
- **外部服务配置**：Nacos、Redis连接配置

#### 3.6.2 规则同步
- **实时同步**：规则变更的实时同步
- **批量同步**：规则的批量同步机制
- **同步状态监控**：同步状态的监控和告警
- **回滚机制**：规则同步失败的回滚处理

## 4. 数据库设计

### 4.1 数据库概览
数据库名称：`sentinel_flow_control`
字符集：`utf8mb4`
排序规则：`utf8mb4_unicode_ci`

### 4.2 核心数据表

#### 4.2.1 流量控制规则表 (flow_rule)
```sql
CREATE TABLE `flow_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `resource_name` varchar(200) NOT NULL COMMENT '资源名称',
  `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
  `limit_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '限流模式：0-QPS，1-线程数',
  `threshold` int(11) NOT NULL COMMENT '阈值',
  `strategy` tinyint(1) NOT NULL DEFAULT 0 COMMENT '流控策略：0-直接，1-关联，2-链路',
  `related_resource` varchar(200) DEFAULT NULL COMMENT '关联资源',
  `behavior` tinyint(1) NOT NULL DEFAULT 0 COMMENT '流控行为：0-快速失败，1-预热，2-排队等待',
  `warm_up_period` int(11) DEFAULT NULL COMMENT '预热时长（秒）',
  `queue_timeout` int(11) DEFAULT NULL COMMENT '排队超时时间（毫秒）',
  `cluster_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否集群模式：0-否，1-是',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '规则状态：0-禁用，1-启用',
  `priority` int(11) NOT NULL DEFAULT 1 COMMENT '优先级',
  `description` text COMMENT '规则描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_name` (`rule_name`),
  KEY `idx_resource_name` (`resource_name`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`)
) COMMENT='流量控制规则表';
```

#### 4.2.2 IP流量规则表 (ip_flow_rule)
```sql
CREATE TABLE `ip_flow_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
  `rule_type` varchar(32) NOT NULL DEFAULT 'SINGLE_IP' COMMENT '规则类型：SINGLE_IP-单个IP，IP_RANGE-IP范围，IP_CIDR-IP段',
  `ip_value` varchar(100) NOT NULL COMMENT 'IP值（单个IP、IP段或IP范围）',
  `list_type` varchar(32) NOT NULL DEFAULT 'BLACKLIST' COMMENT '名单类型：WHITELIST-白名单，BLACKLIST-黑名单，LIMIT-限流',
  `qps_limit` int(11) DEFAULT NULL COMMENT 'QPS限制（限流类型时使用）',
  `time_window` int(11) DEFAULT 1 COMMENT '时间窗口（秒）',
  `status` int(11) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `priority` int(11) DEFAULT 1 COMMENT '优先级',
  `description` varchar(512) DEFAULT NULL COMMENT '规则描述',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` int(11) DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_name` (`rule_name`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_ip_value` (`ip_value`),
  KEY `idx_status` (`status`)
) COMMENT='IP流量规则表';
```

#### 4.2.3 租户流量规则表 (tenant_flow_rules)
```sql
CREATE TABLE `tenant_flow_rules` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `grade` tinyint(1) NOT NULL DEFAULT 1 COMMENT '限流模式：0-线程数，1-QPS',
  `count` double NOT NULL DEFAULT 100 COMMENT '限流阈值',
  `control_behavior` tinyint(1) NOT NULL DEFAULT 0 COMMENT '流控效果：0-快速失败，1-Warm Up，2-排队等待，3-Warm Up + 排队等待',
  `warm_up_period_sec` int(11) DEFAULT NULL COMMENT '预热时间（秒）',
  `max_queueing_time_ms` int(11) DEFAULT NULL COMMENT '最大排队时间（毫秒）',
  `strategy` tinyint(1) DEFAULT 0 COMMENT '流控策略：0-直接，1-关联，2-链路',
  `ref_resource` varchar(256) DEFAULT NULL COMMENT '关联资源',
  `cluster_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '集群模式：0-单机，1-集群',
  `cluster_config` text COMMENT '集群配置JSON',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `description` text COMMENT '规则描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_rule_name` (`tenant_id`,`rule_name`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_priority` (`priority`)
) COMMENT='租户流控规则表';
```

#### 4.2.4 租户信息表 (tenant_info)
```sql
CREATE TABLE `tenant_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
  `tenant_name` varchar(100) NOT NULL COMMENT '租户名称',
  `description` varchar(500) DEFAULT NULL COMMENT '租户描述',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `total_qps_limit` int(11) DEFAULT NULL COMMENT '租户总QPS限制',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`)
) COMMENT='租户信息表';
```

#### 4.2.5 流量控制日志表 (flow_control_log)
```sql
CREATE TABLE `flow_control_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resource_name` varchar(200) NOT NULL COMMENT '资源名称',
  `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
  `rule_name` varchar(100) DEFAULT NULL COMMENT '触发的规则名称',
  `event_type` varchar(20) NOT NULL COMMENT '事件类型：PASS-通过，BLOCK-阻塞，QUEUE-排队',
  `request_id` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `user_id` varchar(50) DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_params` text COMMENT '请求参数',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间（毫秒）',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_resource_name` (`resource_name`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_create_time` (`create_time`)
) COMMENT='流量控制事件日志表';
```

#### 4.2.6 监控统计表 (monitor_statistics)
```sql
CREATE TABLE `monitor_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `total_requests` bigint(20) DEFAULT 0 COMMENT '总请求数',
  `blocked_requests` bigint(20) DEFAULT 0 COMMENT '被阻断请求数',
  `avg_response_time` decimal(10,2) DEFAULT NULL COMMENT '平均响应时间(ms)',
  `max_qps` int(11) DEFAULT 0 COMMENT '最大QPS',
  `max_thread_count` int(11) DEFAULT 0 COMMENT '最大线程数',
  `statistics_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '统计时间',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_statistics_time` (`statistics_time`)
) COMMENT='监控统计表';
```

#### 4.2.7 系统配置表 (system_config)
```sql
CREATE TABLE `system_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(20) NOT NULL DEFAULT 'STRING' COMMENT '配置类型：STRING-字符串，NUMBER-数字，BOOLEAN-布尔值，JSON-JSON对象',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) COMMENT='系统配置表';
```

### 4.3 数据表关系

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   tenant_info   │────▶│ tenant_flow_rules│────▶│  flow_rule      │
│   租户信息表     │     │  租户流量规则表   │     │  流量规则表      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                        │                        │
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ ip_flow_rule    │     │flow_control_log │     │monitor_statistics│
│ IP流量规则表     │     │ 流量控制日志表   │     │  监控统计表      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## 5. API接口设计

### 5.1 API设计原则
- **RESTful风格**：遵循REST API设计规范
- **统一响应格式**：使用统一的响应结构
- **参数验证**：完善的请求参数验证
- **错误处理**：标准化的错误响应
- **API文档**：使用Swagger生成API文档

### 5.2 通用响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-01-15T10:30:00Z"
}
```

### 5.3 核心API接口

#### 5.3.1 流量规则管理API

**基础路径**: `/api/flow-rules`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询流量规则 | page, size, tenantId, resourceName, status |
| GET | `/{id}` | 根据ID查询流量规则 | id |
| POST | `/` | 创建流量规则 | FlowRuleDTO |
| PUT | `/{id}` | 更新流量规则 | id, FlowRuleDTO |
| DELETE | `/{id}` | 删除流量规则 | id |
| DELETE | `/batch` | 批量删除流量规则 | ids[] |
| PUT | `/{id}/status` | 启用/禁用流量规则 | id, status |
| PUT | `/batch/status` | 批量启用/禁用流量规则 | ids[], status |
| GET | `/tenant/{tenantId}` | 根据租户ID查询流量规则 | tenantId |
| POST | `/sync/{tenantId}` | 同步规则到Sentinel | tenantId |
| POST | `/import` | 导入流量规则 | FlowRuleDTO[] |
| GET | `/export` | 导出流量规则 | tenantId |
| GET | `/statistics` | 获取流量规则统计 | tenantId |
| POST | `/validate` | 验证流量规则 | FlowRuleDTO |

#### 5.3.2 IP流量规则管理API

**基础路径**: `/api/ip-flow-rules`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询IP流量规则 | page, size, tenantId, ruleType, status |
| GET | `/{id}` | 根据ID查询IP流量规则 | id |
| POST | `/` | 创建IP流量规则 | IPFlowRuleDTO |
| PUT | `/{id}` | 更新IP流量规则 | id, IPFlowRuleDTO |
| DELETE | `/{id}` | 删除IP流量规则 | id |
| DELETE | `/batch` | 批量删除IP流量规则 | ids[] |
| PUT | `/{id}/status` | 启用/禁用IP流量规则 | id, status |
| PUT | `/batch/status` | 批量启用/禁用IP流量规则 | ids[], status |
| GET | `/tenant/{tenantId}` | 根据租户ID查询IP流量规则 | tenantId, status, limit |
| POST | `/sync/{tenantId}` | 同步规则到Sentinel | tenantId |
| POST | `/import` | 导入IP流量规则 | IPFlowRuleDTO[], overwrite |
| GET | `/export` | 导出IP流量规则 | tenantId, status |
| GET | `/statistics` | 获取IP流量规则统计 | tenantId |
| POST | `/validate` | 验证IP流量规则 | IPFlowRuleDTO |
| POST | `/cache/refresh` | 刷新规则缓存 | tenantId |

#### 5.3.3 租户流量规则管理API

**基础路径**: `/api/tenant-flow-rules`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询租户流量规则 | page, size, tenantId, ruleName, enabled, controlBehavior |
| GET | `/{id}` | 根据ID查询租户流量规则 | id |
| POST | `/` | 创建租户流量规则 | TenantFlowRuleDTO |
| PUT | `/{id}` | 更新租户流量规则 | id, TenantFlowRuleDTO |
| DELETE | `/{id}` | 删除租户流量规则 | id |
| DELETE | `/batch` | 批量删除租户流量规则 | ids[] |
| PUT | `/{id}/status` | 启用/禁用租户流量规则 | id, enabled |
| PUT | `/batch/status` | 批量启用/禁用租户流量规则 | ids[], enabled |
| GET | `/tenant/{tenantId}` | 根据租户ID查询租户流量规则 | tenantId, enabled, limit |
| GET | `/enabled` | 获取有效流量规则列表 | tenantId, limit |
| GET | `/priority` | 按优先级排序查询流量规则 | tenantId, enabled, limit |
| POST | `/batch` | 批量创建租户流量规则 | TenantFlowRuleDTO[] |
| POST | `/{id}/copy` | 复制租户流量规则 | id, newRuleName, targetTenantId |
| POST | `/batch/copy` | 批量复制到其他租户 | sourceIds[], targetTenantId, namePrefix |
| POST | `/import` | 导入租户流量规则 | TenantFlowRuleDTO[], overwrite |
| GET | `/export` | 导出租户流量规则 | tenantId, enabled |
| POST | `/validate` | 验证租户流量规则 | TenantFlowRuleDTO |
| GET | `/statistics/enabled` | 获取启用状态统计 | tenantId |
| GET | `/statistics/tenant` | 获取租户流量规则分布统计 | limit |
| GET | `/statistics/control-behavior` | 获取流控效果统计 | tenantId |
| GET | `/max-priority` | 获取最大优先级 | tenantId |
| GET | `/priority-range` | 按优先级范围查询流量规则 | tenantId, minPriority, maxPriority, enabled |
| GET | `/expiring` | 获取即将过期的流量规则 | tenantId, hours |
| PUT | `/disable-expired` | 禁用过期流量规则 | - |
| GET | `/valid` | 获取当前有效流量规则 | tenantId |
| GET | `/summary/{tenantId}` | 获取租户流量规则汇总 | tenantId |
| GET | `/summary/global` | 获取全局流量规则汇总 | - |
| POST | `/sync/{tenantId}` | 同步流量规则到网关 | tenantId |

### 5.4 数据传输对象(DTO)

#### 5.4.1 FlowRuleDTO
```java
public class FlowRuleDTO {
    private String ruleName;        // 规则名称
    private String resourceName;    // 资源名称
    private String tenantId;        // 租户ID
    private Integer limitMode;      // 限流模式：0-QPS，1-线程数
    private Integer threshold;      // 阈值
    private Integer strategy;       // 流控策略：0-直接，1-关联，2-链路
    private String relatedResource; // 关联资源
    private Integer behavior;       // 流控行为：0-快速失败，1-预热，2-排队等待
    private Integer warmUpPeriod;   // 预热时长（秒）
    private Integer queueTimeout;   // 排队超时时间（毫秒）
    private Boolean clusterMode;    // 是否集群模式
    private Integer status;         // 规则状态：0-禁用，1-启用
    private Integer priority;       // 优先级
    private String description;     // 规则描述
}
```

#### 5.4.2 IPFlowRuleDTO
```java
public class IPFlowRuleDTO {
    private String ruleName;        // 规则名称
    private String tenantId;        // 租户ID
    private String ruleType;        // 规则类型：SINGLE_IP, IP_RANGE, IP_CIDR
    private String ipValue;         // IP值
    private String listType;        // 名单类型：WHITELIST, BLACKLIST, LIMIT
    private Integer qpsLimit;       // QPS限制
    private Integer timeWindow;     // 时间窗口（秒）
    private Integer status;         // 状态：0-禁用，1-启用
    private Integer priority;       // 优先级
    private String description;     // 规则描述
}
```

#### 5.4.3 TenantFlowRuleDTO
```java
public class TenantFlowRuleDTO {
    private String tenantId;            // 租户ID
    private String ruleName;            // 规则名称
    private Integer grade;              // 限流模式：0-线程数，1-QPS
    private Double count;               // 限流阈值
    private Integer controlBehavior;    // 流控效果：0-快速失败，1-Warm Up，2-排队等待，3-Warm Up + 排队等待
    private Integer warmUpPeriodSec;    // 预热时间（秒）
    private Integer maxQueueingTimeMs;  // 最大排队时间（毫秒）
    private Integer strategy;           // 流控策略：0-直接，1-关联，2-链路
    private String refResource;         // 关联资源
    private Boolean clusterMode;        // 集群模式：false-单机，true-集群
    private String clusterConfig;       // 集群配置JSON
    private Integer priority;           // 优先级
    private Boolean enabled;            // 是否启用
    private LocalDateTime startTime;    // 生效开始时间
    private LocalDateTime endTime;      // 生效结束时间
    private String description;         // 规则描述
}
```

## 6. 流量控制流程

### 6.1 请求处理流程

```
┌─────────────────┐
│   客户端请求     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  Spring Cloud   │
│    Gateway      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│MultiDimensionFlow│
│     Filter      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  提取请求上下文   │
│ - 租户ID        │
│ - 资源名称       │
│ - 客户端IP      │
│ - 请求ID        │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  第一层：IP级限流 │
│ - 检查IP黑白名单 │
│ - 执行IP流量控制 │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 第二层：租户级限流│
│ - 租户总QPS检查  │
│ - 租户配额控制   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│第三层：接口级限流 │
│ - 接口QPS检查    │
│ - 接口流控策略   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│   流量控制结果   │
│ - 通过：继续处理 │
│ - 限流：返回错误 │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│   记录监控数据   │
│ - 请求统计      │
│ - 限流统计      │
│ - 响应时间      │
└─────────────────┘
```

### 6.2 多层限流检查逻辑

#### 6.2.1 IP级限流（第一层）
```java
// 1. 查找匹配的IP规则
String ipResource = findMatchingIPResource(clientIp);

// 2. 执行IP级流量控制
Entry ipEntry = SphU.entry(ipResource);

// 3. IP级限流通过，继续下一层检查
```

#### 6.2.2 租户级限流（第二层）
```java
// 1. 构建租户资源标识
String tenantTotalResource = "tenant:" + tenantId;

// 2. 执行租户总QPS限流
Entry tenantTotalEntry = SphU.entry(tenantTotalResource);

// 3. 租户级限流通过，继续下一层检查
```

#### 6.2.3 接口级限流（第三层）
```java
// 1. 构建接口资源标识
String tenantInterfaceResource = "tenant:" + tenantId + ":" + resource;

// 2. 执行接口级流量控制
Entry tenantInterfaceEntry = SphU.entry(tenantInterfaceResource);

// 3. 接口级限流通过，请求继续处理
```

### 6.3 限流异常处理

#### 6.3.1 异常类型判断
```java
private String determineLimitType(BlockException e, Entry ipEntry, 
                                 Entry tenantTotalEntry, Entry tenantInterfaceEntry) {
    if (ipEntry == null) {
        return "IP_LIMIT";
    } else if (tenantTotalEntry == null) {
        return "TENANT_TOTAL_LIMIT";
    } else if (tenantInterfaceEntry == null) {
        return "TENANT_INTERFACE_LIMIT";
    } else {
        return "UNKNOWN_LIMIT";
    }
}
```

#### 6.3.2 限流响应格式
```json
{
  "code": 429,
  "message": "请求过于频繁，请稍后重试",
  "data": {
    "limitType": "TENANT_TOTAL_LIMIT",
    "requestId": "req_123456789",
    "retryAfter": 1
  },
  "timestamp": "2025-01-15T10:30:00Z"
}
```

### 6.4 规则同步机制

#### 6.4.1 规则同步流程
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  管理后台修改    │────▶│   数据库更新     │────▶│   触发同步事件   │
│    规则配置      │     │     规则数据     │     │                │
└─────────────────┘     └─────────────────┘     └─────────┬───────┘
                                                          │
                                                          ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   网关服务接收   │◀────│   Nacos配置推送  │◀────│  规则同步服务    │
│    规则更新      │     │                │     │                │
└─────────┬───────┘     └─────────────────┘     └─────────────────┘
          │
          ▼
┌─────────────────┐
│ Sentinel规则更新 │
│   - 流量规则     │
│   - IP规则      │
│   - 租户规则     │
└─────────────────┘
```

#### 6.4.2 规则同步实现
```java
@Service
public class RuleSyncService {
    
    @Autowired
    private DatabaseRuleService databaseRuleService;
    
    @Autowired
    private NacosConfigService nacosConfigService;
    
    /**
     * 同步规则到Sentinel
     */
    public void syncRulesToSentinel(String tenantId) {
        // 1. 从数据库加载规则
        List<FlowRule> flowRules = databaseRuleService.loadFlowRules(tenantId);
        List<IPFlowRule> ipRules = databaseRuleService.loadIPRules(tenantId);
        List<TenantFlowRule> tenantRules = databaseRuleService.loadTenantRules(tenantId);
        
        // 2. 转换为Sentinel规则格式
        List<com.alibaba.csp.sentinel.slots.block.flow.FlowRule> sentinelRules = 
            convertToSentinelRules(flowRules, ipRules, tenantRules);
        
        // 3. 推送到Nacos配置中心
        nacosConfigService.publishConfig(tenantId, sentinelRules);
        
        // 4. 更新本地规则缓存
        FlowRuleManager.loadRules(sentinelRules);
    }
}
```

## 7. 监控与统计

### 7.1 监控指标

#### 7.1.1 核心监控指标
- **QPS（每秒请求数）**：系统处理的请求频率
- **响应时间**：请求处理的平均响应时间
- **限流次数**：被限流的请求数量
- **成功率**：请求处理的成功率
- **并发线程数**：系统当前的并发处理线程数

#### 7.1.2 维度统计
- **租户维度**：按租户统计流量使用情况
- **接口维度**：按接口统计访问频率和性能
- **IP维度**：按IP地址统计访问模式
- **时间维度**：按时间段统计流量趋势

### 7.2 监控数据收集

#### 7.2.1 实时数据收集
```java
@Component
public class FlowControlMonitor {
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    /**
     * 记录请求指标
     */
    public void recordRequest(String tenantId, String resource, String clientIp, 
                             boolean success, long responseTime) {
        // 记录QPS
        Counter.builder("flow.control.requests")
            .tag("tenant", tenantId)
            .tag("resource", resource)
            .tag("status", success ? "success" : "blocked")
            .register(meterRegistry)
            .increment();
        
        // 记录响应时间
        Timer.builder("flow.control.response.time")
            .tag("tenant", tenantId)
            .tag("resource", resource)
            .register(meterRegistry)
            .record(responseTime, TimeUnit.MILLISECONDS);
        
        // 记录IP访问
        Counter.builder("flow.control.ip.requests")
            .tag("ip", clientIp)
            .tag("tenant", tenantId)
            .register(meterRegistry)
            .increment();
    }
}
```

#### 7.2.2 定时统计任务
```java
@Component
public class StatisticsScheduler {
    
    @Autowired
    private MonitorStatisticsService statisticsService;
    
    /**
     * 每分钟统计一次数据
     */
    @Scheduled(fixedRate = 60000)
    public void collectStatistics() {
        // 收集各租户的统计数据
        List<String> tenantIds = getTenantIds();
        
        for (String tenantId : tenantIds) {
            MonitorStatistics statistics = new MonitorStatistics();
            statistics.setTenantId(tenantId);
            statistics.setTotalRequests(getTotalRequests(tenantId));
            statistics.setBlockedRequests(getBlockedRequests(tenantId));
            statistics.setAvgResponseTime(getAvgResponseTime(tenantId));
            statistics.setMaxQps(getMaxQps(tenantId));
            statistics.setStatisticsTime(LocalDateTime.now());
            
            statisticsService.save(statistics);
        }
    }
}
```

### 7.3 监控面板

#### 7.3.1 Grafana监控面板配置
```json
{
  "dashboard": {
    "title": "流量控制系统监控",
    "panels": [
      {
        "title": "系统QPS",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(flow_control_requests_total[1m])",
            "legendFormat": "{{tenant}}-{{resource}}"
          }
        ]
      },
      {
        "title": "限流统计",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(rate(flow_control_requests_total{status=\"blocked\"}[5m]))",
            "legendFormat": "限流次数/秒"
          }
        ]
      },
      {
        "title": "响应时间分布",
        "type": "heatmap",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(flow_control_response_time_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

#### 7.3.2 告警规则配置
```yaml
groups:
  - name: flow_control_alerts
    rules:
      - alert: HighBlockRate
        expr: rate(flow_control_requests_total{status="blocked"}[5m]) / rate(flow_control_requests_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "流量控制限流率过高"
          description: "租户 {{ $labels.tenant }} 的限流率超过10%，当前值：{{ $value }}"
      
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(flow_control_response_time_bucket[5m])) > 1000
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "响应时间过长"
          description: "95%的请求响应时间超过1秒，当前值：{{ $value }}ms"
```

## 8. 部署架构

### 8.1 部署环境

#### 8.1.1 开发环境
- **单机部署**：所有服务部署在同一台机器
- **内存数据库**：使用H2内存数据库
- **本地缓存**：使用本地Redis实例
- **配置管理**：使用本地配置文件

#### 8.1.2 测试环境
- **容器化部署**：使用Docker容器部署
- **MySQL数据库**：独立的MySQL实例
- **Redis集群**：Redis主从配置
- **Nacos集群**：Nacos配置中心集群

#### 8.1.3 生产环境
- **Kubernetes部署**：使用K8s进行容器编排
- **高可用数据库**：MySQL主从+读写分离
- **Redis集群**：Redis Cluster模式
- **负载均衡**：Nginx + Spring Cloud Gateway
- **监控告警**：Prometheus + Grafana + AlertManager

### 8.2 Docker部署

#### 8.2.1 Docker Compose配置
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0.33
    container_name: flow-control-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: sentinel_flow_control
      MYSQL_USER: flow_user
      MYSQL_PASSWORD: flow_pass
    ports:
      - "33016:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - flow-control-network

  redis:
    image: redis:7-alpine
    container_name: flow-control-redis
    ports:
      - "63079:6379"
    volumes:
      - redis_data:/data
    networks:
      - flow-control-network

  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: flow-control-nacos
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_DB_NAME: nacos_config
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: root123
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    networks:
      - flow-control-network

  gateway-service:
    build:
      context: ./gateway-service
      dockerfile: Dockerfile
    container_name: flow-control-gateway
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      REDIS_HOST: redis
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - flow-control-network

  admin-service:
    build:
      context: ./flow-control-admin
      dockerfile: Dockerfile
    container_name: flow-control-admin
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      NACOS_SERVER_ADDR: nacos:8848
    ports:
      - "8081:8081"
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - flow-control-network

volumes:
  mysql_data:
  redis_data:

networks:
  flow-control-network:
    driver: bridge
```

#### 8.2.2 服务Dockerfile
```dockerfile
# Gateway Service Dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/gateway-service-1.0.0.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

```dockerfile
# Admin Service Dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/flow-control-admin-1.0.0.jar app.jar

EXPOSE 8081

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 8.3 Kubernetes部署

#### 8.3.1 命名空间配置
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: flow-control
```

#### 8.3.2 ConfigMap配置
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: flow-control-config
  namespace: flow-control
data:
  application.yml: |
    spring:
      datasource:
        url: *****************************************************
        username: flow_user
        password: flow_pass
      redis:
        host: redis-service
        port: 6379
      cloud:
        nacos:
          discovery:
            server-addr: nacos-service:8848
          config:
            server-addr: nacos-service:8848
```

#### 8.3.3 Deployment配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway-service
  namespace: flow-control
spec:
  replicas: 3
  selector:
    matchLabels:
      app: gateway-service
  template:
    metadata:
      labels:
        app: gateway-service
    spec:
      containers:
      - name: gateway-service
        image: flow-control/gateway-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: flow-control-config
```

#### 8.3.4 Service配置
```yaml
apiVersion: v1
kind: Service
metadata:
  name: gateway-service
  namespace: flow-control
spec:
  selector:
    app: gateway-service
  ports:
  - protocol: TCP
    port: 8080
    targetPort: 8080
  type: LoadBalancer
```

#### 8.3.5 Ingress配置
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: flow-control-ingress
  namespace: flow-control
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: flow-control.example.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: gateway-service
            port:
              number: 8080
      - path: /admin
        pathType: Prefix
        backend:
          service:
            name: admin-service
            port:
              number: 8081

### 8.4 高可用架构

#### 8.4.1 服务高可用
- **多实例部署**：每个服务部署多个实例
- **负载均衡**：使用Nginx或K8s Service进行负载均衡
- **健康检查**：配置服务健康检查和自动重启
- **熔断降级**：使用Sentinel实现服务熔断和降级

#### 8.4.2 数据高可用
- **MySQL主从复制**：配置MySQL主从复制和读写分离
- **Redis集群**：使用Redis Cluster实现数据分片和高可用
- **数据备份**：定期备份数据库和配置数据

#### 8.4.3 配置高可用
- **Nacos集群**：部署Nacos集群确保配置服务高可用
- **配置备份**：定期备份配置数据
- **配置版本管理**：支持配置版本回滚

## 9. 性能优化

### 9.1 系统性能指标

#### 9.1.1 性能目标
- **QPS处理能力**：单实例支持10,000+ QPS
- **响应时间**：P99响应时间 < 100ms
- **并发支持**：支持1000+并发连接
- **内存使用**：单实例内存使用 < 2GB
- **CPU使用率**：正常负载下CPU使用率 < 70%

#### 9.1.2 性能测试
```bash
# 使用JMeter进行性能测试
jmeter -n -t flow-control-test.jmx -l results.jtl -e -o report/

# 使用wrk进行压力测试
wrk -t12 -c400 -d30s --script=flow-control.lua http://localhost:8080/api/test
```

### 9.2 缓存优化

#### 9.2.1 多级缓存架构
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   本地缓存       │────▶│   Redis缓存      │────▶│   数据库         │
│ (Caffeine)      │     │                │     │   (MySQL)       │
│ - 规则缓存       │     │ - 规则数据       │     │ - 持久化数据     │
│ - 配置缓存       │     │ - 统计数据       │     │ - 历史数据       │
│ - 热点数据       │     │ - 会话数据       │     │                │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

#### 9.2.2 缓存策略实现
```java
@Service
public class CacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private final Cache<String, Object> localCache = Caffeine.newBuilder()
        .maximumSize(10000)
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build();
    
    /**
     * 多级缓存获取
     */
    public <T> T get(String key, Class<T> type, Supplier<T> loader) {
        // 1. 先查本地缓存
        T value = (T) localCache.getIfPresent(key);
        if (value != null) {
            return value;
        }
        
        // 2. 查Redis缓存
        value = (T) redisTemplate.opsForValue().get(key);
        if (value != null) {
            localCache.put(key, value);
            return value;
        }
        
        // 3. 从数据源加载
        value = loader.get();
        if (value != null) {
            redisTemplate.opsForValue().set(key, value, Duration.ofMinutes(30));
            localCache.put(key, value);
        }
        
        return value;
    }
}
```

### 9.3 数据库优化

#### 9.3.1 索引优化
```sql
-- 流量规则表索引
CREATE INDEX idx_flow_rule_tenant_resource ON flow_rule(tenant_id, resource_name);
CREATE INDEX idx_flow_rule_status_priority ON flow_rule(status, priority);

-- IP流量规则表索引
CREATE INDEX idx_ip_flow_rule_tenant_type ON ip_flow_rule(tenant_id, rule_type);
CREATE INDEX idx_ip_flow_rule_ip_status ON ip_flow_rule(ip_value, status);

-- 监控统计表索引
CREATE INDEX idx_monitor_tenant_time ON monitor_statistics(tenant_id, statistics_time);
CREATE INDEX idx_monitor_ip_time ON monitor_statistics(ip_address, statistics_time);
```

#### 9.3.2 分库分表策略
```java
@Configuration
public class ShardingConfig {
    
    /**
     * 按租户ID分表
     */
    @Bean
    public ShardingRuleConfiguration shardingRuleConfig() {
        ShardingRuleConfiguration config = new ShardingRuleConfiguration();
        
        // 流量控制日志按租户分表
        TableRuleConfiguration logTableRule = new TableRuleConfiguration("flow_control_log");
        logTableRule.setActualDataNodes("ds0.flow_control_log_${0..15}");
        logTableRule.setTableShardingStrategyConfig(
            new StandardShardingStrategyConfiguration("tenant_id", new TenantShardingAlgorithm()));
        
        config.getTableRuleConfigs().add(logTableRule);
        return config;
    }
}
```

### 9.4 JVM优化

#### 9.4.1 JVM参数配置
```bash
# 生产环境JVM参数
JAVA_OPTS="-Xms2g -Xmx4g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+UnlockExperimentalVMOptions \
  -XX:+UseStringDeduplication \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -Xloggc:/app/logs/gc.log \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=/app/logs/heapdump.hprof"
```

#### 9.4.2 内存监控
```java
@Component
public class MemoryMonitor {
    
    private final MeterRegistry meterRegistry;
    
    @EventListener
    @Async
    public void handleMemoryWarning(MemoryWarningEvent event) {
        // 记录内存告警
        Gauge.builder("jvm.memory.warning")
            .register(meterRegistry, event.getUsedMemory());
        
        // 触发内存清理
        System.gc();
        
        // 清理本地缓存
        localCache.invalidateAll();
    }
}
```

## 10. 安全设计

### 10.1 认证授权

#### 10.1.1 API认证
```java
@Component
public class ApiAuthenticationFilter implements GlobalFilter {
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 1. 提取API Key
        String apiKey = request.getHeaders().getFirst("X-API-Key");
        if (StringUtils.isEmpty(apiKey)) {
            return unauthorized(exchange);
        }
        
        // 2. 验证API Key
        if (!isValidApiKey(apiKey)) {
            return unauthorized(exchange);
        }
        
        // 3. 提取租户信息
        String tenantId = extractTenantId(apiKey);
        ServerHttpRequest mutatedRequest = request.mutate()
            .header("X-Tenant-Id", tenantId)
            .build();
        
        return chain.filter(exchange.mutate().request(mutatedRequest).build());
    }
}
```

#### 10.1.2 权限控制
```java
@PreAuthorize("hasRole('ADMIN') or hasPermission(#tenantId, 'TENANT_MANAGE')")
public ResponseEntity<FlowRuleDTO> createFlowRule(@PathVariable String tenantId, 
                                                  @RequestBody FlowRuleDTO ruleDTO) {
    // 创建流量规则
    return ResponseEntity.ok(flowRuleService.create(ruleDTO));
}
```

### 10.2 数据安全

#### 10.2.1 敏感数据加密
```java
@Entity
public class TenantInfo {
    
    @Column(name = "contact_phone")
    @Convert(converter = EncryptionConverter.class)
    private String contactPhone;
    
    @Column(name = "contact_email")
    @Convert(converter = EncryptionConverter.class)
    private String contactEmail;
}

@Converter
public class EncryptionConverter implements AttributeConverter<String, String> {
    
    @Override
    public String convertToDatabaseColumn(String attribute) {
        return AESUtil.encrypt(attribute);
    }
    
    @Override
    public String convertToEntityAttribute(String dbData) {
        return AESUtil.decrypt(dbData);
    }
}
```

#### 10.2.2 SQL注入防护
```java
@Repository
public class FlowRuleMapper {
    
    /**
     * 使用参数化查询防止SQL注入
     */
    @Select("SELECT * FROM flow_rule WHERE tenant_id = #{tenantId} AND status = #{status}")
    List<FlowRule> findByTenantAndStatus(@Param("tenantId") String tenantId, 
                                        @Param("status") Integer status);
}
```

### 10.3 网络安全

#### 10.3.1 HTTPS配置
```yaml
server:
  port: 8443
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12
    key-alias: flow-control
```

#### 10.3.2 跨域配置
```java
@Configuration
public class CorsConfig {
    
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.addAllowedOriginPattern("https://*.example.com");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        
        return new CorsWebFilter(source);
    }
}
```

## 11. 运维管理

### 11.1 日志管理

#### 11.1.1 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/app/logs/flow-control.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/app/logs/flow-control.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
    </springProfile>
    
    <logger name="com.example.flowcontrol" level="INFO"/>
    <logger name="com.alibaba.csp.sentinel" level="WARN"/>
    
    <root level="INFO">
        <appender-ref ref="FILE"/>
    </root>
</configuration>
```

#### 11.1.2 结构化日志
```java
@Component
public class StructuredLogger {
    
    private final Logger logger = LoggerFactory.getLogger(StructuredLogger.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public void logFlowControlEvent(String tenantId, String resource, String event, 
                                   Map<String, Object> details) {
        try {
            Map<String, Object> logData = new HashMap<>();
            logData.put("timestamp", Instant.now().toString());
            logData.put("tenantId", tenantId);
            logData.put("resource", resource);
            logData.put("event", event);
            logData.put("details", details);
            
            logger.info(objectMapper.writeValueAsString(logData));
        } catch (Exception e) {
            logger.error("Failed to log structured event", e);
        }
    }
}
```

### 11.2 健康检查

#### 11.2.1 自定义健康检查
```java
@Component
public class FlowControlHealthIndicator implements HealthIndicator {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private DataSource dataSource;
    
    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();
        
        try {
            // 检查Redis连接
            redisTemplate.opsForValue().get("health-check");
            builder.withDetail("redis", "UP");
            
            // 检查数据库连接
            try (Connection connection = dataSource.getConnection()) {
                if (connection.isValid(5)) {
                    builder.withDetail("database", "UP");
                } else {
                    builder.withDetail("database", "DOWN");
                    return builder.down().build();
                }
            }
            
            // 检查Sentinel状态
            if (SentinelResourceUtils.getResourcesAmount() > 0) {
                builder.withDetail("sentinel", "UP");
            } else {
                builder.withDetail("sentinel", "DOWN");
                return builder.down().build();
            }
            
            return builder.up().build();
            
        } catch (Exception e) {
            return builder.down(e).build();
        }
    }
}
```

### 11.3 配置管理

#### 11.3.1 配置热更新
```java
@Component
@RefreshScope
public class FlowControlConfig {
    
    @Value("${flow.control.enabled:true}")
    private boolean enabled;
    
    @Value("${flow.control.default.qps:100}")
    private int defaultQps;
    
    @NacosValue(value = "${flow.control.rules.sync.interval:60}", autoRefreshed = true)
    private int syncInterval;
    
    @NacosConfigListener(dataId = "flow-control-rules", groupId = "DEFAULT_GROUP")
    public void onConfigChange(String newContent) {
        logger.info("Flow control rules updated: {}", newContent);
        // 重新加载规则
        reloadRules(newContent);
    }
}
```

## 12. 总结

### 12.1 系统特点

本流量控制系统具有以下核心特点：

1. **多维度流量控制**：支持IP、租户、接口三个维度的精细化流量管控
2. **高性能架构**：基于Spring Cloud Gateway和Sentinel构建，支持高并发场景
3. **灵活配置管理**：支持动态规则配置和实时同步更新
4. **完善监控体系**：提供实时监控、统计分析和告警功能
5. **高可用设计**：支持集群部署、故障转移和数据备份
6. **安全可靠**：完善的认证授权、数据加密和网络安全机制

### 12.2 技术优势

1. **成熟技术栈**：采用Spring Boot、Spring Cloud等成熟框架
2. **云原生支持**：支持Docker容器化和Kubernetes部署
3. **可扩展架构**：模块化设计，易于扩展和维护
4. **标准化接口**：RESTful API设计，易于集成
5. **丰富的监控**：集成Prometheus、Grafana等监控工具

### 12.3 应用场景

本系统适用于以下场景：

1. **微服务架构**：为微服务提供统一的流量控制能力
2. **多租户平台**：支持多租户的差异化流量管控
3. **API网关**：作为API网关的流量控制组件
4. **高并发系统**：保护后端服务免受流量冲击
5. **SaaS平台**：为SaaS平台提供租户级别的资源控制

### 12.4 未来规划

1. **智能化流控**：基于机器学习的智能流量预测和自适应限流
2. **更多维度支持**：支持用户级、地域级等更多维度的流量控制
3. **可视化增强**：提供更丰富的可视化监控和配置界面
4. **性能优化**：持续优化系统性能，支持更大规模的并发
5. **生态集成**：与更多开源组件和云服务集成

---

**文档版本**: v1.0  
**创建日期**: 2025-01-15  
**最后更新**: 2025-01-15  
**文档状态**: 正式版