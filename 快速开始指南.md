# 🚀 快速开始指南

本指南将帮助你在5分钟内启动Sentinel流量控制系统的本地开发环境。

## 📋 准备工作清单

在开始之前，请确保你已经准备好以下内容：

- [ ] Java 8+ 已安装并配置环境变量
- [ ] Maven 3.6+ 已安装
- [ ] 下载了 `nacos-server-2.2.4.zip`
- [ ] 下载了 `sentinel-dashboard-1.8.6.jar`
- [ ] （可选）Redis已安装

## 🔧 第一步：安装依赖服务

### 1.1 安装Nacos

1. 创建目录 `D:\nacos`
2. 将 `nacos-server-2.2.4.zip` 解压到该目录
3. 确保解压后的结构为：
   ```
   D:\nacos\
   ├── bin\
   ├── conf\
   ├── data\
   └── ...
   ```

### 1.2 安装Sentinel

1. 创建目录 `D:\sentinel`
2. 将 `sentinel-dashboard-1.8.6.jar` 放入该目录
3. 确保文件路径为：`D:\sentinel\sentinel-dashboard-1.8.6.jar`

### 1.3 安装Redis（可选但推荐）

**选项A：Windows安装版（推荐）**
- 下载：https://github.com/microsoftarchive/redis/releases
- 安装后Redis会作为Windows服务运行

**选项B：便携版**
- 下载Redis便携版并解压
- 手动运行 `redis-server.exe`

## 🚀 第二步：一键启动所有服务

在项目根目录下，双击运行：

```
start-all-services.bat
```

这个脚本会自动：
1. ✅ 检查并启动Redis
2. ✅ 启动Nacos配置中心
3. ✅ 启动Sentinel控制台
4. ✅ 启动Gateway网关服务

## 🌐 第三步：验证服务

启动完成后，你可以访问以下地址：

| 服务 | 地址 | 用户名/密码 | 说明 |
|------|------|-------------|------|
| **Nacos控制台** | http://localhost:8848/nacos | nacos/nacos | 配置中心和服务发现 |
| **Sentinel控制台** | http://localhost:8858 | sentinel/sentinel | 流量控制规则管理 |
| **Gateway服务** | http://localhost:8080 | - | API网关服务 |
| **Redis** | localhost:6379 | - | 缓存和队列服务 |

## 🔍 第四步：检查服务状态

运行状态检查脚本：

```
check-services-status.bat
```

你应该看到所有服务都显示为 ✅ 正常运行。

## 🧪 第五步：测试流量控制

### 5.1 配置流量控制规则

1. 访问Sentinel控制台：http://localhost:8858
2. 登录（sentinel/sentinel）
3. 等待Gateway服务出现在"机器列表"中
4. 配置流量控制规则

### 5.2 测试API请求

发送测试请求到Gateway：

```bash
# 测试正常请求
curl http://localhost:8080/api/test

# 测试高频请求（触发限流）
for i in {1..20}; do curl http://localhost:8080/api/test; done
```

## 🛑 停止服务

当你完成测试后，运行：

```
stop-all-services.bat
```

## ❓ 常见问题

### Q1: 服务启动失败怎么办？

**A1**: 运行 `check-services-status.bat` 查看具体哪个服务失败，然后：
- 检查端口是否被占用
- 确认Java环境是否正确
- 查看错误日志

### Q2: 无法访问控制台？

**A2**: 
- 确认服务已完全启动（首次启动需要1-2分钟）
- 检查防火墙设置
- 确认端口8848和8858未被占用

### Q3: Gateway服务在Sentinel中不显示？

**A3**:
- 确认Gateway服务已启动
- 发送几个测试请求到Gateway
- 检查Gateway的application.yml配置

### Q4: Redis连接失败？

**A4**:
- 确认Redis服务已启动
- 检查端口6379是否可用
- 如果使用便携版，确保redis-server.exe正在运行

## 📚 下一步

恭喜！你已经成功启动了Sentinel流量控制系统。接下来你可以：

1. 📖 阅读 [Sentinel和Nacos安装文档.md](./Sentinel和Nacos安装文档.md) 了解详细配置
2. 🔧 探索Sentinel控制台的各种流量控制规则
3. 📊 在Nacos中配置服务发现和配置管理
4. 🚀 开始开发你的微服务应用

## 🆘 获取帮助

如果遇到问题，请：
1. 查看项目根目录下的详细文档
2. 检查服务日志文件
3. 确认所有依赖服务都已正确安装

---

**提示**: 建议将这些服务设置为开机自启动，这样每次开发时就不需要手动启动了。