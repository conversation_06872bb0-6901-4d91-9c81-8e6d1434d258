# 租户QPS限流测试脚本使用说明

## 1. 脚本功能介绍

`tenant_qps_test_updated.py` 是一个专门用于测试租户级别QPS限流功能的Python脚本。该脚本基于用户提供的5条真实租户限流规则数据，能够模拟高并发请求场景，验证不同控制行为下的限流效果。

### 主要功能特性：
- **多租户并发测试**：支持同时测试多个租户的限流规则
- **多种控制行为**：支持快速失败、预热启动、排队等待、综合限流四种模式
- **详细性能分析**：提供响应时间、成功率、限流效果等详细统计
- **灵活配置**：支持自定义测试参数（持续时间、RPS、测试端点等）
- **完整报告**：生成JSON格式的详细测试报告
- **实时监控**：测试过程中实时显示进度和结果

## 2. 租户规则详细说明

脚本包含以下5个租户的限流规则配置：

### 2.1 租户1 - 默认QPS限流
- **租户ID**: `tenant1`
- **规则名称**: 租户1默认QPS限流
- **QPS限制**: 5
- **控制行为**: 快速失败 (0)
- **特点**: 最基础的限流模式，超过限制立即拒绝
- **适用场景**: 对响应时间要求严格的业务

### 2.2 租户2 - 预热限流
- **租户ID**: `tenant2`
- **规则名称**: 租户2预热限流
- **QPS限制**: 5
- **控制行为**: 预热启动 (1)
- **预热时间**: 10秒
- **特点**: 系统启动后逐渐达到目标QPS，避免冷启动冲击
- **适用场景**: 需要预热的缓存或数据库连接池场景

### 2.3 租户3 - 排队限流
- **租户ID**: `tenant3`
- **规则名称**: 租户3排队限流
- **QPS限制**: 5
- **控制行为**: 排队等待 (2)
- **最大排队时间**: 5000ms
- **特点**: 超过限制的请求进入队列等待，而不是立即拒绝
- **适用场景**: 可以容忍一定延迟但希望提高成功率的业务

### 2.4 租户4 - 线程数限流
- **租户ID**: `tenant4`
- **规则名称**: 租户4线程数限流
- **QPS限制**: 5
- **控制行为**: 快速失败 (0)
- **优先级**: 3 (最高)
- **特点**: 基于线程数的限流，快速失败模式
- **适用场景**: 资源敏感的高优先级业务

### 2.5 租户5 - 综合限流
- **租户ID**: `tenant5`
- **规则名称**: 租户5综合限流
- **QPS限制**: 5
- **控制行为**: 预热+排队 (3)
- **预热时间**: 15秒
- **最大排队时间**: 3000ms
- **特点**: 结合预热启动和排队等待的综合模式
- **适用场景**: 复杂业务场景，需要平衡性能和稳定性

## 3. 使用方法和命令行参数

### 3.1 基本使用

```bash
# 使用默认参数运行所有租户测试
python tenant_qps_test_updated.py

# 指定服务器地址
python tenant_qps_test_updated.py --url http://localhost:8080

# 自定义测试参数
python tenant_qps_test_updated.py --duration 60 --rps 1000 --endpoint /api/flow-control
```

### 3.2 命令行参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--url` | string | `http://localhost:8088` | 目标服务器地址 |
| `--duration` | int | `30` | 测试持续时间（秒） |
| `--rps` | int | `500` | 每秒请求数 |
| `--endpoint` | string | `/api/test` | 测试端点路径 |
| `--output` | string | 自动生成 | 输出报告文件名 |
| `--rules-only` | flag | - | 只显示租户规则，不执行测试 |
| `--tenant` | string | - | 只测试指定租户ID |

### 3.3 特殊用法

```bash
# 只查看租户规则配置
python tenant_qps_test_updated.py --rules-only

# 只测试特定租户
python tenant_qps_test_updated.py --tenant tenant1

# 高强度测试
python tenant_qps_test_updated.py --duration 120 --rps 2000

# 指定输出文件
python tenant_qps_test_updated.py --output my_test_report.json
```

## 4. 测试示例

### 4.1 完整测试示例

```bash
# 运行完整的租户限流测试
python tenant_qps_test_updated.py --url http://localhost:8088 --duration 60 --rps 800
```

**预期输出**：
```
================================================================================
租户总QPS限流测试开始
测试时间: 2024-01-15 14:30:00
服务地址: http://localhost:8088
测试配置: 60秒, 800 RPS
测试端点: /api/test
租户规则数量: 5
================================================================================

[1/5] 测试租户: tenant1 - 租户1默认QPS限流
租户ID: tenant1
QPS限制: 5
控制行为: 快速失败
测试配置: 60秒, 800 RPS
预期成功请求数: 300

等待 48000 个请求完成...

=== 测试结果 ===
总请求数: 48000
成功请求: 298 (0.6%)
被限流: 47702 (99.4%)
错误请求: 0 (0.0%)
实际QPS: 800.00
成功QPS: 4.97 (限制: 5)
...
```

### 4.2 单租户测试示例

```bash
# 测试预热启动模式的租户2
python tenant_qps_test_updated.py --tenant tenant2 --duration 30 --rps 100
```

### 4.3 规则查看示例

```bash
# 查看所有租户规则
python tenant_qps_test_updated.py --rules-only
```

**输出**：
```
租户限流规则列表 (基于最新数据):
================================================================================
1. 租户1默认QPS限流 (tenant1)
   QPS限制: 5, 控制行为: 0 (快速失败)
   优先级: 1, 状态: 启用
   描述: 租户1的默认QPS限流规则
   更新时间: 2025-08-26 14:40:44

2. 租户2预热限流 (tenant2)
   QPS限制: 5, 控制行为: 1 (预热启动)
   预热时间: 10秒
   优先级: 2, 状态: 启用
   描述: 租户2的预热限流规则
   更新时间: 2025-08-26 14:40:48
...
```

## 5. 输出报告说明

### 5.1 控制台输出

测试过程中会实时显示：
- 测试进度和当前租户信息
- 每个租户的详细测试结果
- 控制行为分析
- 最终汇总统计

### 5.2 JSON报告文件

自动生成的JSON报告包含以下结构：

```json
{
  "test_time": "2024-01-15T14:30:00",
  "base_url": "http://localhost:8088",
  "test_config": {
    "duration": 60,
    "requests_per_second": 800,
    "test_endpoint": "/api/test"
  },
  "tenant_rules": [...],
  "results": [
    {
      "rule_info": {
        "rule_name": "租户1默认QPS限流",
        "tenant_id": "tenant1",
        "qps_limit": 5,
        "control_behavior": 0,
        "control_behavior_name": "快速失败"
      },
      "test_config": {...},
      "actual_results": {
        "total_requests": 48000,
        "success_requests": 298,
        "blocked_requests": 47702,
        "error_requests": 0,
        "actual_qps": 800.00,
        "success_qps": 4.97
      },
      "statistics": {
        "success_rate": 0.6,
        "block_rate": 99.4,
        "avg_response_time": 0.025,
        "p95_response_time": 0.045
      },
      "analysis": {
        "qps_accuracy": 0.6,
        "limit_effectiveness": true,
        "behavior_analysis": {...}
      }
    }
  ],
  "summary": {
    "total_tenant_tests": 5,
    "successful_tests": 5,
    "total_requests": 240000,
    "overall_success_rate": 1.2,
    "overall_block_rate": 98.8,
    "average_qps_accuracy_deviation": 2.1,
    "behavior_statistics": {...}
  }
}
```

### 5.3 关键指标说明

- **QPS准确度偏差**: 实际成功QPS与限制QPS的偏差百分比
- **限流有效率**: 成功触发限流的租户比例
- **控制行为分析**: 针对不同控制行为的专门分析
- **响应时间统计**: 包括平均值、中位数、P95等

## 6. 注意事项和故障排除

### 6.1 环境要求

- **Python版本**: 3.7或更高版本
- **依赖库**: `aiohttp`, `asyncio`（Python标准库）
- **网络**: 确保能访问目标服务器
- **权限**: 确保有文件写入权限（用于保存报告）

### 6.2 安装依赖

```bash
# 安装必要的依赖
pip install aiohttp

# 或使用requirements.txt
echo "aiohttp>=3.8.0" > requirements.txt
pip install -r requirements.txt
```

### 6.3 常见问题及解决方案

#### 问题1: 连接超时
```
Error: Cannot connect to host localhost:8088
```
**解决方案**:
- 检查目标服务是否启动
- 确认端口号是否正确
- 检查防火墙设置
- 使用 `--url` 参数指定正确的服务地址

#### 问题2: 权限被拒绝
```
PermissionError: [Errno 13] Permission denied
```
**解决方案**:
- 检查当前目录的写入权限
- 使用 `--output` 参数指定有权限的目录
- 以管理员权限运行脚本

#### 问题3: 内存不足
```
MemoryError: Unable to allocate array
```
**解决方案**:
- 降低 `--rps` 参数值
- 减少 `--duration` 测试时间
- 使用 `--tenant` 参数单独测试租户

#### 问题4: 测试结果异常
```
所有请求都成功，没有触发限流
```
**解决方案**:
- 检查服务端限流配置是否生效
- 确认请求头中的租户ID是否正确传递
- 验证服务端是否正确识别租户信息
- 检查服务端日志确认限流规则是否加载

### 6.4 性能调优建议

1. **合理设置RPS**: 根据服务器性能调整请求频率
2. **分批测试**: 对于大量租户，可以分批进行测试
3. **监控资源**: 测试过程中监控CPU和内存使用情况
4. **网络优化**: 确保网络延迟较低，避免影响测试结果

### 6.5 测试最佳实践

1. **预热服务**: 测试前先进行小规模预热
2. **隔离环境**: 在专门的测试环境中进行
3. **多次测试**: 进行多轮测试确保结果稳定性
4. **结果对比**: 对比不同配置下的测试结果
5. **日志分析**: 结合服务端日志分析限流效果

### 6.6 扩展使用

脚本支持以下扩展：

1. **自定义规则**: 修改 `create_tenant_rules()` 函数添加新规则
2. **新增指标**: 在分析函数中添加自定义统计指标
3. **报告格式**: 扩展报告生成功能支持其他格式（CSV、HTML等）
4. **集成测试**: 集成到CI/CD流水线中进行自动化测试

---

## 联系支持

如果在使用过程中遇到问题，请：
1. 检查本文档的故障排除部分
2. 查看脚本生成的错误日志
3. 确认环境配置是否正确
4. 联系技术支持团队获取帮助

**最后更新**: 2024年1月15日
**脚本版本**: tenant_qps_test_updated.py v1.0