<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.IPFlowRuleMapper">

	<!-- 结果映射 -->
	<resultMap id="BaseResultMap" type="com.example.common.entity.IPFlowRule">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
		<result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
		<result column="rule_type" property="ruleType" jdbcType="INTEGER"/>
		<result column="ip_value" property="ipValue" jdbcType="VARCHAR"/>
		<result column="qps_limit" property="qpsLimit" jdbcType="INTEGER"/>
		<result column="priority" property="priority" jdbcType="INTEGER"/>
		<result column="description" property="description" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="create_by" property="createBy" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="deleted" property="deleted" jdbcType="INTEGER"/>
	</resultMap>

	<!-- 基础字段 -->
	<sql id="Base_Column_List">
        id, rule_name, tenant_id, rule_type, ip_value, qps_limit,
        priority, description, status, create_by, create_time, update_by, update_time, deleted
	</sql>

	<!-- IPFlowRuleVO结果映射 -->
	<resultMap id="IPFlowRuleVOResultMap" type="com.example.admin.vo.IPFlowRuleVO">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
		<result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
		<result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
		<result column="rule_type" property="ruleType" jdbcType="VARCHAR"/>
		<result column="rule_type_name" property="ruleTypeName" jdbcType="VARCHAR"/>
		<result column="ip_value" property="ipValue" jdbcType="VARCHAR"/>
		<result column="qps_limit" property="qpsLimit" jdbcType="INTEGER"/>

		<result column="priority" property="priority" jdbcType="INTEGER"/>
		<result column="description" property="description" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="status_name" property="statusName" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>

	<!-- 分页查询IP流控规则VO（带租户名称和枚举值名称） -->
	<select id="selectIPFlowRuleVOPage" resultMap="IPFlowRuleVOResultMap">
        SELECT
            ir.id,
            ir.rule_name,
            ir.tenant_id,
            COALESCE(ti.tenant_name, ir.tenant_id) as tenant_name,
            ir.rule_type,
            CASE ir.rule_type
                WHEN 'SINGLE_IP' THEN '单个IP'
                WHEN 'IP_RANGE' THEN 'IP范围'
                WHEN 'IP_CIDR' THEN 'IP段'
                WHEN 'WILDCARD' THEN '通配符'
                ELSE '未知'
            END as rule_type_name,
            ir.ip_value,
            ir.qps_limit,
            ir.priority,
            ir.description,
            ir.status,
            CASE ir.status
                WHEN 0 THEN '禁用'
                WHEN 1 THEN '启用'
                ELSE '未知'
            END as status_name,
            ir.create_time,
            ir.update_time
        FROM ip_flow_rule ir
        LEFT JOIN tenant_info ti ON ir.tenant_id = ti.tenant_id
        WHERE ir.deleted = 0
		<if test="tenantId != null and tenantId != ''">
            AND ir.tenant_id = #{tenantId}
		</if>
		<if test="ruleType != null and ruleType != ''">
            AND ir.rule_type = #{ruleType}
		</if>
		<if test="status != null">
            AND ir.status = #{status}
		</if>
        ORDER BY ir.priority ASC, ir.create_time DESC
	</select>

	<!-- 分页查询IP流控规则 -->
	<select id="selectIPFlowRulePage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ip_flow_rule
        WHERE deleted = 0
	<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
	</if>
	<if test="ruleName != null and ruleName != ''">
            AND rule_name LIKE CONCAT('%', #{ruleName}, '%')
	</if>
	<if test="ruleType != null">
            AND rule_type = #{ruleType}
	</if>
	<if test="status != null">
            AND status = #{status}
	</if>
	<if test="ipValue != null and ipValue != ''">
            AND ip_value LIKE CONCAT('%', #{ipValue}, '%')
	</if>
        ORDER BY priority ASC, create_time DESC
</select>

<!-- 根据租户ID查询IP流控规则 -->
<select id="selectByTenantId" resultMap="IPFlowRuleVOResultMap">
        SELECT
            ir.id,
            ir.rule_name,
            ir.tenant_id,
            COALESCE(ti.tenant_name, ir.tenant_id) as tenant_name,
            ir.rule_type,
            CASE ir.rule_type
                WHEN 'SINGLE_IP' THEN '单个IP'
                WHEN 'IP_RANGE' THEN 'IP范围'
                WHEN 'IP_CIDR' THEN 'IP段'
                WHEN 'CIDR' THEN 'IP段'
                WHEN 'WILDCARD' THEN '通配符'
                ELSE '未知'
            END as rule_type_name,
            ir.ip_value,
            ir.qps_limit,
            ir.priority,
            ir.description,
            ir.status,
            CASE ir.status
                WHEN 0 THEN '禁用'
                WHEN 1 THEN '启用'
                ELSE '未知'
            END as status_name,
            ir.create_time,
            ir.update_time
        FROM ip_flow_rule ir
        LEFT JOIN tenant_info ti ON ir.tenant_id = ti.tenant_id
        WHERE ir.tenant_id = #{tenantId} AND ir.deleted = 0
	<if test="status != null">
            AND ir.status = #{status}
	</if>
        ORDER BY ir.priority ASC, ir.create_time DESC
	<if test="limit != null">
            LIMIT #{limit}
	</if>
</select>

<!-- 根据规则名称查询（用于重名检查） -->
<select id="selectByRuleName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ip_flow_rule
        WHERE rule_name = #{ruleName} AND deleted = 0
<if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
</if>
        LIMIT 1
</select>

<!-- 根据IP值查询规则 -->
<select id="selectByIpValue" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ip_flow_rule
        WHERE ip_value = #{ipValue} AND deleted = 0
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
<if test="status != null">
            AND status = #{status}
</if>
        ORDER BY priority ASC, create_time DESC
<if test="limit != null">
            LIMIT #{limit}
</if>
</select>

<!-- 批量更新规则状态 -->
<update id="batchUpdateStatus">
        UPDATE ip_flow_rule
        SET status = #{status}, update_time = NOW()
        WHERE id IN
<foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
</foreach>
        AND deleted = 0
</update>

<!-- 统计租户IP规则数量 -->
<select id="countByTenantId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ip_flow_rule
        WHERE tenant_id = #{tenantId} AND deleted = 0
<if test="status != null">
            AND status = #{status}
</if>
</select>

<!-- 查询启用的IP规则 -->
<select id="selectEnabledRules" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ip_flow_rule
        WHERE status = 1 AND deleted = 0
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
        ORDER BY priority ASC, create_time DESC
<if test="limit != null">
            LIMIT #{limit}
</if>
</select>

<!-- 统计规则类型分布 -->
<select id="selectRuleTypeStatistics" resultType="java.util.Map">
        SELECT 
            rule_type,
            COUNT(*) as count,
            CASE rule_type
                WHEN 1 THEN '单个IP'
                WHEN 2 THEN 'IP范围'
                WHEN 3 THEN 'CIDR'
                ELSE '未知'
            END as type_name
        FROM ip_flow_rule
        WHERE deleted = 0
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
        GROUP BY rule_type
        ORDER BY rule_type
</select>

<!-- 统计租户IP规则分布 -->
<select id="selectTenantIPRuleStatistics" resultType="java.util.Map">
        SELECT 
            ifr.tenant_id,
            ti.tenant_name,
            COUNT(*) as rule_count,
            SUM(CASE WHEN ifr.status = 1 THEN 1 ELSE 0 END) as enabled_count
        FROM ip_flow_rule ifr
        LEFT JOIN tenant_info ti ON ifr.tenant_id = ti.tenant_id
        WHERE ifr.deleted = 0
        GROUP BY ifr.tenant_id, ti.tenant_name
        ORDER BY rule_count DESC
<if test="limit != null">
            LIMIT #{limit}
</if>
</select>

<!-- 批量插入IP规则 -->
<insert id="batchInsert">
        INSERT INTO ip_flow_rule (
            rule_name, tenant_id, rule_type, ip_value, qps_limit,
            priority, description, status, create_by, create_time, update_by, update_time, deleted
        ) VALUES
<foreach collection="list" item="item" separator=",">
            (
                #{item.ruleName}, #{item.tenantId}, #{item.ruleType}, #{item.ipValue},
                #{item.qpsLimit}, #{item.priority}, #{item.description},
                #{item.status}, #{item.createBy}, NOW(), #{item.updateBy}, NOW(), 0
            )
</foreach>
</insert>

<!-- 检查规则名称是否存在 -->
<select id="existsByRuleName" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM ip_flow_rule
        WHERE rule_name = #{ruleName} AND deleted = 0
<if test="excludeId != null">
            AND id != #{excludeId}
</if>
</select>

<!-- 检查IP值是否存在 -->
<select id="existsByIpValue" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ip_flow_rule
        WHERE ip_value = #{ipValue} AND deleted = 0
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
<if test="excludeId != null">
            AND id != #{excludeId}
</if>
</select>

<!-- 查询IP匹配的规则（用于流控判断） -->
<select id="selectMatchingRules" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ip_flow_rule
        WHERE status = 1 AND deleted = 0
<if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
</if>
        AND (
            (rule_type = 1 AND ip_value = #{clientIp})
            OR (rule_type = 2 AND #{clientIp} BETWEEN SUBSTRING_INDEX(ip_value, '-', 1) AND SUBSTRING_INDEX(ip_value, '-', -1))
            OR (rule_type = 3 AND INET_ATON(#{clientIp}) &amp; INET_ATON(SUBSTRING_INDEX(ip_value, '/', -1)) = INET_ATON(SUBSTRING_INDEX(ip_value, '/', 1)))
        )
        ORDER BY priority ASC
</select>

</mapper>