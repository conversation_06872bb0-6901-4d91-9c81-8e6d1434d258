package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.Result;
import com.example.admin.dto.SystemConfigDTO;
import com.example.admin.service.SystemConfigService;
import com.example.admin.vo.SystemConfigVO;
import com.example.common.entity.SystemConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 系统配置控制器 提供系统配置的CRUD操作、批量管理、统计分析等功能
 */
@Tag(name = "系统配置管理")
@RestController
@RequestMapping("/api/system-config")
@Validated
public class SystemConfigController {

	private static final Logger log = LoggerFactory.getLogger(SystemConfigController.class);

	@Autowired
	private SystemConfigService systemConfigService;

	/**
	 * 分页查询系统配置
	 */
	@Operation(summary = "分页查询系统配置")
	@GetMapping
	public Result<Page<SystemConfigVO>> pageSystemConfig(
			@Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
			@Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
			@Parameter(description = "配置键") @RequestParam(required = false) String configKey,
			@Parameter(description = "配置类型") @RequestParam(required = false) String configType,
			@Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
		try {
			Page<SystemConfigVO> pageParam = new Page<>(page, size);
			Page<SystemConfigVO> result = systemConfigService.selectSystemConfigPage(pageParam, configKey, configType,
					tenantId, null);
			return Result.success(result);
		} catch (Exception e) {
			log.error("分页查询系统配置失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 根据ID查询系统配置
	 */
	@Operation(summary = "根据ID查询系统配置")
	@GetMapping("/{id}")
	public Result<SystemConfigVO> getSystemConfigById(@Parameter(description = "配置ID") @PathVariable @NotNull Long id) {
		try {
			SystemConfigVO result = systemConfigService.getSystemConfigById(id);
			if (result == null) {
				return Result.error("配置不存在");
			}
			return Result.success(result);
		} catch (Exception e) {
			log.error("根据ID查询系统配置失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 根据配置键查询系统配置
	 */
	@Operation(summary = "根据配置键查询系统配置")
	@GetMapping("/key/{configKey}")
	public Result<SystemConfigVO> getSystemConfigByKey(
			@Parameter(description = "配置键") @PathVariable @NotNull String configKey) {
		try {
			SystemConfigVO result = systemConfigService.getSystemConfigByKey(configKey, null);
			if (result == null) {
				return Result.error("配置不存在");
			}
			return Result.success(result);
		} catch (Exception e) {
			log.error("根据配置键查询系统配置失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 根据类型查询系统配置
	 */
	@Operation(summary = "根据类型查询系统配置")
	@GetMapping("/type/{configType}")
	public Result<List<SystemConfigVO>> getSystemConfigByType(
			@Parameter(description = "配置类型") @PathVariable @NotNull String configType) {
		try {
			List<SystemConfigVO> result = systemConfigService.getSystemConfigsByType(configType, null, null, null);
			return Result.success(result);
		} catch (Exception e) {
			log.error("根据类型查询系统配置失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 根据租户ID查询系统配置
	 */
	@Operation(summary = "根据租户ID查询系统配置")
	@GetMapping("/tenant/{tenantId}")
	public Result<List<SystemConfigVO>> getSystemConfigByTenantId(
			@Parameter(description = "租户ID") @PathVariable @NotNull String tenantId) {
		try {
			List<SystemConfigVO> result = systemConfigService.getSystemConfigsByTenantId(tenantId, null, null, null);
			return Result.success(result);
		} catch (Exception e) {
			log.error("根据租户ID查询系统配置失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 创建系统配置
	 */
	@Operation(summary = "创建系统配置")
	@PostMapping
	public Result<String> createSystemConfig(
			@Parameter(description = "系统配置信息") @RequestBody @Valid SystemConfigDTO systemConfigDTO) {
		try {
			boolean result = systemConfigService.createSystemConfig(systemConfigDTO);
			if (result) {
				return Result.success("创建成功");
			} else {
				return Result.error("创建失败");
			}
		} catch (Exception e) {
			log.error("创建系统配置失败", e);
			return Result.error("创建失败: " + e.getMessage());
		}
	}

	/**
	 * 更新系统配置
	 */
	@Operation(summary = "更新系统配置")
	@PutMapping("/{id}")
	public Result<String> updateSystemConfig(@Parameter(description = "配置ID") @PathVariable @NotNull Long id,
			@Parameter(description = "系统配置信息") @RequestBody @Valid SystemConfigDTO systemConfigDTO) {
		try {
			boolean result = systemConfigService.updateSystemConfig(id, systemConfigDTO);
			if (result) {
				return Result.success("更新成功");
			} else {
				return Result.error("更新失败");
			}
		} catch (Exception e) {
			log.error("更新系统配置失败", e);
			return Result.error("更新失败: " + e.getMessage());
		}
	}

	/**
	 * 删除系统配置
	 */
	@Operation(summary = "删除系统配置")
	@DeleteMapping("/{id}")
	public Result<String> deleteSystemConfig(@Parameter(description = "配置ID") @PathVariable @NotNull Long id) {
		try {
			boolean result = systemConfigService.deleteSystemConfig(id);
			if (result) {
				return Result.success("删除成功");
			} else {
				return Result.error("删除失败");
			}
		} catch (Exception e) {
			log.error("删除系统配置失败", e);
			return Result.error("删除失败: " + e.getMessage());
		}
	}

	/**
	 * 批量删除系统配置
	 */
	@Operation(summary = "批量删除系统配置")
	@DeleteMapping("/batch")
	public Result<String> batchDeleteSystemConfig(
			@Parameter(description = "配置ID列表") @RequestBody @NotEmpty List<Long> ids) {
		try {
			boolean result = systemConfigService.batchDeleteSystemConfigs(ids);
			if (result) {
				return Result.success("批量删除成功");
			} else {
				return Result.error("批量删除失败");
			}
		} catch (Exception e) {
			log.error("分页查询系统配置失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 更新配置状态
	 */
	@Operation(summary = "更新配置状态")
	@PutMapping("/{id}/status")
	public Result<String> updateConfigStatus(@Parameter(description = "配置ID") @PathVariable @NotNull Long id,
			@Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer status) {
		try {
			boolean result = systemConfigService.updateConfigStatus(id, status);
			if (result) {
				String message = status == 1 ? "启用成功" : "禁用成功";
				return Result.success(message);
			} else {
				return Result.error("操作失败");
			}
		} catch (Exception e) {
			log.error("更新配置状态失败", e);
			return Result.error("操作失败: " + e.getMessage());
		}
	}

	/**
	 * 批量更新配置状态
	 */
	@Operation(summary = "批量更新配置状态")
	@PutMapping("/batch/status")
	public Result<String> batchUpdateConfigStatus(
			@Parameter(description = "配置ID列表") @RequestBody @NotEmpty List<Long> ids,
			@Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer status) {
		try {
			boolean result = systemConfigService.batchUpdateConfigStatus(ids, status);
			if (result) {
				String message = status == 1 ? "批量启用成功" : "批量禁用成功";
				return Result.success(message);
			} else {
				return Result.error("批量操作失败");
			}
		} catch (Exception e) {
			log.error("批量更新配置状态失败", e);
			return Result.error("批量操作失败: " + e.getMessage());
		}
	}

	/**
	 * 检查配置键是否存在
	 */
	@Operation(summary = "检查配置键是否存在")
	@GetMapping("/exists/{configKey}")
	public Result<Boolean> existsByConfigKey(@Parameter(description = "配置键") @PathVariable @NotNull String configKey) {
		try {
			boolean result = systemConfigService.existsByConfigKey(configKey, null, null);
			return Result.success(result);
		} catch (Exception e) {
			log.error("检查配置键是否存在失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 获取配置数量
	 */
	@Operation(summary = "获取配置数量")
	@GetMapping("/count")
	public Result<Long> getConfigCount(
			@Parameter(description = "配置类型") @RequestParam(required = false) String configType) {
		try {
			long result = systemConfigService.countSystemConfigs(configType, null, null);
			return Result.success(result);
		} catch (Exception e) {
			log.error("获取配置数量失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 导出系统配置
	 */
	@Operation(summary = "导出系统配置")
	@GetMapping("/export")
	public Result<List<SystemConfigVO>> exportSystemConfig(
			@Parameter(description = "配置类型") @RequestParam(required = false) String configType,
			@Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
		try {
			List<SystemConfigVO> result = systemConfigService.exportSystemConfigs(configType, tenantId, null);
			return Result.success(result);
		} catch (Exception e) {
			log.error("导出系统配置失败", e);
			return Result.error("导出失败: " + e.getMessage());
		}
	}

	/**
	 * 导入系统配置
	 */
	@Operation(summary = "导入系统配置")
	@PostMapping("/import")
	public Result<String> importSystemConfig(
			@Parameter(description = "配置列表") @RequestBody @NotEmpty List<SystemConfigDTO> systemConfigList) {
		try {
			Map<String, Object> importResult = systemConfigService.importSystemConfigs(systemConfigList, false);
			boolean result = (Boolean) importResult.getOrDefault("success", false);
			if (result) {
				return Result.success("导入成功");
			} else {
				return Result.error("导入失败");
			}
		} catch (Exception e) {
			log.error("导入系统配置失败", e);
			return Result.error("导入失败: " + e.getMessage());
		}
	}

	/**
	 * 获取系统配置统计
	 */
	@Operation(summary = "获取系统配置统计")
	@GetMapping("/statistics")
	public Result<Map<String, Object>> getSystemConfigStatistics() {
		try {
			Map<String, Object> result = systemConfigService.getConfigCacheStatistics();
			return Result.success(result);
		} catch (Exception e) {
			log.error("获取系统配置统计失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 验证系统配置
	 */
	@Operation(summary = "验证系统配置")
	@PostMapping("/validate")
	public Result<String> validateSystemConfig(
			@Parameter(description = "系统配置") @RequestBody @Valid SystemConfigDTO systemConfigDTO) {
		try {
			Map<String, Object> validateResult = systemConfigService.validateConfigValue(systemConfigDTO.getConfigKey(),
					systemConfigDTO.getConfigValue(), systemConfigDTO.getConfigType());
			boolean result = (Boolean) validateResult.getOrDefault("valid", false);
			if (result) {
				return Result.success("验证通过");
			} else {
				return Result.error("验证失败");
			}
		} catch (Exception e) {
			log.error("验证系统配置失败", e);
			return Result.error("验证失败: " + e.getMessage());
		}
	}
}