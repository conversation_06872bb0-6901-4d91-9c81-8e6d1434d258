package com.example.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 流量规则实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("flow_rule")
public class FlowRuleEntity {

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 规则名称
	 */
	@TableField("rule_name")
	private String ruleName;

	/**
	 * 资源名称
	 */
	@TableField("resource_name")
	private String resourceName;

	/**
	 * 租户ID
	 */
	@TableField("tenant_id")
	private String tenantId;

	/**
	 * 限流阈值
	 */
	@TableField("threshold")
	private Integer threshold;

	/**
	 * 限流模式：0-QPS，1-线程数
	 */
	@TableField("limit_mode")
	private Integer limitMode;

	/**
	 * 调用关系限流策略：0-直接，1-关联，2-链路
	 */
	@TableField("strategy")
	private Integer strategy;

	/**
	 * 关联资源
	 */
	@TableField("related_resource")
	private String relatedResource;

	/**
	 * 流控效果：0-快速失败，1-预热，2-排队等待
	 */
	@TableField("behavior")
	private Integer behavior;

	/**
	 * 预热时长（秒）
	 */
	@TableField("warm_up_period")
	private Integer warmUpPeriod;

	/**
	 * 排队等待超时时间（毫秒）
	 */
	@TableField("queue_timeout")
	private Integer queueTimeout;

	/**
	 * 是否集群模式
	 */
	@TableField("cluster_mode")
	private Boolean clusterMode;

	/**
	 * 集群阈值模式：0-单机均摊，1-集群总体阈值
	 */
	@TableField("cluster_threshold_type")
	private Integer clusterThresholdType;

	/**
	 * 集群配置
	 */
	@TableField("cluster_config")
	private String clusterConfig;

	/**
	 * 规则状态：0-禁用，1-启用
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 优先级
	 */
	@TableField("priority")
	private Integer priority;

	/**
	 * 描述
	 */
	@TableField("description")
	private String description;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除标志
	 */
	@TableLogic
	@TableField("deleted")
	private Integer deleted;
}