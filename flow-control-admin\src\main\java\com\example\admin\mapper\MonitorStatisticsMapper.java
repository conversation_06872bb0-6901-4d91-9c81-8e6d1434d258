package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.entity.MonitorStatistics;
import com.example.admin.vo.MonitorVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 监控统计Mapper接口
 */
@Mapper
public interface MonitorStatisticsMapper extends BaseMapper<MonitorStatistics> {
    
    /**
     * 分页查询监控统计数据（带租户名称）
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @param statType 统计类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 监控统计VO列表
     */
    Page<MonitorVO> selectMonitorVOPage(Page<MonitorVO> page,
                                        @Param("tenantId") String tenantId,
                                        @Param("resourceName") String resourceName,
                                        @Param("statType") String statType,
                                        @Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据时间范围查询监控统计数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @param statType 统计类型
     * @return 监控统计列表
     */
    List<MonitorStatistics> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime,
                                              @Param("tenantId") String tenantId,
                                              @Param("resourceName") String resourceName,
                                              @Param("statType") String statType);
    
    /**
     * 根据租户ID查询监控统计数据
     *
     * @param tenantId 租户ID
     * @param statType 统计类型
     * @param limit 限制数量
     * @return 监控统计列表
     */
    List<MonitorStatistics> selectByTenantId(@Param("tenantId") String tenantId,
                                             @Param("statType") String statType,
                                             @Param("limit") Integer limit);
    
    /**
     * 根据资源名称查询监控统计数据
     *
     * @param resourceName 资源名称
     * @param statType 统计类型
     * @param limit 限制数量
     * @return 监控统计列表
     */
    List<MonitorStatistics> selectByResourceName(@Param("resourceName") String resourceName,
                                                  @Param("statType") String statType,
                                                  @Param("limit") Integer limit);
    
    /**
     * 查询实时监控数据（最近N分钟）
     *
     * @param minutes 分钟数
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @return 实时监控数据列表
     */
    List<MonitorStatistics> selectRealtimeData(@Param("minutes") int minutes,
                                               @Param("tenantId") String tenantId,
                                               @Param("resourceName") String resourceName);
    
    /**
     * 查询趋势数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @param statType 统计类型
     * @return 趋势数据列表
     */
    List<java.util.Map<String, Object>> selectTrendData(@Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime,
                                                         @Param("tenantId") String tenantId,
                                                         @Param("resourceName") String resourceName,
                                                         @Param("statType") String statType);
    
    /**
     * 统计总请求数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @return 总请求数
     */
    Long sumTotalRequests(@Param("startTime") LocalDateTime startTime,
                          @Param("endTime") LocalDateTime endTime,
                          @Param("tenantId") String tenantId);
    
    /**
     * 统计阻塞请求数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @return 阻塞请求数
     */
    Long sumBlockRequests(@Param("startTime") LocalDateTime startTime,
                          @Param("endTime") LocalDateTime endTime,
                          @Param("tenantId") String tenantId);
    
    /**
     * 计算平均响应时间
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @return 平均响应时间
     */
    Double avgResponseTime(@Param("startTime") LocalDateTime startTime,
                           @Param("endTime") LocalDateTime endTime,
                           @Param("tenantId") String tenantId,
                           @Param("resourceName") String resourceName);
    
    /**
     * 查询资源排行榜（按QPS）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 资源排行榜
     */
    List<java.util.Map<String, Object>> selectResourceRankByQps(@Param("startTime") LocalDateTime startTime,
                                                                 @Param("endTime") LocalDateTime endTime,
                                                                 @Param("limit") int limit);
    
    /**
     * 查询资源排行榜（按响应时间）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 资源排行榜
     */
    List<java.util.Map<String, Object>> selectResourceRankByRt(@Param("startTime") LocalDateTime startTime,
                                                                @Param("endTime") LocalDateTime endTime,
                                                                @Param("limit") int limit);
    
    /**
     * 查询租户统计数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 租户统计数据
     */
    List<java.util.Map<String, Object>> selectTenantStatistics(@Param("startTime") LocalDateTime startTime,
                                                                @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询系统概览统计数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 系统概览统计数据
     */
    java.util.Map<String, Object> selectSystemOverview(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 批量插入监控统计数据
     *
     * @param monitorStatisticsList 监控统计数据列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<MonitorStatistics> monitorStatisticsList);
    
    /**
     * 删除过期的监控数据
     *
     * @param beforeTime 过期时间点
     * @param statType 统计类型
     * @return 删除数量
     */
    int deleteExpiredData(@Param("beforeTime") LocalDateTime beforeTime,
                          @Param("statType") String statType);
    
    /**
     * 查询异常数据（响应时间过高、阻塞率过高等）
     *
     * @param maxRt 最大响应时间阈值
     * @param maxBlockRate 最大阻塞率阈值
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 异常数据列表
     */
    List<MonitorStatistics> selectAbnormalData(@Param("maxRt") Integer maxRt,
                                               @Param("maxBlockRate") Double maxBlockRate,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);
}