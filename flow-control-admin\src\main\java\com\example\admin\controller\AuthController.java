package com.example.admin.controller;

import com.example.admin.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 提供用户登录、登出、用户信息获取等功能
 */
@Tag(name = "用户认证管理")
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    private static final Logger log = LoggerFactory.getLogger(AuthController.class);

    /**
     * 用户登录
     */
    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        try {
            String username = loginRequest.get("username");
            String password = loginRequest.get("password");
            
            log.info("用户登录请求: username={}", username);
            
            // 简单的用户验证逻辑（实际项目中应该查询数据库）
            if ("admin".equals(username) && "123456".equals(password)) {
                Map<String, Object> data = new HashMap<>();
                data.put("token", "mock-jwt-token-" + System.currentTimeMillis());
                
                Map<String, Object> user = new HashMap<>();
                user.put("id", 1);
                user.put("username", "admin");
                user.put("name", "管理员");
                user.put("email", "<EMAIL>");
                user.put("role", "admin");
                
                data.put("user", user);
                
                log.info("用户登录成功: username={}", username);
                return Result.success(data);
            } else {
                log.warn("用户登录失败: username={}, 用户名或密码错误", username);
                return Result.error("用户名或密码错误");
            }
        } catch (Exception e) {
            log.error("用户登录异常", e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    @Operation(summary = "用户登出")
    @PostMapping("/logout")
    public Result<Void> logout() {
        try {
            log.info("用户登出");
            return Result.success();
        } catch (Exception e) {
            log.error("用户登出异常", e);
            return Result.error("登出失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @Operation(summary = "获取当前用户信息")
    @GetMapping("/user")
    public Result<Map<String, Object>> getUserInfo(@RequestHeader(value = "Authorization", required = false) String token) {
        try {
            log.info("获取用户信息请求: token={}", token);
            
            // 简单的token验证（实际项目中应该解析JWT token）
            if (token != null && token.startsWith("Bearer ")) {
                Map<String, Object> user = new HashMap<>();
                user.put("id", 1);
                user.put("username", "admin");
                user.put("name", "管理员");
                user.put("email", "<EMAIL>");
                user.put("role", "admin");
                
                return Result.success(user);
            } else {
                log.warn("获取用户信息失败: token无效");
                return Result.unauthorized();
            }
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }
}