// 系统配置模块
const state = {
  // 全局参数配置
  globalConfig: {
    maxQps: 1000,
    maxResponseTime: 3000,
    statisticInterval: 60,
    timeout: 5000,
    warmUpPeriod: 10,
    coldFactor: 3
  },
  
  // 告警阈值配置
  alertThresholds: {
    qpsThreshold: 800,
    responseTimeThreshold: 2000,
    errorRateThreshold: 5.0,
    enableQpsAlert: true,
    enableRtAlert: true,
    enableErrorRateAlert: true
  },
  
  // 通知配置
  notificationConfig: {
    email: {
      enabled: false,
      smtpServer: '',
      smtpPort: 587,
      username: '',
      password: '',
      recipients: [],
      enableSsl: true
    },
    sms: {
      enabled: false,
      provider: 'aliyun',
      accessKey: '',
      secretKey: '',
      signName: '',
      templateCode: '',
      phoneNumbers: []
    },
    webhook: {
      enabled: false,
      url: '',
      method: 'POST',
      headers: {},
      timeout: 10000
    }
  },
  
  // 系统日志配置
  logConfig: {
    logLevel: 'INFO',
    maxLogSize: 100,
    retentionDays: 30,
    enableFileLog: true,
    enableConsoleLog: true
  },
  
  // 系统日志数据
  systemLogs: [],
  
  // 加载状态
  loading: {
    config: false,
    logs: false,
    saving: false,
    testing: false
  }
}

const mutations = {
  // 设置全局配置
  SET_GLOBAL_CONFIG(state, config) {
    state.globalConfig = { ...state.globalConfig, ...config }
  },
  
  // 设置告警阈值
  SET_ALERT_THRESHOLDS(state, thresholds) {
    state.alertThresholds = { ...state.alertThresholds, ...thresholds }
  },
  
  // 设置通知配置
  SET_NOTIFICATION_CONFIG(state, config) {
    state.notificationConfig = { ...state.notificationConfig, ...config }
  },
  
  // 设置邮件配置
  SET_EMAIL_CONFIG(state, emailConfig) {
    state.notificationConfig.email = { ...state.notificationConfig.email, ...emailConfig }
  },
  
  // 设置短信配置
  SET_SMS_CONFIG(state, smsConfig) {
    state.notificationConfig.sms = { ...state.notificationConfig.sms, ...smsConfig }
  },
  
  // 设置Webhook配置
  SET_WEBHOOK_CONFIG(state, webhookConfig) {
    state.notificationConfig.webhook = { ...state.notificationConfig.webhook, ...webhookConfig }
  },
  
  // 设置日志配置
  SET_LOG_CONFIG(state, logConfig) {
    state.logConfig = { ...state.logConfig, ...logConfig }
  },
  
  // 设置系统日志
  SET_SYSTEM_LOGS(state, logs) {
    state.systemLogs = logs
  },
  
  // 添加系统日志
  ADD_SYSTEM_LOG(state, log) {
    state.systemLogs.unshift(log)
    // 限制日志数量
    if (state.systemLogs.length > state.logConfig.maxLogSize) {
      state.systemLogs = state.systemLogs.slice(0, state.logConfig.maxLogSize)
    }
  },
  
  // 清空系统日志
  CLEAR_SYSTEM_LOGS(state) {
    state.systemLogs = []
  },
  
  // 设置加载状态
  SET_LOADING(state, { type, status }) {
    state.loading[type] = status
  }
}

const actions = {
  // 加载系统配置
  async loadConfig({ commit }) {
    commit('SET_LOADING', { type: 'config', status: true })
    try {
      // TODO: 调用API获取配置
      // const response = await api.getSystemConfig()
      // commit('SET_GLOBAL_CONFIG', response.data.globalConfig)
      // commit('SET_ALERT_THRESHOLDS', response.data.alertThresholds)
      // commit('SET_NOTIFICATION_CONFIG', response.data.notificationConfig)
      // commit('SET_LOG_CONFIG', response.data.logConfig)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 使用默认配置
      console.log('配置加载完成')
    } catch (error) {
      console.error('加载配置失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', { type: 'config', status: false })
    }
  },
  
  // 保存全局配置
  async saveGlobalConfig({ commit }, config) {
    commit('SET_LOADING', { type: 'saving', status: true })
    try {
      // TODO: 调用API保存配置
      // await api.saveGlobalConfig(config)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      commit('SET_GLOBAL_CONFIG', config)
      return { success: true, message: '全局配置保存成功' }
    } catch (error) {
      console.error('保存全局配置失败:', error)
      return { success: false, message: '保存失败: ' + error.message }
    } finally {
      commit('SET_LOADING', { type: 'saving', status: false })
    }
  },
  
  // 保存告警阈值
  async saveAlertThresholds({ commit }, thresholds) {
    commit('SET_LOADING', { type: 'saving', status: true })
    try {
      // TODO: 调用API保存告警阈值
      // await api.saveAlertThresholds(thresholds)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      commit('SET_ALERT_THRESHOLDS', thresholds)
      return { success: true, message: '告警阈值保存成功' }
    } catch (error) {
      console.error('保存告警阈值失败:', error)
      return { success: false, message: '保存失败: ' + error.message }
    } finally {
      commit('SET_LOADING', { type: 'saving', status: false })
    }
  },
  
  // 保存通知配置
  async saveNotificationConfig({ commit }, config) {
    commit('SET_LOADING', { type: 'saving', status: true })
    try {
      // TODO: 调用API保存通知配置
      // await api.saveNotificationConfig(config)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      commit('SET_NOTIFICATION_CONFIG', config)
      return { success: true, message: '通知配置保存成功' }
    } catch (error) {
      console.error('保存通知配置失败:', error)
      return { success: false, message: '保存失败: ' + error.message }
    } finally {
      commit('SET_LOADING', { type: 'saving', status: false })
    }
  },
  
  // 测试邮件通知
  async testEmailNotification({ commit, state }) {
    commit('SET_LOADING', { type: 'testing', status: true })
    try {
      // TODO: 调用API测试邮件
      // await api.testEmailNotification(state.notificationConfig.email)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      return { success: true, message: '邮件测试成功' }
    } catch (error) {
      console.error('邮件测试失败:', error)
      return { success: false, message: '测试失败: ' + error.message }
    } finally {
      commit('SET_LOADING', { type: 'testing', status: false })
    }
  },
  
  // 测试短信通知
  async testSmsNotification({ commit, state }) {
    commit('SET_LOADING', { type: 'testing', status: true })
    try {
      // TODO: 调用API测试短信
      // await api.testSmsNotification(state.notificationConfig.sms)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      return { success: true, message: '短信测试成功' }
    } catch (error) {
      console.error('短信测试失败:', error)
      return { success: false, message: '测试失败: ' + error.message }
    } finally {
      commit('SET_LOADING', { type: 'testing', status: false })
    }
  },
  
  // 测试Webhook通知
  async testWebhookNotification({ commit, state }) {
    commit('SET_LOADING', { type: 'testing', status: true })
    try {
      // TODO: 调用API测试Webhook
      // await api.testWebhookNotification(state.notificationConfig.webhook)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      return { success: true, message: 'Webhook测试成功' }
    } catch (error) {
      console.error('Webhook测试失败:', error)
      return { success: false, message: '测试失败: ' + error.message }
    } finally {
      commit('SET_LOADING', { type: 'testing', status: false })
    }
  },
  
  // 加载系统日志
  async loadSystemLogs({ commit }, { level, startDate, endDate, keyword } = {}) {
    commit('SET_LOADING', { type: 'logs', status: true })
    try {
      // TODO: 调用API获取系统日志
      // const response = await api.getSystemLogs({ level, startDate, endDate, keyword })
      // commit('SET_SYSTEM_LOGS', response.data)
      
      // 模拟API调用和数据
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockLogs = generateMockSystemLogs(50)
      commit('SET_SYSTEM_LOGS', mockLogs)
      
      return mockLogs
    } catch (error) {
      console.error('加载系统日志失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', { type: 'logs', status: false })
    }
  },
  
  // 清空系统日志
  async clearSystemLogs({ commit }) {
    try {
      // TODO: 调用API清空日志
      // await api.clearSystemLogs()
      
      commit('CLEAR_SYSTEM_LOGS')
      return { success: true, message: '日志清空成功' }
    } catch (error) {
      console.error('清空日志失败:', error)
      return { success: false, message: '清空失败: ' + error.message }
    }
  }
}

// 生成模拟系统日志的辅助函数
function generateMockSystemLogs(count = 50) {
    const levels = ['DEBUG', 'INFO', 'WARN', 'ERROR']
    const loggers = ['com.example.controller', 'com.example.service', 'com.example.dao', 'com.example.util']
    const messages = [
      '用户登录成功',
      '数据库连接建立',
      '缓存更新完成',
      '配置文件加载',
      '定时任务执行',
      '接口调用异常',
      '内存使用率过高',
      '网络连接超时',
      '文件读取失败',
      '权限验证通过'
    ]
    
    const logs = []
    for (let i = 0; i < count; i++) {
      logs.push({
        id: i + 1,
        timestamp: Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000, // 最近7天
        level: levels[Math.floor(Math.random() * levels.length)],
        logger: loggers[Math.floor(Math.random() * loggers.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        thread: `thread-${Math.floor(Math.random() * 10) + 1}`
      })
    }
    
    return logs.sort((a, b) => b.timestamp - a.timestamp)
}

const getters = {
  // 获取全局配置
  globalConfig: state => state.globalConfig,
  
  // 获取告警阈值
  alertThresholds: state => state.alertThresholds,
  
  // 获取通知配置
  notificationConfig: state => state.notificationConfig,
  
  // 获取邮件配置
  emailConfig: state => state.notificationConfig.email,
  
  // 获取短信配置
  smsConfig: state => state.notificationConfig.sms,
  
  // 获取Webhook配置
  webhookConfig: state => state.notificationConfig.webhook,
  
  // 获取日志配置
  logConfig: state => state.logConfig,
  
  // 获取系统日志
  systemLogs: state => state.systemLogs,
  
  // 获取加载状态
  loading: state => state.loading,
  
  // 获取过滤后的系统日志
  filteredSystemLogs: state => (filters) => {
    let logs = state.systemLogs
    
    if (filters.level && filters.level !== 'ALL') {
      logs = logs.filter(log => log.level === filters.level)
    }
    
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase()
      logs = logs.filter(log => 
        log.message.toLowerCase().includes(keyword) ||
        log.logger.toLowerCase().includes(keyword)
      )
    }
    
    if (filters.startDate) {
      const startTime = new Date(filters.startDate).getTime()
      logs = logs.filter(log => log.timestamp >= startTime)
    }
    
    if (filters.endDate) {
      const endTime = new Date(filters.endDate).getTime() + 24 * 60 * 60 * 1000 - 1
      logs = logs.filter(log => log.timestamp <= endTime)
    }
    
    return logs
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}