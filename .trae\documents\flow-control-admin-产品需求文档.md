# Sentinel流量控制系统产品需求文档

## 1. Product Overview

Sentinel流量控制系统是一个基于多维度的分布式流量控制管理平台，支持租户级别、接口级别、IP级别的精细化流量控制。系统提供实时监控、规则管理、排队机制等核心功能，帮助企业实现高可用的服务治理。

系统主要解决高并发场景下的流量控制问题，支持多租户架构，为不同业务场景提供差异化的流量管理策略，确保系统稳定性和服务质量。

## 2. Core Features

### 2.1 User Roles

| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| 系统管理员 | 系统预置账号 | 全局配置管理、租户管理、系统监控 |
| 租户管理员 | 系统管理员分配 | 租户内规则管理、监控查看、配置管理 |
| 普通用户 | 租户管理员分配 | 规则查看、基础监控查看 |

### 2.2 Feature Module

我们的流量控制系统包含以下主要页面：

1. **首页大屏**：系统概览、实时监控数据、关键指标展示
2. **流量规则管理**：规则创建、编辑、删除、批量操作
3. **IP规则管理**：IP黑白名单、IP段控制、地理位置管理
4. **监控统计**：实时监控、历史数据、性能分析
5. **系统配置**：全局配置、租户配置、告警配置
6. **租户管理**：租户信息管理、权限分配
7. **日志管理**：操作日志、流控事件日志

### 2.3 Page Details

| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| 首页大屏 | 实时监控面板 | 展示系统整体QPS、响应时间、限流次数等关键指标，支持实时刷新和时间范围选择 |
| 首页大屏 | 租户概览 | 显示各租户的流量使用情况、规则数量、异常告警等统计信息 |
| 首页大屏 | 系统状态 | 展示系统健康状态、服务可用性、资源使用情况 |
| 流量规则管理 | 规则列表 | 分页展示流量规则，支持按租户、资源名称、状态等条件筛选和搜索 |
| 流量规则管理 | 规则编辑 | 创建和编辑流量规则，包括QPS阈值、流控策略、行为配置等参数设置 |
| 流量规则管理 | 批量操作 | 支持规则的批量启用、禁用、删除、导入导出功能 |
| IP规则管理 | IP规则配置 | 管理IP黑白名单，支持单IP、IP段(CIDR)、IP范围等多种格式 |
| IP规则管理 | 地理位置管理 | 基于IP归属地的访问控制，支持国家、省份、城市级别的限制 |
| IP规则管理 | IP统计分析 | 展示IP访问频率、异常IP识别、访问趋势分析 |
| 监控统计 | 实时监控 | 实时展示各资源的流量数据、响应时间、限流情况 |
| 监控统计 | 历史统计 | 按天、小时维度的历史数据查询和图表展示 |
| 监控统计 | 性能分析 | 系统性能指标分析，包括吞吐量、延迟分布、错误率等 |
| 系统配置 | 全局配置 | 系统级别的配置管理，如默认QPS、超时时间、缓存策略等 |
| 系统配置 | 租户配置 | 租户级别的个性化配置，支持差异化的流控策略 |
| 系统配置 | 告警配置 | 配置告警规则、通知方式、告警阈值等 |
| 租户管理 | 租户信息 | 租户的基本信息管理，包括名称、描述、联系方式等 |
| 租户管理 | 权限管理 | 租户用户的权限分配和角色管理 |
| 日志管理 | 操作日志 | 记录用户的操作行为，支持日志查询和审计 |
| 日志管理 | 流控日志 | 记录流量控制事件，包括通过、阻塞、排队等详细信息 |

## 3. Core Process

### 系统管理员流程
1. 系统管理员登录系统
2. 查看系统整体监控大屏
3. 管理租户信息和权限分配
4. 配置全局系统参数
5. 查看系统日志和审计信息

### 租户管理员流程
1. 租户管理员登录系统
2. 查看租户监控数据
3. 创建和管理流量控制规则
4. 配置IP访问控制规则
5. 查看流控事件和统计报告
6. 管理租户用户权限

### 普通用户流程
1. 普通用户登录系统
2. 查看授权范围内的监控数据
3. 查看流量规则配置
4. 查看相关的统计报告

```mermaid
graph TD
    A[登录页面] --> B[首页大屏]
    B --> C[流量规则管理]
    B --> D[IP规则管理]
    B --> E[监控统计]
    B --> F[系统配置]
    B --> G[租户管理]
    B --> H[日志管理]
    C --> C1[规则列表]
    C --> C2[规则编辑]
    D --> D1[IP规则配置]
    D --> D2[地理位置管理]
    E --> E1[实时监控]
    E --> E2[历史统计]
    F --> F1[全局配置]
    F --> F2[告警配置]
```

## 4. User Interface Design

### 4.1 Design Style

- **主色调**：蓝色系 (#1890FF) 作为主色，灰色系 (#F5F5F5) 作为背景色
- **辅助色**：绿色 (#52C41A) 表示正常状态，红色 (#FF4D4F) 表示告警状态，橙色 (#FA8C16) 表示警告状态
- **按钮样式**：圆角按钮，主要按钮使用实心样式，次要按钮使用边框样式
- **字体**：系统默认字体，标题使用16px-20px，正文使用14px，辅助文字使用12px
- **布局风格**：卡片式布局，顶部导航栏，左侧菜单栏，主内容区域
- **图标风格**：使用Ant Design图标库，简洁现代的线性图标

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| 首页大屏 | 实时监控面板 | 大屏展示风格，深色背景，数据卡片使用渐变色，实时数字跳动效果，ECharts图表展示 |
| 首页大屏 | 租户概览 | 网格布局的租户卡片，每个卡片显示租户名称、状态指示灯、关键指标 |
| 流量规则管理 | 规则列表 | 表格布局，支持排序和筛选，状态使用标签显示，操作按钮组 |
| 流量规则管理 | 规则编辑 | 表单布局，分步骤配置，实时预览效果，参数验证提示 |
| IP规则管理 | IP规则配置 | 树形结构展示IP规则层级，支持拖拽排序，批量选择操作 |
| 监控统计 | 实时监控 | 仪表盘风格，多种图表类型（折线图、柱状图、饼图），时间选择器 |
| 系统配置 | 全局配置 | 分组的配置项，使用折叠面板组织，配置项说明和默认值提示 |

### 4.3 Responsiveness

系统采用桌面优先的响应式设计，支持1920x1080及以上分辨率的最佳显示效果。在平板和移动设备上进行适配，菜单自动收缩为抽屉式，表格支持横向滚动，图表自适应屏幕尺寸。考虑触摸交互优化，按钮和链接区域适当增大，支持手势操作。