#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量控制测试脚本 - 可配置版本
支持自定义测试参数：测试时间、每秒请求数、处理方式等
"""

import asyncio
import aiohttp
import time
import json
from datetime import datetime
from typing import Dict, List, Any
import argparse
import sys

class FlowControlTester:
    def __init__(self, base_url: str = "http://localhost:8088"):
        self.base_url = base_url
        self.results = []
        
    async def send_request(self, session: aiohttp.ClientSession, url: str, tenant_id: str) -> Dict[str, Any]:
        """发送单个HTTP请求"""
        headers = {
            'X-Tenant-Id': tenant_id,
            'Content-Type': 'application/json'
        }
        
        start_time = time.time()
        try:
            async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                return {
                    'status_code': response.status,
                    'response_time': response_time,
                    'success': response.status == 200,
                    'blocked': response.status == 429,
                    'error': False,
                    'timestamp': start_time
                }
        except Exception as e:
            end_time = time.time()
            return {
                'status_code': 0,
                'response_time': end_time - start_time,
                'success': False,
                'blocked': False,
                'error': True,
                'error_msg': str(e),
                'timestamp': start_time
            }
    
    async def test_single_rule(self, rule: Dict[str, Any], test_duration: int, requests_per_second: int) -> Dict[str, Any]:
        """测试单个限流规则"""
        resource = rule['resource']
        tenant_id = rule['tenant_id']
        rule_name = rule['rule_name']
        expected_limit = rule['count']
        
        url = f"{self.base_url}{resource}"
        
        print(f"\n开始测试规则: {rule_name}")
        print(f"接口: {resource}, 租户: {tenant_id}")
        print(f"预期限制: {expected_limit} QPS")
        print(f"测试参数: {test_duration}秒, {requests_per_second} RPS")
        print(f"理论通过请求数: {expected_limit * test_duration}")
        print(f"理论拦截请求数: {requests_per_second * test_duration - expected_limit * test_duration}")
        
        results = []
        start_test_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            # 计算每秒需要发送的请求间隔
            interval = 1.0 / requests_per_second
            
            tasks = []
            request_count = 0
            
            while time.time() - start_test_time < test_duration:
                # 创建请求任务
                task = asyncio.create_task(self.send_request(session, url, tenant_id))
                tasks.append(task)
                request_count += 1
                
                # 控制请求频率
                await asyncio.sleep(interval)
            
            # 等待所有请求完成
            print(f"等待 {len(tasks)} 个请求完成...")
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_test_time = time.time()
        actual_duration = end_test_time - start_test_time
        
        # 统计结果
        total_requests = len(results)
        success_requests = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
        blocked_requests = sum(1 for r in results if isinstance(r, dict) and r.get('blocked', False))
        error_requests = sum(1 for r in results if isinstance(r, dict) and r.get('error', False))
        
        # 计算响应时间统计
        response_times = [r['response_time'] for r in results if isinstance(r, dict) and 'response_time' in r]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        max_response_time = max(response_times) if response_times else 0
        min_response_time = min(response_times) if response_times else 0
        
        # 计算实际QPS
        actual_qps = total_requests / actual_duration if actual_duration > 0 else 0
        success_qps = success_requests / actual_duration if actual_duration > 0 else 0
        
        # 计算限流效果
        expected_success = expected_limit * actual_duration
        success_rate = (success_requests / total_requests * 100) if total_requests > 0 else 0
        block_rate = (blocked_requests / total_requests * 100) if total_requests > 0 else 0
        
        result = {
            'rule_name': rule_name,
            'tenant_id': tenant_id,
            'resource': resource,
            'expected_limit': expected_limit,
            'test_config': {
                'duration': test_duration,
                'requests_per_second': requests_per_second,
                'expected_total_requests': requests_per_second * test_duration,
                'expected_success_requests': int(expected_success)
            },
            'actual_results': {
                'total_requests': total_requests,
                'success_requests': success_requests,
                'blocked_requests': blocked_requests,
                'error_requests': error_requests,
                'actual_duration': actual_duration,
                'actual_qps': actual_qps,
                'success_qps': success_qps
            },
            'statistics': {
                'success_rate': success_rate,
                'block_rate': block_rate,
                'error_rate': (error_requests / total_requests * 100) if total_requests > 0 else 0,
                'avg_response_time': avg_response_time,
                'max_response_time': max_response_time,
                'min_response_time': min_response_time
            },
            'analysis': {
                'qps_accuracy': abs(success_qps - expected_limit) / expected_limit * 100 if expected_limit > 0 else 0,
                'expected_vs_actual_success': success_requests - expected_success,
                'limit_effectiveness': blocked_requests > 0
            }
        }
        
        # 打印测试结果
        print(f"\n=== 测试结果 ===")
        print(f"总请求数: {total_requests}")
        print(f"成功请求: {success_requests} ({success_rate:.1f}%)")
        print(f"被限流: {blocked_requests} ({block_rate:.1f}%)")
        print(f"错误请求: {error_requests}")
        print(f"实际QPS: {actual_qps:.2f}")
        print(f"成功QPS: {success_qps:.2f} (预期: {expected_limit})")
        print(f"QPS准确度偏差: {result['analysis']['qps_accuracy']:.1f}%")
        print(f"平均响应时间: {avg_response_time*1000:.2f}ms")
        
        return result
    
    async def run_all_tests(self, rules: List[Dict[str, Any]], test_duration: int, requests_per_second: int) -> Dict[str, Any]:
        """运行所有测试规则"""
        print(f"\n{'='*60}")
        print(f"流量控制测试开始")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"服务地址: {self.base_url}")
        print(f"测试配置: {test_duration}秒, {requests_per_second} RPS")
        print(f"测试规则数量: {len(rules)}")
        print(f"{'='*60}")
        
        results = []
        
        for i, rule in enumerate(rules, 1):
            print(f"\n[{i}/{len(rules)}] 测试规则: {rule['rule_name']}")
            try:
                result = await self.test_single_rule(rule, test_duration, requests_per_second)
                results.append(result)
                
                # 测试间隔，避免相互影响
                if i < len(rules):
                    print("等待2秒后进行下一个测试...")
                    await asyncio.sleep(2)
                    
            except Exception as e:
                print(f"测试规则 {rule['rule_name']} 时发生错误: {e}")
                results.append({
                    'rule_name': rule['rule_name'],
                    'error': True,
                    'error_message': str(e)
                })
        
        # 生成测试报告
        report = {
            'test_time': datetime.now().isoformat(),
            'base_url': self.base_url,
            'test_config': {
                'duration': test_duration,
                'requests_per_second': requests_per_second
            },
            'test_rules': rules,
            'results': results,
            'summary': self._generate_summary(results)
        }
        
        return report
    
    def _generate_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成测试总结"""
        successful_tests = [r for r in results if not r.get('error', False)]
        
        if not successful_tests:
            return {'error': '所有测试都失败了'}
        
        total_requests = sum(r['actual_results']['total_requests'] for r in successful_tests)
        total_success = sum(r['actual_results']['success_requests'] for r in successful_tests)
        total_blocked = sum(r['actual_results']['blocked_requests'] for r in successful_tests)
        total_errors = sum(r['actual_results']['error_requests'] for r in successful_tests)
        
        avg_qps_accuracy = sum(r['analysis']['qps_accuracy'] for r in successful_tests) / len(successful_tests)
        
        effective_limits = sum(1 for r in successful_tests if r['analysis']['limit_effectiveness'])
        
        return {
            'total_tests': len(results),
            'successful_tests': len(successful_tests),
            'failed_tests': len(results) - len(successful_tests),
            'total_requests': total_requests,
            'total_success_requests': total_success,
            'total_blocked_requests': total_blocked,
            'total_error_requests': total_errors,
            'overall_success_rate': (total_success / total_requests * 100) if total_requests > 0 else 0,
            'overall_block_rate': (total_blocked / total_requests * 100) if total_requests > 0 else 0,
            'average_qps_accuracy_deviation': avg_qps_accuracy,
            'effective_limit_rules': effective_limits,
            'limit_effectiveness_rate': (effective_limits / len(successful_tests) * 100) if successful_tests else 0
        }
    
    def save_report(self, report: Dict[str, Any], filename: str = None) -> str:
        """保存测试报告"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'flow_control_test_report_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return filename
    
    def print_summary(self, report: Dict[str, Any]):
        """打印测试总结"""
        summary = report['summary']
        
        print(f"\n{'='*60}")
        print(f"测试总结报告")
        print(f"{'='*60}")
        print(f"测试时间: {report['test_time']}")
        print(f"服务地址: {report['base_url']}")
        print(f"测试配置: {report['test_config']['duration']}秒, {report['test_config']['requests_per_second']} RPS")
        print(f"")
        print(f"测试统计:")
        print(f"  总测试数: {summary['total_tests']}")
        print(f"  成功测试: {summary['successful_tests']}")
        print(f"  失败测试: {summary['failed_tests']}")
        print(f"")
        print(f"请求统计:")
        print(f"  总请求数: {summary['total_requests']}")
        print(f"  成功请求: {summary['total_success_requests']} ({summary['overall_success_rate']:.1f}%)")
        print(f"  被限流: {summary['total_blocked_requests']} ({summary['overall_block_rate']:.1f}%)")
        print(f"  错误请求: {summary['total_error_requests']}")
        print(f"")
        print(f"限流效果:")
        print(f"  平均QPS准确度偏差: {summary['average_qps_accuracy_deviation']:.1f}%")
        print(f"  有效限流规则: {summary['effective_limit_rules']}/{summary['successful_tests']}")
        print(f"  限流有效率: {summary['limit_effectiveness_rate']:.1f}%")
        print(f"{'='*60}")

def create_test_rules() -> List[Dict[str, Any]]:
    """创建测试规则（基于用户提供的数据）"""
    return [
        {
            'rule_name': '用户查询接口限流',
            'resource': '/api/test',
            'tenant_id': 'tenant_001',
            'grade': 0,
            'count': 10,
            'control_behavior': 0
        },
        {
            'rule_name': '用户管理接口限流',
            'resource': '/api/admin/users',
            'tenant_id': 'tenant_001',
            'grade': 0,
            'count': 10,
            'control_behavior': 0
        },
        {
            'rule_name': '支付接口限流',
            'resource': '/api/admin/ip-rules',
            'tenant_id': 'tenant_001',
            'grade': 0,
            'count': 10,
            'control_behavior': 0
        },
        {
            'rule_name': '数据导出接口限流',
            'resource': '/api/admin/ip-rules',
            'tenant_id': 'tenant_002',
            'grade': 0,
            'count': 10,
            'control_behavior': 0
        },
        {
            'rule_name': '文件上传接口限流',
            'resource': '/api/test',
            'tenant_id': 'tenant_002',
            'grade': 0,
            'count': 10,
            'control_behavior': 0
        },
        {
            'rule_name': '租户2用户管理接口限流',
            'resource': '/api/admin/users',
            'tenant_id': 'tenant_002',
            'grade': 0,
            'count': 10,
            'control_behavior': 0
        }
    ]

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='流量控制测试脚本')
    parser.add_argument('--url', default='http://localhost:8088', help='服务器地址 (默认: http://localhost:8088)')
    parser.add_argument('--duration', type=int, default=10, help='测试持续时间(秒) (默认: 10)')
    parser.add_argument('--rps', type=int, default=100, help='每秒请求数 (默认: 100)')
    parser.add_argument('--output', help='输出报告文件名')
    parser.add_argument('--rules-only', action='store_true', help='只显示测试规则')
    
    args = parser.parse_args()
    
    # 创建测试规则
    rules = create_test_rules()
    
    if args.rules_only:
        print("测试规则列表:")
        for i, rule in enumerate(rules, 1):
            print(f"{i}. {rule['rule_name']} - {rule['resource']} ({rule['tenant_id']}) - QPS限制: {rule['count']}")
        return
    
    # 创建测试器
    tester = FlowControlTester(args.url)
    
    try:
        # 运行测试
        report = await tester.run_all_tests(rules, args.duration, args.rps)
        
        # 保存报告
        report_file = tester.save_report(report, args.output)
        print(f"\n测试报告已保存到: {report_file}")
        
        # 打印总结
        tester.print_summary(report)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 运行测试
    asyncio.run(main())