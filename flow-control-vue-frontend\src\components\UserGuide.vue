<template>
  <div class="user-guide">
    <!-- 引导遮罩层 -->
    <div v-if="showGuide" class="guide-overlay" @click="skipGuide">
      <!-- 引导步骤 -->
      <div 
        class="guide-step" 
        :style="currentStepStyle"
        @click.stop
      >
        <div class="guide-content">
          <div class="guide-header">
            <h3>{{ currentStep.title }}</h3>
            <el-button 
              type="text" 
              class="guide-close"
              @click="skipGuide"
            >
              <i class="el-icon-close"></i>
            </el-button>
          </div>
          
          <div class="guide-body">
            <p>{{ currentStep.content }}</p>
            <div v-if="currentStep.image" class="guide-image">
              <img :src="currentStep.image" :alt="currentStep.title" />
            </div>
          </div>
          
          <div class="guide-footer">
            <div class="guide-progress">
              <span>{{ currentStepIndex + 1 }} / {{ guideSteps.length }}</span>
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: progressPercentage + '%' }"
                ></div>
              </div>
            </div>
            
            <div class="guide-actions">
              <el-button 
                v-if="currentStepIndex > 0" 
                @click="previousStep"
              >
                {{ $t('guide.previous') }}
              </el-button>
              
              <el-button 
                type="primary" 
                @click="nextStep"
              >
                {{ isLastStep ? $t('guide.finish') : $t('guide.next') }}
              </el-button>
              
              <el-button 
                type="text" 
                @click="skipGuide"
              >
                {{ $t('guide.skip') }}
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 箭头指示器 -->
        <div 
          v-if="currentStep.arrow" 
          class="guide-arrow" 
          :class="currentStep.arrow.position"
        ></div>
      </div>
    </div>
    
    <!-- 帮助按钮 -->
    <el-button 
      v-if="!showGuide" 
      class="help-button" 
      type="primary" 
      circle 
      @click="startGuide"
    >
      <i class="el-icon-question"></i>
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'UserGuide',
  props: {
    // 引导步骤配置
    steps: {
      type: Array,
      default: () => []
    },
    // 是否自动开始引导
    autoStart: {
      type: Boolean,
      default: false
    },
    // 引导完成后的回调
    onComplete: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      showGuide: false,
      currentStepIndex: 0,
      guideSteps: []
    }
  },
  computed: {
    currentStep() {
      return this.guideSteps[this.currentStepIndex] || {}
    },
    
    currentStepStyle() {
      const step = this.currentStep
      if (!step.target) return {}
      
      const target = document.querySelector(step.target)
      if (!target) return {}
      
      const rect = target.getBoundingClientRect()
      const style = {
        position: 'fixed',
        zIndex: 10001
      }
      
      // 根据目标元素位置调整引导框位置
      if (step.position === 'top') {
        style.left = rect.left + 'px'
        style.top = (rect.top - 200) + 'px'
      } else if (step.position === 'bottom') {
        style.left = rect.left + 'px'
        style.top = (rect.bottom + 20) + 'px'
      } else if (step.position === 'left') {
        style.left = (rect.left - 320) + 'px'
        style.top = rect.top + 'px'
      } else if (step.position === 'right') {
        style.left = (rect.right + 20) + 'px'
        style.top = rect.top + 'px'
      } else {
        // 默认居中
        style.left = '50%'
        style.top = '50%'
        style.transform = 'translate(-50%, -50%)'
      }
      
      return style
    },
    
    isLastStep() {
      return this.currentStepIndex === this.guideSteps.length - 1
    },
    
    progressPercentage() {
      return ((this.currentStepIndex + 1) / this.guideSteps.length) * 100
    }
  },
  mounted() {
    this.initGuide()
    if (this.autoStart) {
      this.startGuide()
    }
  },
  methods: {
    initGuide() {
      // 使用传入的步骤或默认步骤
      this.guideSteps = this.steps.length > 0 ? this.steps : this.getDefaultSteps()
    },
    
    getDefaultSteps() {
      return [
        {
          title: this.$t('guide.welcome.title'),
          content: this.$t('guide.welcome.content'),
          target: null,
          position: 'center'
        },
        {
          title: this.$t('guide.sidebar.title'),
          content: this.$t('guide.sidebar.content'),
          target: '.sidebar-menu',
          position: 'right',
          arrow: { position: 'left' }
        },
        {
          title: this.$t('guide.dashboard.title'),
          content: this.$t('guide.dashboard.content'),
          target: '.el-menu-item[index="/dashboard"]',
          position: 'right',
          arrow: { position: 'left' }
        },
        {
          title: this.$t('guide.theme.title'),
          content: this.$t('guide.theme.content'),
          target: '.theme-toggle',
          position: 'bottom',
          arrow: { position: 'top' }
        },
        {
          title: this.$t('guide.language.title'),
          content: this.$t('guide.language.content'),
          target: '.language-toggle',
          position: 'bottom',
          arrow: { position: 'top' }
        },
        {
          title: this.$t('guide.complete.title'),
          content: this.$t('guide.complete.content'),
          target: null,
          position: 'center'
        }
      ]
    },
    
    startGuide() {
      this.currentStepIndex = 0
      this.showGuide = true
      this.highlightTarget()
      
      // 记录用户已看过引导
      localStorage.setItem('user_guide_completed', 'false')
    },
    
    nextStep() {
      if (this.isLastStep) {
        this.completeGuide()
      } else {
        this.currentStepIndex++
        this.highlightTarget()
      }
    },
    
    previousStep() {
      if (this.currentStepIndex > 0) {
        this.currentStepIndex--
        this.highlightTarget()
      }
    },
    
    skipGuide() {
      this.completeGuide()
    },
    
    completeGuide() {
      this.showGuide = false
      this.removeHighlight()
      
      // 记录用户已完成引导
      localStorage.setItem('user_guide_completed', 'true')
      
      // 触发完成回调
      this.onComplete()
      
      this.$message.success(this.$t('guide.completed'))
    },
    
    highlightTarget() {
      this.removeHighlight()
      
      const step = this.currentStep
      if (step.target) {
        const target = document.querySelector(step.target)
        if (target) {
          target.classList.add('guide-highlight')
          
          // 滚动到目标元素
          target.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
          })
        }
      }
    },
    
    removeHighlight() {
      const highlighted = document.querySelectorAll('.guide-highlight')
      highlighted.forEach(el => {
        el.classList.remove('guide-highlight')
      })
    },
    
    // 检查是否需要显示引导
    shouldShowGuide() {
      const completed = localStorage.getItem('user_guide_completed')
      return completed !== 'true'
    }
  },
  
  beforeDestroy() {
    this.removeHighlight()
  }
}
</script>

<style scoped>
.user-guide {
  position: relative;
}

.guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guide-step {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  min-width: 300px;
  animation: fadeInScale 0.3s ease-out;
}

.guide-content {
  padding: 20px;
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.guide-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.guide-close {
  color: #909399;
  font-size: 16px;
}

.guide-body {
  margin-bottom: 20px;
}

.guide-body p {
  margin: 0 0 15px 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

.guide-image {
  text-align: center;
  margin-top: 15px;
}

.guide-image img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.guide-footer {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.guide-progress {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 12px;
  color: #909399;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background-color: #ebeef5;
  border-radius: 2px;
  margin-left: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #409eff;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.guide-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.guide-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 8px solid transparent;
}

.guide-arrow.top {
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: white;
}

.guide-arrow.bottom {
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: white;
}

.guide-arrow.left {
  left: -16px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: white;
}

.guide-arrow.right {
  right: -16px;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: white;
}

.help-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  z-index: 1000;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 在大屏幕上隐藏帮助按钮 */
@media (min-width: 1200px) {
  .help-button {
    display: none;
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 高亮样式 */
:global(.guide-highlight) {
  position: relative;
  z-index: 10001;
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.3), 0 0 0 8px rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.3), 0 0 0 8px rgba(64, 158, 255, 0.1);
  }
  50% {
    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0.2), 0 0 0 12px rgba(64, 158, 255, 0.05);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.3), 0 0 0 8px rgba(64, 158, 255, 0.1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .guide-step {
    max-width: 90vw;
    margin: 20px;
  }
  
  .help-button {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
  }
}
</style>