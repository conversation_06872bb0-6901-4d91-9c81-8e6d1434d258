[{"tenant_id": "tenant1", "concurrent_threads": 10, "total_requests": 500, "success_count": 500, "blocked_count": 0, "error_count": 0, "success_rate": 100.0, "block_rate": 0.0, "expected_block_rate": 50.0, "avg_response_time": 0.008, "limit_types": {}, "passed": false, "message": "线程限流测试失败 - 实际限流率: 0.00%, 期望限流率: 50.00%"}, {"tenant_id": "tenant2", "concurrent_threads": 10, "total_requests": 500, "success_count": 500, "blocked_count": 0, "error_count": 0, "success_rate": 100.0, "block_rate": 0.0, "expected_block_rate": 50.0, "avg_response_time": 0.008, "limit_types": {}, "passed": false, "message": "线程限流测试失败 - 实际限流率: 0.00%, 期望限流率: 50.00%"}, {"tenant_id": "tenant3", "concurrent_threads": 10, "total_requests": 500, "success_count": 500, "blocked_count": 0, "error_count": 0, "success_rate": 100.0, "block_rate": 0.0, "expected_block_rate": 50.0, "avg_response_time": 0.008, "limit_types": {}, "passed": false, "message": "线程限流测试失败 - 实际限流率: 0.00%, 期望限流率: 50.00%"}, {"tenant_id": "tenant4", "concurrent_threads": 10, "total_requests": 500, "success_count": 500, "blocked_count": 0, "error_count": 0, "success_rate": 100.0, "block_rate": 0.0, "expected_block_rate": 50.0, "avg_response_time": 0.007, "limit_types": {}, "passed": false, "message": "线程限流测试失败 - 实际限流率: 0.00%, 期望限流率: 50.00%"}, {"tenant_id": "tenant5", "concurrent_threads": 10, "total_requests": 500, "success_count": 500, "blocked_count": 0, "error_count": 0, "success_rate": 100.0, "block_rate": 0.0, "expected_block_rate": 50.0, "avg_response_time": 0.008, "limit_types": {}, "passed": false, "message": "线程限流测试失败 - 实际限流率: 0.00%, 期望限流率: 50.00%"}]