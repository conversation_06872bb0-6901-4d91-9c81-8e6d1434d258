# IP维度配置功能实施方案

## 1. 功能概述

基于现有的Sentinel流量控制系统，增强IP维度的流量控制能力，提供更精细化的IP级别管控功能。支持单个IP、IP段(CIDR格式)、IP范围配置，以及黑白名单管理。

- **目标**: 实现全面的IP级别流量控制
- **范围**: 后端API增强 + 前端管理界面
- **优先级**: 高优先级任务

## 2. 功能需求分析

### 2.1 核心功能

#### 2.1.1 IP规则类型
- **单个IP控制**: 支持IPv4和IPv6地址
- **IP段控制**: 支持CIDR格式 (如: ***********/24)
- **IP范围控制**: 支持起始-结束IP范围
- **黑白名单**: 支持IP黑名单和白名单机制

#### 2.1.2 规则配置
- **QPS限制**: 每个IP的访问频率控制
- **时间窗口**: 支持秒级、分钟级、小时级时间窗口
- **生效时间**: 支持定时生效和失效
- **优先级**: 支持规则优先级排序

#### 2.1.3 管理功能
- **批量导入**: 支持CSV/Excel格式批量导入
- **批量导出**: 支持规则导出备份
- **规则模板**: 预定义常用规则模板
- **规则验证**: 配置前验证规则有效性

### 2.2 增强功能

#### 2.2.1 IP信息增强
- **归属地查询**: 显示IP地址的地理位置信息
- **访问统计**: 统计IP的访问频率和行为
- **异常检测**: 自动识别异常访问模式的IP
- **历史记录**: 记录IP访问历史和限流记录

#### 2.2.2 智能分析
- **访问模式分析**: 分析IP访问时间分布
- **风险评估**: 基于访问行为评估IP风险等级
- **自动建议**: 根据访问模式自动建议限流规则
- **趋势预测**: 预测IP访问趋势变化

## 3. 技术架构设计

### 3.1 数据库设计

#### 3.1.1 IP规则表 (ip_flow_rules)
```sql
CREATE TABLE ip_flow_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_type ENUM('SINGLE_IP', 'IP_RANGE', 'IP_CIDR') NOT NULL COMMENT '规则类型',
    ip_address VARCHAR(45) COMMENT '单个IP地址',
    ip_start VARCHAR(45) COMMENT 'IP范围起始地址',
    ip_end VARCHAR(45) COMMENT 'IP范围结束地址',
    ip_cidr VARCHAR(50) COMMENT 'CIDR格式IP段',
    list_type ENUM('WHITELIST', 'BLACKLIST', 'LIMIT') NOT NULL COMMENT '名单类型',
    qps_limit INT DEFAULT 0 COMMENT 'QPS限制',
    time_window INT DEFAULT 1 COMMENT '时间窗口(秒)',
    priority INT DEFAULT 0 COMMENT '优先级',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    start_time DATETIME COMMENT '生效开始时间',
    end_time DATETIME COMMENT '生效结束时间',
    description TEXT COMMENT '规则描述',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_rule_type (rule_type),
    INDEX idx_enabled (enabled),
    INDEX idx_priority (priority),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time)
) COMMENT 'IP流量控制规则表';
```

#### 3.1.2 IP访问统计表 (ip_access_stats)
```sql
CREATE TABLE ip_access_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    ip_address VARCHAR(45) NOT NULL,
    access_date DATE NOT NULL,
    access_hour TINYINT NOT NULL,
    request_count BIGINT DEFAULT 0,
    blocked_count BIGINT DEFAULT 0,
    avg_response_time DECIMAL(10,2) DEFAULT 0,
    last_access_time DATETIME,
    location_country VARCHAR(50),
    location_province VARCHAR(50),
    location_city VARCHAR(50),
    risk_score DECIMAL(5,2) DEFAULT 0,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_ip_date_hour (ip_address, access_date, access_hour),
    INDEX idx_access_date (access_date),
    INDEX idx_risk_score (risk_score)
) COMMENT 'IP访问统计表';
```

#### 3.1.3 IP地理位置缓存表 (ip_location_cache)
```sql
CREATE TABLE ip_location_cache (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    ip_address VARCHAR(45) NOT NULL UNIQUE,
    country VARCHAR(50),
    province VARCHAR(50),
    city VARCHAR(50),
    isp VARCHAR(100),
    latitude DECIMAL(10,6),
    longitude DECIMAL(10,6),
    cache_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    expire_time DATETIME,
    INDEX idx_expire_time (expire_time)
) COMMENT 'IP地理位置缓存表';
```

### 3.2 后端API设计

#### 3.2.1 IP规则管理API
```java
@RestController
@RequestMapping("/api/ip-rules")
public class IPRuleController {
    
    @GetMapping
    public Result<PageResult<IPFlowRule>> getIPRules(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String ruleType,
            @RequestParam(required = false) String listType) {
        // 分页查询IP规则
    }
    
    @PostMapping
    public Result<IPFlowRule> createIPRule(@RequestBody @Valid IPRuleCreateRequest request) {
        // 创建IP规则
    }
    
    @PutMapping("/{id}")
    public Result<IPFlowRule> updateIPRule(
            @PathVariable Long id, 
            @RequestBody @Valid IPRuleUpdateRequest request) {
        // 更新IP规则
    }
    
    @DeleteMapping("/{id}")
    public Result<Void> deleteIPRule(@PathVariable Long id) {
        // 删除IP规则
    }
    
    @PostMapping("/batch")
    public Result<BatchOperationResult> batchOperation(
            @RequestBody @Valid BatchOperationRequest request) {
        // 批量操作(启用/禁用/删除)
    }
    
    @PostMapping("/import")
    public Result<ImportResult> importRules(
            @RequestParam("file") MultipartFile file,
            @RequestParam(defaultValue = "false") boolean overwrite) {
        // 批量导入规则
    }
    
    @GetMapping("/export")
    public void exportRules(
            HttpServletResponse response,
            @RequestParam(required = false) List<Long> ids) {
        // 导出规则
    }
    
    @GetMapping("/templates")
    public Result<List<IPRuleTemplate>> getRuleTemplates() {
        // 获取规则模板
    }
    
    @PostMapping("/validate")
    public Result<ValidationResult> validateRule(
            @RequestBody @Valid IPRuleValidationRequest request) {
        // 验证规则有效性
    }
}
```

#### 3.2.2 IP统计分析API
```java
@RestController
@RequestMapping("/api/ip-stats")
public class IPStatsController {
    
    @GetMapping("/access")
    public Result<List<IPAccessStats>> getAccessStats(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) String ipAddress) {
        // 获取IP访问统计
    }
    
    @GetMapping("/top")
    public Result<List<TopIPStats>> getTopIPs(
            @RequestParam(defaultValue = "REQUEST_COUNT") String sortBy,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(defaultValue = "TODAY") String timeRange) {
        // 获取TOP IP统计
    }
    
    @GetMapping("/location")
    public Result<List<IPLocationStats>> getLocationStats(
            @RequestParam(defaultValue = "TODAY") String timeRange) {
        // 获取IP地理位置统计
    }
    
    @GetMapping("/risk")
    public Result<List<RiskIPStats>> getRiskIPs(
            @RequestParam(defaultValue = "HIGH") String riskLevel,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        // 获取风险IP列表
    }
    
    @GetMapping("/trends")
    public Result<IPTrendStats> getAccessTrends(
            @RequestParam String ipAddress,
            @RequestParam(defaultValue = "7") int days) {
        // 获取IP访问趋势
    }
}
```

### 3.3 核心服务实现

#### 3.3.1 IP规则匹配服务
```java
@Service
public class IPRuleMatchService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final IPRuleRepository ipRuleRepository;
    
    /**
     * 检查IP是否匹配规则
     */
    public IPRuleMatchResult matchIPRule(String ipAddress, String tenantId) {
        // 1. 从缓存获取IP规则
        List<IPFlowRule> rules = getCachedIPRules(tenantId);
        
        // 2. 按优先级排序规则
        rules.sort(Comparator.comparing(IPFlowRule::getPriority).reversed());
        
        // 3. 逐个匹配规则
        for (IPFlowRule rule : rules) {
            if (isIPMatched(ipAddress, rule)) {
                return new IPRuleMatchResult(rule, true);
            }
        }
        
        return new IPRuleMatchResult(null, false);
    }
    
    /**
     * 检查IP是否匹配单个规则
     */
    private boolean isIPMatched(String ipAddress, IPFlowRule rule) {
        switch (rule.getRuleType()) {
            case SINGLE_IP:
                return ipAddress.equals(rule.getIpAddress());
            case IP_RANGE:
                return isIPInRange(ipAddress, rule.getIpStart(), rule.getIpEnd());
            case IP_CIDR:
                return isIPInCIDR(ipAddress, rule.getIpCidr());
            default:
                return false;
        }
    }
    
    /**
     * 检查IP是否在CIDR范围内
     */
    private boolean isIPInCIDR(String ipAddress, String cidr) {
        try {
            SubnetUtils subnet = new SubnetUtils(cidr);
            return subnet.getInfo().isInRange(ipAddress);
        } catch (Exception e) {
            log.error("Invalid CIDR format: {}", cidr, e);
            return false;
        }
    }
    
    /**
     * 检查IP是否在范围内
     */
    private boolean isIPInRange(String ipAddress, String startIP, String endIP) {
        try {
            long ip = ipToLong(ipAddress);
            long start = ipToLong(startIP);
            long end = ipToLong(endIP);
            return ip >= start && ip <= end;
        } catch (Exception e) {
            log.error("Invalid IP range: {} - {}", startIP, endIP, e);
            return false;
        }
    }
    
    /**
     * IP地址转换为长整型
     */
    private long ipToLong(String ipAddress) {
        String[] parts = ipAddress.split("\\.");
        long result = 0;
        for (int i = 0; i < 4; i++) {
            result = result * 256 + Integer.parseInt(parts[i]);
        }
        return result;
    }
}
```

#### 3.3.2 IP地理位置服务
```java
@Service
public class IPLocationService {
    
    private final IPLocationCacheRepository cacheRepository;
    private final RestTemplate restTemplate;
    
    /**
     * 获取IP地理位置信息
     */
    public IPLocationInfo getIPLocation(String ipAddress) {
        // 1. 先从缓存查询
        IPLocationCache cached = cacheRepository.findByIpAddress(ipAddress);
        if (cached != null && !cached.isExpired()) {
            return convertToLocationInfo(cached);
        }
        
        // 2. 调用第三方API查询
        IPLocationInfo locationInfo = queryFromThirdParty(ipAddress);
        
        // 3. 更新缓存
        updateLocationCache(ipAddress, locationInfo);
        
        return locationInfo;
    }
    
    /**
     * 调用第三方IP地理位置API
     */
    private IPLocationInfo queryFromThirdParty(String ipAddress) {
        try {
            // 使用免费的IP地理位置API
            String url = "http://ip-api.com/json/" + ipAddress + "?lang=zh-CN";
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> data = response.getBody();
                return IPLocationInfo.builder()
                    .country((String) data.get("country"))
                    .province((String) data.get("regionName"))
                    .city((String) data.get("city"))
                    .isp((String) data.get("isp"))
                    .latitude((Double) data.get("lat"))
                    .longitude((Double) data.get("lon"))
                    .build();
            }
        } catch (Exception e) {
            log.error("Failed to query IP location for: {}", ipAddress, e);
        }
        
        return IPLocationInfo.unknown();
    }
    
    /**
     * 更新地理位置缓存
     */
    private void updateLocationCache(String ipAddress, IPLocationInfo locationInfo) {
        IPLocationCache cache = new IPLocationCache();
        cache.setIpAddress(ipAddress);
        cache.setCountry(locationInfo.getCountry());
        cache.setProvince(locationInfo.getProvince());
        cache.setCity(locationInfo.getCity());
        cache.setIsp(locationInfo.getIsp());
        cache.setLatitude(locationInfo.getLatitude());
        cache.setLongitude(locationInfo.getLongitude());
        cache.setCacheTime(LocalDateTime.now());
        cache.setExpireTime(LocalDateTime.now().plusDays(7)); // 缓存7天
        
        cacheRepository.save(cache);
    }
}
```

#### 3.3.3 IP访问统计服务
```java
@Service
public class IPAccessStatsService {
    
    private final IPAccessStatsRepository statsRepository;
    private final IPLocationService locationService;
    
    /**
     * 记录IP访问统计
     */
    @Async
    public void recordIPAccess(String ipAddress, boolean blocked, long responseTime) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate date = now.toLocalDate();
        int hour = now.getHour();
        
        // 查找或创建统计记录
        IPAccessStats stats = statsRepository
            .findByIpAddressAndAccessDateAndAccessHour(ipAddress, date, hour)
            .orElse(new IPAccessStats(ipAddress, date, hour));
        
        // 更新统计数据
        stats.incrementRequestCount();
        if (blocked) {
            stats.incrementBlockedCount();
        }
        stats.updateResponseTime(responseTime);
        stats.setLastAccessTime(now);
        
        // 获取地理位置信息(首次访问时)
        if (stats.getLocationCountry() == null) {
            IPLocationInfo location = locationService.getIPLocation(ipAddress);
            stats.setLocationCountry(location.getCountry());
            stats.setLocationProvince(location.getProvince());
            stats.setLocationCity(location.getCity());
        }
        
        // 计算风险评分
        stats.setRiskScore(calculateRiskScore(stats));
        
        statsRepository.save(stats);
    }
    
    /**
     * 计算IP风险评分
     */
    private double calculateRiskScore(IPAccessStats stats) {
        double score = 0.0;
        
        // 基于访问频率
        if (stats.getRequestCount() > 1000) {
            score += 30;
        } else if (stats.getRequestCount() > 500) {
            score += 20;
        } else if (stats.getRequestCount() > 100) {
            score += 10;
        }
        
        // 基于被阻止次数
        double blockRate = (double) stats.getBlockedCount() / stats.getRequestCount();
        if (blockRate > 0.5) {
            score += 40;
        } else if (blockRate > 0.2) {
            score += 20;
        } else if (blockRate > 0.1) {
            score += 10;
        }
        
        // 基于响应时间异常
        if (stats.getAvgResponseTime().doubleValue() > 5000) {
            score += 20;
        } else if (stats.getAvgResponseTime().doubleValue() > 2000) {
            score += 10;
        }
        
        return Math.min(score, 100.0);
    }
}
```

## 4. 前端界面设计

### 4.1 IP规则管理页面

#### 4.1.1 页面布局
```vue
<template>
  <div class="ip-rules-page">
    <!-- 搜索和操作栏 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索IP地址或规则名称"
            clearable
            @change="handleSearch">
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.ruleType" placeholder="规则类型" clearable>
            <el-option label="单个IP" value="SINGLE_IP" />
            <el-option label="IP范围" value="IP_RANGE" />
            <el-option label="IP段" value="IP_CIDR" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.listType" placeholder="名单类型" clearable>
            <el-option label="白名单" value="WHITELIST" />
            <el-option label="黑名单" value="BLACKLIST" />
            <el-option label="限流" value="LIMIT" />
          </el-select>
        </el-col>
        <el-col :span="10">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增规则
          </el-button>
          <el-button @click="handleBatchImport">
            <el-icon><Upload /></el-icon>
            批量导入
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出规则
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedRules.length === 0"
            @click="handleBatchDelete">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 规则列表 -->
    <div class="rules-table">
      <el-table
        :data="rulesList"
        v-loading="loading"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="ruleName" label="规则名称" width="150" />
        <el-table-column prop="ruleType" label="规则类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getRuleTypeTagType(row.ruleType)">
              {{ getRuleTypeText(row.ruleType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="IP配置" width="200">
          <template #default="{ row }">
            <div v-if="row.ruleType === 'SINGLE_IP'">
              {{ row.ipAddress }}
            </div>
            <div v-else-if="row.ruleType === 'IP_RANGE'">
              {{ row.ipStart }} - {{ row.ipEnd }}
            </div>
            <div v-else-if="row.ruleType === 'IP_CIDR'">
              {{ row.ipCidr }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="listType" label="名单类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getListTypeTagType(row.listType)">
              {{ getListTypeText(row.listType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="qpsLimit" label="QPS限制" width="100" />
        <el-table-column prop="priority" label="优先级" width="80" />
        <el-table-column prop="enabled" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.enabled"
              @change="handleStatusChange(row)" />
          </template>
        </el-table-column>
        <el-table-column label="生效时间" width="180">
          <template #default="{ row }">
            <div v-if="row.startTime">
              {{ formatDateTime(row.startTime) }}
            </div>
            <div v-if="row.endTime" class="text-gray-500">
              至 {{ formatDateTime(row.endTime) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button size="small" @click="handleViewStats(row)">
              统计
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange" />
      </div>
    </div>

    <!-- 规则编辑对话框 -->
    <IPRuleDialog
      v-model:visible="dialogVisible"
      :rule="currentRule"
      :mode="dialogMode"
      @success="handleDialogSuccess" />

    <!-- 批量导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      @success="handleImportSuccess" />

    <!-- IP统计对话框 -->
    <IPStatsDialog
      v-model:visible="statsDialogVisible"
      :ip-address="currentIP" />
  </div>
</template>
```

#### 4.1.2 规则编辑对话框
```vue
<template>
  <el-dialog
    :title="dialogTitle"
    v-model="visible"
    width="800px"
    :before-close="handleClose">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规则名称" prop="ruleName">
            <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规则类型" prop="ruleType">
            <el-select 
              v-model="form.ruleType" 
              placeholder="请选择规则类型"
              @change="handleRuleTypeChange">
              <el-option label="单个IP" value="SINGLE_IP" />
              <el-option label="IP范围" value="IP_RANGE" />
              <el-option label="IP段(CIDR)" value="IP_CIDR" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 单个IP配置 -->
      <el-form-item 
        v-if="form.ruleType === 'SINGLE_IP'"
        label="IP地址" 
        prop="ipAddress">
        <el-input 
          v-model="form.ipAddress" 
          placeholder="请输入IP地址，如：*************">
          <template #append>
            <el-button @click="handleIPLookup">查询位置</el-button>
          </template>
        </el-input>
      </el-form-item>

      <!-- IP范围配置 -->
      <el-row v-if="form.ruleType === 'IP_RANGE'" :gutter="20">
        <el-col :span="12">
          <el-form-item label="起始IP" prop="ipStart">
            <el-input v-model="form.ipStart" placeholder="如：***********" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束IP" prop="ipEnd">
            <el-input v-model="form.ipEnd" placeholder="如：*************" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- CIDR配置 -->
      <el-form-item 
        v-if="form.ruleType === 'IP_CIDR'"
        label="IP段(CIDR)" 
        prop="ipCidr">
        <el-input 
          v-model="form.ipCidr" 
          placeholder="请输入CIDR格式，如：***********/24">
          <template #append>
            <el-button @click="handleCIDRValidate">验证</el-button>
          </template>
        </el-input>
        <div class="form-tip">
          CIDR格式说明：/24表示前24位为网络位，可匹配256个IP地址
        </div>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="名单类型" prop="listType">
            <el-select v-model="form.listType" placeholder="请选择名单类型">
              <el-option label="白名单" value="WHITELIST">
                <span>白名单</span>
                <span class="option-desc">允许访问，不受限制</span>
              </el-option>
              <el-option label="黑名单" value="BLACKLIST">
                <span>黑名单</span>
                <span class="option-desc">禁止访问</span>
              </el-option>
              <el-option label="限流" value="LIMIT">
                <span>限流</span>
                <span class="option-desc">限制访问频率</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-input-number 
              v-model="form.priority" 
              :min="0" 
              :max="999" 
              placeholder="数值越大优先级越高" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 限流配置 -->
      <el-row v-if="form.listType === 'LIMIT'" :gutter="20">
        <el-col :span="12">
          <el-form-item label="QPS限制" prop="qpsLimit">
            <el-input-number 
              v-model="form.qpsLimit" 
              :min="1" 
              placeholder="每秒请求数限制" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="时间窗口" prop="timeWindow">
            <el-select v-model="form.timeWindow" placeholder="选择时间窗口">
              <el-option label="1秒" :value="1" />
              <el-option label="10秒" :value="10" />
              <el-option label="1分钟" :value="60" />
              <el-option label="5分钟" :value="300" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 生效时间 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="生效开始时间">
            <el-date-picker
              v-model="form.startTime"
              type="datetime"
              placeholder="选择开始时间"
              format="YYYY-MM-DD HH:mm:ss" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效结束时间">
            <el-date-picker
              v-model="form.endTime"
              type="datetime"
              placeholder="选择结束时间"
              format="YYYY-MM-DD HH:mm:ss" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="规则描述">
        <el-input 
          v-model="form.description" 
          type="textarea" 
          :rows="3" 
          placeholder="请输入规则描述" />
      </el-form-item>

      <!-- IP位置信息显示 -->
      <el-form-item v-if="ipLocationInfo" label="IP位置信息">
        <div class="ip-location-info">
          <el-tag>{{ ipLocationInfo.country }}</el-tag>
          <el-tag v-if="ipLocationInfo.province">{{ ipLocationInfo.province }}</el-tag>
          <el-tag v-if="ipLocationInfo.city">{{ ipLocationInfo.city }}</el-tag>
          <el-tag v-if="ipLocationInfo.isp" type="info">{{ ipLocationInfo.isp }}</el-tag>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button @click="handleValidate">验证规则</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ mode === 'create' ? '创建' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>
```

### 4.2 IP统计分析页面

#### 4.2.1 统计概览
```vue
<template>
  <div class="ip-stats-page">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#409EFF"><Monitor /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ totalIPs }}</div>
              <div class="stats-label">活跃IP数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#67C23A"><Connection /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ totalRequests }}</div>
              <div class="stats-label">总请求数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#F56C6C"><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ blockedRequests }}</div>
              <div class="stats-label">被阻止请求</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#E6A23C"><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ riskIPs }}</div>
              <div class="stats-label">风险IP数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card title="访问趋势">
          <AccessTrendChart :data="trendData" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card title="地理分布">
          <LocationDistributionChart :data="locationData" />
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <el-col :span="24">
        <el-card title="TOP IP统计">
          <TopIPsTable :data="topIPsData" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 风险IP列表 -->
    <el-card title="风险IP监控" class="risk-ips-section">
      <RiskIPsTable :data="riskIPsData" @add-rule="handleAddRule" />
    </el-card>
  </div>
</template>
```

## 5. 实施计划

### 5.1 开发阶段

#### 第一阶段：数据库和基础API (1周)
- [X] 设计IP规则相关数据表
- [ ] 实现基础的CRUD API接口
- [ ] 实现IP规则匹配核心逻辑
- [ ] 集成IP地理位置查询服务

#### 第二阶段：规则管理功能 (1-2周)
- [ ] 实现IP规则管理界面
- [ ] 支持多种IP规则类型配置
- [ ] 实现批量导入导出功能
- [ ] 添加规则验证和预览功能

#### 第三阶段：统计分析功能 (1周)
- [ ] 实现IP访问统计收集
- [ ] 开发统计分析API
- [ ] 创建统计分析界面
- [ ] 实现风险IP识别算法

#### 第四阶段：集成测试 (1周)
- [ ] 与现有流量控制系统集成
- [ ] 编写完整的测试用例
- [ ] 性能测试和优化
- [ ] 文档编写和培训

### 5.2 测试验证

#### 5.2.1 功能测试
- [ ] IP规则匹配准确性测试
- [ ] 批量操作功能测试
- [ ] 地理位置查询准确性测试
- [ ] 统计数据准确性测试

#### 5.2.2 性能测试
- [ ] 大量IP规则匹配性能测试
- [ ] 高并发访问统计性能测试
- [ ] 数据库查询性能优化
- [ ] 缓存策略效果验证

#### 5.2.3 安全测试
- [ ] IP地址格式验证测试
- [ ] CIDR格式安全性测试
- [ ] 批量导入安全性测试
- [ ] 权限控制测试

## 6. 风险评估

### 6.1 技术风险
- **IP地址解析复杂性**: 需要处理IPv4和IPv6格式
- **CIDR计算性能**: 大量CIDR规则匹配可能影响性能
- **第三方API依赖**: IP地理位置查询依赖外部服务

### 6.2 业务风险
- **规则配置错误**: 错误的IP规则可能影响正常用户访问
- **数据隐私**: IP地址属于敏感信息，需要合规处理
- **存储成本**: IP访问统计数据量可能很大

### 6.3 应对措施
- 实现完善的规则验证机制
- 建立IP数据脱敏和清理策略
- 采用数据分区和归档策略
- 提供规则回滚和恢复功能

## 7. 成功标准

### 7.1 功能完整性
- [X] 支持单个IP、IP段、IP范围三种规则类型
- [ ] 实现黑白名单和限流三种控制模式
- [ ] 提供批量导入导出功能
- [ ] 集成IP地理位置查询
- [ ] 实现访问统计和风险分析

### 7.2 性能指标
- [ ] IP规则匹配响应时间 < 10ms
- [ ] 支持10万+IP规则同时生效
- [ ] 统计数据查询响应时间 < 1s
- [ ] 批量导入1万条规则 < 30s

### 7.3 用户体验
- [ ] 界面操作简洁直观
- [ ] 提供规则配置向导
- [ ] 错误提示清晰准确
- [ ] 支持规则模板和快速配置

这个IP维度配置功能实施方案提供了完整的技术架构、开发计划和实施细节，可以作为项目开发的详细指导文档。