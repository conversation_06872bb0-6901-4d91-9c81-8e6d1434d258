# IP地址维度限制配置功能需求文档

## 1. 产品概述

本文档描述了Sentinel流量控制系统中IP地址维度限制配置功能的详细需求。该功能将增强现有的IP级别流量控制，提供更灵活和强大的IP管理能力，包括IP段控制、黑白名单管理、批量操作等功能。

- **主要目标**: 提供精细化的IP级别访问控制
- **适用场景**: 防止恶意攻击、限制特定地区访问、VIP用户白名单等
- **核心价值**: 提升系统安全性和访问控制的灵活性

## 2. 核心功能

### 2.1 用户角色

| 角色 | 权限描述 | IP规则权限 |
|------|----------|------------|
| 系统管理员 | 完全权限 | 创建、修改、删除所有IP规则 |
| 安全管理员 | IP安全管理 | 管理黑白名单、查看IP统计 |
| 运维人员 | 基础操作 | 查看IP规则、临时封禁IP |
| 开发人员 | 只读权限 | 查看IP规则和统计数据 |

### 2.2 功能模块

本IP配置功能包含以下主要模块：

1. **IP规则管理**: IP黑白名单的增删改查、规则优先级管理
2. **IP段配置**: 支持CIDR格式的IP段配置和管理
3. **批量操作**: IP规则的批量导入、导出、启用、禁用
4. **IP统计分析**: IP访问频率统计、归属地显示、异常检测
5. **规则模板**: 预定义的IP规则模板和快速配置
6. **定时任务**: IP规则的定时生效和失效管理

### 2.3 页面详细功能

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| IP规则管理 | 规则列表 | 展示所有IP规则，支持按类型、状态、创建时间筛选 |
| IP规则管理 | 规则创建 | 创建新的IP规则，支持单IP、IP段、IP范围配置 |
| IP规则管理 | 规则编辑 | 修改现有IP规则，支持批量编辑和快速操作 |
| IP规则管理 | 优先级管理 | 设置IP规则的优先级，处理规则冲突 |
| 黑白名单管理 | 黑名单配置 | 管理被禁止访问的IP地址和IP段 |
| 黑白名单管理 | 白名单配置 | 管理允许访问的IP地址，不受其他限制影响 |
| 批量操作 | 批量导入 | 支持CSV、Excel格式的IP规则批量导入 |
| 批量操作 | 批量导出 | 导出IP规则配置，支持多种格式 |
| IP统计分析 | 访问统计 | 显示IP访问频率、请求量统计 |
| IP统计分析 | 归属地分析 | 显示IP地址的地理位置信息 |
| IP统计分析 | 异常检测 | 自动识别异常访问模式的IP地址 |
| 规则模板 | 模板管理 | 创建和管理IP规则模板 |
| 规则模板 | 快速配置 | 基于模板快速创建IP规则 |

## 3. 核心流程

### 3.1 IP规则管理流程

**创建IP规则流程**:
选择规则类型 → 配置IP地址/段 → 设置限制参数 → 设置生效时间 → 保存并生效 → 验证规则效果

**批量导入流程**:
准备导入文件 → 上传文件 → 数据验证 → 预览导入结果 → 确认导入 → 规则生效

**异常IP处理流程**:
异常检测告警 → 查看IP详情 → 分析访问模式 → 决定处理方式 → 添加到黑名单 → 监控效果

### 3.2 系统交互流程

```mermaid
graph TD
    A[用户请求] --> B[Gateway网关]
    B --> C[IP规则检查]
    C --> D{IP规则匹配?}
    D -->|匹配黑名单| E[拒绝访问]
    D -->|匹配白名单| F[允许访问]
    D -->|匹配限流规则| G[应用限流策略]
    D -->|无匹配规则| H[默认处理]
    G --> I{是否超过限制?}
    I -->|是| J[限流或排队]
    I -->|否| F
    E --> K[记录访问日志]
    F --> K
    J --> K
    H --> K
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**: 蓝色系 (#409EFF) 表示正常规则，红色 (#F56C6C) 表示黑名单
- **状态色彩**: 绿色表示白名单，橙色表示限流规则，灰色表示禁用规则
- **按钮样式**: 危险操作使用红色按钮，批量操作使用主色调按钮
- **图标使用**: 使用直观的图标表示不同类型的IP规则
- **布局风格**: 表格 + 侧边详情面板的布局方式

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| IP规则列表 | 筛选器 | 下拉选择框、日期选择器、搜索框组合 |
| IP规则列表 | 数据表格 | 支持排序、分页、行选择的表格组件 |
| IP规则列表 | 操作按钮 | 批量操作工具栏，单行操作下拉菜单 |
| 规则创建/编辑 | 表单组件 | 分步骤表单，动态显示配置项 |
| 规则创建/编辑 | IP输入器 | 支持IP地址验证的输入组件 |
| 批量导入 | 文件上传 | 拖拽上传组件，支持格式验证 |
| 批量导入 | 数据预览 | 表格形式预览导入数据，支持错误标记 |
| IP统计 | 图表展示 | 使用ECharts展示IP访问统计和地理分布 |
| IP统计 | 数据卡片 | 关键指标的卡片式展示 |

### 4.3 交互设计

- **智能提示**: IP地址输入时提供格式验证和自动补全
- **批量选择**: 支持全选、反选、条件选择等批量操作
- **实时验证**: 表单输入时实时验证IP地址格式和规则冲突
- **操作确认**: 危险操作（如删除、禁用）需要二次确认
- **状态反馈**: 操作结果的即时反馈和进度显示

## 5. 技术实现

### 5.1 数据库设计

#### IP规则表 (ip_rules)

```sql
CREATE TABLE ip_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_type ENUM('WHITELIST', 'BLACKLIST', 'RATE_LIMIT') NOT NULL COMMENT '规则类型',
    ip_address VARCHAR(45) COMMENT '单个IP地址',
    ip_range_start VARCHAR(45) COMMENT 'IP范围起始',
    ip_range_end VARCHAR(45) COMMENT 'IP范围结束',
    cidr_notation VARCHAR(50) COMMENT 'CIDR格式IP段',
    rate_limit_qps INT COMMENT '限流QPS',
    priority INT DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    effective_time DATETIME COMMENT '生效时间',
    expiry_time DATETIME COMMENT '失效时间',
    description TEXT COMMENT '规则描述',
    created_by VARCHAR(50) COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_rule_type (rule_type),
    INDEX idx_enabled (enabled),
    INDEX idx_priority (priority DESC),
    INDEX idx_effective_time (effective_time),
    INDEX idx_ip_address (ip_address)
);
```

#### IP访问统计表 (ip_access_stats)

```sql
CREATE TABLE ip_access_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    ip_address VARCHAR(45) NOT NULL,
    access_count BIGINT DEFAULT 0 COMMENT '访问次数',
    blocked_count BIGINT DEFAULT 0 COMMENT '被阻止次数',
    last_access_time TIMESTAMP COMMENT '最后访问时间',
    country VARCHAR(50) COMMENT '国家',
    region VARCHAR(50) COMMENT '地区',
    city VARCHAR(50) COMMENT '城市',
    isp VARCHAR(100) COMMENT '运营商',
    risk_score INT DEFAULT 0 COMMENT '风险评分 0-100',
    stat_date DATE NOT NULL COMMENT '统计日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_ip_date (ip_address, stat_date),
    INDEX idx_access_count (access_count DESC),
    INDEX idx_risk_score (risk_score DESC),
    INDEX idx_stat_date (stat_date)
);
```

### 5.2 后端API设计

#### IP规则管理API

```java
@RestController
@RequestMapping("/api/ip-rules")
public class IpRuleController {
    
    // 获取IP规则列表
    @GetMapping
    public PageResult<IpRuleVO> getIpRules(
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "20") int size,
        @RequestParam(required = false) String ruleType,
        @RequestParam(required = false) Boolean enabled,
        @RequestParam(required = false) String keyword
    );
    
    // 创建IP规则
    @PostMapping
    public Result<Long> createIpRule(@RequestBody @Valid IpRuleCreateDTO dto);
    
    // 更新IP规则
    @PutMapping("/{id}")
    public Result<Void> updateIpRule(@PathVariable Long id, @RequestBody @Valid IpRuleUpdateDTO dto);
    
    // 删除IP规则
    @DeleteMapping("/{id}")
    public Result<Void> deleteIpRule(@PathVariable Long id);
    
    // 批量操作
    @PostMapping("/batch")
    public Result<Void> batchOperation(@RequestBody @Valid IpRuleBatchDTO dto);
    
    // 批量导入
    @PostMapping("/import")
    public Result<IpRuleImportResult> importIpRules(@RequestParam("file") MultipartFile file);
    
    // 导出IP规则
    @GetMapping("/export")
    public void exportIpRules(HttpServletResponse response, @RequestParam(required = false) List<Long> ids);
    
    // 验证IP规则
    @PostMapping("/validate")
    public Result<IpRuleValidationResult> validateIpRule(@RequestBody IpRuleValidateDTO dto);
}
```

#### IP统计分析API

```java
@RestController
@RequestMapping("/api/ip-stats")
public class IpStatsController {
    
    // 获取IP访问统计
    @GetMapping("/access")
    public Result<List<IpAccessStatsVO>> getIpAccessStats(
        @RequestParam(required = false) String startDate,
        @RequestParam(required = false) String endDate,
        @RequestParam(defaultValue = "10") int topN
    );
    
    // 获取IP地理分布
    @GetMapping("/geo-distribution")
    public Result<List<IpGeoDistributionVO>> getIpGeoDistribution(
        @RequestParam(required = false) String startDate,
        @RequestParam(required = false) String endDate
    );
    
    // 获取异常IP列表
    @GetMapping("/anomaly")
    public Result<List<AnomalyIpVO>> getAnomalyIps(
        @RequestParam(defaultValue = "70") int riskThreshold
    );
    
    // 获取IP详细信息
    @GetMapping("/detail/{ip}")
    public Result<IpDetailVO> getIpDetail(@PathVariable String ip);
}
```

### 5.3 核心算法实现

#### IP规则匹配算法

```java
@Component
public class IpRuleMatcher {
    
    /**
     * 匹配IP规则，按优先级顺序检查
     */
    public IpRuleMatchResult matchIpRules(String clientIp, List<IpRule> rules) {
        // 按优先级排序
        rules.sort((r1, r2) -> Integer.compare(r2.getPriority(), r1.getPriority()));
        
        for (IpRule rule : rules) {
            if (!rule.isEnabled() || !isRuleEffective(rule)) {
                continue;
            }
            
            if (isIpMatched(clientIp, rule)) {
                return new IpRuleMatchResult(rule, true);
            }
        }
        
        return new IpRuleMatchResult(null, false);
    }
    
    /**
     * 检查IP是否匹配规则
     */
    private boolean isIpMatched(String clientIp, IpRule rule) {
        if (StringUtils.isNotBlank(rule.getIpAddress())) {
            return clientIp.equals(rule.getIpAddress());
        }
        
        if (StringUtils.isNotBlank(rule.getCidrNotation())) {
            return isIpInCidr(clientIp, rule.getCidrNotation());
        }
        
        if (StringUtils.isNotBlank(rule.getIpRangeStart()) && 
            StringUtils.isNotBlank(rule.getIpRangeEnd())) {
            return isIpInRange(clientIp, rule.getIpRangeStart(), rule.getIpRangeEnd());
        }
        
        return false;
    }
    
    /**
     * 检查IP是否在CIDR范围内
     */
    private boolean isIpInCidr(String ip, String cidr) {
        try {
            SubnetUtils subnet = new SubnetUtils(cidr);
            return subnet.getInfo().isInRange(ip);
        } catch (Exception e) {
            log.error("Invalid CIDR notation: {}", cidr, e);
            return false;
        }
    }
    
    /**
     * 检查IP是否在范围内
     */
    private boolean isIpInRange(String ip, String startIp, String endIp) {
        try {
            long ipLong = ipToLong(ip);
            long startLong = ipToLong(startIp);
            long endLong = ipToLong(endIp);
            return ipLong >= startLong && ipLong <= endLong;
        } catch (Exception e) {
            log.error("Invalid IP range: {} - {}", startIp, endIp, e);
            return false;
        }
    }
}
```

#### IP异常检测算法

```java
@Component
public class IpAnomalyDetector {
    
    /**
     * 检测异常IP
     */
    public List<AnomalyIp> detectAnomalyIps(LocalDate date) {
        List<IpAccessStats> stats = ipStatsRepository.findByStatDate(date);
        List<AnomalyIp> anomalies = new ArrayList<>();
        
        // 计算访问量阈值（基于平均值和标准差）
        double avgAccess = stats.stream().mapToLong(IpAccessStats::getAccessCount).average().orElse(0);
        double stdDev = calculateStandardDeviation(stats);
        long threshold = (long) (avgAccess + 2 * stdDev);
        
        for (IpAccessStats stat : stats) {
            int riskScore = calculateRiskScore(stat, threshold);
            if (riskScore >= 70) { // 高风险阈值
                anomalies.add(new AnomalyIp(stat.getIpAddress(), riskScore, 
                    buildRiskReasons(stat, threshold)));
            }
        }
        
        return anomalies;
