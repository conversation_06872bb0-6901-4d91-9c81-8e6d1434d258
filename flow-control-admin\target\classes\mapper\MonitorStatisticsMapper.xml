<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.MonitorStatisticsMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.common.entity.MonitorStatistics">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="stat_time" property="statTime" jdbcType="TIMESTAMP"/>
        <result column="resource_name" property="resourceName" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="total_requests" property="totalRequests" jdbcType="BIGINT"/>
        <result column="pass_requests" property="passRequests" jdbcType="BIGINT"/>
        <result column="block_requests" property="blockRequests" jdbcType="BIGINT"/>
        <result column="queue_requests" property="queueRequests" jdbcType="BIGINT"/>
        <result column="total_rt" property="totalRt" jdbcType="BIGINT"/>
        <result column="avg_rt" property="avgRt" jdbcType="INTEGER"/>
        <result column="max_rt" property="maxRt" jdbcType="INTEGER"/>
        <result column="min_rt" property="minRt" jdbcType="INTEGER"/>
        <result column="stat_type" property="statType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- MonitorVO结果映射 -->
    <resultMap id="MonitorVOResultMap" type="com.example.admin.vo.MonitorVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="stat_time" property="statTime" jdbcType="TIMESTAMP"/>
        <result column="resource_name" property="resourceName" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="total_requests" property="totalRequests" jdbcType="BIGINT"/>
        <result column="pass_requests" property="passRequests" jdbcType="BIGINT"/>
        <result column="block_requests" property="blockRequests" jdbcType="BIGINT"/>
        <result column="queue_requests" property="queueRequests" jdbcType="BIGINT"/>
        <result column="total_rt" property="totalRt" jdbcType="BIGINT"/>
        <result column="avg_rt" property="avgRt" jdbcType="INTEGER"/>
        <result column="max_rt" property="maxRt" jdbcType="INTEGER"/>
        <result column="min_rt" property="minRt" jdbcType="INTEGER"/>
        <result column="stat_type" property="statType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, stat_time, resource_name, tenant_id, total_requests, pass_requests,
        block_requests, queue_requests, total_rt, avg_rt, max_rt, min_rt, stat_type, create_time
    </sql>

    <!-- 分页查询监控统计数据（带租户名称） -->
    <select id="selectMonitorVOPage" resultMap="MonitorVOResultMap">
        SELECT 
            ms.id, ms.stat_time, ms.resource_name, ms.tenant_id, ti.tenant_name,
            ms.total_requests, ms.pass_requests, ms.block_requests, ms.queue_requests,
            ms.total_rt, ms.avg_rt, ms.max_rt, ms.min_rt, ms.stat_type, ms.create_time
        FROM monitor_statistics ms
        LEFT JOIN tenant_info ti ON ms.tenant_id = ti.tenant_id
        WHERE 1=1
        <if test="tenantId != null and tenantId != ''">
            AND ms.tenant_id = #{tenantId}
        </if>
        <if test="resourceName != null and resourceName != ''">
            AND ms.resource_name LIKE CONCAT('%', #{resourceName}, '%')
        </if>
        <if test="statType != null and statType != ''">
            AND ms.stat_type = #{statType}
        </if>
        <if test="startTime != null">
            AND ms.stat_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND ms.stat_time &lt;= #{endTime}
        </if>
        ORDER BY ms.stat_time DESC
    </select>

    <!-- 根据时间范围查询监控统计数据 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM monitor_statistics
        WHERE stat_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="resourceName != null and resourceName != ''">
            AND resource_name = #{resourceName}
        </if>
        <if test="statType != null and statType != ''">
            AND stat_type = #{statType}
        </if>
        ORDER BY stat_time DESC
    </select>

    <!-- 根据租户ID查询监控统计数据 -->
    <select id="selectByTenantId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM monitor_statistics
        WHERE tenant_id = #{tenantId}
        <if test="statType != null and statType != ''">
            AND stat_type = #{statType}
        </if>
        ORDER BY stat_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据资源名称查询监控统计数据 -->
    <select id="selectByResourceName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM monitor_statistics
        WHERE resource_name = #{resourceName}
        <if test="statType != null and statType != ''">
            AND stat_type = #{statType}
        </if>
        ORDER BY stat_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询实时监控数据（最近N分钟） -->
    <select id="selectRealtimeData" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM monitor_statistics
        WHERE stat_time &gt;= DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="resourceName != null and resourceName != ''">
            AND resource_name = #{resourceName}
        </if>
        ORDER BY stat_time DESC
    </select>

    <!-- 查询趋势数据 -->
    <select id="selectTrendData" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(stat_time, '%Y-%m-%d %H:%i:00') as time_point,
            SUM(total_requests) as total_requests,
            SUM(pass_requests) as pass_requests,
            SUM(block_requests) as block_requests,
            SUM(queue_requests) as queue_requests,
            AVG(avg_rt) as avg_rt,
            MAX(max_rt) as max_rt,
            MIN(min_rt) as min_rt
        FROM monitor_statistics
        WHERE stat_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="resourceName != null and resourceName != ''">
            AND resource_name = #{resourceName}
        </if>
        <if test="statType != null and statType != ''">
            AND stat_type = #{statType}
        </if>
        GROUP BY DATE_FORMAT(stat_time, '%Y-%m-%d %H:%i:00')
        ORDER BY time_point
    </select>

    <!-- 统计总请求数 -->
    <select id="sumTotalRequests" resultType="java.lang.Long">
        SELECT COALESCE(SUM(total_requests), 0)
        FROM monitor_statistics
        WHERE stat_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 统计阻塞请求数 -->
    <select id="sumBlockRequests" resultType="java.lang.Long">
        SELECT COALESCE(SUM(block_requests), 0)
        FROM monitor_statistics
        WHERE stat_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 计算平均响应时间 -->
    <select id="avgResponseTime" resultType="java.lang.Double">
        SELECT AVG(avg_rt)
        FROM monitor_statistics
        WHERE stat_time BETWEEN #{startTime} AND #{endTime}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="resourceName != null and resourceName != ''">
            AND resource_name = #{resourceName}
        </if>
    </select>

    <!-- 查询资源排行榜（按QPS） -->
    <select id="selectResourceRankByQps" resultType="java.util.Map">
        SELECT 
            resource_name,
            SUM(total_requests) / (TIMESTAMPDIFF(SECOND, #{startTime}, #{endTime}) / 60.0) as qps,
            SUM(total_requests) as total_requests,
            SUM(block_requests) as block_requests,
            ROUND(SUM(block_requests) * 100.0 / SUM(total_requests), 2) as block_rate
        FROM monitor_statistics
        WHERE stat_time BETWEEN #{startTime} AND #{endTime}
        AND total_requests > 0
        GROUP BY resource_name
        ORDER BY qps DESC
        LIMIT #{limit}
    </select>

    <!-- 查询资源排行榜（按响应时间） -->
    <select id="selectResourceRankByRt" resultType="java.util.Map">
        SELECT 
            resource_name,
            AVG(avg_rt) as avg_rt,
            MAX(max_rt) as max_rt,
            MIN(min_rt) as min_rt,
            SUM(total_requests) as total_requests
        FROM monitor_statistics
        WHERE stat_time BETWEEN #{startTime} AND #{endTime}
        AND total_requests > 0
        GROUP BY resource_name
        ORDER BY avg_rt DESC
        LIMIT #{limit}
    </select>

    <!-- 查询租户统计数据 -->
    <select id="selectTenantStatistics" resultType="java.util.Map">
        SELECT 
            ms.tenant_id,
            ti.tenant_name,
            SUM(ms.total_requests) as total_requests,
            SUM(ms.pass_requests) as pass_requests,
            SUM(ms.block_requests) as block_requests,
            ROUND(SUM(ms.block_requests) * 100.0 / SUM(ms.total_requests), 2) as block_rate,
            AVG(ms.avg_rt) as avg_rt
        FROM monitor_statistics ms
        LEFT JOIN tenant_info ti ON ms.tenant_id = ti.tenant_id
        WHERE ms.stat_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY ms.tenant_id, ti.tenant_name
        ORDER BY total_requests DESC
    </select>

    <!-- 查询系统概览统计数据 -->
    <select id="selectSystemOverview" resultType="java.util.Map">
        SELECT 
            SUM(total_requests) as total_requests,
            SUM(pass_requests) as pass_requests,
            SUM(block_requests) as block_requests,
            SUM(queue_requests) as queue_requests,
            ROUND(SUM(block_requests) * 100.0 / SUM(total_requests), 2) as block_rate,
            ROUND(SUM(pass_requests) * 100.0 / SUM(total_requests), 2) as pass_rate,
            AVG(avg_rt) as avg_rt,
            MAX(max_rt) as max_rt,
            COUNT(DISTINCT resource_name) as resource_count,
            COUNT(DISTINCT tenant_id) as tenant_count
        FROM monitor_statistics
        WHERE stat_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 批量插入监控统计数据 -->
    <insert id="batchInsert">
        INSERT INTO monitor_statistics (
            stat_time, resource_name, tenant_id, total_requests, pass_requests,
            block_requests, queue_requests, total_rt, avg_rt, max_rt, min_rt, stat_type, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.statTime}, #{item.resourceName}, #{item.tenantId}, #{item.totalRequests},
                #{item.passRequests}, #{item.blockRequests}, #{item.queueRequests}, #{item.totalRt},
                #{item.avgRt}, #{item.maxRt}, #{item.minRt}, #{item.statType}, NOW()
            )
        </foreach>
    </insert>

    <!-- 删除过期的监控数据 -->
    <delete id="deleteExpiredData">
        DELETE FROM monitor_statistics
        WHERE stat_time &lt; #{beforeTime}
        <if test="statType != null and statType != ''">
            AND stat_type = #{statType}
        </if>
    </delete>

    <!-- 查询异常数据（响应时间过高、阻塞率过高等） -->
    <select id="selectAbnormalData" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM monitor_statistics
        WHERE stat_time BETWEEN #{startTime} AND #{endTime}
        AND (
            avg_rt &gt; #{maxRt}
            OR (total_requests &gt; 0 AND block_requests * 100.0 / total_requests &gt; #{maxBlockRate})
        )
        ORDER BY stat_time DESC
    </select>

</mapper>