#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
租户总QPS限流测试脚本 (更新版)
基于租户维度的流量控制规则进行测试验证
支持多种控制行为：快速失败、预热启动、排队等待、综合限流
根据用户提供的5条租户规则数据生成
"""

import asyncio
import aiohttp
import time
import json
from datetime import datetime
from typing import Dict, List, Any
import argparse
import sys
import statistics

class TenantQPSFlowControlTester:
    def __init__(self, base_url: str = "http://localhost:8088"):
        self.base_url = base_url
        self.results = []
        
    async def send_request(self, session: aiohttp.ClientSession, url: str, tenant_id: str) -> Dict[str, Any]:
        """发送单个HTTP请求"""
        headers = {
            'X-Tenant-Id': tenant_id,
            'Content-Type': 'application/json'
        }
        
        start_time = time.time()
        try:
            async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                return {
                    'status_code': response.status,
                    'response_time': response_time,
                    'success': response.status == 200,
                    'blocked': response.status == 429,
                    'error': False,
                    'timestamp': start_time
                }
        except Exception as e:
            end_time = time.time()
            return {
                'status_code': 0,
                'response_time': end_time - start_time,
                'success': False,
                'blocked': False,
                'error': True,
                'error_msg': str(e),
                'timestamp': start_time
            }
    
    def get_control_behavior_name(self, control_behavior: int) -> str:
        """获取控制行为名称"""
        behavior_map = {
            0: "快速失败",
            1: "预热启动", 
            2: "排队等待",
            3: "预热+排队"
        }
        return behavior_map.get(control_behavior, f"未知({control_behavior})")
    
    def calculate_expected_success_requests(self, rule: Dict[str, Any], test_duration: int, requests_per_second: int) -> int:
        """根据控制行为计算预期成功请求数"""
        qps_limit = rule['count']
        control_behavior = rule['control_behavior']
        warm_up_period = rule.get('warm_up_period_sec', 0) or 0
        
        if control_behavior == 0:  # 快速失败
            return min(qps_limit * test_duration, requests_per_second * test_duration)
        elif control_behavior == 1:  # 预热启动
            if warm_up_period > 0 and test_duration > warm_up_period:
                # 预热期间逐渐增加到目标QPS
                warm_up_requests = int(qps_limit * warm_up_period * 0.5)  # 预热期平均50%
                normal_requests = qps_limit * (test_duration - warm_up_period)
                return min(warm_up_requests + normal_requests, requests_per_second * test_duration)
            else:
                return min(qps_limit * test_duration * 0.7, requests_per_second * test_duration)  # 预热期平均70%
        elif control_behavior == 2:  # 排队等待
            # 排队等待可能允许更多请求通过，但响应时间会增加
            return min(qps_limit * test_duration * 1.2, requests_per_second * test_duration)  # 允许20%缓冲
        elif control_behavior == 3:  # 预热+排队
            # 综合模式，预热期+排队缓冲
            base_requests = qps_limit * test_duration
            if warm_up_period > 0:
                base_requests *= 0.8  # 预热影响
            return min(base_requests * 1.1, requests_per_second * test_duration)  # 排队缓冲
        else:
            return min(qps_limit * test_duration, requests_per_second * test_duration)
    
    async def test_tenant_rule(self, rule: Dict[str, Any], test_duration: int, requests_per_second: int, test_endpoint: str = "/api/test") -> Dict[str, Any]:
        """测试单个租户限流规则"""
        tenant_id = rule['tenant_id']
        rule_name = rule['rule_name']
        qps_limit = rule['count']
        control_behavior = rule['control_behavior']
        warm_up_period = rule.get('warm_up_period_sec', 0) or 0
        max_queueing_time = rule.get('max_queueing_time_ms', 0) or 0
        
        url = f"{self.base_url}{test_endpoint}"
        
        print(f"\n开始测试租户规则: {rule_name}")
        print(f"租户ID: {tenant_id}")
        print(f"QPS限制: {qps_limit}")
        print(f"控制行为: {self.get_control_behavior_name(control_behavior)}")
        if warm_up_period > 0:
            print(f"预热时间: {warm_up_period}秒")
        if max_queueing_time > 0:
            print(f"最大排队时间: {max_queueing_time}ms")
        print(f"测试配置: {test_duration}秒, {requests_per_second} RPS")
        
        expected_success = self.calculate_expected_success_requests(rule, test_duration, requests_per_second)
        print(f"预期成功请求数: {expected_success}")
        
        results = []
        start_test_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            # 计算每秒需要发送的请求间隔
            interval = 1.0 / requests_per_second
            
            tasks = []
            request_count = 0
            
            while time.time() - start_test_time < test_duration:
                # 创建请求任务
                task = asyncio.create_task(self.send_request(session, url, tenant_id))
                tasks.append(task)
                request_count += 1
                
                # 控制请求频率
                await asyncio.sleep(interval)
            
            # 等待所有请求完成
            print(f"等待 {len(tasks)} 个请求完成...")
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_test_time = time.time()
        actual_duration = end_test_time - start_test_time
        
        # 统计结果
        valid_results = [r for r in results if isinstance(r, dict)]
        total_requests = len(valid_results)
        success_requests = sum(1 for r in valid_results if r.get('success', False))
        blocked_requests = sum(1 for r in valid_results if r.get('blocked', False))
        error_requests = sum(1 for r in valid_results if r.get('error', False))
        
        # 计算响应时间统计
        response_times = [r['response_time'] for r in valid_results if 'response_time' in r]
        if response_times:
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
            median_response_time = statistics.median(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 20 else max_response_time
        else:
            avg_response_time = max_response_time = min_response_time = median_response_time = p95_response_time = 0
        
        # 计算实际QPS
        actual_qps = total_requests / actual_duration if actual_duration > 0 else 0
        success_qps = success_requests / actual_duration if actual_duration > 0 else 0
        
        # 计算限流效果
        success_rate = (success_requests / total_requests * 100) if total_requests > 0 else 0
        block_rate = (blocked_requests / total_requests * 100) if total_requests > 0 else 0
        error_rate = (error_requests / total_requests * 100) if total_requests > 0 else 0
        
        # 分析控制行为效果
        behavior_analysis = self.analyze_control_behavior(rule, valid_results, actual_duration)
        
        result = {
            'rule_info': {
                'rule_name': rule_name,
                'tenant_id': tenant_id,
                'qps_limit': qps_limit,
                'control_behavior': control_behavior,
                'control_behavior_name': self.get_control_behavior_name(control_behavior),
                'warm_up_period_sec': warm_up_period,
                'max_queueing_time_ms': max_queueing_time
            },
            'test_config': {
                'duration': test_duration,
                'requests_per_second': requests_per_second,
                'expected_total_requests': requests_per_second * test_duration,
                'expected_success_requests': expected_success,
                'test_endpoint': test_endpoint
            },
            'actual_results': {
                'total_requests': total_requests,
                'success_requests': success_requests,
                'blocked_requests': blocked_requests,
                'error_requests': error_requests,
                'actual_duration': actual_duration,
                'actual_qps': actual_qps,
                'success_qps': success_qps
            },
            'statistics': {
                'success_rate': success_rate,
                'block_rate': block_rate,
                'error_rate': error_rate,
                'avg_response_time': avg_response_time,
                'max_response_time': max_response_time,
                'min_response_time': min_response_time,
                'median_response_time': median_response_time,
                'p95_response_time': p95_response_time
            },
            'analysis': {
                'qps_accuracy': abs(success_qps - qps_limit) / qps_limit * 100 if qps_limit > 0 else 0,
                'expected_vs_actual_success': success_requests - expected_success,
                'limit_effectiveness': blocked_requests > 0,
                'behavior_analysis': behavior_analysis
            }
        }
        
        # 打印测试结果
        print(f"\n=== 测试结果 ===")
        print(f"总请求数: {total_requests}")
        print(f"成功请求: {success_requests} ({success_rate:.1f}%)")
        print(f"被限流: {blocked_requests} ({block_rate:.1f}%)")
        print(f"错误请求: {error_requests} ({error_rate:.1f}%)")
        print(f"实际QPS: {actual_qps:.2f}")
        print(f"成功QPS: {success_qps:.2f} (限制: {qps_limit})")
        print(f"QPS准确度偏差: {result['analysis']['qps_accuracy']:.1f}%")
        print(f"平均响应时间: {avg_response_time*1000:.2f}ms")
        print(f"P95响应时间: {p95_response_time*1000:.2f}ms")
        print(f"控制行为分析: {behavior_analysis['summary']}")
        
        return result
    
    def analyze_control_behavior(self, rule: Dict[str, Any], results: List[Dict[str, Any]], duration: float) -> Dict[str, Any]:
        """分析控制行为的效果"""
        control_behavior = rule['control_behavior']
        warm_up_period = rule.get('warm_up_period_sec', 0) or 0
        max_queueing_time = rule.get('max_queueing_time_ms', 0) or 0
        
        analysis = {
            'behavior_type': self.get_control_behavior_name(control_behavior),
            'summary': '',
            'details': {}
        }
        
        if control_behavior == 0:  # 快速失败
            blocked_count = sum(1 for r in results if r.get('blocked', False))
            analysis['summary'] = f"快速失败模式，共拦截{blocked_count}个请求"
            analysis['details']['blocked_immediately'] = blocked_count
            
        elif control_behavior == 1:  # 预热启动
            # 分析预热期间的请求分布
            if warm_up_period > 0:
                warm_up_results = [r for r in results if r['timestamp'] - results[0]['timestamp'] <= warm_up_period]
                normal_results = [r for r in results if r['timestamp'] - results[0]['timestamp'] > warm_up_period]
                
                warm_up_success = sum(1 for r in warm_up_results if r.get('success', False))
                normal_success = sum(1 for r in normal_results if r.get('success', False))
                
                analysis['summary'] = f"预热启动模式，预热期成功{warm_up_success}个，正常期成功{normal_success}个"
                analysis['details']['warm_up_success'] = warm_up_success
                analysis['details']['normal_success'] = normal_success
                analysis['details']['warm_up_period'] = warm_up_period
            else:
                analysis['summary'] = "预热启动模式，但未设置预热时间"
                
        elif control_behavior == 2:  # 排队等待
            # 分析响应时间分布，排队等待会增加响应时间
            response_times = [r['response_time'] for r in results if 'response_time' in r and r.get('success', False)]
            if response_times:
                avg_rt = statistics.mean(response_times)
                max_rt = max(response_times)
                queued_requests = sum(1 for rt in response_times if rt > 0.1)  # 响应时间>100ms认为可能排队
                
                analysis['summary'] = f"排队等待模式，平均响应时间{avg_rt*1000:.1f}ms，{queued_requests}个请求可能排队"
                analysis['details']['avg_response_time'] = avg_rt
                analysis['details']['max_response_time'] = max_rt
                analysis['details']['queued_requests'] = queued_requests
                analysis['details']['max_queueing_time_ms'] = max_queueing_time
            else:
                analysis['summary'] = "排队等待模式，但无有效响应时间数据"
                
        elif control_behavior == 3:  # 预热+排队
            # 综合分析预热和排队效果
            response_times = [r['response_time'] for r in results if 'response_time' in r and r.get('success', False)]
            if warm_up_period > 0 and response_times:
                warm_up_results = [r for r in results if r['timestamp'] - results[0]['timestamp'] <= warm_up_period]
                normal_results = [r for r in results if r['timestamp'] - results[0]['timestamp'] > warm_up_period]
                
                warm_up_success = sum(1 for r in warm_up_results if r.get('success', False))
                normal_success = sum(1 for r in normal_results if r.get('success', False))
                avg_rt = statistics.mean(response_times)
                
                analysis['summary'] = f"预热+排队模式，预热期{warm_up_success}个成功，正常期{normal_success}个成功，平均响应时间{avg_rt*1000:.1f}ms"
                analysis['details']['warm_up_success'] = warm_up_success
                analysis['details']['normal_success'] = normal_success
                analysis['details']['avg_response_time'] = avg_rt
            else:
                analysis['summary'] = "预热+排队模式，配置或数据不完整"
        
        return analysis
    
    async def run_all_tenant_tests(self, rules: List[Dict[str, Any]], test_duration: int, requests_per_second: int, test_endpoint: str = "/api/test") -> Dict[str, Any]:
        """运行所有租户限流测试"""
        print(f"\n{'='*80}")
        print(f"租户总QPS限流测试开始")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"服务地址: {self.base_url}")
        print(f"测试配置: {test_duration}秒, {requests_per_second} RPS")
        print(f"测试端点: {test_endpoint}")
        print(f"租户规则数量: {len(rules)}")
        print(f"{'='*80}")
        
        results = []
        
        for i, rule in enumerate(rules, 1):
            print(f"\n[{i}/{len(rules)}] 测试租户: {rule['tenant_id']} - {rule['rule_name']}")
            try:
                result = await self.test_tenant_rule(rule, test_duration, requests_per_second, test_endpoint)
                results.append(result)
                
                # 测试间隔，避免相互影响
                if i < len(rules):
                    print("等待3秒后进行下一个租户测试...")
                    await asyncio.sleep(3)
                    
            except Exception as e:
                print(f"测试租户 {rule['tenant_id']} 时发生错误: {e}")
                results.append({
                    'rule_info': {
                        'tenant_id': rule['tenant_id'],
                        'rule_name': rule['rule_name']
                    },
                    'error': True,
                    'error_message': str(e)
                })
        
        # 生成测试报告
        report = {
            'test_time': datetime.now().isoformat(),
            'base_url': self.base_url,
            'test_config': {
                'duration': test_duration,
                'requests_per_second': requests_per_second,
                'test_endpoint': test_endpoint
            },
            'tenant_rules': rules,
            'results': results,
            'summary': self._generate_tenant_summary(results)
        }
        
        return report
    
    def _generate_tenant_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成租户测试总结"""
        successful_tests = [r for r in results if not r.get('error', False)]
        
        if not successful_tests:
            return {'error': '所有租户测试都失败了'}
        
        total_requests = sum(r['actual_results']['total_requests'] for r in successful_tests)
        total_success = sum(r['actual_results']['success_requests'] for r in successful_tests)
        total_blocked = sum(r['actual_results']['blocked_requests'] for r in successful_tests)
        total_errors = sum(r['actual_results']['error_requests'] for r in successful_tests)
        
        # 按控制行为分组统计
        behavior_stats = {}
        for result in successful_tests:
            behavior = result['rule_info']['control_behavior_name']
            if behavior not in behavior_stats:
                behavior_stats[behavior] = {
                    'count': 0,
                    'total_requests': 0,
                    'success_requests': 0,
                    'blocked_requests': 0,
                    'avg_qps_accuracy': 0
                }
            
            stats = behavior_stats[behavior]
            stats['count'] += 1
            stats['total_requests'] += result['actual_results']['total_requests']
            stats['success_requests'] += result['actual_results']['success_requests']
            stats['blocked_requests'] += result['actual_results']['blocked_requests']
            stats['avg_qps_accuracy'] += result['analysis']['qps_accuracy']
        
        # 计算平均值
        for behavior, stats in behavior_stats.items():
            if stats['count'] > 0:
                stats['avg_qps_accuracy'] /= stats['count']
                stats['success_rate'] = (stats['success_requests'] / stats['total_requests'] * 100) if stats['total_requests'] > 0 else 0
                stats['block_rate'] = (stats['blocked_requests'] / stats['total_requests'] * 100) if stats['total_requests'] > 0 else 0
        
        avg_qps_accuracy = sum(r['analysis']['qps_accuracy'] for r in successful_tests) / len(successful_tests)
        effective_limits = sum(1 for r in successful_tests if r['analysis']['limit_effectiveness'])
        
        return {
            'total_tenant_tests': len(results),
            'successful_tests': len(successful_tests),
            'failed_tests': len(results) - len(successful_tests),
            'total_requests': total_requests,
            'total_success_requests': total_success,
            'total_blocked_requests': total_blocked,
            'total_error_requests': total_errors,
            'overall_success_rate': (total_success / total_requests * 100) if total_requests > 0 else 0,
            'overall_block_rate': (total_blocked / total_requests * 100) if total_requests > 0 else 0,
            'average_qps_accuracy_deviation': avg_qps_accuracy,
            'effective_limit_tenants': effective_limits,
            'limit_effectiveness_rate': (effective_limits / len(successful_tests) * 100) if successful_tests else 0,
            'behavior_statistics': behavior_stats
        }
    
    def save_report(self, report: Dict[str, Any], filename: str = None) -> str:
        """保存测试报告"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'tenant_qps_flow_control_test_report_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return filename
    
    def print_summary(self, report: Dict[str, Any]):
        """打印测试总结"""
        summary = report['summary']
        
        print(f"\n{'='*80}")
        print(f"租户总QPS限流测试总结报告")
        print(f"{'='*80}")
        print(f"测试时间: {report['test_time']}")
        print(f"服务地址: {report['base_url']}")
        print(f"测试配置: {report['test_config']['duration']}秒, {report['test_config']['requests_per_second']} RPS")
        print(f"测试端点: {report['test_config']['test_endpoint']}")
        print(f"")
        print(f"租户测试统计:")
        print(f"  总租户数: {summary['total_tenant_tests']}")
        print(f"  成功测试: {summary['successful_tests']}")
        print(f"  失败测试: {summary['failed_tests']}")
        print(f"")
        print(f"请求统计:")
        print(f"  总请求数: {summary['total_requests']}")
        print(f"  成功请求: {summary['total_success_requests']} ({summary['overall_success_rate']:.1f}%)")
        print(f"  被限流: {summary['total_blocked_requests']} ({summary['overall_block_rate']:.1f}%)")
        print(f"  错误请求: {summary['total_error_requests']}")
        print(f"")
        print(f"限流效果:")
        print(f"  平均QPS准确度偏差: {summary['average_qps_accuracy_deviation']:.1f}%")
        print(f"  有效限流租户: {summary['effective_limit_tenants']}/{summary['successful_tests']}")
        print(f"  限流有效率: {summary['limit_effectiveness_rate']:.1f}%")
        print(f"")
        print(f"控制行为统计:")
        for behavior, stats in summary['behavior_statistics'].items():
            print(f"  {behavior}: {stats['count']}个租户, 成功率{stats['success_rate']:.1f}%, 限流率{stats['block_rate']:.1f}%, QPS偏差{stats['avg_qps_accuracy']:.1f}%")
        print(f"{'='*80}")

def create_tenant_rules() -> List[Dict[str, Any]]:
    """创建租户限流规则（基于用户提供的最新数据库记录）"""
    return [
        {
            'id': 1,
            'tenant_id': 'tenant1',
            'rule_name': '租户1默认QPS限流',
            'grade': 1,
            'count': 5,
            'control_behavior': 0,  # 快速失败
            'warm_up_period_sec': None,
            'max_queueing_time_ms': None,
            'strategy': 0,
            'priority': 1,
            'enabled': 1,
            'description': '租户1的默认QPS限流规则',
            'create_time': '2025-08-14 16:11:24',
            'update_time': '2025-08-26 14:40:44'
        },
        {
            'id': 2,
            'tenant_id': 'tenant2',
            'rule_name': '租户2预热限流',
            'grade': 1,
            'count': 5,
            'control_behavior': 1,  # 预热启动
            'warm_up_period_sec': 10,
            'max_queueing_time_ms': None,
            'strategy': 0,
            'priority': 2,
            'enabled': 1,
            'description': '租户2的预热限流规则',
            'create_time': '2025-08-14 16:11:24',
            'update_time': '2025-08-26 14:40:48'
        },
        {
            'id': 3,
            'tenant_id': 'tenant3',
            'rule_name': '租户3排队限流',
            'grade': 1,
            'count': 5,
            'control_behavior': 2,  # 排队等待
            'warm_up_period_sec': None,
            'max_queueing_time_ms': 5000,
            'strategy': 0,
            'priority': 1,
            'enabled': 1,
            'description': '租户3的排队等待限流规则',
            'create_time': '2025-08-14 16:11:24',
            'update_time': '2025-08-26 14:40:49'
        },
        {
            'id': 4,
            'tenant_id': 'tenant4',
            'rule_name': '租户4线程数限流',
            'grade': 1,
            'count': 5,
            'control_behavior': 0,  # 快速失败
            'warm_up_period_sec': None,
            'max_queueing_time_ms': None,
            'strategy': 0,
            'priority': 3,
            'enabled': 1,
            'description': '租户4的线程数限流规则',
            'create_time': '2025-08-14 16:11:24',
            'update_time': '2025-08-26 14:40:21'
        },
        {
            'id': 5,
            'tenant_id': 'tenant5',
            'rule_name': '租户5综合限流',
            'grade': 1,
            'count': 5,
            'control_behavior': 3,  # 预热+排队
            'warm_up_period_sec': 15,
            'max_queueing_time_ms': 3000,
            'strategy': 0,
            'priority': 1,
            'enabled': 1,
            'description': '租户5的Warm Up + 排队等待限流规则',
            'create_time': '2025-08-14 16:11:24',
            'update_time': '2025-08-26 14:44:42'
        }
    ]

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='租户总QPS限流测试脚本 (更新版)')
    parser.add_argument('--url', default='http://localhost:8088', help='服务器地址 (默认: http://localhost:8088)')
    parser.add_argument('--duration', type=int, default=30, help='测试持续时间(秒) (默认: 30)')
    parser.add_argument('--rps', type=int, default=500, help='每秒请求数 (默认: 500)')
    parser.add_argument('--endpoint', default='/api/test', help='测试端点 (默认: /api/test)')
    parser.add_argument('--output', help='输出报告文件名')
    parser.add_argument('--rules-only', action='store_true', help='只显示租户规则')
    parser.add_argument('--tenant', help='只测试指定租户ID')
    
    args = parser.parse_args()
    
    # 创建租户规则
    rules = create_tenant_rules()
    
    # 过滤指定租户
    if args.tenant:
        rules = [rule for rule in rules if rule['tenant_id'] == args.tenant]
        if not rules:
            print(f"错误: 未找到租户ID '{args.tenant}' 的规则")
            return
    
    if args.rules_only:
        print("租户限流规则列表 (基于最新数据):")
        print(f"{'='*80}")
        for i, rule in enumerate(rules, 1):
            print(f"{i}. {rule['rule_name']} ({rule['tenant_id']})")
            print(f"   QPS限制: {rule['count']}, 控制行为: {rule['control_behavior']} ({TenantQPSFlowControlTester().get_control_behavior_name(rule['control_behavior'])})")
            if rule.get('warm_up_period_sec'):
                print(f"   预热时间: {rule['warm_up_period_sec']}秒")
            if rule.get('max_queueing_time_ms'):
                print(f"   最大排队时间: {rule['max_queueing_time_ms']}ms")
            print(f"   优先级: {rule['priority']}, 状态: {'启用' if rule['enabled'] else '禁用'}")
            print(f"   描述: {rule['description']}")
            print(f"   更新时间: {rule['update_time']}")
            print()
        print(f"{'='*80}")
        return
    
    # 创建测试器
    tester = TenantQPSFlowControlTester(args.url)
    
    try:
        # 运行测试
        report = await tester.run_all_tenant_tests(rules, args.duration, args.rps, args.endpoint)
        
        # 保存报告
        report_file = tester.save_report(report, args.output)
        print(f"\n测试报告已保存到: {report_file}")
        
        # 打印总结
        tester.print_summary(report)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 运行测试
    asyncio.run(main())