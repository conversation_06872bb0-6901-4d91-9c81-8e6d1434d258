<template>
  <div class="dashboard">
    <layout>
      <div class="dashboard-content">
        <div class="dashboard-header">
          <h1>系统概览</h1>
          <p>实时监控系统运行状态</p>
        </div>
        
        <!-- 统计卡片 -->
        <el-row :gutter="20" class="stats-row">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon qps">
                <i class="el-icon-data-line"></i>
              </div>
              <div class="stat-content">
                <h3>{{ realTimeData.qps }}</h3>
                <p>当前QPS</p>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon rt">
                <i class="el-icon-timer"></i>
              </div>
              <div class="stat-content">
                <h3>{{ realTimeData.rt }}ms</h3>
                <p>平均响应时间</p>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon success">
                <i class="el-icon-success"></i>
              </div>
              <div class="stat-content">
                <h3>{{ realTimeData.successRate.toFixed(1) }}%</h3>
                <p>成功率</p>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon error">
                <i class="el-icon-warning"></i>
              </div>
              <div class="stat-content">
                <h3>{{ realTimeData.errorRate.toFixed(1) }}%</h3>
                <p>错误率</p>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <!-- 图表区域 -->
        <el-row :gutter="20" class="charts-row">
          <el-col :span="12">
            <div class="chart-card">
              <h3>QPS趋势</h3>
              <div id="qps-chart" class="chart-container"></div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-card">
              <h3>响应时间趋势</h3>
              <div id="rt-chart" class="chart-container"></div>
            </div>
          </el-col>
        </el-row>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
          <h3>快速操作</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-card class="action-card" @click.native="navigateTo('/flow-control')">
                <div class="action-content">
                  <i class="el-icon-s-order action-icon"></i>
                  <h4>流量控制</h4>
                  <p>管理租户、接口、IP三维度流量控制</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="action-card" @click.native="navigateTo('/monitor')">
                <div class="action-content">
                  <i class="el-icon-monitor action-icon"></i>
                  <h4>实时监控</h4>
                  <p>查看系统实时运行状态</p>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="action-card" @click.native="navigateTo('/statistics')">
                <div class="action-content">
                  <i class="el-icon-data-analysis action-icon"></i>
                  <h4>统计分析</h4>
                  <p>查看详细的统计报表</p>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <!-- 最近告警 -->
        <div class="alerts-section">
          <div class="section-header">
            <h3>最近告警</h3>
            <el-button type="text" @click="navigateTo('/monitor')">查看全部</el-button>
          </div>
          <el-table :data="alerts.slice(0, 5)" style="width: 100%" v-if="alerts.length > 0">
            <el-table-column prop="level" label="级别" width="80">
              <template slot-scope="scope">
                <el-tag :type="getAlertType(scope.row.level)">{{ scope.row.level }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="告警信息"></el-table-column>
            <el-table-column prop="time" label="时间" width="180">
              <template slot-scope="scope">
                {{ formatTime(scope.row.time) }}
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="no-data">
            <i class="el-icon-info"></i>
            <p>暂无告警信息</p>
          </div>
        </div>
      </div>
    </layout>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import Layout from '../components/Layout.vue'

export default {
  name: 'Dashboard',
  components: {
    Layout
  },
  data() {
    return {
      timer: null
    }
  },
  computed: {
    ...mapGetters('monitor', ['realTimeData', 'alerts'])
  },
  mounted() {
    this.startRealTimeUpdate()
    this.fetchAlerts()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    ...mapActions('monitor', ['fetchRealTimeData', 'fetchAlerts']),
    startRealTimeUpdate() {
      this.fetchRealTimeData()
      this.timer = setInterval(() => {
        this.fetchRealTimeData()
      }, 5000) // 每5秒更新一次
    },
    getAlertType(level) {
      const types = {
        '严重': 'danger',
        '警告': 'warning',
        '信息': 'info'
      }
      return types[level] || 'info'
    },
    formatTime(timestamp) {
      if (!timestamp) return '-'
      // 统一使用 YYYY-MM-DD HH:mm:ss 格式
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    navigateTo(path) {
      this.$router.push(path)
    }
  }
}
</script>

<style scoped>
.dashboard-content {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  color: #333;
  margin-bottom: 5px;
}

.dashboard-header p {
  color: #666;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.qps {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.rt {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.error {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
}

.stat-content p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.charts-row {
  margin-bottom: 30px;
}

.chart-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-card h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 16px;
}

.chart-container {
  height: 300px;
  background: #f8f9fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.quick-actions {
  margin-bottom: 30px;
}

.quick-actions h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 18px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 120px;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-content {
  text-align: center;
  padding: 10px;
}

.action-icon {
  font-size: 32px;
  color: #409EFF;
  margin-bottom: 10px;
}

.action-content h4 {
  color: #333;
  margin: 8px 0 5px 0;
  font-size: 16px;
  font-weight: 600;
}

.action-content p {
  color: #666;
  font-size: 12px;
  margin: 0;
  line-height: 1.4;
}

.alerts-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  color: #333;
  margin: 0;
  font-size: 16px;
}

.no-data {
  text-align: center;
  padding: 40px 0;
  color: #999;
}

.no-data i {
  font-size: 48px;
  margin-bottom: 10px;
  display: block;
}

.no-data p {
  margin: 0;
  font-size: 14px;
}
</style>