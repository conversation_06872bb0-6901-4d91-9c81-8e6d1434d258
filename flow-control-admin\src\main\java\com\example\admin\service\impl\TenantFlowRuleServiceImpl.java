package com.example.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.dto.TenantFlowRuleDTO;
import com.example.common.entity.TenantFlowRule;
import com.example.admin.mapper.TenantFlowRuleMapper;
import com.example.admin.service.TenantFlowRuleService;
import com.example.admin.vo.TenantFlowRuleVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;
import java.util.stream.Collectors;

/**
 * 租户流控规则服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class TenantFlowRuleServiceImpl extends ServiceImpl<TenantFlowRuleMapper, TenantFlowRule>
		implements TenantFlowRuleService {

	private static final Logger log = LoggerFactory.getLogger(TenantFlowRuleServiceImpl.class);

	@Resource
	private TenantFlowRuleMapper tenantFlowRuleMapper;

	@Override
	public Page<TenantFlowRuleVO> selectTenantFlowRulePage(Page<TenantFlowRuleVO> page, String tenantId,
			String ruleName, Integer enabled, Integer controlBehavior) {
		return tenantFlowRuleMapper.selectTenantFlowRuleVOPage(page, tenantId, ruleName, enabled, controlBehavior);
	}

	@Override
	public TenantFlowRuleVO getTenantFlowRuleById(Long id) {
		TenantFlowRule tenantFlowRule = this.getById(id);
		if (tenantFlowRule == null) {
			return null;
		}
		return convertToVO(tenantFlowRule);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean createTenantFlowRule(TenantFlowRuleDTO tenantFlowRuleDTO) {
		// 验证租户流控规则配置
		Map<String, Object> validationResult = validateTenantFlowRule(tenantFlowRuleDTO);
		if (!(Boolean) validationResult.get("valid")) {
			throw new RuntimeException((String) validationResult.get("message"));
		}

		// 检查规则名称是否已存在
		if (existsByRuleName(tenantFlowRuleDTO.getTenantId(), tenantFlowRuleDTO.getRuleName(), null)) {
			throw new RuntimeException("规则名称在该租户下已存在");
		}

		TenantFlowRule tenantFlowRule = new TenantFlowRule();
		BeanUtils.copyProperties(tenantFlowRuleDTO, tenantFlowRule);
		tenantFlowRule.setCreateTime(LocalDateTime.now());
		tenantFlowRule.setUpdateTime(LocalDateTime.now());
		tenantFlowRule.setDeleted(0);

		boolean result = this.save(tenantFlowRule);

		if (result) {
			log.info("创建租户流控规则成功: tenantId={}, ruleName={}", tenantFlowRuleDTO.getTenantId(),
					tenantFlowRuleDTO.getRuleName());
			// 这里可以添加流控规则发布到网关的逻辑
			publishTenantFlowRules(tenantFlowRuleDTO.getTenantId());
		}

		return result;
	}

	/**
	 * 验证流控行为参数是否有效
	 */
	private boolean isValidFlowBehaviorParams(TenantFlowRuleDTO dto) {
		if (dto.getControlBehavior() == null) {
			return false;
		}

		// Warm Up 模式需要预热时间
		if ((dto.getControlBehavior() == 1 || dto.getControlBehavior() == 3)
				&& (dto.getWarmUpPeriodSec() == null || dto.getWarmUpPeriodSec() <= 0)) {
			return false;
		}

		// 排队等待模式需要超时时间
		if ((dto.getControlBehavior() == 2 || dto.getControlBehavior() == 3)
				&& (dto.getMaxQueueingTimeMs() == null || dto.getMaxQueueingTimeMs() <= 0)) {
			return false;
		}

		return true;
	}

	/**
	 * 验证时间范围是否有效
	 */
	private boolean isValidTimeRange(TenantFlowRuleDTO dto) {
		if (dto.getStartTime() != null && dto.getEndTime() != null) {
			return dto.getStartTime().isBefore(dto.getEndTime());
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateTenantFlowRule(Long id, TenantFlowRuleDTO tenantFlowRuleDTO) {
		TenantFlowRule existingRule = this.getById(id);
		if (existingRule == null) {
			throw new RuntimeException("租户流控规则不存在");
		}

		// 验证租户流控规则配置
		Map<String, Object> validationResult = validateTenantFlowRule(tenantFlowRuleDTO);
		if (!(Boolean) validationResult.get("valid")) {
			throw new RuntimeException((String) validationResult.get("message"));
		}

		// 检查规则名称是否已存在（排除当前规则）
		if (existsByRuleName(tenantFlowRuleDTO.getTenantId(), tenantFlowRuleDTO.getRuleName(), id)) {
			throw new RuntimeException("规则名称在该租户下已存在");
		}

		BeanUtils.copyProperties(tenantFlowRuleDTO, existingRule);
		existingRule.setId(id);
		existingRule.setUpdateTime(LocalDateTime.now());

		boolean result = this.updateById(existingRule);

		if (result) {
			log.info("更新租户流控规则成功: id={}, tenantId={}, ruleName={}", id, tenantFlowRuleDTO.getTenantId(),
					tenantFlowRuleDTO.getRuleName());
			publishTenantFlowRules(tenantFlowRuleDTO.getTenantId());
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteTenantFlowRule(Long id) {
		TenantFlowRule tenantFlowRule = this.getById(id);
		if (tenantFlowRule == null) {
			return false;
		}

		boolean result = this.removeById(id);

		if (result) {
			log.info("删除租户流控规则成功: id={}, tenantId={}", id, tenantFlowRule.getTenantId());
			publishTenantFlowRules(tenantFlowRule.getTenantId());
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchDeleteTenantFlowRules(List<Long> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return false;
		}

		// 获取要删除的规则信息，用于后续发布
		List<TenantFlowRule> rules = this.listByIds(ids);
		Set<String> tenantIds = rules.stream().map(TenantFlowRule::getTenantId).collect(Collectors.toSet());

		boolean result = this.removeByIds(ids);

		if (result) {
			log.info("批量删除租户流控规则成功: ids={}", ids);
			// 发布所有相关租户的流控规则更新
			tenantIds.forEach(this::publishTenantFlowRules);
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean enableTenantFlowRule(Long id) {
		return updateRuleEnabled(id, 1);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean disableTenantFlowRule(Long id) {
		return updateRuleEnabled(id, 0);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchUpdateEnabled(List<Long> ids, Integer enabled) {
		if (CollectionUtils.isEmpty(ids)) {
			return false;
		}

		// 获取规则信息，用于后续发布
		List<TenantFlowRule> rules = this.listByIds(ids);
		Set<String> tenantIds = rules.stream().map(TenantFlowRule::getTenantId).collect(Collectors.toSet());

		boolean result = tenantFlowRuleMapper.batchUpdateEnabled(ids, enabled, "system") > 0;

		if (result) {
			log.info("批量更新租户流控规则状态成功: ids={}, enabled={}", ids, enabled);
			tenantIds.forEach(this::publishTenantFlowRules);
		}

		return result;
	}

	@Override
	public List<TenantFlowRuleVO> getTenantFlowRulesByTenantId(String tenantId, Integer enabled, Integer limit) {
		List<TenantFlowRule> rules = tenantFlowRuleMapper.selectByTenantId(tenantId);
		// 根据enabled和limit参数进行过滤和限制
		Stream<TenantFlowRule> stream = rules.stream();
		if (enabled != null) {
			stream = stream.filter(rule -> rule.getEnabled().equals(enabled));
		}
		if (limit != null && limit > 0) {
			stream = stream.limit(limit);
		}
		return stream.map(this::convertToVO).collect(Collectors.toList());
	}

	// 租户限流为全局生效，移除资源匹配模式相关方法
	// @Override
	// public List<TenantFlowRuleVO> getTenantFlowRulesByResourcePattern(String
	// resourcePattern, String tenantId, Integer enabled,
	// Integer limit) {
	// List<TenantFlowRule> rules =
	// tenantFlowRuleMapper.selectByResourcePattern(resourcePattern);
	// // 根据tenantId、enabled和limit参数进行过滤和限制
	// Stream<TenantFlowRule> stream = rules.stream();
	// if (tenantId != null) {
	// stream = stream.filter(rule -> rule.getTenantId().equals(tenantId));
	// }
	// if (enabled != null) {
	// stream = stream.filter(rule -> rule.getEnabled().equals(enabled));
	// }
	// if (limit != null && limit > 0) {
	// stream = stream.limit(limit);
	// }
	// return stream.map(this::convertToVO).collect(Collectors.toList());
	// }

	@Override
	public boolean existsByRuleName(String tenantId, String ruleName, Long excludeId) {
		return tenantFlowRuleMapper.countByTenantIdAndRuleName(tenantId, ruleName, excludeId) > 0;
	}

	@Override
	public int countByTenantId(String tenantId, Integer enabled) {
		// 由于Mapper中没有countByTenantId方法，我们通过查询然后计数来实现
		List<TenantFlowRule> rules = tenantFlowRuleMapper.selectByTenantId(tenantId);
		if (enabled == null) {
			return rules.size();
		}
		return (int) rules.stream().filter(rule -> rule.getEnabled().equals(enabled)).count();
	}

	@Override
	public List<TenantFlowRuleVO> getEnabledRules(String tenantId, Integer limit) {
		List<String> tenantIds = Arrays.asList(tenantId);
		List<TenantFlowRule> rules = tenantFlowRuleMapper.selectEnabledRules(tenantIds);
		return rules.stream().map(this::convertToVO).collect(Collectors.toList());
	}

	@Override
	public List<TenantFlowRuleVO> getRulesByPriorityOrder(String tenantId, Integer enabled, Integer limit) {
		List<String> tenantIds = Arrays.asList(tenantId);
		List<TenantFlowRule> rules = tenantFlowRuleMapper.selectByPriorityOrder(tenantIds);
		return rules.stream().map(this::convertToVO).collect(Collectors.toList());
	}

	@Override
	public List<Map<String, Object>> getEnabledStatistics(String tenantId) {
		List<String> tenantIds = Arrays.asList(tenantId);
		return tenantFlowRuleMapper.selectEnabledStatistics(tenantIds);
	}

	@Override
	public List<Map<String, Object>> getTenantFlowRuleStatistics(Integer limit) {
		return tenantFlowRuleMapper.selectTenantFlowRuleStatistics(limit);
	}

	@Override
	public List<Map<String, Object>> getFlowBehaviorStatistics(String tenantId) {
		return tenantFlowRuleMapper.selectControlBehaviorStatistics(tenantId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchCreateTenantFlowRules(List<TenantFlowRuleDTO> tenantFlowRuleDTOList) {
		if (CollectionUtils.isEmpty(tenantFlowRuleDTOList)) {
			return false;
		}

		List<TenantFlowRule> rules = new ArrayList<>();
		Set<String> tenantIds = new HashSet<>();
		LocalDateTime now = LocalDateTime.now();

		for (TenantFlowRuleDTO dto : tenantFlowRuleDTOList) {
			// 验证每个规则
			Map<String, Object> validationResult = validateTenantFlowRule(dto);
			if (!(Boolean) validationResult.get("valid")) {
				throw new RuntimeException("规则验证失败: " + validationResult.get("message"));
			}

			TenantFlowRule rule = new TenantFlowRule();
			BeanUtils.copyProperties(dto, rule);
			rule.setCreateTime(now);
			rule.setUpdateTime(now);
			rule.setDeleted(0);
			rules.add(rule);
			tenantIds.add(dto.getTenantId());
		}

		boolean result = this.saveBatch(rules);

		if (result) {
			log.info("批量创建租户流控规则成功: count={}", rules.size());
			tenantIds.forEach(this::publishTenantFlowRules);
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean copyTenantFlowRule(Long id, String newRuleName, String targetTenantId) {
		TenantFlowRule sourceRule = this.getById(id);
		if (sourceRule == null) {
			throw new RuntimeException("源规则不存在");
		}

		String finalTenantId = StringUtils.hasText(targetTenantId) ? targetTenantId : sourceRule.getTenantId();

		// 检查新规则名称是否已存在
		if (existsByRuleName(finalTenantId, newRuleName, null)) {
			throw new RuntimeException("规则名称在目标租户下已存在");
		}

		TenantFlowRule newRule = new TenantFlowRule();
		BeanUtils.copyProperties(sourceRule, newRule);
		newRule.setId(null);
		newRule.setRuleName(newRuleName);
		newRule.setTenantId(finalTenantId);
		newRule.setCreateTime(LocalDateTime.now());
		newRule.setUpdateTime(LocalDateTime.now());
		newRule.setDeleted(0);

		boolean result = this.save(newRule);

		if (result) {
			log.info("复制租户流控规则成功: sourceId={}, newRuleName={}, targetTenantId={}", id, newRuleName, finalTenantId);
			publishTenantFlowRules(finalTenantId);
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<String, Object> importTenantFlowRules(List<TenantFlowRuleDTO> tenantFlowRuleDTOList, boolean overwrite) {
		Map<String, Object> result = new HashMap<>();
		int successCount = 0;
		int failCount = 0;
		List<String> errors = new ArrayList<>();
		Set<String> tenantIds = new HashSet<>();

		for (TenantFlowRuleDTO dto : tenantFlowRuleDTOList) {
			try {
				// 验证规则
				Map<String, Object> validationResult = validateTenantFlowRule(dto);
				if (!(Boolean) validationResult.get("valid")) {
					errors.add("规则 " + dto.getRuleName() + " 验证失败: " + validationResult.get("message"));
					failCount++;
					continue;
				}

				boolean exists = existsByRuleName(dto.getTenantId(), dto.getRuleName(), null);
				if (exists && !overwrite) {
					errors.add("规则 " + dto.getRuleName() + " 已存在，跳过导入");
					failCount++;
					continue;
				}

				if (exists && overwrite) {
					// 更新现有规则
					TenantFlowRule existingRule = tenantFlowRuleMapper.selectByRuleName(dto.getTenantId(),
							dto.getRuleName(), null);
					if (updateTenantFlowRule(existingRule.getId(), dto)) {
						successCount++;
						tenantIds.add(dto.getTenantId());
					} else {
						failCount++;
						errors.add("更新规则 " + dto.getRuleName() + " 失败");
					}
				} else {
					// 创建新规则
					if (createTenantFlowRule(dto)) {
						successCount++;
						tenantIds.add(dto.getTenantId());
					} else {
						failCount++;
						errors.add("创建规则 " + dto.getRuleName() + " 失败");
					}
				}
			} catch (Exception e) {
				failCount++;
				errors.add("处理规则 " + dto.getRuleName() + " 时发生异常: " + e.getMessage());
				log.error("导入租户流控规则异常", e);
			}
		}

		result.put("successCount", successCount);
		result.put("failCount", failCount);
		result.put("errors", errors);
		result.put("totalCount", tenantFlowRuleDTOList.size());

		log.info("导入租户流控规则完成: 成功={}, 失败={}, 总数={}", successCount, failCount, tenantFlowRuleDTOList.size());

		return result;
	}

	@Override
	public List<TenantFlowRuleDTO> exportTenantFlowRules(String tenantId, Integer enabled) {
		List<TenantFlowRule> rules = tenantFlowRuleMapper.selectForExport(tenantId, enabled);
		return rules.stream().map(this::convertToDTO).collect(Collectors.toList());
	}

	@Override
	public Map<String, Object> validateTenantFlowRule(TenantFlowRuleDTO tenantFlowRuleDTO) {
		Map<String, Object> result = new HashMap<>();
		List<String> errors = new ArrayList<>();

		// 基本字段验证
		if (tenantFlowRuleDTO.getTenantId() == null) {
			errors.add("租户ID不能为空");
		}
		if (!StringUtils.hasText(tenantFlowRuleDTO.getRuleName())) {
			errors.add("规则名称不能为空");
		}

		if (tenantFlowRuleDTO.getCount() == null || tenantFlowRuleDTO.getCount() <= 0) {
			errors.add("限流阈值必须大于0");
		}

		// 流控行为参数验证
		if (!isValidFlowBehaviorParams(tenantFlowRuleDTO)) {
			errors.add("流控行为参数配置无效");
		}

		// 时间范围验证
		if (!isValidTimeRange(tenantFlowRuleDTO)) {
			errors.add("时间范围配置无效");
		}

		result.put("valid", errors.isEmpty());
		result.put("message", errors.isEmpty() ? "验证通过" : String.join(", ", errors));
		result.put("errors", errors);

		return result;
	}

	@Override
	public Integer getMaxPriority(String tenantId) {
		return tenantFlowRuleMapper.selectMaxPriority(tenantId);
	}

	@Override
	public List<TenantFlowRuleVO> getRulesByPriorityRange(String tenantId, Integer minPriority, Integer maxPriority,
			Integer enabled) {
		List<TenantFlowRule> rules = tenantFlowRuleMapper.selectByPriorityRange(tenantId, minPriority, maxPriority);
		return rules.stream().filter(rule -> enabled == null || enabled.equals(rule.getEnabled()))
				.map(this::convertToVO).collect(Collectors.toList());
	}

	@Override
	public List<TenantFlowRuleVO> getExpiringRules(String tenantId, Integer hours) {
		List<TenantFlowRule> rules = tenantFlowRuleMapper.selectExpiringRules(hours);
		return rules.stream().filter(rule -> tenantId == null || tenantId.equals(rule.getTenantId()))
				.map(this::convertToVO).collect(Collectors.toList());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public int disableExpiredRules() {
		int count = tenantFlowRuleMapper.disableExpiredRules("system");
		if (count > 0) {
			log.info("自动禁用过期租户流控规则: count={}", count);
			// 这里可以添加通知逻辑
		}
		return count;
	}

	@Override
	public List<TenantFlowRuleVO> getValidRules(String tenantId) {
		// 租户限流为全局生效，移除资源匹配模式参数
		List<TenantFlowRule> rules = tenantFlowRuleMapper.selectValidRules(tenantId);
		return rules.stream().map(this::convertToVO).collect(Collectors.toList());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<String, Object> batchCopyToTenant(List<Long> sourceIds, String targetTenantId, String namePrefix) {
		Map<String, Object> result = new HashMap<>();
		int successCount = 0;
		int failCount = 0;
		List<String> errors = new ArrayList<>();

		for (Long sourceId : sourceIds) {
			try {
				TenantFlowRule sourceRule = this.getById(sourceId);
				if (sourceRule == null) {
					errors.add("源规则不存在: ID=" + sourceId);
					failCount++;
					continue;
				}

				String newRuleName = namePrefix + sourceRule.getRuleName();
				if (copyTenantFlowRule(sourceId, newRuleName, targetTenantId)) {
					successCount++;
				} else {
					failCount++;
					errors.add("复制规则失败: ID=" + sourceId);
				}
			} catch (Exception e) {
				failCount++;
				errors.add("复制规则异常: ID=" + sourceId + ", 错误=" + e.getMessage());
				log.error("批量复制租户流控规则异常", e);
			}
		}

		result.put("successCount", successCount);
		result.put("failCount", failCount);
		result.put("errors", errors);
		result.put("totalCount", sourceIds.size());

		return result;
	}

	@Override
	public Map<String, Object> getTenantFlowRuleSummary(String tenantId) {
		return tenantFlowRuleMapper.selectTenantFlowRuleSummary(tenantId);
	}

	@Override
	public Map<String, Object> getGlobalFlowRuleSummary() {
		return tenantFlowRuleMapper.selectGlobalFlowRuleSummary();
	}

	// 私有辅助方法

	/**
	 * 更新流控规则启用状态
	 */
	private boolean updateRuleEnabled(Long id, Integer enabled) {
		TenantFlowRule rule = this.getById(id);
		if (rule == null) {
			return false;
		}

		rule.setEnabled(enabled);
		rule.setUpdateTime(LocalDateTime.now());
		boolean result = this.updateById(rule);

		if (result) {
			log.info("更新租户流控规则状态成功: id={}, enabled={}", id, enabled);
			publishTenantFlowRules(rule.getTenantId());
		}

		return result;
	}

	/**
	 * 发布租户流控规则到网关
	 */
	private void publishTenantFlowRules(String tenantId) {
		try {
			// 这里实现流控规则发布到网关的逻辑
			// 例如：发布到Nacos、Redis或直接调用网关API
			log.info("发布租户流控规则到网关: tenantId={}", tenantId);
		} catch (Exception e) {
			log.error("发布租户流控规则到网关失败: tenantId={}", tenantId, e);
		}
	}

	/**
	 * 转换为VO对象
	 */
	private TenantFlowRuleVO convertToVO(TenantFlowRule tenantFlowRule) {
		TenantFlowRuleVO vo = new TenantFlowRuleVO();
		BeanUtils.copyProperties(tenantFlowRule, vo);

		// 设置计算字段
		vo.setIsValid(isRuleCurrentlyValid(tenantFlowRule));
		vo.setIsExpiring(isRuleExpiring(tenantFlowRule, 24)); // 24小时内过期
		vo.setRemainingHours(calculateRemainingHours(tenantFlowRule));

		return vo;
	}

	/**
	 * 转换为DTO对象
	 */
	private TenantFlowRuleDTO convertToDTO(TenantFlowRule tenantFlowRule) {
		TenantFlowRuleDTO dto = new TenantFlowRuleDTO();
		BeanUtils.copyProperties(tenantFlowRule, dto);
		return dto;
	}

	/**
	 * 检查流控规则是否当前有效
	 */
	private boolean isRuleCurrentlyValid(TenantFlowRule rule) {
		if (rule.getEnabled() == null || rule.getEnabled() != 1) {
			return false;
		}

		LocalDateTime now = LocalDateTime.now();

		if (rule.getStartTime() != null && now.isBefore(rule.getStartTime())) {
			return false;
		}

		if (rule.getEndTime() != null && now.isAfter(rule.getEndTime())) {
			return false;
		}

		return true;
	}

	/**
	 * 检查流控规则是否即将过期
	 */
	private boolean isRuleExpiring(TenantFlowRule rule, int hours) {
		if (rule.getEndTime() == null) {
			return false;
		}

		LocalDateTime now = LocalDateTime.now();
		LocalDateTime threshold = now.plusHours(hours);

		return rule.getEndTime().isBefore(threshold) && rule.getEndTime().isAfter(now);
	}

	/**
	 * 计算剩余有效时间（小时）
	 */
	private long calculateRemainingHours(TenantFlowRule rule) {
		if (rule.getEndTime() == null) {
			return -1; // 表示永久有效
		}

		LocalDateTime now = LocalDateTime.now();
		if (now.isAfter(rule.getEndTime())) {
			return 0; // 已过期
		}

		return java.time.Duration.between(now, rule.getEndTime()).toHours();
	}
}