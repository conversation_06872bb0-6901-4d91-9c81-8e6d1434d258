package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.IPFlowRuleDTO;
import com.example.common.entity.IPFlowRule;
import com.example.admin.vo.IPFlowRuleVO;

import java.util.List;
import java.util.Map;

/**
 * IP流控规则服务接口
 */
public interface IPFlowRuleService extends IService<IPFlowRule> {

	/**
	 * 分页查询IP流控规则
	 *
	 * @param page     分页参数
	 * @param tenantId 租户ID
	 * @param ruleType 规则类型
	 * @param status   状态
	 * @param ipValue  IP值
	 * @return IP流控规则VO分页结果
	 */
	Page<IPFlowRuleVO> selectIPFlowRulePage(Page<IPFlowRuleVO> page, String tenantId, String ruleName, String ruleType,
			Integer status, String ipValue);

	/**
	 * 根据ID查询IP流控规则详情
	 *
	 * @param id 规则ID
	 * @return IP流控规则VO
	 */
	IPFlowRuleVO getIPFlowRuleById(Long id);

	/**
	 * 创建IP流控规则
	 *
	 * @param ipFlowRuleDTO IP流控规则DTO
	 * @return 是否成功
	 */
	boolean createIPFlowRule(IPFlowRuleDTO ipFlowRuleDTO);

	/**
	 * 更新IP流控规则
	 *
	 * @param id            规则ID
	 * @param ipFlowRuleDTO IP流控规则DTO
	 * @return 是否成功
	 */
	boolean updateIPFlowRule(Long id, IPFlowRuleDTO ipFlowRuleDTO);

	/**
	 * 删除IP流控规则
	 *
	 * @param id 规则ID
	 * @return 是否成功
	 */
	boolean deleteIPFlowRule(Long id);

	/**
	 * 批量删除IP流控规则
	 *
	 * @param ids 规则ID列表
	 * @return 是否成功
	 */
	boolean batchDeleteIPFlowRules(List<Long> ids);

	/**
	 * 启用IP流控规则
	 *
	 * @param id 规则ID
	 * @return 是否成功
	 */
	boolean enableIPFlowRule(Long id);

	/**
	 * 禁用IP流控规则
	 *
	 * @param id 规则ID
	 * @return 是否成功
	 */
	boolean disableIPFlowRule(Long id);

	/**
	 * 批量更新规则状态
	 *
	 * @param ids    规则ID列表
	 * @param status 状态
	 * @return 是否成功
	 */
	boolean batchUpdateStatus(List<Long> ids, Integer status);

	/**
	 * 根据租户ID查询IP流控规则
	 *
	 * @param tenantId 租户ID
	 * @param status   状态（可选）
	 * @param limit    限制数量
	 * @return IP流控规则列表
	 */
	List<IPFlowRuleVO> getIPFlowRulesByTenantId(String tenantId, Integer status, Integer limit);

	/**
	 * 检查规则名称是否存在
	 *
	 * @param ruleName  规则名称
	 * @param excludeId 排除的ID（用于更新时检查）
	 * @return 是否存在
	 */
	boolean existsByRuleName(String ruleName, Long excludeId);

	/**
	 * 统计租户IP规则数量
	 *
	 * @param tenantId 租户ID
	 * @param status   状态（可选）
	 * @return 规则数量
	 */
	int countByTenantId(String tenantId, Integer status);

	/**
	 * 查询启用的IP规则
	 *
	 * @param tenantId 租户ID（可选）
	 * @param limit    限制数量
	 * @return 启用的IP规则列表
	 */
	List<IPFlowRuleVO> getEnabledIPRules(String tenantId, Integer limit);

	/**
	 * 批量创建IP流控规则
	 *
	 * @param ipFlowRuleDTOList IP流控规则DTO列表
	 * @return 是否成功
	 */
	boolean batchCreateIPFlowRules(List<IPFlowRuleDTO> ipFlowRuleDTOList);

	/**
	 * 复制IP流控规则
	 *
	 * @param id          原规则ID
	 * @param newRuleName 新规则名称
	 * @return 是否成功
	 */
	boolean copyIPFlowRule(Long id, String newRuleName);

	/**
	 * 导入IP流控规则
	 *
	 * @param ipFlowRuleDTOList IP流控规则DTO列表
	 * @param overwrite         是否覆盖已存在的规则
	 * @return 导入结果
	 */
	Map<String, Object> importIPFlowRules(List<IPFlowRuleDTO> ipFlowRuleDTOList, boolean overwrite);

	/**
	 * 导出IP流控规则
	 *
	 * @param tenantId 租户ID（可选）
	 * @param status   状态（可选）
	 * @return IP流控规则列表
	 */
	List<IPFlowRuleVO> exportIPFlowRules(String tenantId, Integer status);

	/**
	 * 验证IP流控规则配置
	 *
	 * @param ipFlowRuleDTO IP流控规则DTO
	 * @return 验证结果
	 */
	Map<String, Object> validateIPFlowRule(IPFlowRuleDTO ipFlowRuleDTO);

	/**
	 * 验证IP格式
	 *
	 * @param ipValue  IP值
	 * @param ruleType 规则类型
	 * @return 是否有效
	 */
	boolean validateIpFormat(String ipValue, String ruleType);

	/**
	 * 检查IP是否匹配规则
	 *
	 * @param clientIp 客户端IP
	 * @param tenantId 租户ID
	 * @return 匹配的规则列表
	 */
	List<IPFlowRuleVO> getMatchingRules(String clientIp, String tenantId);

	/**
	 * 同步规则到Sentinel
	 *
	 * @param id 规则ID
	 * @return 是否成功
	 */
	boolean syncRuleToSentinel(Long id);

	/**
	 * 批量同步规则到Sentinel
	 *
	 * @param ids 规则ID列表
	 * @return 同步结果
	 */
	Map<String, Object> batchSyncRulesToSentinel(List<Long> ids);

	/**
	 * 从Sentinel同步规则
	 *
	 * @param tenantId 租户ID
	 * @return 同步结果
	 */
	Map<String, Object> syncRulesFromSentinel(String tenantId);

	/**
	 * 获取IP流量规则统计
	 *
	 * @param tenantId 租户ID（可选）
	 * @return 统计结果
	 */
	Object getIPFlowRuleStatistics(String tenantId);
}