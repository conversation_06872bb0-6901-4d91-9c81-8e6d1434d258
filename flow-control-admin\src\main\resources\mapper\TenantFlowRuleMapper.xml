<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.TenantFlowRuleMapper">

	<!-- 基础结果映射 -->
	<resultMap id="BaseResultMap" type="com.example.common.entity.TenantFlowRule">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
		<result column="rule_name" property="ruleName" jdbcType="VARCHAR" />

		<result column="grade" property="grade" jdbcType="INTEGER" />
		<result column="count" property="count" jdbcType="DOUBLE" />
		<result column="control_behavior" property="controlBehavior" jdbcType="INTEGER" />
		<result column="warm_up_period_sec" property="warmUpPeriodSec" jdbcType="INTEGER" />
		<result column="max_queueing_time_ms" property="maxQueueingTimeMs" jdbcType="INTEGER" />
		<result column="strategy" property="strategy" jdbcType="INTEGER" />
		<result column="ref_resource" property="refResource" jdbcType="VARCHAR" />
		<result column="cluster_mode" property="clusterMode" jdbcType="BOOLEAN" />
		<result column="cluster_config" property="clusterConfig" jdbcType="VARCHAR" />
		<result column="priority" property="priority" jdbcType="INTEGER" />
		<result column="enabled" property="enabled" jdbcType="INTEGER" />
		<result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
		<result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
		<result column="description" property="description" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="deleted" property="deleted" jdbcType="INTEGER" />
	</resultMap>

	<!-- TenantFlowRuleVO结果映射 -->
	<resultMap id="TenantFlowRuleVOResultMap" type="com.example.admin.vo.TenantFlowRuleVO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
		<result column="tenant_name" property="tenantName" jdbcType="VARCHAR" />
		<result column="rule_name" property="ruleName" jdbcType="VARCHAR" />

		<result column="grade" property="grade" jdbcType="INTEGER" />
		<result column="grade_name" property="gradeName" jdbcType="VARCHAR" />
		<result column="count" property="count" jdbcType="DOUBLE" />
		<result column="control_behavior" property="controlBehavior" jdbcType="INTEGER" />
		<result column="control_behavior_name" property="controlBehaviorName" jdbcType="VARCHAR" />
		<result column="warm_up_period_sec" property="warmUpPeriodSec" jdbcType="INTEGER" />
		<result column="max_queueing_time_ms" property="maxQueueingTimeMs" jdbcType="INTEGER" />
		<result column="strategy" property="strategy" jdbcType="INTEGER" />
		<result column="ref_resource" property="refResource" jdbcType="VARCHAR" />
		<result column="cluster_mode" property="clusterMode" jdbcType="BOOLEAN" />
		<result column="cluster_config" property="clusterConfig" jdbcType="VARCHAR" />
		<result column="priority" property="priority" jdbcType="INTEGER" />
		<result column="enabled" property="enabled" jdbcType="INTEGER" />
		<result column="enabled_name" property="enabledName" jdbcType="VARCHAR" />
		<result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
		<result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
		<result column="description" property="description" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
	</resultMap>

	<!-- 分页查询租户流控规则VO -->
	<select id="selectTenantFlowRuleVOPage" resultMap="TenantFlowRuleVOResultMap"> SELECT tr.id,
		tr.tenant_id, ti.tenant_name, tr.rule_name, tr.grade, CASE tr.grade WHEN 0 THEN '线程数' WHEN 1
		THEN 'QPS' ELSE '未知' END as grade_name, tr.count, tr.control_behavior, CASE
		tr.control_behavior WHEN 0 THEN '快速失败' WHEN 1 THEN 'Warm Up' WHEN 2 THEN '排队等待' WHEN 3 THEN
		'Warm Up + 排队等待' ELSE '未知' END as control_behavior_name, tr.warm_up_period_sec,
		tr.max_queueing_time_ms, tr.strategy, tr.ref_resource, tr.cluster_mode, tr.cluster_config,
		tr.priority, tr.enabled, CASE tr.enabled WHEN 0 THEN 'DISABLED' WHEN 1 THEN 'ENABLED' ELSE
		'UNKNOWN' END as enabled_name, tr.start_time, tr.end_time, tr.description, tr.create_time,
		tr.update_time, tr.create_by, tr.update_by FROM tenant_flow_rules tr LEFT JOIN tenant_info
		ti ON tr.tenant_id = ti.tenant_id WHERE tr.deleted = 0 <if
			test="tenantId != null and tenantId != ''"> AND tr.tenant_id = #{tenantId} </if>
        <if
			test="ruleName != null and ruleName != ''"> AND tr.rule_name LIKE CONCAT('%',
		#{ruleName}, '%') </if>
        <if test="enabled != null"> AND tr.enabled = #{enabled} </if>
        <if
			test="controlBehavior != null"> AND tr.control_behavior = #{controlBehavior} </if> ORDER
		BY tr.priority DESC, tr.create_time DESC </select>

	<!-- 分页查询租户流控规则（基础版本） -->
	<select id="selectPageWithConditions" resultMap="BaseResultMap"> SELECT * FROM tenant_flow_rules
		WHERE deleted = 0 <if test="tenantId != null and tenantId != ''"> AND tenant_id =
		#{tenantId} </if>
        <if test="ruleName != null and ruleName != ''"> AND rule_name LIKE
		CONCAT('%', #{ruleName}, '%') </if>

        <if test="enabled != null"> AND enabled = #{enabled} </if>
        <if
			test="controlBehavior != null"> AND control_behavior = #{controlBehavior} </if> ORDER BY
		priority DESC, create_time DESC </select>

	<!-- 批量更新启用状态 -->
	<update id="batchUpdateEnabled"> UPDATE tenant_flow_rules SET enabled = #{enabled}, update_time
		= NOW() WHERE id IN <foreach collection="ids" item="id" open="(" separator="," close=")">
		#{id} </foreach> AND deleted = 0 </update>

	<!-- 批量删除（逻辑删除） -->
	<update id="batchDelete"> UPDATE tenant_flow_rules SET deleted = 1, update_time = NOW() WHERE id
		IN <foreach
			collection="ids" item="id" open="(" separator="," close=")"> #{id} </foreach> AND
		deleted = 0 </update>

	<!-- 根据租户ID和规则名称统计数量 -->
	<select id="countByTenantIdAndRuleName" resultType="java.lang.Integer"> SELECT COUNT(*) FROM
		tenant_flow_rules WHERE tenant_id = #{tenantId} AND rule_name = #{ruleName} AND deleted = 0 <if
			test="excludeId != null"> AND id != #{excludeId} </if>
	</select>

	<!-- 流控效果统计 -->
	<select id="selectControlBehaviorStatistics" resultType="java.util.Map"> SELECT control_behavior
		as controlBehavior, CASE control_behavior WHEN 0 THEN '快速失败' WHEN 1 THEN 'Warm Up' WHEN 2
		THEN '排队等待' WHEN 3 THEN 'Warm Up + 排队等待' ELSE '未知' END as controlBehaviorName, COUNT(*) as
		count FROM tenant_flow_rules WHERE deleted = 0 <if
			test="tenantId != null and tenantId != ''"> AND tenant_id = #{tenantId} </if> GROUP BY
		control_behavior ORDER BY control_behavior </select>

</mapper>