package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.entity.IPWhitelist;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Insert;

import java.util.List;
import java.util.Map;

/**
 * IP白名单Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface IPWhitelistMapper extends BaseMapper<IPWhitelist> {

    /**
     * 根据租户ID查询IP白名单
     * 
     * @param tenantId 租户ID
     * @return IP白名单列表
     */
    @Select("SELECT * FROM ip_whitelist " +
            "WHERE tenant_id = #{tenantId} AND deleted = 0 " +
            "ORDER BY create_time DESC")
    List<IPWhitelist> selectByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据租户ID和启用状态查询IP白名单
     * 
     * @param tenantId 租户ID
     * @param enabled 启用状态
     * @return IP白名单列表
     */
    @Select("SELECT * FROM ip_whitelist " +
            "WHERE tenant_id = #{tenantId} AND status = #{enabled} AND deleted = 0 " +
            "ORDER BY create_time DESC")
    List<IPWhitelist> selectByTenantIdAndEnabled(@Param("tenantId") String tenantId,
                                                                     @Param("enabled") Integer enabled);

    /**
     * 根据IP地址查询匹配的白名单（用于IP检查）
     * 
     * @param tenantId 租户ID
     * @param ipAddress IP地址
     * @return 匹配的白名单列表
     */
    @Select("SELECT * FROM ip_whitelist " +
            "WHERE tenant_id = #{tenantId} AND status = 1 AND deleted = 0 " +
            "AND (" +
            "  (ip_type = 'SINGLE' AND ip_address = #{ipAddress}) OR " +
            "  (ip_type = 'RANGE' AND INET_ATON(#{ipAddress}) BETWEEN INET_ATON(ip_start) AND INET_ATON(ip_end)) OR " +
            "  (ip_type = 'CIDR' AND #{ipAddress} LIKE CONCAT(SUBSTRING_INDEX(ip_cidr, '/', 1), '%'))" +
            ") " +
            "AND (start_time IS NULL OR start_time <= NOW()) " +
            "AND (end_time IS NULL OR end_time >= NOW()) " +
            "ORDER BY create_time DESC")
    List<IPWhitelist> selectMatchingRules(@Param("tenantId") String tenantId, 
                                                   @Param("ipAddress") String ipAddress);

    /**
     * 分页查询IP白名单
     * 
     * @param page 分页参数
     * @param tenantId 租户ID（可选）
     * @param enabled 启用状态（可选）
     * @param listName 名单名称（可选，模糊查询）
     * @param ipAddress IP地址（可选，模糊查询）
     * @return 分页结果
     */
    IPage<IPWhitelist> selectPageWithConditions(
            Page<IPWhitelist> page,
            @Param("tenantId") String tenantId,
            @Param("enabled") Integer enabled,
            @Param("listName") String listName,
            @Param("ipAddress") String ipAddress
    );

    /**
     * 批量插入IP白名单
     * 
     * @param ipLists IP白名单列表
     * @return 插入数量
     */
    @Insert("<script>" +
            "INSERT INTO ip_whitelist (tenant_id, ip_type, ip_address, " +
            "ip_start, ip_end, ip_cidr, status, description, " +
            "create_time, update_time) VALUES " +
            "<foreach collection='ipLists' item='item' separator=','>" +
            "(#{item.tenantId}, #{item.ipType}, #{item.ipAddress}, " +
            "#{item.ipStart}, #{item.ipEnd}, #{item.ipCidr}, #{item.status}, #{item.description}, " +
            "NOW(), NOW())" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("ipLists") List<IPWhitelist> ipLists);

    /**
     * 批量启用/禁用IP白名单
     * 
     * @param ids ID列表
     * @param enabled 启用状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    @Update("<script>" +
            "UPDATE ip_whitelist SET status = #{enabled}, update_time = NOW() " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "AND deleted = 0" +
            "</script>")
    int batchUpdateEnabled(@Param("ids") List<Long> ids, @Param("enabled") Integer enabled);

    /**
     * 批量删除IP白名单（逻辑删除）
     * 
     * @param ids ID列表
     * @param updateBy 更新人
     * @return 删除数量
     */
    @Update("<script>" +
            "UPDATE ip_whitelist SET deleted = 1, update_time = NOW() " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "AND deleted = 0" +
            "</script>")
    int batchDelete(@Param("ids") List<Long> ids);

    /**
     * 检查名单名称是否存在（同一租户下）
     * 
     * @param tenantId 租户ID
     * @param listName 名单名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在的数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM ip_whitelist " +
            "WHERE tenant_id = #{tenantId} AND deleted = 0 " +
            "<if test='excludeId != null'>" +
            "AND id != #{excludeId}" +
            "</if>" +
            "</script>")
    int countByTenantIdAndListName(@Param("tenantId") String tenantId, 
                                   @Param("listName") String listName, 
                                   @Param("excludeId") Long excludeId);

    /**
     * 获取IP白名单统计信息
     * 
     * @param tenantId 租户ID
     * @return 统计信息
     */
    @Select("SELECT " +
            "ip_type, " +
            "COUNT(*) as count, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as activeCount " +
            "FROM ip_whitelist " +
            "WHERE tenant_id = #{tenantId} AND deleted = 0 " +
            "GROUP BY ip_type")
    List<Map<String, Object>> getIPListStats(@Param("tenantId") String tenantId);

    /**
     * 获取所有租户的IP白名单统计信息
     * 
     * @return 统计信息列表
     */
    @Select("SELECT " +
            "t.tenant_name, " +
            "i.tenant_id, " +
            "i.ip_type, " +
            "COUNT(*) as totalCount, " +
            "SUM(CASE WHEN i.status = 1 THEN 1 ELSE 0 END) as activeCount, " +
            "SUM(CASE WHEN i.status = 0 THEN 1 ELSE 0 END) as inactiveCount " +
            "FROM ip_whitelist i " +
            "LEFT JOIN tenant_flow_rules t ON i.tenant_id = t.tenant_id " +
            "WHERE i.deleted = 0 " +
            "GROUP BY i.tenant_id, t.tenant_name, i.ip_type")
    List<Map<String, Object>> getAllIPListStats();

    /**
     * 根据来源查询IP白名单
     * 
     * @param tenantId 租户ID
     * @param source 来源
     * @return IP白名单列表
     */
    @Select("SELECT * FROM ip_whitelist " +
            "WHERE tenant_id = #{tenantId} AND source = #{source} AND deleted = 0 " +
            "ORDER BY create_time DESC")
    List<IPWhitelist> selectByTenantIdAndSource(@Param("tenantId") String tenantId, 
                                                         @Param("source") String source);

    /**
     * 获取即将过期的IP白名单（7天内过期）
     * 
     * @param tenantId 租户ID
     * @return 即将过期的IP白名单列表
     */
    @Select("SELECT * FROM ip_whitelist " +
            "WHERE tenant_id = #{tenantId} AND status = 1 AND deleted = 0 " +
            "AND end_time IS NOT NULL " +
            "AND end_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY) " +
            "ORDER BY end_time ASC")
    List<IPWhitelist> selectExpiringRules(@Param("tenantId") String tenantId);

    /**
     * 清理过期的IP白名单
     * 
     * @return 清理数量
     */
    @Update("UPDATE ip_whitelist SET status = 0, update_time = NOW() " +
            "WHERE status = 1 AND deleted = 0 " +
            "AND end_time IS NOT NULL AND end_time < NOW()")
    int disableExpiredRules();

    /**
     * 根据优先级范围查询IP白名单
     * 
     * @param tenantId 租户ID
     * @param minPriority 最小优先级
     * @param maxPriority 最大优先级
     * @return IP白名单列表
     */
    @Select("SELECT * FROM ip_whitelist " +
            "WHERE tenant_id = #{tenantId} " +
            "AND deleted = 0 " +
            "ORDER BY create_time DESC")
    List<IPWhitelist> selectByPriorityRange(@Param("tenantId") String tenantId);

    /**
     * 获取租户指定类型名单的最大优先级
     * 
     * @param tenantId 租户ID
     * @return 最大优先级
     */
    @Select("SELECT COALESCE(MAX(id), 0) FROM ip_whitelist " +
            "WHERE tenant_id = #{tenantId} AND deleted = 0")
    Integer getMaxPriorityByTenantId(@Param("tenantId") String tenantId);

    /**
     * 复制IP白名单到其他租户
     * 
     * @param sourceIds 源名单ID列表
     * @param targetTenantId 目标租户ID
     * @param createBy 创建人
     * @return 复制数量
     */
    @Insert("<script>" +
            "INSERT INTO ip_whitelist (tenant_id, ip_type, ip_address, " +
            "ip_start, ip_end, ip_cidr, status, description, " +
            "create_time, update_time) " +
            "SELECT #{targetTenantId}, ip_type, ip_address, " +
            "ip_start, ip_end, ip_cidr, status, description, " +
            "NOW(), NOW() " +
            "FROM ip_whitelist " +
            "WHERE id IN " +
            "<foreach collection='sourceIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "AND deleted = 0" +
            "</script>")
    int copyToTenant(@Param("sourceIds") List<Long> sourceIds,
                     @Param("targetTenantId") String targetTenantId);
}