package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.MonitorStatisticsDTO;
import com.example.common.entity.MonitorStatistics;
import com.example.admin.vo.MonitorVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 监控统计服务接口
 */
public interface MonitorStatisticsService extends IService<MonitorStatistics> {
    
    /**
     * 分页查询监控统计数据
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 监控统计VO分页结果
     */
    Page<MonitorVO> selectMonitorStatisticsPage(Page<MonitorVO> page, String tenantId, String resourceName,
                                                LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据ID查询监控统计详情
     *
     * @param id 统计ID
     * @return 监控统计VO
     */
    MonitorVO getMonitorStatisticsById(Long id);
    
    /**
     * 创建监控统计记录
     *
     * @param monitorStatisticsDTO 监控统计DTO
     * @return 是否成功
     */
    boolean createMonitorStatistics(MonitorStatisticsDTO monitorStatisticsDTO);
    
    /**
     * 更新监控统计记录
     *
     * @param id 统计ID
     * @param monitorStatisticsDTO 监控统计DTO
     * @return 是否成功
     */
    boolean updateMonitorStatistics(Long id, MonitorStatisticsDTO monitorStatisticsDTO);
    
    /**
     * 删除监控统计记录
     *
     * @param id 统计ID
     * @return 是否成功
     */
    boolean deleteMonitorStatistics(Long id);
    
    /**
     * 批量删除监控统计记录
     *
     * @param ids 统计ID列表
     * @return 是否成功
     */
    boolean batchDeleteMonitorStatistics(List<Long> ids);
    
    /**
     * 根据时间范围查询监控统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param limit 限制数量
     * @return 监控统计列表
     */
    List<MonitorVO> getMonitorStatisticsByTimeRange(LocalDateTime startTime, LocalDateTime endTime,
                                                     String tenantId, String resourceName, Integer limit);
    
    /**
     * 根据租户ID查询监控统计
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return 监控统计列表
     */
    List<MonitorVO> getMonitorStatisticsByTenantId(String tenantId, LocalDateTime startTime,
                                                    LocalDateTime endTime, Integer limit);
    
    /**
     * 根据资源名称查询监控统计
     *
     * @param resourceName 资源名称
     * @param tenantId 租户ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return 监控统计列表
     */
    List<MonitorVO> getMonitorStatisticsByResourceName(String resourceName, String tenantId,
                                                        LocalDateTime startTime, LocalDateTime endTime, Integer limit);
    
    /**
     * 查询实时监控数据
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param minutes 最近多少分钟
     * @param limit 限制数量
     * @return 实时监控数据列表
     */
    List<MonitorVO> getRealtimeMonitorData(String tenantId, String resourceName, Integer minutes, Integer limit);
    
    /**
     * 查询趋势数据
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 时间间隔（分钟）
     * @return 趋势数据列表
     */
    List<Map<String, Object>> getTrendData(String tenantId, String resourceName, LocalDateTime startTime,
                                           LocalDateTime endTime, Integer interval);
    
    /**
     * 统计总请求数
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 总请求数
     */
    Long getTotalRequestCount(String tenantId, String resourceName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计被阻塞请求数
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 被阻塞请求数
     */
    Long getBlockedRequestCount(String tenantId, String resourceName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 计算平均响应时间
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 平均响应时间（毫秒）
     */
    Double getAverageResponseTime(String tenantId, String resourceName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取资源QPS排行
     *
     * @param tenantId 租户ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return QPS排行列表
     */
    List<Map<String, Object>> getResourceQpsRanking(String tenantId, LocalDateTime startTime,
                                                     LocalDateTime endTime, Integer limit);
    
    /**
     * 获取资源响应时间排行
     *
     * @param tenantId 租户ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return 响应时间排行列表
     */
    List<Map<String, Object>> getResourceResponseTimeRanking(String tenantId, LocalDateTime startTime,
                                                              LocalDateTime endTime, Integer limit);
    
    /**
     * 获取租户统计信息
     *
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量
     * @return 租户统计信息列表
     */
    List<Map<String, Object>> getTenantStatistics(LocalDateTime startTime, LocalDateTime endTime, Integer limit);
    
    /**
     * 获取系统概览信息
     *
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 系统概览信息
     */
    Map<String, Object> getSystemOverview(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 批量插入监控统计数据
     *
     * @param monitorStatisticsList 监控统计数据列表
     * @return 是否成功
     */
    boolean batchInsertMonitorStatistics(List<MonitorStatistics> monitorStatisticsList);
    
    /**
     * 删除过期的监控数据
     *
     * @param beforeTime 过期时间点
     * @return 删除的记录数
     */
    int deleteExpiredData(LocalDateTime beforeTime);
    
    /**
     * 查询异常数据
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param threshold 异常阈值
     * @param limit 限制数量
     * @return 异常数据列表
     */
    List<MonitorVO> getAbnormalData(String tenantId, String resourceName, LocalDateTime startTime,
                                    LocalDateTime endTime, Double threshold, Integer limit);
    
    /**
     * 获取监控数据汇总
     *
     * @param tenantId 租户ID（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组字段（hour, day, month）
     * @return 汇总数据列表
     */
    List<Map<String, Object>> getMonitorSummary(String tenantId, LocalDateTime startTime,
                                                 LocalDateTime endTime, String groupBy);
    
    /**
     * 导出监控统计数据
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 监控统计数据列表
     */
    List<MonitorVO> exportMonitorStatistics(String tenantId, String resourceName,
                                             LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取热点资源
     *
     * @param tenantId 租户ID（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 热点资源列表
     */
    List<Map<String, Object>> getHotResources(String tenantId, LocalDateTime startTime,
                                               LocalDateTime endTime, Integer limit);
    
    /**
     * 获取性能指标
     *
     * @param tenantId 租户ID（可选）
     * @param resourceName 资源名称（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 性能指标
     */
    Map<String, Object> getPerformanceMetrics(String tenantId, String resourceName,
                                               LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 清理监控数据
     *
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanupMonitorData(Integer retentionDays);
    
    /**
     * 获取监控数据存储统计
     *
     * @return 存储统计信息
     */
    Map<String, Object> getStorageStatistics();
    
    /**
     * 获取仪表板数据
     *
     * @param tenantId 租户ID（可选）
     * @return 仪表板数据
     */
    Map<String, Object> getDashboardData(String tenantId);
}