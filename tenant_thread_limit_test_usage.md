# 租户线程数限流测试脚本使用说明

## 脚本概述

`tenant_thread_limit_test.py` 是一个专门用于测试租户线程数限流功能的Python脚本。该脚本基于Sentinel流控规则，通过并发请求和长时间运行的请求来验证线程数限制的有效性。

## 功能特性

### 1. 线程数限流测试
- **并发线程控制**: 通过ThreadPoolExecutor控制并发线程数量
- **长时间请求模拟**: 每个请求持续指定时间来占用线程资源
- **线程占用验证**: 验证实际并发线程数是否符合限制
- **多种控制行为支持**: 快速失败、预热启动、排队等待、预热+排队

### 2. 租户规则配置
基于用户提供的SQL数据，包含5个租户的线程数限流规则：

| 租户ID | 规则名称 | 线程限制 | 控制行为 | 特殊参数 | 描述 |
|--------|----------|----------|----------|----------|------|
| tenant1 | 租户1默认线程数限流 | 5 | 快速失败(0) | - | 超过线程限制立即拒绝 |
| tenant2 | 租户2预热限流 | 5 | 预热启动(1) | 预热时间10秒 | 逐渐增加到最大线程数 |
| tenant3 | 租户3排队限流 | 5 | 排队等待(2) | 最大排队5000ms | 超限请求排队等待 |
| tenant4 | 租户4线程数限流 | 5 | 快速失败(0) | - | 超过线程限制立即拒绝 |
| tenant5 | 租户5综合限流 | 5 | 预热+排队(3) | 预热15秒+排队3000ms | 预热启动+排队等待组合 |

### 3. 测试报告生成
- **JSON格式报告**: 详细的测试数据和统计信息
- **控制台摘要**: 直观的测试结果展示
- **线程使用分析**: 实际并发线程数统计
- **限流效果验证**: 验证线程限制是否生效

## 使用方法

### 基本语法
```bash
python tenant_thread_limit_test.py [选项]
```

### 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--url` | string | http://localhost:8080 | 测试目标URL |
| `--duration` | float | 2.0 | 单个请求持续时间（秒） |
| `--concurrent` | int | 10 | 并发线程数 |
| `--test-duration` | int | 30 | 测试持续时间（秒） |
| `--tenant` | string | - | 只测试指定租户 |
| `--list-rules` | flag | - | 显示所有租户规则 |
| `--output` | string | - | 输出报告文件名 |

## 使用示例

### 1. 查看所有租户规则
```bash
python tenant_thread_limit_test.py --list-rules
```

### 2. 完整测试所有租户
```bash
python tenant_thread_limit_test.py --url http://localhost:8080 --concurrent 15 --test-duration 60
```

### 3. 测试特定租户
```bash
python tenant_thread_limit_test.py --tenant tenant2 --concurrent 8 --duration 3.0
```

### 4. 自定义参数测试
```bash
python tenant_thread_limit_test.py --url http://*************:8080 --duration 1.5 --concurrent 12 --test-duration 45 --output my_test_report.json
```

### 5. 快速测试（短时间）
```bash
python tenant_thread_limit_test.py --duration 1.0 --concurrent 8 --test-duration 15
```

## 测试原理

### 线程数限流 vs QPS限流
- **QPS限流**: 控制每秒请求数量
- **线程数限流**: 控制同时处理的请求数量（并发线程数）

### 测试策略
1. **并发请求**: 同时发起多个请求来占用线程
2. **长时间请求**: 每个请求持续一定时间来保持线程占用
3. **线程统计**: 记录实际使用的线程数量
4. **限流验证**: 检查是否超过线程限制

### 控制行为说明

#### 快速失败 (control_behavior=0)
- 超过线程限制时立即返回429错误
- 不等待，直接拒绝新请求

#### 预热启动 (control_behavior=1)
- 系统启动时逐渐增加到最大线程数
- 预热期间线程限制逐步放宽
- 适用于系统冷启动场景

#### 排队等待 (control_behavior=2)
- 超过线程限制时请求进入队列等待
- 有最大等待时间限制
- 适用于可以容忍延迟的场景

#### 预热+排队 (control_behavior=3)
- 结合预热启动和排队等待
- 预热期间逐渐增加线程数，超限请求排队
- 提供最灵活的流控策略

## 输出报告

### JSON报告结构
```json
{
  "test_type": "tenant_thread_limit",
  "test_time": "2024-01-15T10:30:00",
  "base_url": "http://localhost:8080",
  "request_duration": 2.0,
  "summary": {
    "total_tenants": 5,
    "total_requests": 50,
    "total_successful": 25,
    "total_blocked": 25,
    "overall_success_rate": 0.5
  },
  "tenant_results": [
    {
      "tenant_id": "tenant1",
      "rule_name": "租户1默认线程数限流",
      "thread_limit": 5,
      "control_behavior": 0,
      "control_behavior_name": "快速失败",
      "concurrent_threads": 10,
      "actual_concurrent_threads": 5,
      "success_rate": 0.5,
      "thread_limit_effective": true,
      "requests": [...]
    }
  ]
}
```

### 控制台输出示例
```
================================================================================
租户线程数限流测试结果摘要
================================================================================

租户: tenant1 (租户1默认线程数限流)
  线程限制: 5
  控制行为: 快速失败
  并发线程: 10
  实际并发线程: 5
  总请求数: 10
  成功请求: 5
  被阻止请求: 5
  成功率: 50.00%
  平均响应时间: 2.05秒
  线程限流有效: 是

总体统计:
  测试租户数: 5
  总请求数: 50
  总成功数: 25
  总阻止数: 25
  总体成功率: 50.00%
```

## 注意事项

### 1. 环境要求
- Python 3.6+
- requests库
- 目标服务需要支持线程数限流

### 2. 测试参数调优
- **请求持续时间**: 建议1-5秒，太短无法有效占用线程，太长影响测试效率
- **并发线程数**: 建议设置为线程限制的1.5-3倍，以验证限流效果
- **测试持续时间**: 建议30-120秒，确保预热和排队机制充分测试

### 3. 服务端配置
- 确保目标服务已配置对应的租户线程数限流规则
- 服务端需要正确识别X-Tenant-ID请求头
- 建议服务端支持sleep参数来模拟长时间请求

### 4. 结果分析
- **thread_limit_effective=true**: 线程限流生效
- **actual_concurrent_threads <= thread_limit**: 限流正常工作
- **success_rate**: 成功率反映限流策略的效果
- **response_time**: 响应时间反映排队等待的效果

### 5. 故障排除
- **连接超时**: 检查目标URL是否可访问
- **所有请求失败**: 检查服务端是否正确配置
- **限流不生效**: 检查租户ID和规则配置是否匹配
- **预热效果不明显**: 增加测试持续时间或调整预热参数

## 扩展功能

### 自定义租户规则
可以修改`create_tenant_rules()`函数来添加或修改租户规则：

```python
def create_tenant_rules():
    rules = [
        {
            'tenant_id': 'custom_tenant',
            'rule_name': '自定义租户规则',
            'count': 3,  # 线程数限制
            'control_behavior': 1,  # 控制行为
            'warm_up_period_sec': 5,  # 预热时间
            # ... 其他参数
        }
    ]
    return rules
```

### 集成到CI/CD
可以将脚本集成到持续集成流程中：

```bash
# 在CI脚本中运行
python tenant_thread_limit_test.py --url $TEST_URL --output ci_test_report.json
# 检查测试结果
if [ $? -eq 0 ]; then
    echo "线程数限流测试通过"
else
    echo "线程数限流测试失败"
    exit 1
fi
```