<template>
  <div class="ip-blackwhite-list">
    <layout>
      <div class="page-content">
        <div class="page-header">
          <h1>IP黑白名单配置</h1>
          <p>配置IP黑名单和白名单，支持单个IP、IP范围和CIDR网段的配置</p>
        </div>

        <!-- 标签页 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <!-- 白名单标签页 -->
          <el-tab-pane label="IP白名单" name="whitelist">
            <div class="tab-content">
              <!-- 操作栏 -->
              <div class="operation-bar">
                <el-button type="primary" icon="el-icon-plus" @click="showAddDialog('whitelist')">
                  新增白名单
                </el-button>
                
                <div class="filter-group">
                  <el-select
                    v-model="whitelistSearch.ipType"
                    placeholder="选择IP类型"
                    clearable
                    style="width: 150px; margin-right: 10px"
                    @change="handleWhitelistSearch"
                  >
                    <el-option label="单个IP" value="SINGLE" />
                    <el-option label="IP范围" value="RANGE" />
                    <el-option label="CIDR网段" value="CIDR" />
                  </el-select>
                  <el-input
                    v-model="whitelistSearch.keyword"
                    placeholder="请输入IP地址或描述"
                    clearable
                    style="width: 250px"
                    @keyup.enter.native="handleWhitelistSearch"
                  >
                    <el-button slot="append" icon="el-icon-search" @click="handleWhitelistSearch"></el-button>
                  </el-input>
                </div>
              </div>

              <!-- 白名单表格 -->
              <el-table
                :data="whitelistData"
                :loading="whitelistLoading"
                stripe
                border
                style="width: 100%"
              >
                <el-table-column prop="ipValue" label="IP地址" width="200" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-tag size="mini" type="info">{{ getIpTypeText(scope.row.ipType) }}</el-tag>
                    {{ scope.row.ipValue }}
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" show-overflow-tooltip />
                <el-table-column prop="status" label="状态" width="80">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                      {{ scope.row.status === 1 ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="effectiveTime" label="生效时间" width="160">
                  <template slot-scope="scope">
                    {{ formatTime(scope.row.effectiveTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="expiryTime" label="过期时间" width="160">
                  <template slot-scope="scope">
                    <span v-if="scope.row.expiryTime">
                      {{ formatTime(scope.row.expiryTime) }}
                      <el-tag v-if="isExpiringSoon(scope.row.expiryTime)" size="mini" type="warning">即将过期</el-tag>
                    </span>
                    <span v-else class="text-muted">永久</span>
                  </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="160">
                  <template slot-scope="scope">
                    {{ formatTime(scope.row.createTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="250">
                  <template slot-scope="scope">
                    <el-button size="mini" @click="editItem(scope.row, 'whitelist')">编辑</el-button>
                    <el-button
                      size="mini"
                      :type="scope.row.status === 1 ? 'warning' : 'success'"
                      @click="toggleStatus(scope.row, 'whitelist')"
                    >
                      {{ scope.row.status === 1 ? '禁用' : '启用' }}
                    </el-button>
                    <el-button size="mini" type="danger" @click="deleteItem(scope.row, 'whitelist')">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 白名单分页 -->
              <div class="pagination-wrapper">
                <el-pagination
                  @size-change="handleWhitelistSizeChange"
                  @current-change="handleWhitelistCurrentChange"
                  :current-page="whitelistPagination.current"
                  :page-sizes="[10, 20, 50, 100]"
                  :page-size="whitelistPagination.size"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="whitelistPagination.total"
                >
                </el-pagination>
              </div>
            </div>
          </el-tab-pane>

          <!-- 黑名单标签页 -->
          <el-tab-pane label="IP黑名单" name="blacklist">
            <div class="tab-content">
              <!-- 操作栏 -->
              <div class="operation-bar">
                <el-button type="danger" icon="el-icon-plus" @click="showAddDialog('blacklist')">
                  新增黑名单
                </el-button>
                
                <div class="filter-group">
                  <el-select
                    v-model="blacklistSearch.ipType"
                    placeholder="选择IP类型"
                    clearable
                    style="width: 150px; margin-right: 10px"
                    @change="handleBlacklistSearch"
                  >
                    <el-option label="单个IP" value="SINGLE" />
                    <el-option label="IP范围" value="RANGE" />
                    <el-option label="CIDR网段" value="CIDR" />
                  </el-select>
                  <el-input
                    v-model="blacklistSearch.keyword"
                    placeholder="请输入IP地址或描述"
                    clearable
                    style="width: 250px"
                    @keyup.enter.native="handleBlacklistSearch"
                  >
                    <el-button slot="append" icon="el-icon-search" @click="handleBlacklistSearch"></el-button>
                  </el-input>
                </div>
              </div>

              <!-- 黑名单表格 -->
              <el-table
                :data="blacklistData"
                :loading="blacklistLoading"
                stripe
                border
                style="width: 100%"
              >
                <el-table-column prop="ipValue" label="IP地址" width="200" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-tag size="mini" type="info">{{ getIpTypeText(scope.row.ipType) }}</el-tag>
                    {{ scope.row.ipValue }}
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" show-overflow-tooltip />
                <el-table-column prop="status" label="状态" width="80">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                      {{ scope.row.status === 1 ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="effectiveTime" label="生效时间" width="160">
                  <template slot-scope="scope">
                    {{ formatTime(scope.row.effectiveTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="expiryTime" label="过期时间" width="160">
                  <template slot-scope="scope">
                    <span v-if="scope.row.expiryTime">
                      {{ formatTime(scope.row.expiryTime) }}
                      <el-tag v-if="isExpiringSoon(scope.row.expiryTime)" size="mini" type="warning">即将过期</el-tag>
                    </span>
                    <span v-else class="text-muted">永久</span>
                  </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="160">
                  <template slot-scope="scope">
                    {{ formatTime(scope.row.createTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="250">
                  <template slot-scope="scope">
                    <el-button size="mini" @click="editItem(scope.row, 'blacklist')">编辑</el-button>
                    <el-button
                      size="mini"
                      :type="scope.row.status === 1 ? 'warning' : 'success'"
                      @click="toggleStatus(scope.row, 'blacklist')"
                    >
                      {{ scope.row.status === 1 ? '禁用' : '启用' }}
                    </el-button>
                    <el-button size="mini" type="danger" @click="deleteItem(scope.row, 'blacklist')">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 黑名单分页 -->
              <div class="pagination-wrapper">
                <el-pagination
                  @size-change="handleBlacklistSizeChange"
                  @current-change="handleBlacklistCurrentChange"
                  :current-page="blacklistPagination.current"
                  :page-sizes="[10, 20, 50, 100]"
                  :page-size="blacklistPagination.size"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="blacklistPagination.total"
                >
                </el-pagination>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 新增/编辑对话框 -->
        <el-dialog
          :title="getDialogTitle()"
          :visible.sync="dialogVisible"
          width="600px"
          :close-on-click-modal="false"
        >
          <el-form
            ref="itemForm"
            :model="itemForm"
            :rules="computedFormRules"
            label-width="120px"
          >
            <el-form-item label="IP类型" prop="ipType">
              <el-radio-group v-model="itemForm.ipType">
                <el-radio label="SINGLE">单个IP</el-radio>
                <el-radio label="RANGE">IP范围</el-radio>
                <el-radio label="CIDR">CIDR网段</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="IP地址" prop="ipValue">
              <el-input
                v-model="itemForm.ipValue"
                :placeholder="getIpPlaceholder()"
              />
              <div class="form-tip">
                <span v-if="itemForm.ipType === 'SINGLE'">示例：*************</span>
                <span v-else-if="itemForm.ipType === 'RANGE'">示例：***********-*************</span>
                <span v-else-if="itemForm.ipType === 'CIDR'">示例：***********/24</span>
              </div>
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input
                v-model="itemForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入描述信息"
              />
            </el-form-item>
            <el-form-item label="生效时间">
              <el-date-picker
                v-model="itemForm.effectiveTime"
                type="datetime"
                placeholder="选择生效时间"
                style="width: 100%"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="过期时间">
              <el-date-picker
                v-model="itemForm.expiryTime"
                type="datetime"
                placeholder="选择过期时间（不选择表示永久有效）"
                style="width: 100%"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-switch
                v-model="itemForm.status"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" :loading="submitting" @click="submitForm">
              {{ isEdit ? '更新' : '创建' }}
            </el-button>
          </div>
        </el-dialog>
      </div>
    </layout>
  </div>
</template>

<script>
import Layout from '@/components/Layout.vue'
import { ipBlackWhiteListApi } from '@/api/ipBlackWhiteList'
import validationRules from '@/utils/validation'

export default {
  name: 'IpBlackWhiteList',
  components: {
    Layout
  },
  beforeCreate() {
    // 注册验证规则到Vue实例
    this.$validation = validationRules;
  },
  data() {
    return {
      activeTab: 'whitelist',
      dialogVisible: false,
      isEdit: false,
      submitting: false,
      currentListType: 'whitelist',
      
      // 白名单数据
      whitelistData: [],
      whitelistLoading: false,
      whitelistSearch: {
        ipType: '',
        keyword: ''
      },
      whitelistPagination: {
        current: 1,
        size: 20,
        total: 0
      },
      
      // 黑名单数据
      blacklistData: [],
      blacklistLoading: false,
      blacklistSearch: {
        ipType: '',
        keyword: ''
      },
      blacklistPagination: {
        current: 1,
        size: 20,
        total: 0
      },
      
      // 表单数据
      itemForm: {
        ipType: 'SINGLE',
        ipValue: '',
        description: '',
        effectiveTime: null,
        expiryTime: null,
        status: 1
      },
      
      formRules: {}
    }
  },
  computed: {
    // 动态计算表单验证规则
    computedFormRules() {
      const currentListType = this.activeTab === 'whitelist' ? 'whitelist' : 'blacklist';
      const rules = currentListType === 'whitelist' 
        ? this.$validation.ipWhitelistRules 
        : this.$validation.ipBlacklistRules;
      
      const formRules = {
        ipType: rules.ipType,
        ipValue: rules.singleIp, // 默认使用单IP验证
        description: rules.description
      };
      
      // 根据IP类型动态调整验证规则
      if (this.itemForm.ipType === 'SINGLE') {
        formRules.ipValue = rules.singleIp;
      } else if (this.itemForm.ipType === 'RANGE') {
        formRules.ipValue = rules.startIp; // IP范围验证可以复用startIp规则
      } else if (this.itemForm.ipType === 'CIDR') {
        formRules.ipValue = rules.cidrIp;
      }
      
      return formRules;
     }
   },
  mounted() {
    this.loadWhitelistData()
  },
  methods: {
    handleTabClick(tab) {
      if (tab.name === 'whitelist') {
        this.loadWhitelistData()
      } else if (tab.name === 'blacklist') {
        this.loadBlacklistData()
      }
    },
    
    // 白名单相关方法
    async loadWhitelistData() {
      this.whitelistLoading = true
      try {
        const params = {
          current: this.whitelistPagination.current,
          size: this.whitelistPagination.size,
          listType: 'WHITELIST',
          ipType: this.whitelistSearch.ipType,
          keyword: this.whitelistSearch.keyword
        }
        const response = await ipBlackWhiteListApi.getWhiteList(params)
        this.whitelistData = response.data.records || []
        this.whitelistPagination.total = response.data.total || 0
      } catch (error) {
        this.$message.error('加载白名单数据失败：' + error.message)
      } finally {
        this.whitelistLoading = false
      }
    },
    handleWhitelistSearch() {
      this.whitelistPagination.current = 1
      this.loadWhitelistData()
    },
    handleWhitelistSizeChange(size) {
      this.whitelistPagination.size = size
      this.whitelistPagination.current = 1
      this.loadWhitelistData()
    },
    handleWhitelistCurrentChange(current) {
      this.whitelistPagination.current = current
      this.loadWhitelistData()
    },
    
    // 黑名单相关方法
    async loadBlacklistData() {
      this.blacklistLoading = true
      try {
        const params = {
          current: this.blacklistPagination.current,
          size: this.blacklistPagination.size,
          listType: 'BLACKLIST',
          ipType: this.blacklistSearch.ipType,
          keyword: this.blacklistSearch.keyword
        }
        const response = await ipBlackWhiteListApi.getBlackList(params)
        this.blacklistData = response.data.records || []
        this.blacklistPagination.total = response.data.total || 0
      } catch (error) {
        this.$message.error('加载黑名单数据失败：' + error.message)
      } finally {
        this.blacklistLoading = false
      }
    },
    handleBlacklistSearch() {
      this.blacklistPagination.current = 1
      this.loadBlacklistData()
    },
    handleBlacklistSizeChange(size) {
      this.blacklistPagination.size = size
      this.blacklistPagination.current = 1
      this.loadBlacklistData()
    },
    handleBlacklistCurrentChange(current) {
      this.blacklistPagination.current = current
      this.loadBlacklistData()
    },
    
    // 通用操作方法
    showAddDialog(listType) {
      this.isEdit = false
      this.currentListType = listType
      this.resetForm()
      this.dialogVisible = true
    },
    editItem(row, listType) {
      this.isEdit = true
      this.currentListType = listType
      this.itemForm = { ...row }
      this.dialogVisible = true
    },
    async toggleStatus(row, listType) {
      try {
        const newStatus = row.status === 1 ? 0 : 1
        if (listType === 'whitelist') {
          await ipBlackWhiteListApi.updateWhiteStatus(row.id, newStatus)
        } else {
          await ipBlackWhiteListApi.updateBlackStatus(row.id, newStatus)
        }
        this.$message.success('状态更新成功')
        if (listType === 'whitelist') {
          this.loadWhitelistData()
        } else {
          this.loadBlacklistData()
        }
      } catch (error) {
        this.$message.error('状态更新失败：' + error.message)
      }
    },
    async deleteItem(row, listType) {
      try {
        const listTypeName = listType === 'whitelist' ? '白名单' : '黑名单'
        await this.$confirm(`确定要删除这条${listTypeName}记录吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        if (listType === 'whitelist') {
          await ipBlackWhiteListApi.deleteWhite(row.id)
        } else {
          await ipBlackWhiteListApi.deleteBlack(row.id)
        }
        this.$message.success('删除成功')
        if (listType === 'whitelist') {
          this.loadWhitelistData()
        } else {
          this.loadBlacklistData()
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败：' + error.message)
        }
      }
    },
    async submitForm() {
      try {
        await this.$refs.itemForm.validate()
        this.submitting = true
        
        const formData = {
          ...this.itemForm,
          listType: this.currentListType === 'whitelist' ? 'WHITELIST' : 'BLACKLIST'
        }
        
        if (this.isEdit) {
          if (this.currentListType === 'whitelist') {
            await ipBlackWhiteListApi.updateWhite(formData.id, formData)
          } else {
            await ipBlackWhiteListApi.updateBlack(formData.id, formData)
          }
          this.$message.success('更新成功')
        } else {
          if (this.currentListType === 'whitelist') {
            await ipBlackWhiteListApi.createWhite(formData)
          } else {
            await ipBlackWhiteListApi.createBlack(formData)
          }
          this.$message.success('创建成功')
        }
        
        this.dialogVisible = false
        if (this.currentListType === 'whitelist') {
          this.loadWhitelistData()
        } else {
          this.loadBlacklistData()
        }
      } catch (error) {
        if (error.message) {
          this.$message.error('操作失败：' + error.message)
        }
      } finally {
        this.submitting = false
      }
    },
    resetForm() {
      this.itemForm = {
        ipType: 'SINGLE',
        ipValue: '',
        description: '',
        effectiveTime: null,
        expiryTime: null,
        status: 1
      }
      if (this.$refs.itemForm) {
        this.$refs.itemForm.clearValidate()
      }
    },
    
    // 工具方法
    getDialogTitle() {
      const listTypeName = this.currentListType === 'whitelist' ? '白名单' : '黑名单'
      return this.isEdit ? `编辑${listTypeName}` : `新增${listTypeName}`
    },
    getIpPlaceholder() {
      const placeholders = {
        'SINGLE': '请输入单个IP地址，如：*************',
        'RANGE': '请输入IP范围，如：***********-*************',
        'CIDR': '请输入CIDR网段，如：***********/24'
      }
      return placeholders[this.itemForm.ipType] || '请输入IP地址'
    },
    getIpTypeText(ipType) {
      const map = {
        'SINGLE': '单IP',
        'RANGE': '范围',
        'CIDR': 'CIDR'
      }
      return map[ipType] || '未知'
    },
    validateIpValue(rule, value, callback) {
      if (!value) {
        callback(new Error('请输入IP地址'))
        return
      }
      
      const ipType = this.itemForm.ipType
      const ipRegex = {
        'SINGLE': /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
        'RANGE': /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)-((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
        'CIDR': /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)\/([0-9]|[1-2][0-9]|3[0-2])$/
      }
      
      if (!ipRegex[ipType] || !ipRegex[ipType].test(value)) {
        callback(new Error('IP地址格式不正确'))
        return
      }
      
      callback()
    },
    formatTime(timestamp) {
      if (!timestamp) return '-'
      // 统一使用 YYYY-MM-DD HH:mm:ss 格式
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    isExpiringSoon(expiryTime) {
      if (!expiryTime) return false
      const now = new Date().getTime()
      const expiry = new Date(expiryTime).getTime()
      const threeDays = 3 * 24 * 60 * 60 * 1000
      return expiry - now <= threeDays && expiry > now
    }
  }
}
</script>

<style scoped>
.ip-blackwhite-list {
  height: 100%;
}

.page-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  width: 100%;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.text-muted {
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

@media (max-width: 768px) {
  .operation-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    flex-direction: column;
    width: 100%;
  }
  
  .filter-group .el-select,
  .filter-group .el-input {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 10px;
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>