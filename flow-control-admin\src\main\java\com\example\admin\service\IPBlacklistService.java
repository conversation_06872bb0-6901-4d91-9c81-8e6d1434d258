package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.IPBlacklistDTO;
import com.example.common.entity.IPBlacklist;
import com.example.admin.vo.IPBlacklistVO;

import java.util.List;
import java.util.Map;

/**
 * IP黑名单服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IPBlacklistService extends IService<IPBlacklist> {
    
    /**
     * 分页查询IP黑名单
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param listName 名单名称
     * @param ipType IP类型
     * @param enabled 启用状态
     * @return IP黑名单VO分页结果
     */
    Page<IPBlacklistVO> selectIPBlacklistPage(Page<IPBlacklistVO> page, String tenantId, String listName, 
                                                  String ipType, Integer enabled);
    
    /**
     * 根据ID查询IP黑名单详情
     *
     * @param id 名单ID
     * @return IP黑名单VO
     */
    IPBlacklistVO getIPBlacklistById(Long id);
    
    /**
     * 创建IP黑名单
     *
     * @param ipBlacklistDTO IP黑名单DTO
     * @return 是否成功
     */
    boolean createIPBlacklist(IPBlacklistDTO ipBlacklistDTO);
    
    /**
     * 更新IP黑名单
     *
     * @param id 名单ID
     * @param ipBlacklistDTO IP黑名单DTO
     * @return 是否成功
     */
    boolean updateIPBlacklist(Long id, IPBlacklistDTO ipBlacklistDTO);
    
    /**
     * 删除IP黑名单
     *
     * @param id 名单ID
     * @return 是否成功
     */
    boolean deleteIPBlacklist(Long id);
    
    /**
     * 批量删除IP黑名单
     *
     * @param ids 名单ID列表
     * @return 是否成功
     */
    boolean batchDeleteIPBlacklists(List<Long> ids);
    
    /**
     * 启用IP黑名单
     *
     * @param id 名单ID
     * @return 是否成功
     */
    boolean enableIPBlacklist(Long id);
    
    /**
     * 禁用IP黑名单
     *
     * @param id 名单ID
     * @return 是否成功
     */
    boolean disableIPBlacklist(Long id);
    
    /**
     * 批量更新名单状态
     *
     * @param ids 名单ID列表
     * @param enabled 启用状态
     * @return 是否成功
     */
    boolean batchUpdateEnabled(List<Long> ids, Integer enabled);
    
    /**
     * 根据租户ID查询IP黑名单
     *
     * @param tenantId 租户ID
     * @param enabled 启用状态（可选）
     * @param limit 限制数量
     * @return IP黑名单列表
     */
    List<IPBlacklistVO> getIPBlacklistsByTenantId(String tenantId, Integer enabled, Integer limit);
    
    /**
     * 检查名单名称是否存在
     *
     * @param tenantId 租户ID
     * @param listName 名单名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByListName(String tenantId, String listName, Long excludeId);
    
    /**
     * 统计租户IP黑名单数量
     *
     * @param tenantId 租户ID
     * @param enabled 启用状态（可选）
     * @return 名单数量
     */
    int countByTenantId(String tenantId, Integer enabled);
    
    /**
     * 查询启用的黑名单（按优先级排序）
     *
     * @param tenantId 租户ID（可选）
     * @param limit 限制数量
     * @return 启用的黑名单列表
     */
    List<IPBlacklistVO> getEnabledBlacklists(String tenantId, Integer limit);
    
    /**
     * 查询优先级排序的黑名单
     *
     * @param tenantId 租户ID（可选）
     * @param enabled 启用状态（可选）
     * @param limit 限制数量
     * @return 优先级排序的黑名单列表
     */
    List<IPBlacklistVO> getBlacklistsByPriorityOrder(String tenantId, Integer enabled, Integer limit);
    
    /**
     * 统计名单状态分布
     *
     * @param tenantId 租户ID（可选）
     * @return 状态统计结果
     */
    List<Map<String, Object>> getEnabledStatistics(String tenantId);
    
    /**
     * 统计IP类型分布
     *
     * @param tenantId 租户ID（可选）
     * @return IP类型统计结果
     */
    List<Map<String, Object>> getIPTypeStatistics(String tenantId);
    
    /**
     * 统计租户IP黑名单分布
     *
     * @param limit 限制数量
     * @return 租户IP黑名单统计结果
     */
    List<Map<String, Object>> getTenantIPStatistics(Integer limit);
    
    /**
     * 批量创建IP黑名单
     *
     * @param ipBlacklistDTOList IP黑名单DTO列表
     * @return 是否成功
     */
    boolean batchCreateIPBlacklists(List<IPBlacklistDTO> ipBlacklistDTOList);
    
    /**
     * 复制IP黑名单
     *
     * @param id 原名单ID
     * @param newListName 新名单名称
     * @param targetTenantId 目标租户ID（可选，为空则复制到同一租户）
     * @return 是否成功
     */
    boolean copyIPBlacklist(Long id, String newListName, String targetTenantId);
    
    /**
     * 导入IP黑名单
     *
     * @param ipBlacklistDTOList IP黑名单DTO列表
     * @param overwrite 是否覆盖已存在的名单
     * @return 导入结果
     */
    Map<String, Object> importIPBlacklists(List<IPBlacklistDTO> ipBlacklistDTOList, boolean overwrite);
    
    /**
     * 导出IP黑名单
     *
     * @param tenantId 租户ID（可选）
     * @param enabled 启用状态（可选）
     * @return IP黑名单DTO列表
     */
    List<IPBlacklistDTO> exportIPBlacklists(String tenantId, Integer enabled);
    
    /**
     * 验证IP黑名单配置
     *
     * @param ipBlacklistDTO IP黑名单DTO
     * @return 验证结果
     */
    Map<String, Object> validateIPBlacklist(IPBlacklistDTO ipBlacklistDTO);
    
    /**
     * 获取租户IP黑名单的最大优先级
     *
     * @param tenantId 租户ID
     * @return 最大优先级
     */
    Integer getMaxPriority(String tenantId);
    
    /**
     * 按优先级范围查询黑名单
     *
     * @param tenantId 租户ID
     * @param minPriority 最小优先级
     * @param maxPriority 最大优先级
     * @param enabled 启用状态（可选）
     * @return 黑名单列表
     */
    List<IPBlacklistVO> getBlacklistsByPriorityRange(String tenantId, Integer minPriority, Integer maxPriority, 
                                                         Integer enabled);
    
    /**
     * 查询即将过期的黑名单
     *
     * @param tenantId 租户ID（可选）
     * @param hours 提前小时数
     * @return 即将过期的黑名单列表
     */
    List<IPBlacklistVO> getExpiringBlacklists(String tenantId, Integer hours);
    
    /**
     * 自动禁用过期黑名单
     *
     * @return 禁用的名单数量
     */
    int disableExpiredBlacklists();
    
    /**
     * 批量复制名单到其他租户
     *
     * @param sourceIds 源名单ID列表
     * @param targetTenantId 目标租户ID
     * @param namePrefix 新名单名称前缀
     * @return 复制结果
     */
    Map<String, Object> batchCopyToTenant(List<Long> sourceIds, String targetTenantId, String namePrefix);
    
    /**
     * 获取租户IP黑名单汇总统计
     *
     * @param tenantId 租户ID
     * @return 汇总统计结果
     */
    Map<String, Object> getTenantIPSummary(String tenantId);
    
    /**
     * 获取全局IP黑名单汇总统计
     *
     * @return 全局汇总统计结果
     */
    Map<String, Object> getGlobalIPSummary();
    
    /**
     * 获取当前有效黑名单
     *
     * @param tenantId 租户ID（可选）
     * @return 有效黑名单列表
     */
    List<IPBlacklistVO> getValidBlacklists(String tenantId);
    
    /**
     * 检查IP地址是否匹配黑名单
     *
     * @param ipAddress 待检查的IP地址
     * @param tenantId 租户ID
     * @return 匹配结果
     */
    Map<String, Object> checkIPMatch(String ipAddress, String tenantId);
    
    /**
     * 批量IP地址匹配检查
     *
     * @param tenantId 租户ID
     * @param ipAddresses 待检查的IP地址列表
     * @return 批量匹配结果
     */
    List<Map<String, Object>> batchCheckIPMatch(String tenantId, List<String> ipAddresses);
    
    /**
     * 从文件导入IP地址
     *
     * @param tenantId 租户ID
     * @param listName 名单名称
     * @param fileContent 文件内容
     * @param fileType 文件类型（txt, csv, json等）
     * @return 导入结果
     */
    Map<String, Object> importIPsFromFile(String tenantId, String listName, 
                                         String fileContent, String fileType);
    
    /**
     * 从上传文件导入IP地址
     *
     * @param file 上传的文件
     * @param tenantId 租户ID
     * @param listName 名单名称
     * @param ipType IP类型
     * @param priority 优先级
     * @return 导入结果
     */
    Map<String, Object> importIPsFromFile(org.springframework.web.multipart.MultipartFile file, String tenantId, String listName, 
                                         String ipType, Integer priority);
    
    /**
     * 导出IP地址到文件
     *
     * @param tenantId 租户ID
     * @param enabled 启用状态（可选）
     * @param fileType 文件类型（txt, csv, json等）
     * @return 文件内容
     */
    String exportIPsToFile(String tenantId, Integer enabled, String fileType);
}