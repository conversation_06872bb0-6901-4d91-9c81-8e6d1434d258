<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.example</groupId>
	<artifactId>sentinel-gateway-flow-control</artifactId>
	<version>1.0.0</version>
	<packaging>pom</packaging>
	<name>Sentinel Gateway Flow Control</name>
	<description>基于Sentinel的Spring Gateway流量控制系统</description>

	<modules>
		<module>flow-control-common</module>
		<module>gateway-service</module>
		<module>flow-control-admin</module>
	</modules>

	<properties>
		<maven.compiler.source>21</maven.compiler.source>
		<maven.compiler.target>21</maven.compiler.target>
		<maven.compiler.proc>full</maven.compiler.proc>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<spring-boot.version>3.2.1</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.1.0</spring-cloud-alibaba.version>
		<sentinel.version>1.8.6</sentinel.version>
		<nacos.version>2.2.4</nacos.version>
		<mybatis-plus.version>3.5.5</mybatis-plus.version>
		<mysql.version>8.0.33</mysql.version>

		<fastjson.version>2.0.40</fastjson.version>
		<druid.version>1.2.18</druid.version>
		<hutool.version>5.8.22</hutool.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- Spring Boot -->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!-- Spring Cloud -->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!-- Spring Cloud Alibaba -->
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>${spring-cloud-alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!-- Sentinel -->
			<dependency>
				<groupId>com.alibaba.csp</groupId>
				<artifactId>sentinel-core</artifactId>
				<version>${sentinel.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.csp</groupId>
				<artifactId>sentinel-spring-cloud-gateway-adapter</artifactId>
				<version>${sentinel.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.csp</groupId>
				<artifactId>sentinel-datasource-nacos</artifactId>
				<version>${sentinel.version}</version>
			</dependency>

			<!-- MyBatis Plus for Spring Boot 3 -->
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
				<version>${mybatis-plus.version}</version>
			</dependency>

			<!-- MySQL -->
			<dependency>
				<groupId>com.mysql</groupId>
				<artifactId>mysql-connector-j</artifactId>
				<version>${mysql.version}</version>
			</dependency>



			<!-- FastJSON -->
			<dependency>
				<groupId>com.alibaba.fastjson2</groupId>
				<artifactId>fastjson2</artifactId>
				<version>${fastjson.version}</version>
			</dependency>

			<!-- Druid -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid-spring-boot-starter</artifactId>
				<version>${druid.version}</version>
			</dependency>

			<!-- Hutool -->
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>${hutool.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-maven-plugin</artifactId>
					<version>${spring-boot.version}</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.11.0</version>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>21</source>
					<target>21</target>
					<encoding>UTF-8</encoding>
					<compilerArgs>
						<arg>-parameters</arg>
						<arg>--add-opens</arg>
						<arg>jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>
					</compilerArgs>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>