server:
  port: 8088

spring:
  application:
    name: gateway-service
  datasource:
    url: *********************************************************************************************************************************************************************
    username: root
    password: aids520a
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: GatewayHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  # JPA配置已移除，使用MyBatis-Plus
  cloud:
    sentinel:
      filter:
        enabled: false  # 禁用Sentinel自动配置的过滤器，使用自定义的MultiDimensionFlowFilter
    gateway:
      routes:
        # 流量控制管理后台路由
        - id: flow-control-admin
          uri: http://localhost:8081
          predicates:
            - Path=/api/**
          filters:
            - StripPrefix=0
        # 测试接口路由
        - id: test-route
          uri: http://localhost:8081
          predicates:
            - Path=/api/test
          filters:
            - StripPrefix=0
        # 管理员用户接口路由
        - id: admin-users-route
          uri: http://localhost:8081
          predicates:
            - Path=/api/admin/users
          filters:
            - StripPrefix=0
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

# Sentinel配置
sentinel:
  transport:
    dashboard: ${SENTINEL_DASHBOARD:localhost:8858}
    port: 8719

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,sentinel
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  charset:
    console: UTF-8
    file: UTF-8
  level:
    com.example.gateway: DEBUG
    com.example.gateway.util.RequestContextExtractor: DEBUG
    com.alibaba.csp.sentinel: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId}] %logger{36} - %msg%n"
  file:
    name: logs/gateway-service.log
    max-size: 100MB
    max-history: 30

# 自定义配置
