<template>
	<div class="ip-management">
		<layout>
			<div class="ip-management-content">
				<div class="page-header">
					<h1>IP管理</h1>
					<p>统一管理IP白名单、黑名单和流量规则</p>
				</div>

				<!-- 标签页 -->
				<el-tabs v-model="activeTab" @tab-click="handleTabClick">
					<!-- IP白名单 -->
					<el-tab-pane label="IP白名单" name="whitelist">
						<ip-whitelist-tab />
					</el-tab-pane>

					<!-- IP黑名单 -->
					<el-tab-pane label="IP黑名单" name="blacklist">
						<ip-blacklist-tab />
					</el-tab-pane>

					<!-- IP流量规则 -->
					<el-tab-pane label="IP流量规则" name="flowrules">
						<ip-flow-rules-tab />
					</el-tab-pane>
				</el-tabs>
			</div>
		</layout>
	</div>
</template>

<script>
import Layout from '@/components/Layout.vue'
import IpWhitelistTab from '@/components/IpWhitelistTab.vue'
import IpBlacklistTab from '@/components/IpBlacklistTab.vue'
import IpFlowRulesTab from '@/components/IpFlowRulesTab.vue'

export default {
	name: 'IpManagement',
	components: {
		Layout,
		IpWhitelistTab,
		IpBlacklistTab,
		IpFlowRulesTab
	},
	data() {
		return {
			activeTab: 'whitelist'
		}
	},
	methods: {
		handleTabClick(tab) {
			this.activeTab = tab.name
		}
	}
}
</script>

<style scoped>
.ip-management {
	padding: 20px;
}

.page-header {
	margin-bottom: 20px;
}

.page-header h1 {
	margin: 0 0 10px 0;
	font-size: 24px;
	color: #303133;
}

.page-header p {
	margin: 0;
	color: #606266;
	font-size: 14px;
}
</style>