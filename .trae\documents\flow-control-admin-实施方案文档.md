# Sentinel流量控制系统实施方案文档

## 1. 项目结构设计

### 1.1 Maven模块结构

```
flow-control-admin/
├── src/main/java/com/example/admin/
│   ├── AdminApplication.java                 # 主启动类
│   ├── config/                              # 配置类
│   │   ├── RedisConfig.java                 # Redis配置
│   │   ├── MybatisPlusConfig.java           # MyBatis Plus配置
│   │   ├── SentinelConfig.java              # Sentinel配置
│   │   └── WebConfig.java                   # Web配置
│   ├── controller/                          # 控制器层
│   │   ├── FlowRuleController.java          # 流量规则控制器
│   │   ├── IPFlowRuleController.java        # IP规则控制器
│   │   ├── MonitorController.java           # 监控控制器
│   │   ├── TenantController.java            # 租户控制器
│   │   └── SystemConfigController.java      # 系统配置控制器
│   ├── entity/                              # 实体类
│   │   ├── FlowRule.java                    # 流量规则实体
│   │   ├── IPFlowRule.java                  # IP规则实体
│   │   ├── TenantInfo.java                  # 租户信息实体
│   │   ├── MonitorStatisticsDaily.java      # 日统计实体
│   │   ├── MonitorStatisticsHourly.java     # 小时统计实体
│   │   ├── FlowControlLog.java              # 流控日志实体
│   │   └── SystemConfig.java               # 系统配置实体
│   ├── dto/                                 # 数据传输对象
│   │   ├── FlowRuleDTO.java                 # 流量规则DTO
│   │   ├── IPFlowRuleDTO.java               # IP规则DTO
│   │   ├── MonitorDataDTO.java              # 监控数据DTO
│   │   └── StatisticsDTO.java               # 统计数据DTO
│   ├── vo/                                  # 视图对象
│   │   ├── FlowRuleVO.java                  # 流量规则VO
│   │   ├── MonitorVO.java                   # 监控数据VO
│   │   └── DashboardVO.java                 # 仪表盘VO
│   ├── mapper/                              # 数据访问层
│   │   ├── FlowRuleMapper.java              # 流量规则Mapper
│   │   ├── IPFlowRuleMapper.java            # IP规则Mapper
│   │   ├── TenantInfoMapper.java            # 租户信息Mapper
│   │   ├── MonitorStatisticsMapper.java     # 监控统计Mapper
│   │   └── FlowControlLogMapper.java        # 流控日志Mapper
│   ├── service/                             # 服务接口
│   │   ├── FlowRuleService.java             # 流量规则服务接口
│   │   ├── IPFlowRuleService.java           # IP规则服务接口
│   │   ├── MonitorService.java              # 监控服务接口
│   │   ├── TenantService.java               # 租户服务接口
│   │   └── SystemConfigService.java        # 系统配置服务接口
│   ├── service/impl/                        # 服务实现
│   │   ├── FlowRuleServiceImpl.java         # 流量规则服务实现
│   │   ├── IPFlowRuleServiceImpl.java       # IP规则服务实现
│   │   ├── MonitorServiceImpl.java          # 监控服务实现
│   │   ├── TenantServiceImpl.java           # 租户服务实现
│   │   └── SystemConfigServiceImpl.java    # 系统配置服务实现
│   ├── common/                              # 公共类
│   │   ├── Result.java                      # 统一返回结果
│   │   ├── PageResult.java                  # 分页结果
│   │   ├── Constants.java                   # 常量定义
│   │   └── enums/                           # 枚举类
│   │       ├── LimitModeEnum.java           # 限流模式枚举
│   │       ├── StrategyEnum.java            # 流控策略枚举
│   │       ├── BehaviorEnum.java            # 流控行为枚举
│   │       └── IPRuleTypeEnum.java          # IP规则类型枚举
│   └── util/                                # 工具类
│       ├── IPUtils.java                     # IP工具类
│       ├── RedisUtils.java                  # Redis工具类
│       └── SentinelUtils.java               # Sentinel工具类
└── src/main/resources/
    ├── application.yml                      # 应用配置
    ├── mapper/                              # MyBatis映射文件
    │   ├── FlowRuleMapper.xml
    │   ├── IPFlowRuleMapper.xml
    │   ├── TenantInfoMapper.xml
    │   ├── MonitorStatisticsMapper.xml
    │   └── FlowControlLogMapper.xml
    └── static/                              # 静态资源
```

## 2. 核心实体类设计

### 2.1 FlowRule.java - 流量规则实体

```java
package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 流量控制规则实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("flow_rule")
public class FlowRule {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;
    
    /**
     * 资源名称
     */
    @TableField("resource_name")
    private String resourceName;
    
    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
    
    /**
     * 限流模式：0-QPS，1-线程数
     */
    @TableField("limit_mode")
    private Integer limitMode;
    
    /**
     * 阈值
     */
    @TableField("threshold")
    private Integer threshold;
    
    /**
     * 流控策略：0-直接，1-关联，2-链路
     */
    @TableField("strategy")
    private Integer strategy;
    
    /**
     * 关联资源
     */
    @TableField("related_resource")
    private String relatedResource;
    
    /**
     * 流控行为：0-快速失败，1-预热，2-排队等待
     */
    @TableField("behavior")
    private Integer behavior;
    
    /**
     * 预热时长（秒）
     */
    @TableField("warm_up_period")
    private Integer warmUpPeriod;
    
    /**
     * 排队超时时间（毫秒）
     */
    @TableField("queue_timeout")
    private Integer queueTimeout;
    
    /**
     * 是否集群模式：0-否，1-是
     */
    @TableField("cluster_mode")
    private Integer clusterMode;
    
    /**
     * 规则状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;
    
    /**
     * 规则描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;
    
    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;
    
    /**
     * 删除标志：0-未删除，1-已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
}
```

### 2.2 IPFlowRule.java - IP规则实体

```java
package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * IP流量控制规则实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ip_flow_rule")
public class IPFlowRule {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;
    
    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
    
    /**
     * 规则类型：SINGLE_IP-单个IP，IP_RANGE-IP范围，CIDR-IP段
     */
    @TableField("rule_type")
    private String ruleType;
    
    /**
     * IP值
     */
    @TableField("ip_value")
    private String ipValue;
    
    /**
     * 列表类型：WHITELIST-白名单，BLACKLIST-黑名单
     */
    @TableField("list_type")
    private String listType;
    
    /**
     * QPS限制
     */
    @TableField("qps_limit")
    private Integer qpsLimit;
    
    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;
    
    /**
     * 规则描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 规则状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;
}
```

### 2.3 TenantInfo.java - 租户信息实体

```java
package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 租户信息实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tenant_info")
public class TenantInfo {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
    
    /**
     * 租户名称
     */
    @TableField("tenant_name")
    private String tenantName;
    
    /**
     * 租户描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 联系人
     */
    @TableField("contact_person")
    private String contactPerson;
    
    /**
     * 联系电话
     */
    @TableField("contact_phone")
    private String contactPhone;
    
    /**
     * 联系邮箱
     */
    @TableField("contact_email")
    private String contactEmail;
    
    /**
     * 状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;
    
    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;
}
```

## 3. 核心服务层设计

### 3.1 FlowRuleService.java - 流量规则服务接口

```java
package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.FlowRuleDTO;
import com.example.admin.entity.FlowRule;
import com.example.admin.vo.FlowRuleVO;

import java.util.List;

/**
 * 流量规则服务接口
 */
public interface FlowRuleService extends IService<FlowRule> {
    
    /**
     * 分页查询流量规则
     */
    Page<FlowRuleVO> pageFlowRules(int current, int size, String tenantId, String resourceName, Integer status);
    
    /**
     * 创建流量规则
     */
    boolean createFlowRule(FlowRuleDTO flowRuleDTO);
    
    /**
     * 更新流量规则
     */
    boolean updateFlowRule(Long id, FlowRuleDTO flowRuleDTO);
    
    /**
     * 删除流量规则
     */
    boolean deleteFlowRule(Long id);
    
    /**
     * 批量删除流量规则
     */
    boolean batchDeleteFlowRules(List<Long> ids);
    
    /**
     * 启用/禁用流量规则
     */
    boolean toggleFlowRuleStatus(Long id, Integer status);
    
    /**
     * 批量启用/禁用流量规则
     */
    boolean batchToggleFlowRuleStatus(List<Long> ids, Integer status);
    
    /**
     * 根据租户ID获取流量规则
     */
    List<FlowRule> getFlowRulesByTenantId(String tenantId);
    
    /**
     * 同步规则到Sentinel
     */
    boolean syncRulesToSentinel(String tenantId);
    
    /**
     * 导入流量规则
     */
    boolean importFlowRules(List<FlowRuleDTO> flowRuleDTOs);
    
    /**
     * 导出流量规则
     */
    List<FlowRuleVO> exportFlowRules(String tenantId);
}
```

### 3.2 FlowRuleServiceImpl.java - 流量规则服务实现

```java
package com.example.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.dto.FlowRuleDTO;
import com.example.admin.entity.FlowRule;
import com.example.admin.mapper.FlowRuleMapper;
import com.example.admin.service.FlowRuleService;
import com.example.admin.util.SentinelUtils;
import com.example.admin.vo.FlowRuleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 流量规则服务实现
 */
@Slf4j
@Service
public class FlowRuleServiceImpl extends ServiceImpl<FlowRuleMapper, FlowRule> implements FlowRuleService {
    
    @Override
    public Page<FlowRuleVO> pageFlowRules(int current, int size, String tenantId, String resourceName, Integer status) {
        LambdaQueryWrapper<FlowRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.hasText(tenantId), FlowRule::getTenantId, tenantId)
                   .like(StringUtils.hasText(resourceName), FlowRule::getResourceName, resourceName)
                   .eq(status != null, FlowRule::getStatus, status)
                   .orderByDesc(FlowRule::getCreateTime);
        
        Page<FlowRule> page = new Page<>(current, size);
        Page<FlowRule> flowRulePage = this.page(page, queryWrapper);
        
        // 转换为VO
        Page<FlowRuleVO> voPage = new Page<>();
        BeanUtils.copyProperties(flowRulePage, voPage);
        List<FlowRuleVO> voList = flowRulePage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        voPage.setRecords(voList);
        
        return voPage;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createFlowRule(FlowRuleDTO flowRuleDTO) {
        try {
            // 检查规则名称是否重复
            LambdaQueryWrapper<FlowRule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FlowRule::getRuleName, flowRuleDTO.getRuleName());
            if (this.count(queryWrapper) > 0) {
                throw new RuntimeException("规则名称已存在");
            }
            
            FlowRule flowRule = new FlowRule();
            BeanUtils.copyProperties(flowRuleDTO, flowRule);
            
            boolean result = this.save(flowRule);
            if (result) {
                // 同步到Sentinel
                syncRulesToSentinel(flowRule.getTenantId());
            }
            
            return result;
        } catch (Exception e) {
            log.error("创建流量规则失败", e);
            throw new RuntimeException("创建流量规则失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFlowRule(Long id, FlowRuleDTO flowRuleDTO) {
        try {
            FlowRule existingRule = this.getById(id);
            if (existingRule == null) {
                throw new RuntimeException("流量规则不存在");
            }
            
            // 检查规则名称是否重复（排除当前规则）
            LambdaQueryWrapper<FlowRule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FlowRule::getRuleName, flowRuleDTO.getRuleName())
                       .ne(FlowRule::getId, id);
            if (this.count(queryWrapper) > 0) {
                throw new RuntimeException("规则名称已存在");
            }
            
            BeanUtils.copyProperties(flowRuleDTO, existingRule);
            existingRule.setId(id);
            
            boolean result = this.updateById(existingRule);
            if (result) {
                // 同步到Sentinel
                syncRulesToSentinel(existingRule.getTenantId());
            }
            
            return result;
        } catch (Exception e) {
            log.error("更新流量规则失败", e);
            throw new RuntimeException("更新流量规则失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFlowRule(Long id) {
        try {
            FlowRule flowRule = this.getById(id);
            if (flowRule == null) {
                throw new RuntimeException("流量规则不存在");
            }
            
            boolean result = this.removeById(id);
            if (result) {
                // 同步到Sentinel
                syncRulesToSentinel(flowRule.getTenantId());
            }
            
            return result;
        } catch (Exception e) {
            log.error("删除流量规则失败", e);
            throw new RuntimeException("删除流量规则失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteFlowRules(List<Long> ids) {
        try {
            List<FlowRule> flowRules = this.listByIds(ids);
            boolean result = this.removeByIds(ids);
            
            if (result) {
                // 同步到Sentinel（按租户分组同步）
                flowRules.stream()
                        .map(FlowRule::getTenantId)
                        .distinct()
                        .forEach(this::syncRulesToSentinel);
            }
            
            return result;
        } catch (Exception e) {
            log.error("批量删除流量规则失败", e);
            throw new RuntimeException("批量删除流量规则失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleFlowRuleStatus(Long id, Integer status) {
        try {
            FlowRule flowRule = this.getById(id);
            if (flowRule == null) {
                throw new RuntimeException("流量规则不存在");
            }
            
            flowRule.setStatus(status);
            boolean result = this.updateById(flowRule);
            
            if (result) {
                // 同步到Sentinel
                syncRulesToSentinel(flowRule.getTenantId());
            }
            
            return result;
        } catch (Exception e) {
            log.error("切换流量规则状态失败", e);
            throw new RuntimeException("切换流量规则状态失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchToggleFlowRuleStatus(List<Long> ids, Integer status) {
        try {
            List<FlowRule> flowRules = this.listByIds(ids);
            flowRules.forEach(rule -> rule.setStatus(status));
            
            boolean result = this.updateBatchById(flowRules);
            
            if (result) {
                // 同步到Sentinel（按租户分组同步）
                flowRules.stream()
                        .map(FlowRule::getTenantId)
                        .distinct()
                        .forEach(this::syncRulesToSentinel);
            }
            
            return result;
        } catch (Exception e) {
            log.error("批量切换流量规则状态失败", e);
            throw new RuntimeException("批量切换流量规则状态失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<FlowRule> getFlowRulesByTenantId(String tenantId) {
        LambdaQueryWrapper<FlowRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowRule::getTenantId, tenantId)
                   .eq(FlowRule::getStatus, 1)
                   .orderByAsc(FlowRule::getPriority);
        return this.list(queryWrapper);
    }
    
    @Override
    public boolean syncRulesToSentinel(String tenantId) {
        try {
            List<FlowRule> flowRules = getFlowRulesByTenantId(tenantId);
            return SentinelUtils.syncFlowRules(tenantId, flowRules);
        } catch (Exception e) {
            log.error("同步规则到Sentinel失败: tenantId={}", tenantId, e);
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean importFlowRules(List<FlowRuleDTO> flowRuleDTOs) {
        try {
            List<FlowRule> flowRules = flowRuleDTOs.stream()
                    .map(dto -> {
                        FlowRule flowRule = new FlowRule();
                        BeanUtils.copyProperties(dto, flowRule);
                        return flowRule;
                    })
                    .collect(Collectors.toList());
            
            boolean result = this.saveBatch(flowRules);
            
            if (result) {
                // 同步到Sentinel（按租户分组同步）
                flowRules.stream()
                        .map(FlowRule::getTenantId)
                        .distinct()
                        .forEach(this::syncRulesToSentinel);
            }
            
            return result;
        } catch (Exception e) {
            log.error("导入流量规则失败", e);
            throw new RuntimeException("导入流量规则失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<FlowRuleVO> exportFlowRules(String tenantId) {
        LambdaQueryWrapper<FlowRule> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(tenantId)) {
            queryWrapper.eq(FlowRule::getTenantId, tenantId);
        }
        queryWrapper.orderByDesc(FlowRule::getCreateTime);
        
        List<FlowRule> flowRules = this.list(queryWrapper);
        return flowRules.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    /**
     * 转换为VO对象
     */
    private FlowRuleVO convertToVO(FlowRule flowRule) {
        FlowRuleVO vo = new FlowRuleVO();
        BeanUtils.copyProperties(flowRule, vo);
        
        // 设置枚举描述
        vo.setLimitModeDesc(getLimitModeDesc(flowRule.getLimitMode()));
        vo.setStrategyDesc(getStrategyDesc(flowRule.getStrategy()));
        vo.setBehaviorDesc(getBehaviorDesc(flowRule.getBehavior()));
        vo.setStatusDesc(flowRule.getStatus() == 1 ? "启用" : "禁用");
        
        return vo;
    }
    
    private String getLimitModeDesc(Integer limitMode) {
        return limitMode == 0 ? "QPS" : "线程数";
    }
    
    private String getStrategyDesc(Integer strategy) {
        switch (strategy) {
            case 0: return "直接";
            case 1: return "关联";
            case 2: return "链路";
            default: return "未知";
        }
    }
    
    private String getBehaviorDesc(Integer behavior) {
        switch (behavior) {
            case 0: return "快速失败";
            case 1: return "预热";
            case 2: return "排队等待";
            default: return "未知";
        }
    }
}
```

## 4. 控制器层设计

### 4.1 FlowRuleController.java - 流量规则控制器

```java
package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.Result;
import com.example.admin.dto.FlowRuleDTO;
import com.example.admin.service.FlowRuleService;
import com.example.admin.vo.FlowRuleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 流量规则控制器
 */
@Tag(name = "流量规则管理", description = "流量规则的增删改查操作")
@RestController
@RequestMapping("/api/flow-rules")
@RequiredArgsConstructor
@Validated
public class FlowRuleController {
    
    private final FlowRuleService flowRuleService;
    
    @Operation(summary = "分页查询流量规则")
    @GetMapping
    public Result<Page<FlowRuleVO>> pageFlowRules(
            @Parameter(description = "当前页码") @RequestParam(defaultValue = "1") int current,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "资源名称") @RequestParam(required = false) String resourceName,
            @Parameter(description = "规则状态") @RequestParam(required = false) Integer status) {
        
        Page<FlowRuleVO> page = flowRuleService.pageFlowRules(current, size, tenantId, resourceName, status);
        return Result.success(page);
    }
    
    @Operation(summary = "创建流量规则")
    @PostMapping
    public Result<Boolean> createFlowRule(@Valid @RequestBody FlowRuleDTO flowRuleDTO) {
        boolean result = flowRuleService.createFlowRule(flowRuleDTO);
        return Result.success(result);
    }
    
    @Operation(summary = "更新流量规则")
    @PutMapping("/{id}")
    public Result<Boolean> updateFlowRule(
            @Parameter(description = "规则ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody FlowRuleDTO flowRuleDTO) {
        
        boolean result = flowRuleService.updateFlowRule(id, flowRuleDTO);
        return Result.success(result);
    }
    
    @Operation(summary = "删除流量规则")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteFlowRule(
            @Parameter(description = "规则ID") @PathVariable @NotNull Long id) {
        
        boolean result = flowRuleService.deleteFlowRule(id);
        return Result.success(result);
    }
    
    @Operation(summary = "批量删除流量规则")
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteFlowRules(
            @Parameter(description = "规则ID列表") @RequestBody @NotEmpty List<Long> ids) {
        
        boolean result = flowRuleService.batchDeleteFlowRules(ids);
        return Result.success(result);
    }
    
    @Operation(summary = "启用/禁用流量规则")
    @PutMapping("/{id}/status")
    public Result<Boolean> toggleFlowRuleStatus(
            @Parameter(description = "规则ID") @PathVariable @NotNull Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer status) {
        
        boolean result = flowRuleService.toggleFlowRuleStatus(id, status);
        return Result.success(result);
    }
    
    @Operation(summary = "批量启用/禁用流量规则")
    @PutMapping("/batch/status")
    public Result<Boolean> batchToggleFlowRuleStatus(
            @Parameter(description = "规则ID列表") @RequestParam @NotEmpty List<Long> ids,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer status) {
        
        boolean result = flowRuleService.batchToggleFlowRuleStatus(ids, status);
        return Result.success(result);
    }
    
    @Operation(summary = "同步规则到Sentinel")
    @PostMapping("/sync/{tenantId}")
    public Result<Boolean> syncRulesToSentinel(
            @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId) {
        
        boolean result = flowRuleService.syncRulesToSentinel(tenantId);
        return Result.success(result);
    }
    
    @Operation(summary = "导入流量规则")
    @PostMapping("/import")
    public Result<Boolean> importFlowRules(
            @Parameter(description = "流量规则列表") @RequestBody @Valid List<FlowRuleDTO> flowRuleDTOs) {
        
        boolean result = flowRuleService.importFlowRules(flowRuleDTOs);
        return Result.success(result);
    }
    
    @Operation(summary = "导出流量规则")
    @GetMapping("/export")
    public Result<List<FlowRuleVO>> exportFlowRules(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        
        List<FlowRuleVO> result = flowRuleService.exportFlowRules(tenantId);
        return Result.success(result);
    }
}
```

## 5. 多维度流量控制实现方案

### 5.1 租户级别流量控制

* 基于租户ID进行流量隔离

* 每个租户独立的QPS配额

* 支持租户级别的流控策略配置

### 5.2 接口级别流量控制

* 基于资源名称（URL路径）进行精细化控制

* 支持Ant路径匹配模式

* 可配置不同接口的QPS阈值

### 5.3 IP级别流量控制

* 支持单个IP、IP段(CIDR)、IP范围控制

* 黑白名单机制

* IP地理位置限制

### 5.4 排队等待机制

* 超出QPS限制时进入排队队列

* 可配置排队超时时间

* 支持优先级排队

## 6. 监控与告警功能设计

### 6.1 实时监控数据收集

* 基于Sentinel MetricExtension收集实时数据

* 按租户、资源、时间维度聚合统计

* 存储到Redis缓存和MySQL数据库

### 6.2 历史数据统计

* 按小时和天维度进行数据聚合

* 支持多维度查询和分析

* 提供图表展示和数据导出

### 6.3 告警通知

* 基于规则配置的告警阈值

* 支持邮件、短信、钉钉等通知方式

* 告警静默和恢复机制

## 7. 部署和配置说明

### 7.1 环境要求

* JDK 8+

* MySQL 8.0+

* Redis 6.0+

* Nacos 2.0+

### 7.2 配置文件

```yaml
# application.yml
spring:
  application:
    name: flow-control-admin
  datasource:
    url: *********************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 5000
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 0
        max-wait: -1
  
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: flow-control
      config:
        server-addr: localhost:8848
        namespace: flow-control
        file-extension: yml

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

server:
  port: 8081

logging:
  level:
    com.example.admin: DEBUG
    com.alibaba.csp.sentinel: INFO
```

### 7.3 启动命令

```bash
# 开发环境启动
mvn spring-boot:run

# 生产环境启动
java -jar flow-control-admin-1.0.0.jar --spring.profiles.active=prod
```

