<template>
  <div class="statistics">
    <layout>
      <div class="statistics-content">
        <div class="page-header">
          <h1>统计分析</h1>
          <p>系统流量和性能的历史数据分析</p>
        </div>
        
        <!-- 时间选择器 -->
        <div class="time-selector">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="timestamp"
            @change="onDateRangeChange"
          >
          </el-date-picker>
          <el-button type="primary" @click="refreshData">刷新数据</el-button>
        </div>
        
        <!-- 统计概览 -->
        <el-row :gutter="20" class="overview-row">
          <el-col :span="6">
            <div class="overview-card">
              <div class="overview-icon requests">
                <i class="el-icon-data-line"></i>
              </div>
              <div class="overview-content">
                <h3>{{ formatNumber(totalRequests) }}</h3>
                <p>总请求数</p>
                <span class="overview-change positive">+15.2%</span>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="overview-card">
              <div class="overview-icon avg-qps">
                <i class="el-icon-odometer"></i>
              </div>
              <div class="overview-content">
                <h3>{{ avgQps }}</h3>
                <p>平均QPS</p>
                <span class="overview-change negative">-2.1%</span>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="overview-card">
              <div class="overview-icon avg-rt">
                <i class="el-icon-timer"></i>
              </div>
              <div class="overview-content">
                <h3>{{ avgResponseTime }}ms</h3>
                <p>平均响应时间</p>
                <span class="overview-change positive">-8.5%</span>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="overview-card">
              <div class="overview-icon success-rate">
                <i class="el-icon-success"></i>
              </div>
              <div class="overview-content">
                <h3>{{ successRate }}%</h3>
                <p>成功率</p>
                <span class="overview-change positive">+0.3%</span>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <!-- 图表区域 -->
        <el-row :gutter="20" class="charts-row">
          <el-col :span="24">
            <div class="chart-card">
              <div class="chart-header">
                <h3>流量趋势分析</h3>
                <div class="chart-tabs">
                  <el-radio-group v-model="activeMetric" @change="onMetricChange">
                    <el-radio-button label="qps">QPS</el-radio-button>
                    <el-radio-button label="rt">响应时间</el-radio-button>
                    <el-radio-button label="success_rate">成功率</el-radio-button>
                    <el-radio-button label="error_rate">错误率</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              <div id="trend-chart" class="chart-container"></div>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" class="charts-row">
          <el-col :span="12">
            <div class="chart-card">
              <h3>TOP 10 访问路径</h3>
              <div class="top-paths">
                <div class="path-item" v-for="(path, index) in topPaths" :key="index">
                  <div class="path-rank">{{ index + 1 }}</div>
                  <div class="path-info">
                    <div class="path-name">{{ path.path }}</div>
                    <div class="path-count">{{ formatNumber(path.count) }} 次访问</div>
                  </div>
                  <div class="path-bar">
                    <div class="path-progress" :style="{ width: path.percentage + '%' }"></div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-card">
              <h3>状态码分布</h3>
              <div id="status-pie-chart" class="chart-container"></div>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" class="charts-row">
          <el-col :span="12">
            <div class="chart-card">
              <h3>响应时间分布</h3>
              <div id="rt-histogram-chart" class="chart-container"></div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-card">
              <h3>流量来源分析</h3>
              <div class="traffic-sources">
                <div class="source-item" v-for="(source, index) in trafficSources" :key="index">
                  <div class="source-info">
                    <div class="source-name">{{ source.name }}</div>
                    <div class="source-percentage">{{ source.percentage }}%</div>
                  </div>
                  <div class="source-bar">
                    <div 
                      class="source-progress" 
                      :style="{ width: source.percentage + '%', backgroundColor: source.color }"
                    ></div>
                  </div>
                  <div class="source-count">{{ formatNumber(source.count) }}</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <!-- 详细数据表格 -->
        <div class="data-table">
          <div class="table-header">
            <h3>详细数据</h3>
            <div class="table-controls">
              <el-button size="small" @click="exportData">导出数据</el-button>
            </div>
          </div>
          <el-table
            :data="detailData"
            :loading="tableLoading"
            style="width: 100%"
            stripe
          >
            <el-table-column prop="time" label="时间" width="180">
              <template slot-scope="scope">
                {{ formatTime(scope.row.time) }}
              </template>
            </el-table-column>
            <el-table-column prop="qps" label="QPS" width="100"></el-table-column>
            <el-table-column prop="rt" label="响应时间(ms)" width="120"></el-table-column>
            <el-table-column prop="successRate" label="成功率(%)" width="100">
              <template slot-scope="scope">
                {{ scope.row.successRate.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="errorRate" label="错误率(%)" width="100">
              <template slot-scope="scope">
                {{ scope.row.errorRate.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="totalRequests" label="总请求数" width="120"></el-table-column>
            <el-table-column prop="blockedRequests" label="被阻塞请求" width="120"></el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalCount"
              :page-size-options="[10, 20, 50, 100]"
              :prev-text="$t('pagination.prevPage')"
              :next-text="$t('pagination.nextPage')"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </layout>
  </div>
</template>

<script>
import Layout from '../components/Layout.vue'

export default {
  name: 'Statistics',
  components: {
    Layout
  },
  data() {
    return {
      dateRange: [Date.now() - 24 * 60 * 60 * 1000, Date.now()], // 默认最近24小时
      activeMetric: 'qps',
      tableLoading: false,
      currentPage: 1,
      pageSize: 20,
      totalCount: 1000,
      // 模拟数据
      totalRequests: 1234567,
      avgQps: 856,
      avgResponseTime: 45,
      successRate: 99.2,
      topPaths: [
        { path: '/api/users', count: 125000, percentage: 100 },
        { path: '/api/orders', count: 98000, percentage: 78 },
        { path: '/api/products', count: 76000, percentage: 61 },
        { path: '/api/auth/login', count: 54000, percentage: 43 },
        { path: '/api/dashboard', count: 43000, percentage: 34 },
        { path: '/api/reports', count: 32000, percentage: 26 },
        { path: '/api/settings', count: 28000, percentage: 22 },
        { path: '/api/notifications', count: 21000, percentage: 17 },
        { path: '/api/files', count: 18000, percentage: 14 },
        { path: '/api/logs', count: 15000, percentage: 12 }
      ],
      trafficSources: [
        { name: '移动端', percentage: 45, count: 555555, color: '#409EFF' },
        { name: 'PC端', percentage: 35, count: 432100, color: '#67C23A' },
        { name: 'API调用', percentage: 15, count: 185185, color: '#E6A23C' },
        { name: '其他', percentage: 5, count: 61728, color: '#F56C6C' }
      ],
      detailData: []
    }
  },
  mounted() {
    this.generateMockData()
    this.refreshData()
  },
  methods: {
    onDateRangeChange() {
      this.refreshData()
    },
    onMetricChange() {
      // 切换指标时重新渲染图表
      console.log('切换指标:', this.activeMetric)
    },
    async refreshData() {
      this.tableLoading = true
      try {
        // 调用API获取统计数据
        const params = {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1],
          metric: this.activeMetric
        }
        
        // 获取统计概览数据
        const overviewResponse = await this.$api.statistics.getOverview(params)
        if (overviewResponse.data) {
          // 更新概览数据
          this.updateOverviewData(overviewResponse.data)
        }
        
        // 获取详细统计数据
        const detailResponse = await this.$api.statistics.getDetail(params)
        if (detailResponse.data) {
          this.detailData = detailResponse.data
        } else {
          // 如果API返回空数据，使用模拟数据
          this.generateMockData()
        }
        
        this.$message.success('数据刷新成功')
      } catch (error) {
        console.error('获取统计数据失败:', error)
        // 降级到模拟数据
        this.generateMockData()
        this.$message.warning('使用模拟数据，请检查后端服务')
      } finally {
        this.tableLoading = false
      }
    },
    generateMockData() {
      // 生成模拟的详细数据
      this.detailData = []
      const startTime = this.dateRange[0]
      const endTime = this.dateRange[1]
      const interval = (endTime - startTime) / 100 // 生成100个数据点
      
      for (let i = 0; i < 100; i++) {
        this.detailData.push({
          time: startTime + i * interval,
          qps: Math.floor(Math.random() * 1000) + 500,
          rt: Math.floor(Math.random() * 100) + 20,
          successRate: 95 + Math.random() * 5,
          errorRate: Math.random() * 5,
          totalRequests: Math.floor(Math.random() * 10000) + 5000,
          blockedRequests: Math.floor(Math.random() * 100)
        })
      }
    },
    async exportData() {
      try {
        this.$message.info('正在导出数据...')
        
        const params = {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1],
          metric: this.activeMetric,
          format: 'excel'
        }
        
        // 调用API导出统计数据
        const response = await this.$api.statistics.export(params)
        
        // 处理文件下载
        const blob = new Blob([response.data], { 
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `统计数据_${new Date().toISOString().slice(0, 10)}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)
        
        this.$message.success('数据导出成功')
      } catch (error) {
        console.error('导出数据失败:', error)
        this.$message.error('导出数据失败: ' + (error.message || '未知错误'))
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.refreshData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.refreshData()
    },
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    },
    formatTime(timestamp) {
      if (!timestamp) return '-'
      // 统一使用 YYYY-MM-DD HH:mm:ss 格式
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    
    // 更新概览数据
    updateOverviewData(data) {
      // 根据API返回的数据更新概览卡片
      if (data.totalRequests !== undefined) {
        // 更新总请求数等概览数据
        // 这里可以根据实际API返回的数据结构进行调整
      }
    },
    
    // 加载统计数据
    async loadStatistics() {
      try {
        const params = {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1]
        }
        
        // 获取流量统计
        const trafficStats = await this.$api.statistics.getTrafficStats(params)
        // 获取性能统计
        const performanceStats = await this.$api.statistics.getPerformanceStats(params)
        // 获取错误统计
        const errorStats = await this.$api.statistics.getErrorStats(params)
        
        // 处理统计数据
        this.processStatisticsData({
          traffic: trafficStats.data,
          performance: performanceStats.data,
          error: errorStats.data
        })
      } catch (error) {
        console.error('加载统计数据失败:', error)
        // 使用模拟数据作为降级方案
        this.generateMockData()
      }
    },
    
    // 处理统计数据
    processStatisticsData(data) {
      // 根据API返回的数据更新页面显示
      if (data.traffic) {
        // 处理流量数据
      }
      if (data.performance) {
        // 处理性能数据
      }
      if (data.error) {
        // 处理错误数据
      }
    }
  }
}
</script>

<style scoped>
.statistics-content {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  color: #333;
  margin-bottom: 5px;
}

.page-header p {
  color: #666;
  font-size: 14px;
}

.time-selector {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.overview-row {
  margin-bottom: 30px;
}

.overview-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 28px;
  color: white;
}

.overview-icon.requests {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-icon.avg-qps {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-icon.avg-rt {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-icon.success-rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.overview-content h3 {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
}

.overview-content p {
  color: #666;
  font-size: 14px;
  margin: 0 0 5px 0;
}

.overview-change {
  font-size: 12px;
  font-weight: 600;
}

.overview-change.positive {
  color: #67C23A;
}

.overview-change.negative {
  color: #F56C6C;
}

.charts-row {
  margin-bottom: 30px;
}

.chart-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-header h3 {
  color: #333;
  margin: 0;
  font-size: 16px;
}

.chart-container {
  height: 300px;
  background: #f8f9fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.top-paths {
  padding: 10px 0;
}

.path-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.path-rank {
  width: 30px;
  height: 30px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 15px;
}

.path-info {
  flex: 1;
  margin-right: 15px;
}

.path-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}

.path-count {
  font-size: 12px;
  color: #666;
}

.path-bar {
  width: 100px;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.path-progress {
  height: 100%;
  background: #409EFF;
  transition: width 0.3s ease;
}

.traffic-sources {
  padding: 10px 0;
}

.source-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.source-info {
  width: 100px;
  margin-right: 15px;
}

.source-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}

.source-percentage {
  font-size: 12px;
  color: #666;
}

.source-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 15px;
}

.source-progress {
  height: 100%;
  transition: width 0.3s ease;
}

.source-count {
  width: 80px;
  text-align: right;
  font-size: 12px;
  color: #666;
}

.data-table {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  color: #333;
  margin: 0;
  font-size: 16px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>