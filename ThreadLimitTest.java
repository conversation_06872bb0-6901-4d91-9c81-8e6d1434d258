import java.util.concurrent.*;
import java.net.http.*;
import java.net.URI;
import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadLimitTest {
    private static final String URL = "http://localhost:8088/api/test";
    private static final String TENANT_ID = "111";
    private static final int THREAD_COUNT = 5;
    private static final int REQUESTS_PER_THREAD = 1;
    
    public static void main(String[] args) throws InterruptedException {
        HttpClient client = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();
            
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        System.out.println("开始线程数限流测试...");
        System.out.println("线程数: " + THREAD_COUNT);
        System.out.println("每个线程请求数: " + REQUESTS_PER_THREAD);
        
        long startTime = System.currentTimeMillis();
        
        // 启动所有线程
        for (int i = 0; i < THREAD_COUNT; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < REQUESTS_PER_THREAD; j++) {
                        HttpRequest request = HttpRequest.newBuilder()
                            .uri(URI.create(URL))
                            .header("X-Tenant-ID", TENANT_ID)
                            .GET()
                            .build();
                            
                        try {
                            HttpResponse<String> response = client.send(request, 
                                HttpResponse.BodyHandlers.ofString());
                            
                            System.out.printf("线程%d-请求%d: 状态码=%d\n", 
                                threadId, j+1, response.statusCode());
                                
                            if (response.statusCode() == 200) {
                                successCount.incrementAndGet();
                            } else {
                                errorCount.incrementAndGet();
                                System.out.printf("线程%d-请求%d: 错误响应=%s\n", 
                                    threadId, j+1, response.body());
                            }
                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                            System.out.printf("线程%d-请求%d: 异常=%s\n", 
                                threadId, j+1, e.getMessage());
                        }
                        
                        // 每个请求之间稍微延迟，模拟真实场景
                        Thread.sleep(100);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有线程完成
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("\n=== 测试结果 ===");
        System.out.println("总耗时: " + (endTime - startTime) + "ms");
        System.out.println("成功请求: " + successCount.get());
        System.out.println("失败请求: " + errorCount.get());
        System.out.println("总请求数: " + (successCount.get() + errorCount.get()));
        
        if (errorCount.get() > 0) {
            System.out.println("\n检测到限流！");
            if (successCount.get() == 1 && errorCount.get() == THREAD_COUNT - 1) {
                System.out.println("限流模式可能是: 线程数限流（只允许1个并发线程）");
            } else {
                System.out.println("限流模式可能是: QPS限流");
            }
        } else {
            System.out.println("\n未检测到限流");
        }
    }
}