# Vue前端开发产品需求文档

## 1. 产品概述

本文档描述了Sentinel流量控制系统Vue前端的开发需求。该前端将作为现有React前端的替代方案，提供更现代化的用户界面和更好的用户体验，用于管理和监控流量控制规则。

* **目标用户**: 系统管理员、运维人员、开发人员
* **主要功能**: 流量规则管理、实时监控、系统配置、IP规则管理
* **技术栈**: Vue 2

## 2. 核心功能

### 2.1 用户角色

| 角色       | 权限描述       | 主要功能                   |
| ---------- | -------------- | -------------------------- |
| 系统管理员 | 完全权限       | 所有功能的访问和配置       |
| 运维人员   | 监控和基础配置 | 查看监控数据、基础规则配置 |
| 开发人员   | 只读权限       | 查看规则和监控数据         |

### 2.2 功能模块

本Vue前端包含以下主要页面：

1. **仪表板页面**: 系统概览、关键指标展示、快速操作入口
2. **流量规则管理页面**: 规则的增删改查、批量操作、规则模板
3. **IP规则管理页面**: IP黑白名单、IP段配置、批量导入导出
4. **实时监控页面**: 监控大屏、图表展示、数据导出
5. **系统配置页面**: 系统参数配置、告警设置、用户管理
6. **日志查看页面**: 操作日志、系统日志、审计记录

### 2.3 页面详细功能

| 页面名称     | 模块名称   | 功能描述                                     |
| ------------ | ---------- | -------------------------------------------- |
| 仪表板页面   | 概览面板   | 显示系统状态、QPS统计、限流次数等关键指标    |
| 仪表板页面   | 快速操作   | 提供常用操作的快捷入口，如添加规则、查看告警 |
| 流量规则管理 | 规则列表   | 展示所有流量控制规则，支持搜索、筛选、排序   |
| 流量规则管理 | 规则编辑   | 创建和编辑流量规则，支持多维度配置           |
| 流量规则管理 | 批量操作   | 支持批量启用/禁用、删除、导入导出规则        |
| IP规则管理   | IP黑白名单 | 管理IP黑白名单，支持单个IP和IP段             |
| IP规则管理   | 批量导入   | 支持CSV/Excel格式的IP规则批量导入            |
| IP规则管理   | IP统计     | 显示IP访问统计和归属地信息                   |
| 实时监控     | 监控大屏   | 实时展示系统运行状态和流量数据               |
| 实时监控     | 图表展示   | 使用ECharts展示QPS、响应时间等指标趋势       |
| 系统配置     | 参数配置   | 系统全局参数的配置和管理                     |
| 系统配置     | 告警设置   | 配置告警阈值和通知方式                       |

## 3. 核心流程

### 3.1 用户操作流程

**管理员流程**:
用户登录 → 仪表板概览 → 流量规则管理 → 创建/编辑规则 → 保存并生效 → 监控验证

**运维人员流程**:
用户登录 → 实时监控 → 查看告警 → 调整规则参数 → 验证效果

**开发人员流程**:
用户登录 → 查看规则配置 → 监控数据分析 → 导出报告

### 3.2 页面导航流程

```mermaid
graph TD
    A[登录页面] --> B[仪表板]
    B --> C[流量规则管理]
    B --> D[IP规则管理]
    B --> E[实时监控]
    B --> F[系统配置]
    B --> G[日志查看]
    C --> C1[规则列表]
    C --> C2[创建规则]
    C --> C3[编辑规则]
    D --> D1[IP黑白名单]
    D --> D2[批量导入]
    E --> E1[监控大屏]
    E --> E2[历史数据]
```

## 4. 用户界面设计

### 4.1 设计风格

* **主色调**: 蓝色系 (#409EFF) 作为主色，传达专业和可靠
* **辅助色**: 绿色 (#67C23A) 表示正常状态，红色 (#F56C6C) 表示告警
* **按钮样式**: 圆角按钮，支持多种尺寸和状态
* **字体**: 系统默认字体，主要内容14px，标题16-20px
* **布局风格**: 左侧导航 + 顶部面包屑 + 主内容区域
* **图标风格**: 使用Element UI内置图标，简洁现代

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素                                       |
| -------- | -------- | -------------------------------------------- |
| 仪表板   | 统计卡片 | 使用卡片布局展示关键指标，支持点击跳转详情   |
| 仪表板   | 图表区域 | 使用ECharts展示趋势图表，支持时间范围选择    |
| 规则管理 | 表格组件 | 使用Element UI Table，支持分页、排序、筛选 |
| 规则管理 | 表单组件 | 使用动态表单，根据规则类型显示不同配置项     |
| 监控页面 | 大屏布局 | 全屏展示，深色主题，突出数据可视化           |
| 监控页面 | 实时图表 | WebSocket实时更新数据，流畅的动画效果        |

### 4.3 响应式设计

* **桌面端优先**: 主要针对1920x1080及以上分辨率优化
* **平板适配**: 支持768px-1024px屏幕，调整布局和字体大小
* **移动端支持**: 基础功能在移动端可用，优化触摸操作
* **断点设置**:

  * 大屏: ≥1200px
  * 中屏: 992px-1199px
  * 小屏: 768px-991px
  * 超小屏: <768px

## 5. 技术架构

### 5.1 技术栈选择

** **框架**: Vue 2 (Options API)
* **构建工具**: Vue CLI
* **UI组件库**: Element UI
* **状态管理**: Vuex
* **路由**: Vue Router 3
* **HTTP客户端**: Axios
* **图表库**: ECharts
* **代码规范**: ESLint + Prettier

### 5.2 项目结构

```
flow-control-vue-frontend/
├── public/
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── api/              # API接口定义
│   ├── assets/           # 静态资源
│   ├── components/       # 公共组件
│   ├── composables/      # 组合式函数
│   ├── layouts/          # 布局组件
│   ├── pages/            # 页面组件
│   ├── router/           # 路由配置
│   ├── stores/           # Pinia状态管理
│   ├── styles/           # 样式文件
│   ├── types/            # TypeScript类型定义
│   ├── utils/            # 工具函数
│   ├── App.vue
│   └── main.ts
├── .eslintrc.js
├── .prettierrc
├── package.json
├── tsconfig.json
└── vite.config.ts
```

### 5.3 API集成

与现有的flow-control-admin后端服务集成，复用相同的REST API接口：

* **基础URL**: [http://localhost:8088/api](http://localhost:8088/api)
* **认证方式**: JWT Token
* **数据格式**: JSON
* **错误处理**: 统一的错误响应格式

主要API端点：

* `/api/rules` - 流量规则管理
* `/api/ip-rules` - IP规则管理
* `/api/monitoring` - 监控数据
* `/api/config` - 系统配置
* `/api/logs` - 日志查询

## 6. 开发计划

### 6.1 开发阶段

**第一阶段 (1-2周)**: 项目搭建和基础功能

* 创建Vue项目并配置开发环境
* 实现基础布局和导航
* 完成登录和权限管理
* 实现仪表板页面

**第二阶段 (2-3周)**: 核心功能开发

* 实现流量规则管理功能
* 实现IP规则管理功能
* 集成后端API接口
* 完成基础的CRUD操作

**第三阶段 (1-2周)**: 监控和高级功能

* 实现实时监控大屏
* 集成图表和数据可视化
* 实现批量操作功能
* 添加导入导出功能

**第四阶段 (1周)**: 优化和测试

* 性能优化和代码重构
* 响应式设计调优
* 用户体验优化
* 测试和bug修复

### 6.2 质量保证

* **代码规范**: 使用ESLint和Prettier确保代码质量
* **类型安全**: 充分利用TypeScript的类型检查
* **组件测试**: 使用Vitest进行单元测试
* **E2E测试**: 使用Playwright进行端到端测试
* **性能监控**: 使用Vue DevTools进行性能分析

### 6.3 部署方案

* **开发环境**: 本地Vite开发服务器
* **构建**: 使用Vite构建生产版本
* **部署**: 静态文件部署到Nginx
* **Docker化**: 创建Docker镜像便于部署
* **CI/CD**: 集成到现有的构建流水线

## 7. 风险评估

### 7.1 技术风险

* **低风险**: Vue 2技术栈成熟稳定
* **低风险**: Element UI组件库的兼容性
* **低风险**: 与现有后端API的集成

### 7.2 项目风险

* **中等风险**: 双前端维护成本增加
* **低风险**: 用户从React前端迁移的适应成本
* **低风险**: 开发进度延期风险

### 7.3 缓解措施

* 充分的技术调研和原型验证
* 与React前端保持API接口一致性
* 渐进式迁移，支持两套前端并存
* 详细的用户文档和迁移指南
* 定期的代码审查和质量检查
