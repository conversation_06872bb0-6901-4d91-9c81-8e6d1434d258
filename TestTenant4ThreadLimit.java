import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 测试租户4的线程限流功能
 * 租户4配置：Grade=0（线程限流），Count=5.0（5个线程）
 */
public class TestTenant4ThreadLimit {
    private static final String BASE_URL = "http://localhost:8088/api/test";
    private static final int THREAD_COUNT = 8; // 发送8个并发请求，超过限制的5个线程
    private static final int REQUEST_DURATION = 3000; // 每个请求持续3秒
    
    private static final AtomicInteger successCount = new AtomicInteger(0);
    private static final AtomicInteger blockedCount = new AtomicInteger(0);
    private static final AtomicInteger errorCount = new AtomicInteger(0);
    
    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== 测试租户4线程限流功能 ===");
        System.out.println("租户4配置: Grade=0(线程限流), Count=5.0(5个线程)");
        System.out.println("测试计划: 发送" + THREAD_COUNT + "个并发请求，每个请求持续" + REQUEST_DURATION + "ms");
        System.out.println("预期结果: 最多5个请求成功，其余" + (THREAD_COUNT - 5) + "个被限流\n");
        
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
        
        long startTime = System.currentTimeMillis();
        
        // 启动并发请求
        for (int i = 0; i < THREAD_COUNT; i++) {
            final int requestId = i + 1;
            executor.submit(() -> {
                try {
                    sendRequest(requestId);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有请求完成
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("\n=== 测试结果 ===");
        System.out.println("总请求数: " + THREAD_COUNT);
        System.out.println("成功请求: " + successCount.get());
        System.out.println("被限流请求: " + blockedCount.get());
        System.out.println("错误请求: " + errorCount.get());
        System.out.println("总耗时: " + (endTime - startTime) + "ms");
        
        // 分析结果
        if (successCount.get() <= 5 && blockedCount.get() > 0) {
            System.out.println("\n✅ 线程限流功能正常：成功请求数(" + successCount.get() + ") <= 5，有请求被限流(" + blockedCount.get() + ")");
        } else if (successCount.get() > 5) {
            System.out.println("\n❌ 线程限流功能异常：成功请求数(" + successCount.get() + ") > 5，超过了限制");
        } else {
            System.out.println("\n⚠️ 结果不明确，可能需要进一步分析");
        }
    }
    
    private static void sendRequest(int requestId) {
        try {
            URL url = new URL(BASE_URL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            
            // 设置租户ID为tenant4
            conn.setRequestProperty("X-Tenant-ID", "tenant4");
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(10000);
            
            System.out.println("[" + System.currentTimeMillis() + "] 请求" + requestId + "开始");
            
            int responseCode = conn.getResponseCode();
            
            if (responseCode == 200) {
                // 模拟长时间处理
                Thread.sleep(REQUEST_DURATION);
                
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                successCount.incrementAndGet();
                System.out.println("[" + System.currentTimeMillis() + "] 请求" + requestId + "成功: " + response.toString());
                
            } else if (responseCode == 429) {
                // 被限流
                blockedCount.incrementAndGet();
                System.out.println("[" + System.currentTimeMillis() + "] 请求" + requestId + "被限流: HTTP " + responseCode);
                
            } else {
                errorCount.incrementAndGet();
                System.out.println("[" + System.currentTimeMillis() + "] 请求" + requestId + "错误: HTTP " + responseCode);
            }
            
            conn.disconnect();
            
        } catch (Exception e) {
            errorCount.incrementAndGet();
            System.out.println("[" + System.currentTimeMillis() + "] 请求" + requestId + "异常: " + e.getMessage());
        }
    }
}