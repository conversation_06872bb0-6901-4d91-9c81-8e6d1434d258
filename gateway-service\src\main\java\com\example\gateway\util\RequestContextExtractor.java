package com.example.gateway.util;

import com.example.common.constant.FlowControlConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;

import java.net.InetSocketAddress;
import java.util.List;

/**
 * 请求上下文提取器 负责从请求中提取租户ID、资源标识、客户端IP等信息
 */
@Slf4j
@Component
public class RequestContextExtractor {

	/**
	 * 提取租户ID
	 */
	public String extractTenantId(ServerWebExchange exchange) {
		log.debug("Extracting tenant ID from request");
		log.debug("Looking for header: {}", FlowControlConstants.Headers.TENANT_ID);
		log.debug("All request headers: {}", exchange.getRequest().getHeaders());

		// 1. 从请求头获取
		String tenantId = exchange.getRequest().getHeaders().getFirst(FlowControlConstants.Headers.TENANT_ID);
		log.debug("Extracted tenant ID from header: {}", tenantId);

		if (StringUtils.hasText(tenantId)) {
			log.debug("Using tenant ID from header: {}", tenantId);
			return tenantId;
		}

		// 2. 从查询参数获取
		List<String> tenantParams = exchange.getRequest().getQueryParams().get("tenantId");
		if (tenantParams != null && !tenantParams.isEmpty()) {
			tenantId = tenantParams.get(0);
			if (StringUtils.hasText(tenantId)) {
				return tenantId;
			}
		}

		// 3. 从路径中提取（如果路径包含租户信息）
		String path = exchange.getRequest().getPath().value();
		tenantId = extractTenantFromPath(path);
		if (StringUtils.hasText(tenantId)) {
			return tenantId;
		}

		// 4. 返回默认租户
		log.debug("No tenant ID found, using default: {}", FlowControlConstants.Defaults.DEFAULT_TENANT);
		return FlowControlConstants.Defaults.DEFAULT_TENANT;
	}

	/**
	 * 提取资源标识
	 */
	public String extractResource(ServerWebExchange exchange) {
		String path = exchange.getRequest().getPath().value();
		// 组合HTTP方法和路径作为资源标识
		return path;
	}

	/**
	 * 提取客户端IP
	 */
	public String extractClientIp(ServerWebExchange exchange) {
		log.debug("Extracting client IP from request");

		// 1. 从X-Forwarded-For头获取
		String xForwardedFor = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
		log.debug("X-Forwarded-For header: {}", xForwardedFor);
		if (StringUtils.hasText(xForwardedFor)) {
			// X-Forwarded-For可能包含多个IP，取第一个
			String[] ips = xForwardedFor.split(",");
			String ip = ips[0].trim();
			log.debug("Extracted IP from X-Forwarded-For: {}, isValid: {}", ip, isValidIp(ip));
			if (isValidIp(ip)) {
				return ip;
			}
		}

		// 2. 从X-Real-IP头获取
		String xRealIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP");
		if (StringUtils.hasText(xRealIp) && isValidIp(xRealIp)) {
			return xRealIp;
		}

		// 3. 从X-Client-IP头获取
		String xClientIp = exchange.getRequest().getHeaders().getFirst(FlowControlConstants.Headers.CLIENT_IP);
		if (StringUtils.hasText(xClientIp) && isValidIp(xClientIp)) {
			return xClientIp;
		}

		// 4. 从远程地址获取
		InetSocketAddress remoteAddress = exchange.getRequest().getRemoteAddress();
		if (remoteAddress != null) {
			String ip = remoteAddress.getAddress().getHostAddress();
			log.debug("Remote address IP: {}, isValid: {}", ip, isValidIp(ip));
			if (isValidIp(ip)) {
				return ip;
			}
		}

		log.debug("No valid IP found, returning 'unknown'");
		return "unknown";
	}

	/**
	 * 提取请求ID
	 */
	public String extractRequestId(ServerWebExchange exchange) {
		String requestId = exchange.getRequest().getHeaders().getFirst(FlowControlConstants.Headers.REQUEST_ID);
		if (StringUtils.hasText(requestId)) {
			return requestId;
		}

		// 生成新的请求ID
		return generateRequestId();
	}

	/**
	 * 从路径中提取租户ID 支持路径格式：/tenant/{tenantId}/api/...
	 */
	private String extractTenantFromPath(String path) {
		if (!StringUtils.hasText(path)) {
			return null;
		}

		// 匹配 /tenant/{tenantId}/ 格式
		if (path.startsWith("/tenant/")) {
			String[] segments = path.split("/");
			if (segments.length >= 3) {
				return segments[2];
			}
		}

		return null;
	}

	/**
	 * 验证IP地址是否有效
	 */
	private boolean isValidIp(String ip) {
		if (!StringUtils.hasText(ip)) {
			return false;
		}

		// 排除内网和无效IP
		return !"unknown".equalsIgnoreCase(ip) && !"127.0.0.1".equals(ip) && !"0:0:0:0:0:0:0:1".equals(ip)
				&& !"localhost".equalsIgnoreCase(ip);
	}

	/**
	 * 生成请求ID
	 */
	private String generateRequestId() {
		return System.currentTimeMillis() + "-" + Thread.currentThread().getId();
	}
}