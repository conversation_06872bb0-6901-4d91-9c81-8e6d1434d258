package com.example.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.dto.IPFlowRuleDTO;
import com.example.common.entity.IPFlowRule;
import com.example.admin.mapper.IPFlowRuleMapper;
import com.example.admin.service.IPFlowRuleService;

import com.example.admin.vo.IPFlowRuleVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * IP流控规则服务实现类
 */
@Service
public class IPFlowRuleServiceImpl extends ServiceImpl<IPFlowRuleMapper, IPFlowRule> implements IPFlowRuleService {

	private static final Logger log = LoggerFactory.getLogger(IPFlowRuleServiceImpl.class);

	@Resource
	private IPFlowRuleMapper ipFlowRuleMapper;

	// IP地址正则表达式
	private static final Pattern IP_PATTERN = Pattern
			.compile("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");

	// IP段正则表达式（CIDR格式）
	private static final Pattern CIDR_PATTERN = Pattern.compile(
			"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/([0-9]|[1-2][0-9]|3[0-2])$");

	// IP通配符正则表达式（支持 * 通配符）
	private static final Pattern WILDCARD_PATTERN = Pattern
			.compile("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|\\*)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|\\*)$");

	@Override
	public Page<IPFlowRuleVO> selectIPFlowRulePage(Page<IPFlowRuleVO> page, String tenantId, String ruleName,
			String ruleType, Integer status, String ipValue) {
		return ipFlowRuleMapper.selectIPFlowRuleVOPage(page, tenantId, ruleType, status);
	}

	@Override
	public IPFlowRuleVO getIPFlowRuleById(Long id) {
		IPFlowRule ipFlowRule = this.getById(id);
		if (ipFlowRule == null) {
			return null;
		}
		IPFlowRuleVO ipFlowRuleVO = new IPFlowRuleVO();
		BeanUtils.copyProperties(ipFlowRule, ipFlowRuleVO);
		return ipFlowRuleVO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean createIPFlowRule(IPFlowRuleDTO ipFlowRuleDTO) {
		// 验证IP格式
		if (!validateIpFormat(ipFlowRuleDTO.getIpValue(), ipFlowRuleDTO.getRuleType())) {
			throw new RuntimeException("IP格式不正确");
		}

		// 检查规则名称是否已存在
		if (existsByRuleName(ipFlowRuleDTO.getRuleName(), null)) {
			throw new RuntimeException("规则名称已存在");
		}

		IPFlowRule ipFlowRule = new IPFlowRule();
		BeanUtils.copyProperties(ipFlowRuleDTO, ipFlowRule);
		ipFlowRule.setCreateTime(LocalDateTime.now());
		ipFlowRule.setUpdateTime(LocalDateTime.now());

		boolean result = this.save(ipFlowRule);

		// 发布规则更新
		if (result) {
			publishIpRules();
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateIPFlowRule(Long id, IPFlowRuleDTO ipFlowRuleDTO) {
		IPFlowRule existingRule = this.getById(id);
		if (existingRule == null) {
			throw new RuntimeException("IP流控规则不存在");
		}

		// 验证IP格式
		if (!validateIpFormat(ipFlowRuleDTO.getIpValue(), ipFlowRuleDTO.getRuleType())) {
			throw new RuntimeException("IP格式不正确");
		}

		// 检查规则名称是否已存在（排除当前规则）
		if (existsByRuleName(ipFlowRuleDTO.getRuleName(), id)) {
			throw new RuntimeException("规则名称已存在");
		}

		BeanUtils.copyProperties(ipFlowRuleDTO, existingRule);
		existingRule.setId(id);
		existingRule.setUpdateTime(LocalDateTime.now());

		boolean result = this.updateById(existingRule);

		// 发布规则更新
		if (result) {
			publishIpRules();
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteIPFlowRule(Long id) {
		boolean result = this.removeById(id);

		// 发布规则更新
		if (result) {
			publishIpRules();
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchDeleteIPFlowRules(List<Long> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return false;
		}
		boolean result = this.removeByIds(ids);

		// 发布规则更新
		if (result) {
			publishIpRules();
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean enableIPFlowRule(Long id) {
		return updateRuleStatus(id, 1);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean disableIPFlowRule(Long id) {
		return updateRuleStatus(id, 0);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchUpdateStatus(List<Long> ids, Integer status) {
		if (CollectionUtils.isEmpty(ids)) {
			return false;
		}
		return ipFlowRuleMapper.batchUpdateStatus(ids, status) > 0;
	}

	@Override
	public List<IPFlowRuleVO> getIPFlowRulesByTenantId(String tenantId, Integer status, Integer limit) {
		return ipFlowRuleMapper.selectByTenantId(tenantId, status, limit);
	}

	public List<IPFlowRuleVO> getIPFlowRulesByIpValue(String ipValue, String tenantId, Integer status, Integer limit) {
		// 使用selectByTenantId方法，因为selectByIpValue不存在
		return ipFlowRuleMapper.selectByTenantId(tenantId, status, limit);
	}

	@Override
	public boolean existsByRuleName(String ruleName, Long excludeId) {
		return ipFlowRuleMapper.selectByRuleName(ruleName, excludeId) != null;
	}

	public boolean existsByIpValue(String ipValue, Long excludeId) {
		// 由于existsByIpValue方法不存在，暂时返回false
		// TODO: 实现IP值重复检查逻辑
		return false;
	}

	@Override
	public int countByTenantId(String tenantId, Integer status) {
		return ipFlowRuleMapper.countByTenantId(tenantId, status);
	}

	@Override
	public List<IPFlowRuleVO> getEnabledIPRules(String tenantId, Integer limit) {
		return ipFlowRuleMapper.selectEnabledRules(tenantId, limit);
	}

	public List<Map<String, Object>> getRuleTypeStatistics(String tenantId) {
		return ipFlowRuleMapper.getRuleTypeStatistics(tenantId);
	}

	public List<Map<String, Object>> getTenantIPRuleStatistics(Integer limit) {
		return ipFlowRuleMapper.getTenantIPRuleStatistics(limit);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchCreateIPFlowRules(List<IPFlowRuleDTO> ipFlowRuleDTOList) {
		if (CollectionUtils.isEmpty(ipFlowRuleDTOList)) {
			return false;
		}

		List<IPFlowRule> ipFlowRules = ipFlowRuleDTOList.stream().map(dto -> {
			IPFlowRule ipFlowRule = new IPFlowRule();
			BeanUtils.copyProperties(dto, ipFlowRule);
			ipFlowRule.setCreateTime(LocalDateTime.now());
			ipFlowRule.setUpdateTime(LocalDateTime.now());
			return ipFlowRule;
		}).collect(Collectors.toList());

		return ipFlowRuleMapper.batchInsert(ipFlowRules) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean copyIPFlowRule(Long id, String newRuleName) {
		IPFlowRule originalRule = this.getById(id);
		if (originalRule == null) {
			throw new RuntimeException("原规则不存在");
		}

		if (existsByRuleName(newRuleName, null)) {
			throw new RuntimeException("新规则名称已存在");
		}

		IPFlowRule newRule = new IPFlowRule();
		BeanUtils.copyProperties(originalRule, newRule);
		newRule.setId(null);
		newRule.setRuleName(newRuleName);
		newRule.setCreateTime(LocalDateTime.now());
		newRule.setUpdateTime(LocalDateTime.now());

		return this.save(newRule);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<String, Object> importIPFlowRules(List<IPFlowRuleDTO> ipFlowRuleDTOList, boolean overwrite) {
		Map<String, Object> result = new HashMap<>();
		int successCount = 0;
		int failCount = 0;
		List<String> errorMessages = new ArrayList<>();

		for (IPFlowRuleDTO dto : ipFlowRuleDTOList) {
			try {
				if (overwrite && existsByRuleName(dto.getRuleName(), null)) {
					// 覆盖模式：先删除再创建
					IPFlowRule existingRule = ipFlowRuleMapper.selectByRuleName(dto.getRuleName(), null);
					if (existingRule != null) {
						this.removeById(existingRule.getId());
					}
				}

				if (createIPFlowRule(dto)) {
					successCount++;
				} else {
					failCount++;
					errorMessages.add("规则 " + dto.getRuleName() + " 创建失败");
				}
			} catch (Exception e) {
				failCount++;
				errorMessages.add("规则 " + dto.getRuleName() + " 导入失败: " + e.getMessage());
			}
		}

		result.put("successCount", successCount);
		result.put("failCount", failCount);
		result.put("errorMessages", errorMessages);
		return result;
	}

	@Override
	public List<IPFlowRuleVO> exportIPFlowRules(String tenantId, Integer status) {
		// 使用selectByTenantId方法替代不存在的selectForExport方法
		return ipFlowRuleMapper.selectByTenantId(tenantId, status, null);
	}

	@Override
	public Map<String, Object> validateIPFlowRule(IPFlowRuleDTO ipFlowRuleDTO) {
		Map<String, Object> result = new HashMap<>();
		List<String> errors = new ArrayList<>();

		// 验证必填字段
		if (!StringUtils.hasText(ipFlowRuleDTO.getRuleName())) {
			errors.add("规则名称不能为空");
		}
		if (!StringUtils.hasText(ipFlowRuleDTO.getIpValue())) {
			errors.add("IP值不能为空");
		}
		if (ipFlowRuleDTO.getRuleType() == null) {
			errors.add("规则类型不能为空");
		}

		// 验证IP格式
		if (StringUtils.hasText(ipFlowRuleDTO.getIpValue()) && StringUtils.hasText(ipFlowRuleDTO.getRuleType())) {
			if (!validateIpFormat(ipFlowRuleDTO.getIpValue(), ipFlowRuleDTO.getRuleType())) {
				errors.add("IP格式不正确");
			}
		}

		result.put("valid", errors.isEmpty());
		result.put("errors", errors);
		return result;
	}

	@Override
	public boolean validateIpFormat(String ipValue, String ruleType) {
		if (!StringUtils.hasText(ipValue) || !StringUtils.hasText(ruleType)) {
			return false;
		}

		switch (ruleType) {
		case "SINGLE_IP": // 单个IP
			return IP_PATTERN.matcher(ipValue).matches();
		case "CIDR": // IP段（CIDR格式）
			return CIDR_PATTERN.matcher(ipValue).matches();
		case "IP_RANGE": // IP范围（支持单个IP、CIDR或范围格式）
			return validateIPRange(ipValue);
		case "WILDCARD": // 通配符格式（支持 * 通配符）
			return WILDCARD_PATTERN.matcher(ipValue).matches();
		default:
			return false;
		}
	}

	/**
	 * 验证IP范围格式 支持格式： 1. 单个IP: *********** 2. CIDR: ***********/24 3. 范围:
	 * ***********-***********00 4. 通配符: 192.168.1.*
	 */
	private boolean validateIPRange(String ipValue) {
		if (!StringUtils.hasText(ipValue)) {
			return false;
		}

		// 检查是否为单个IP
		if (IP_PATTERN.matcher(ipValue).matches()) {
			return true;
		}

		// 检查是否为CIDR格式
		if (CIDR_PATTERN.matcher(ipValue).matches()) {
			return true;
		}

		// 检查是否为通配符格式
		if (WILDCARD_PATTERN.matcher(ipValue).matches()) {
			return true;
		}

		// 检查是否为范围格式 (IP1-IP2)
		if (ipValue.contains("-")) {
			String[] parts = ipValue.split("-");
			if (parts.length == 2) {
				String startIP = parts[0].trim();
				String endIP = parts[1].trim();
				return IP_PATTERN.matcher(startIP).matches() && IP_PATTERN.matcher(endIP).matches();
			}
		}

		return false;
	}

	@Override
	public List<IPFlowRuleVO> getMatchingRules(String clientIp, String tenantId) {
		return ipFlowRuleMapper.selectMatchingRules(clientIp, tenantId);
	}

	@Override
	public boolean syncRuleToSentinel(Long id) {
		// TODO: 实现与Sentinel的同步逻辑
		IPFlowRule ipFlowRule = this.getById(id);
		if (ipFlowRule == null) {
			return false;
		}

		// 这里应该调用Sentinel的API来同步规则
		// 暂时返回true表示同步成功
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<String, Object> batchSyncRulesToSentinel(List<Long> ids) {
		Map<String, Object> result = new HashMap<>();
		int successCount = 0;
		int failCount = 0;

		for (Long id : ids) {
			if (syncRuleToSentinel(id)) {
				successCount++;
			} else {
				failCount++;
			}
		}

		result.put("successCount", successCount);
		result.put("failCount", failCount);
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Map<String, Object> syncRulesFromSentinel(String tenantId) {
		// TODO: 实现从Sentinel同步规则的逻辑
		Map<String, Object> result = new HashMap<>();
		result.put("syncCount", 0);
		result.put("message", "暂未实现从Sentinel同步规则功能");
		return result;
	}

	@Override
	public Object getIPFlowRuleStatistics(String tenantId) {
		Map<String, Object> statistics = new HashMap<>();

		// 获取规则类型统计
		List<Map<String, Object>> ruleTypeStats = getRuleTypeStatistics(tenantId);
		statistics.put("ruleTypeStatistics", ruleTypeStats);

		// 获取总数统计
		int totalCount = countByTenantId(tenantId, null);
		statistics.put("totalCount", totalCount);

		// 获取启用规则数量
		int enabledCount = countByTenantId(tenantId, 1);
		statistics.put("enabledCount", enabledCount);

		// 获取禁用规则数量
		int disabledCount = countByTenantId(tenantId, 0);
		statistics.put("disabledCount", disabledCount);

		return statistics;
	}

	public boolean matchIPRule(String ipAddress, String tenantId) {
		if (!StringUtils.hasText(ipAddress)) {
			return false;
		}

		// 获取匹配的规则
		List<IPFlowRuleVO> matchingRules = getMatchingRules(ipAddress, tenantId);

		// 如果有匹配的规则，返回true
		return !CollectionUtils.isEmpty(matchingRules);
	}

	/**
	 * 更新规则状态
	 */
	private boolean updateRuleStatus(Long id, Integer status) {
		IPFlowRule ipFlowRule = this.getById(id);
		if (ipFlowRule == null) {
			throw new RuntimeException("IP流控规则不存在");
		}

		ipFlowRule.setStatus(status);
		ipFlowRule.setUpdateTime(LocalDateTime.now());
		boolean result = this.updateById(ipFlowRule);

		// 发布规则更新
		if (result) {
			publishIpRules();
		}

		return result;
	}

	/**
	 * 发布IP规则（已移除Nacos依赖）
	 */
	private void publishIpRules() {
		try {
			List<IPFlowRule> allRules = this.list();
			log.info("IP rules updated, total count: {}", allRules.size());
			// 规则变更后，Gateway会通过定时任务自动从数据库加载最新规则
		} catch (Exception e) {
			log.error("Failed to process IP rules update", e);
		}
	}
}
