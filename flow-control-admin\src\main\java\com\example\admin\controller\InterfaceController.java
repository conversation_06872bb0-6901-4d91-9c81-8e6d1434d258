package com.example.admin.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 接口资源控制器
 * 提供接口资源的查询功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "接口资源管理")
@RestController
@RequestMapping("/api/interfaces")
public class InterfaceController {

    /**
     * 获取接口资源列表
     *
     * @param tenantId 租户ID
     * @return 接口资源列表
     */
    @Operation(summary = "获取接口资源列表", description = "根据租户ID获取接口资源列表")
    @GetMapping
    public Map<String, Object> getInterfaceList(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        
        // 模拟接口资源数据
        List<Map<String, Object>> interfaces = new ArrayList<>();
        
        // 根据租户ID返回不同的接口资源
        if ("tenant_001".equals(tenantId)) {
            interfaces.add(createInterface("/api/test", "测试接口", "GET"));
            interfaces.add(createInterface("/api/admin/users", "用户管理接口", "GET"));
            interfaces.add(createInterface("/api/admin/ip-rules", "IP规则管理接口", "GET"));
        } else if ("tenant_002".equals(tenantId)) {
            interfaces.add(createInterface("/api/admin/ip-rules", "数据导出接口", "GET"));
            interfaces.add(createInterface("/api/test", "文件上传接口", "POST"));
            interfaces.add(createInterface("/api/admin/users", "租户2用户管理接口", "GET"));
        } else {
            // 默认返回所有接口
            interfaces.add(createInterface("/api/test", "测试接口", "GET"));
            interfaces.add(createInterface("/api/admin/users", "用户管理接口", "GET"));
            interfaces.add(createInterface("/api/admin/ip-rules", "IP规则管理接口", "GET"));
            interfaces.add(createInterface("/api/flow-rules", "流量规则接口", "GET"));
            interfaces.add(createInterface("/api/tenant-flow-rules", "租户流量规则接口", "GET"));
            interfaces.add(createInterface("/api/ip-whitelists", "IP白名单接口", "GET"));
            interfaces.add(createInterface("/api/ip-blacklists", "IP黑名单接口", "GET"));
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "success");
        result.put("data", interfaces);
        result.put("timestamp", new Date());
        
        return result;
    }
    
    /**
     * 创建接口资源对象
     */
    private Map<String, Object> createInterface(String path, String name, String method) {
        Map<String, Object> interfaceInfo = new HashMap<>();
        interfaceInfo.put("resourceName", path);
        interfaceInfo.put("interfaceName", name);
        interfaceInfo.put("method", method);
        interfaceInfo.put("description", name + "的API接口");
        interfaceInfo.put("status", 1);
        return interfaceInfo;
    }
}