package com.example.common.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 */
@Slf4j
public class JsonUtils {
    
    /**
     * 对象转JSON字符串
     */
    public static String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return JSON.toJSONString(obj);
        } catch (JSONException e) {
            log.error("Object to JSON string failed", e);
            return null;
        }
    }
    
    /**
     * JSON字符串转对象
     */
    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString, clazz);
        } catch (JSONException e) {
            log.error("JSON string to object failed: {}", jsonString, e);
            return null;
        }
    }
    
    /**
     * JSON字符串转对象（泛型）
     */
    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString, typeReference);
        } catch (JSONException e) {
            log.error("JSON string to object failed: {}", jsonString, e);
            return null;
        }
    }
    
    /**
     * JSON字符串转List
     */
    public static <T> List<T> parseArray(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return JSON.parseArray(jsonString, clazz);
        } catch (JSONException e) {
            log.error("JSON string to array failed: {}", jsonString, e);
            return null;
        }
    }
    
    /**
     * JSON字符串转Map
     */
    public static Map<String, Object> parseMap(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString, new TypeReference<Map<String, Object>>() {});
        } catch (JSONException e) {
            log.error("JSON string to map failed: {}", jsonString, e);
            return null;
        }
    }
    
    /**
     * 判断字符串是否为有效的JSON
     */
    public static boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false;
        }
        try {
            JSON.parse(jsonString);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }
    
    /**
     * 格式化JSON字符串（美化输出）
     */
    public static String formatJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }
        try {
            Object obj = JSON.parse(jsonString);
            return JSON.toJSONString(obj, com.alibaba.fastjson2.JSONWriter.Feature.PrettyFormat);
        } catch (JSONException e) {
            log.error("Format JSON failed: {}", jsonString, e);
            return jsonString;
        }
    }
}