package com.example.common.constant;

/**
 * 流量控制常量类
 */
public class FlowControlConstants {
    
    /**
     * 限流模式
     */
    public static class Grade {
        public static final int THREAD = 0;  // 线程数限流
        public static final int QPS = 1;     // QPS限流
    }
    
    /**
     * 流控策略
     */
    public static class Strategy {
        public static final int DIRECT = 0;   // 直接
        public static final int RELATE = 1;   // 关联
        public static final int CHAIN = 2;    // 链路
    }
    
    /**
     * 流控行为
     */
    public static class ControlBehavior {
        public static final int REJECT = 0;   // 快速失败
        public static final int WARM_UP = 1;  // Warm Up
        public static final int QUEUE = 2;    // 排队等待
    }
    
    /**
     * 资源前缀
     */
    public static class ResourcePrefix {
        public static final String TENANT = "tenant:";
        public static final String IP = "ip:";
        public static final String RESOURCE = "resource:";
    }
    
    /**
     * 请求头常量
     */
    public static class Headers {
        public static final String TENANT_ID = "X-Tenant-Id";
        public static final String CLIENT_IP = "X-Client-Ip";
        public static final String REQUEST_ID = "X-Request-Id";
    }
    
    /**
     * 默认值
     */
    public static class Defaults {
        public static final String DEFAULT_TENANT = "default";
        public static final String DEFAULT_LIMIT_APP = "default";
        public static final int DEFAULT_QUEUE_TIMEOUT = 5000; // 5秒
        public static final int DEFAULT_WARM_UP_PERIOD = 10;  // 10秒
        public static final double DEFAULT_QPS_LIMIT = 10.0;
        public static final int DEFAULT_MAX_CONCURRENCY = 10;
    }
    
    /**
     * 响应码
     */
    public static class ResponseCode {
        public static final int SUCCESS = 200;
        public static final int TOO_MANY_REQUESTS = 429;
        public static final int SERVICE_UNAVAILABLE = 503;
        public static final int INTERNAL_ERROR = 500;
    }
    
    /**
     * 配置键
     */
    public static class ConfigKeys {
        public static final String FLOW_RULES = "flow-rules";
        public static final String DEGRADE_RULES = "degrade-rules";
        public static final String SYSTEM_RULES = "system-rules";
        public static final String AUTHORITY_RULES = "authority-rules";
    }
}