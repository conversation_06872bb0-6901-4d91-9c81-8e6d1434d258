/**
 * 统一数据验证规则配置
 * 确保前后端验证规则的一致性
 */

// 通用验证规则
export const commonRules = {
  // 必填验证
  required: {
    required: true,
    message: '此字段为必填项',
    trigger: 'blur'
  },
  
  // 租户ID验证
  tenantId: [
    { required: true, message: '租户ID不能为空', trigger: 'blur' },
    { min: 1, max: 50, message: '租户ID长度在1到50个字符之间', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '租户ID只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  
  // 规则名称验证
  ruleName: [
    { required: true, message: '规则名称不能为空', trigger: 'blur' },
    { min: 1, max: 100, message: '规则名称长度在1到100个字符之间', trigger: 'blur' }
  ],
  
  // 资源名称验证
  resourceName: [
    { required: true, message: '资源名称不能为空', trigger: 'blur' },
    { min: 1, max: 200, message: '资源名称长度在1到200个字符之间', trigger: 'blur' }
  ],
  
  // 描述验证
  description: [
    { max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' }
  ],
  
  // 优先级验证
  priority: [
    { required: true, message: '优先级不能为空', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '优先级必须在1到1000之间', trigger: 'blur' }
  ],
  
  // 状态验证
  status: [
    { required: true, message: '状态不能为空', trigger: 'change' },
    { type: 'number', min: 0, max: 1, message: '状态值必须为0或1', trigger: 'change' }
  ],
  
  // 启用状态验证
  enabled: [
    { required: true, message: '启用状态不能为空', trigger: 'change' },
    { type: 'number', min: 0, max: 1, message: '启用状态值必须为0或1', trigger: 'change' }
  ]
};

// 租户流量规则验证
export const tenantFlowRuleRules = {
  tenantId: commonRules.tenantId,
  ruleName: commonRules.ruleName,
  
  // 限流阈值类型
  grade: [
    { required: true, message: '限流阈值类型不能为空', trigger: 'change' },
    { type: 'number', min: 0, max: 1, message: '限流阈值类型值必须为0或1', trigger: 'change' }
  ],
  
  // 限流阈值
  count: [
    { required: true, message: '限流阈值不能为空', trigger: 'blur' },
    { type: 'number', min: 1, max: 999999, message: '限流阈值必须在1到999999之间', trigger: 'blur' }
  ],
  
  // 调用来源策略
  strategy: [
    { type: 'number', min: 0, max: 2, message: '调用来源策略值必须在0到2之间', trigger: 'change' }
  ],
  
  // 关联资源
  refResource: [
    { max: 256, message: '关联资源长度不能超过256个字符', trigger: 'blur' }
  ],
  
  // 流控效果
  controlBehavior: [
    { type: 'number', min: 0, max: 3, message: '流控效果值必须在0到3之间', trigger: 'change' }
  ],
  
  // 预热时间
  warmUpPeriodSec: [
    { type: 'number', min: 1, max: 3600, message: '预热时间必须在1到3600秒之间', trigger: 'blur' }
  ],
  
  // 排队超时时间
  maxQueueingTimeMs: [
    { type: 'number', min: 1, max: 60000, message: '排队超时时间必须在1到60000毫秒之间', trigger: 'blur' }
  ],
  
  priority: commonRules.priority,
  enabled: commonRules.enabled,
  description: commonRules.description
};

// IP流量规则验证
export const ipFlowRuleRules = {
  ruleName: commonRules.ruleName,
  tenantId: commonRules.tenantId,
  
  // 规则类型
  ruleType: [
    { required: true, message: '规则类型不能为空', trigger: 'change' },
    { pattern: /^(SINGLE_IP|IP_RANGE|CIDR|WILDCARD)$/, message: '规则类型必须为SINGLE_IP、IP_RANGE、CIDR或WILDCARD', trigger: 'change' }
  ],
  
  // IP值
  ipValue: [
    { required: true, message: 'IP值不能为空', trigger: 'blur' },
    { max: 200, message: 'IP值长度不能超过200个字符', trigger: 'blur' },
    { validator: validateIpValue, trigger: 'blur' }
  ],
  
  // QPS限制
  qpsLimit: [
    { type: 'number', min: 1, max: 1000000, message: 'QPS限制必须在1到1000000之间', trigger: 'blur' }
  ],
  
  // 流控行为
  flowBehavior: [
    { type: 'number', min: 0, max: 2, message: '流控行为值必须在0到2之间', trigger: 'change' }
  ],
  
  // 预热时间
  warmUpPeriod: [
    { type: 'number', min: 1, max: 3600, message: '预热时间必须在1到3600秒之间', trigger: 'blur' }
  ],
  
  // 排队超时时间
  queueTimeout: [
    { type: 'number', min: 1, max: 60000, message: '排队超时时间必须在1到60000毫秒之间', trigger: 'blur' }
  ],
  
  priority: commonRules.priority,
  status: commonRules.status,
  description: commonRules.description
};

// IP白名单验证
export const ipWhitelistRules = {
  // 名单名称
  listName: [
    { required: true, message: '名单名称不能为空', trigger: 'blur' },
    { min: 1, max: 100, message: '名单名称长度在1到100个字符之间', trigger: 'blur' }
  ],
  
  tenantId: commonRules.tenantId,
  
  // IP类型
  ipType: [
    { required: true, message: 'IP类型不能为空', trigger: 'change' },
    { pattern: /^(SINGLE_IP|IP_RANGE|IP_CIDR)$/, message: 'IP类型只能是SINGLE_IP、IP_RANGE或IP_CIDR', trigger: 'change' }
  ],
  
  // 单个IP
  singleIp: [
    { validator: validateSingleIp, trigger: 'blur' }
  ],
  
  // IP范围起始地址
  startIp: [
    { validator: validateIpAddress, trigger: 'blur' }
  ],
  
  // IP范围结束地址
  endIp: [
    { validator: validateIpAddress, trigger: 'blur' }
  ],
  
  // CIDR格式IP
  cidrIp: [
    { validator: validateCidrIp, trigger: 'blur' }
  ],
  
  priority: commonRules.priority,
  enabled: commonRules.enabled,
  description: commonRules.description
};

// IP黑名单验证（与白名单规则相同）
export const ipBlacklistRules = {
  ...ipWhitelistRules
};

// 接口流量规则验证
export const interfaceFlowRuleRules = {
  ruleName: commonRules.ruleName,
  tenantId: [
    { required: true, message: '请选择租户', trigger: 'change' }
  ],
  resourceName: commonRules.resourceName,
  
  // 限制模式
  limitMode: [
    { required: true, message: '请选择限制模式', trigger: 'change' }
  ],
  
  // 阈值
  threshold: [
    { required: true, message: '请输入阈值', trigger: 'blur' },
    { type: 'number', min: 1, message: '阈值必须大于0', trigger: 'blur' }
  ],
  
  // 控制行为
  behavior: [
    { required: true, message: '请选择控制行为', trigger: 'change' }
  ],
  
  // 预热时长
  warmUpPeriod: [
    { type: 'number', min: 1, message: '预热时长必须大于0', trigger: 'blur' }
  ],
  
  // 排队超时时间
  queueTimeout: [
    { type: 'number', min: 1, message: '排队超时时间必须大于0', trigger: 'blur' }
  ]
};

// 告警配置验证
export const alertConfigRules = {
  // QPS告警阈值
  qpsWarningThreshold: [
    { type: 'number', min: 1, message: 'QPS警告阈值必须大于0', trigger: 'blur' }
  ],
  
  qpsCriticalThreshold: [
    { type: 'number', min: 1, message: 'QPS严重告警阈值必须大于0', trigger: 'blur' }
  ],
  
  // 响应时间告警阈值
  responseTimeWarningThreshold: [
    { type: 'number', min: 1, message: '响应时间警告阈值必须大于0', trigger: 'blur' }
  ],
  
  responseTimeCriticalThreshold: [
    { type: 'number', min: 1, message: '响应时间严重告警阈值必须大于0', trigger: 'blur' }
  ],
  
  // 错误率告警阈值
  errorRateWarningThreshold: [
    { type: 'number', min: 0, max: 100, message: '错误率警告阈值必须在0到100之间', trigger: 'blur' }
  ],
  
  errorRateCriticalThreshold: [
    { type: 'number', min: 0, max: 100, message: '错误率严重告警阈值必须在0到100之间', trigger: 'blur' }
  ]
};

// 通知配置验证
export const notificationConfigRules = {
  // 邮件配置
  smtpHost: [
    { required: true, message: 'SMTP服务器不能为空', trigger: 'blur' }
  ],
  
  smtpPort: [
    { required: true, message: 'SMTP端口不能为空', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: 'SMTP端口必须在1到65535之间', trigger: 'blur' }
  ],
  
  smtpUsername: [
    { required: true, message: '发送邮箱不能为空', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  
  smtpPassword: [
    { required: true, message: '邮箱密码不能为空', trigger: 'blur' }
  ],
  
  // 短信配置
  smsProvider: [
    { required: true, message: '短信服务商不能为空', trigger: 'change' }
  ],
  
  smsAccessKey: [
    { required: true, message: 'AccessKey不能为空', trigger: 'blur' }
  ],
  
  smsSecretKey: [
    { required: true, message: 'SecretKey不能为空', trigger: 'blur' }
  ],
  
  // Webhook配置
  webhookUrl: [
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ]
};

// 自定义验证函数

/**
 * 验证IP值（支持多种格式）
 */
function validateIpValue(rule, value, callback) {
  if (!value) {
    callback();
    return;
  }
  
  // 单个IP地址
  const singleIpPattern = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
  // IP范围
  const ipRangePattern = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)-((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
  // CIDR格式
  const cidrPattern = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)\/([0-9]|[1-2][0-9]|3[0-2])$/;
  
  if (singleIpPattern.test(value) || ipRangePattern.test(value) || cidrPattern.test(value)) {
    callback();
  } else {
    callback(new Error('请输入正确的IP格式（支持单个IP、IP段、CIDR格式）'));
  }
}

/**
 * 验证单个IP地址
 */
function validateSingleIp(rule, value, callback) {
  if (!value) {
    callback();
    return;
  }
  
  const ipPattern = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
  if (ipPattern.test(value)) {
    callback();
  } else {
    callback(new Error('请输入正确的IP地址格式'));
  }
}

/**
 * 验证IP地址格式
 */
function validateIpAddress(rule, value, callback) {
  if (!value) {
    callback();
    return;
  }
  
  const ipPattern = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
  if (ipPattern.test(value)) {
    callback();
  } else {
    callback(new Error('请输入正确的IP地址格式'));
  }
}

/**
 * 验证CIDR格式IP
 */
function validateCidrIp(rule, value, callback) {
  if (!value) {
    callback();
    return;
  }
  
  const cidrPattern = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)\/([0-9]|[1-2][0-9]|3[0-2])$/;
  if (cidrPattern.test(value)) {
    callback();
  } else {
    callback(new Error('请输入正确的CIDR格式（如：***********/24）'));
  }
}

/**
 * 验证邮箱列表
 */
export function validateEmailList(emails) {
  if (!emails || emails.length === 0) {
    return { valid: false, message: '邮箱列表不能为空' };
  }
  
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  for (const email of emails) {
    if (!emailPattern.test(email)) {
      return { valid: false, message: `邮箱格式不正确: ${email}` };
    }
  }
  
  return { valid: true };
}

/**
 * 验证手机号列表
 */
export function validatePhoneList(phones) {
  if (!phones || phones.length === 0) {
    return { valid: false, message: '手机号列表不能为空' };
  }
  
  const phonePattern = /^1[3-9]\d{9}$/;
  for (const phone of phones) {
    if (!phonePattern.test(phone)) {
      return { valid: false, message: `手机号格式不正确: ${phone}` };
    }
  }
  
  return { valid: true };
}

/**
 * 验证时间范围
 */
export function validateTimeRange(startTime, endTime) {
  if (!startTime || !endTime) {
    return { valid: true }; // 允许为空
  }
  
  if (new Date(endTime) <= new Date(startTime)) {
    return { valid: false, message: '结束时间必须大于开始时间' };
  }
  
  return { valid: true };
}

/**
 * 验证阈值配置的逻辑关系
 */
export function validateThresholdLogic(warningValue, criticalValue) {
  if (warningValue && criticalValue && criticalValue <= warningValue) {
    return { valid: false, message: '严重告警阈值必须大于警告阈值' };
  }
  
  return { valid: true };
}

// 导出所有验证规则
export default {
  commonRules,
  tenantFlowRuleRules,
  ipFlowRuleRules,
  ipWhitelistRules,
  ipBlacklistRules,
  interfaceFlowRuleRules,
  alertConfigRules,
  notificationConfigRules,
  validateEmailList,
  validatePhoneList,
  validateTimeRange,
  validateThresholdLogic
};