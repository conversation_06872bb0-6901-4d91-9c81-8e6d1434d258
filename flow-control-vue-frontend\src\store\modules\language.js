// 语言管理 Vuex 模块
import { setLanguage, getLocale, getSupportedLanguages } from '@/i18n'

const state = {
  // 当前语言
  currentLanguage: getLocale(),
  // 支持的语言列表
  supportedLanguages: getSupportedLanguages()
}

const mutations = {
  SET_LANGUAGE(state, language) {
    state.currentLanguage = language
    setLanguage(language)
  }
}

const actions = {
  // 设置语言
  setLanguage({ commit }, language) {
    const supportedLangs = getSupportedLanguages().map(lang => lang.value)
    if (supportedLangs.includes(language)) {
      commit('SET_LANGUAGE', language)
      return Promise.resolve(language)
    } else {
      return Promise.reject(new Error('Unsupported language'))
    }
  },
  
  // 切换语言
  toggleLanguage({ commit, state }) {
    const currentLang = state.currentLanguage
    const newLang = currentLang === 'zh-CN' ? 'en-US' : 'zh-CN'
    commit('SET_LANGUAGE', newLang)
    return Promise.resolve(newLang)
  },
  
  // 初始化语言
  initLanguage({ commit }) {
    const currentLang = getLocale()
    commit('SET_LANGUAGE', currentLang)
    return Promise.resolve(currentLang)
  }
}

const getters = {
  // 获取当前语言
  currentLanguage: state => state.currentLanguage,
  
  // 获取支持的语言列表
  supportedLanguages: state => state.supportedLanguages,
  
  // 是否为中文
  isChinese: state => state.currentLanguage === 'zh-CN',
  
  // 是否为英文
  isEnglish: state => state.currentLanguage === 'en-US',
  
  // 获取当前语言的显示名称
  currentLanguageLabel: state => {
    const lang = state.supportedLanguages.find(lang => lang.value === state.currentLanguage)
    return lang ? lang.label : 'Unknown'
  },
  
  // 获取当前语言的国旗
  currentLanguageFlag: state => {
    const lang = state.supportedLanguages.find(lang => lang.value === state.currentLanguage)
    return lang ? lang.flag : '🌐'
  },
  
  // 获取下一个语言（用于切换）
  nextLanguage: state => {
    const currentIndex = state.supportedLanguages.findIndex(lang => lang.value === state.currentLanguage)
    const nextIndex = (currentIndex + 1) % state.supportedLanguages.length
    return state.supportedLanguages[nextIndex]
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}