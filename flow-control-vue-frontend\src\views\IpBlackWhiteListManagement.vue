<template>
	<Layout>
		<div class="ip-management-content">
			<div class="page-header">
				<h1>IP黑白名单管理</h1>
				<p>管理系统的IP黑白名单规则，支持批量导入导出操作</p>
			</div>

			<!-- 操作工具栏 -->
			<div class="toolbar">
				<div class="toolbar-left">
					<el-button type="primary" @click="showAddDialog">
						<i class="el-icon-plus"></i>
						新增IP规则
					</el-button>
					
					<el-button 
						type="success" 
						@click="showImportDialog"
						:disabled="loading"
					>
						<i class="el-icon-upload2"></i>
						批量导入
					</el-button>
					<el-button 
						type="warning" 
						@click="exportRules"
						:disabled="loading"
					>
						<i class="el-icon-download"></i>
						导出规则
					</el-button>
				</div>
				<div class="toolbar-right">
					<el-button 
						type="danger" 
						@click="batchDelete"
						:disabled="selectedRules.length === 0"
					>
						<i class="el-icon-delete"></i>
						批量删除 ({{ selectedRules.length }})
					</el-button>
				</div>
			</div>

			<!-- 搜索过滤器 -->
			<div class="filter-bar">
				<el-form :inline="true" :model="searchForm" class="search-form">
					<el-form-item label="租户ID">
						<el-input 
							v-model="searchForm.tenantId" 
							placeholder="请输入租户ID"
							clearable
							style="width: 200px;"
						></el-input>
					</el-form-item>
					<el-form-item label="IP地址">
						<el-input 
							v-model="searchForm.ipValue" 
							placeholder="请输入IP地址"
							clearable
							style="width: 200px;"
						></el-input>
					</el-form-item>
					<el-form-item label="规则类型">
						<el-select 
							v-model="searchForm.ruleType" 
							placeholder="请选择规则类型"
							clearable
							style="width: 150px;"
						>
							<el-option label="黑名单" value="BLACKLIST"></el-option>
							<el-option label="白名单" value="WHITELIST"></el-option>
							<el-option label="限流" value="LIMIT"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="状态">
						<el-select 
							v-model="searchForm.status" 
							placeholder="请选择状态"
							clearable
							style="width: 120px;"
						>
							<el-option label="启用" :value="1"></el-option>
							<el-option label="禁用" :value="0"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="searchRules">
							<i class="el-icon-search"></i>
							搜索
						</el-button>
						<el-button @click="resetSearch">
							<i class="el-icon-refresh-left"></i>
							重置
						</el-button>
					</el-form-item>
				</el-form>
			</div>

			<!-- IP规则列表 -->
			<div class="table-container">
				<el-table 
					:data="ipRules" 
					v-loading="loading"
					@selection-change="handleSelectionChange"
					style="width: 100%"
				>
					<el-table-column type="selection" width="55"></el-table-column>
					<el-table-column prop="id" label="ID" width="80"></el-table-column>
					<el-table-column prop="tenantId" label="租户ID" width="120"></el-table-column>
					<el-table-column prop="ruleName" label="规则名称" min-width="150" show-overflow-tooltip></el-table-column>
					<el-table-column prop="ipValue" label="IP地址" min-width="180" show-overflow-tooltip></el-table-column>
					<el-table-column prop="ruleType" label="规则类型" width="110">
						<template slot-scope="scope">
							<el-tag 
								:type="getRuleTypeTagType(scope.row.ruleType)"
								size="small"
							>
								{{ getRuleTypeText(scope.row.ruleType) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="limitCount" label="限流数量" width="100">
						<template slot-scope="scope">
							{{ scope.row.ruleType === 'LIMIT' ? scope.row.limitCount : '-' }}
						</template>
					</el-table-column>
					<el-table-column prop="status" label="状态" width="80">
						<template slot-scope="scope">
							<el-tag 
								:type="scope.row.status === 1 ? 'success' : 'danger'"
								size="small"
							>
								{{ scope.row.status === 1 ? '启用' : '禁用' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip></el-table-column>
					<el-table-column prop="createTime" label="创建时间" min-width="160" show-overflow-tooltip>
						<template slot-scope="scope">
							{{ formatDate(scope.row.createTime) }}
						</template>
					</el-table-column>
					<el-table-column label="操作" width="250" fixed="right">
						<template slot-scope="scope">
							<el-button 
								type="text" 
								size="small" 
								@click="editRule(scope.row)"
							>
								编辑
							</el-button>
							<el-button 
								type="text" 
								size="small" 
								@click="toggleStatus(scope.row)"
								:class="scope.row.status === 1 ? 'text-warning' : 'text-success'"
							>
								{{ scope.row.status === 1 ? '禁用' : '启用' }}
							</el-button>
							<el-button 
								type="text" 
								size="small" 
								@click="deleteRule(scope.row)"
								class="text-danger"
							>
								删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<!-- 分页 -->
			<div class="pagination-container">
				<el-pagination
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
					:current-page="pagination.current"
					:page-sizes="[10, 20, 50, 100]"
					:page-size="pagination.size"
					layout="total, sizes, prev, pager, next, jumper"
					:total="pagination.total"
				>
				</el-pagination>
			</div>

			<!-- 新增/编辑对话框 -->
			<el-dialog 
				:title="dialogTitle" 
				:visible.sync="dialogVisible"
				width="600px"
				@close="closeDialog"
			>
				<el-form 
				:model="ruleForm" 
				:rules="computedFormRules" 
				ref="ruleForm" 
				label-width="120px"
			>
					<el-form-item label="租户ID" prop="tenantId">
						<el-input 
							v-model="ruleForm.tenantId" 
							placeholder="请输入租户ID"
						></el-input>
					</el-form-item>
					<el-form-item label="规则名称" prop="ruleName">
						<el-input 
							v-model="ruleForm.ruleName" 
							placeholder="请输入规则名称"
						></el-input>
					</el-form-item>
					<el-form-item label="IP地址" prop="ipValue">
						<el-input 
							v-model="ruleForm.ipValue" 
							placeholder="支持单个IP、IP段、CIDR格式"
						></el-input>
						<div class="form-tip">
							支持格式：***********、***********-*************、***********/24
						</div>
					</el-form-item>
					<el-form-item label="规则类型" prop="ruleType">
						<el-select 
							v-model="ruleForm.ruleType" 
							placeholder="请选择规则类型"
							style="width: 100%;"
						>
							<el-option label="黑名单" value="BLACKLIST"></el-option>
							<el-option label="白名单" value="WHITELIST"></el-option>
							<el-option label="限流" value="LIMIT"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item 
						label="限流数量" 
						prop="limitCount"
						v-if="ruleForm.ruleType === 'LIMIT'"
					>
						<el-input-number 
							v-model="ruleForm.limitCount" 
							:min="1"
							placeholder="请输入限流数量"
							style="width: 100%;"
						></el-input-number>
						<div class="form-tip">每秒允许的最大请求数</div>
					</el-form-item>
					<el-form-item label="描述">
						<el-input 
							v-model="ruleForm.description" 
							type="textarea" 
							:rows="3"
							placeholder="请输入规则描述"
						></el-input>
					</el-form-item>
					<el-form-item label="状态">
						<el-radio-group v-model="ruleForm.status">
							<el-radio :label="1">启用</el-radio>
							<el-radio :label="0">禁用</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button @click="closeDialog">取消</el-button>
					<el-button 
						type="primary" 
						@click="submitForm"
						:loading="submitting"
					>
						确定
					</el-button>
				</div>
			</el-dialog>

			<!-- 批量导入对话框 -->
			<el-dialog 
				title="批量导入IP规则" 
				:visible.sync="importDialogVisible"
				width="500px"
			>
				<el-upload
					class="upload-demo"
					:action="uploadUrl"
					:on-success="handleImportSuccess"
					:on-error="handleImportError"
					:before-upload="beforeUpload"
					accept=".xlsx,.xls,.csv"
					:limit="1"
				>
					<el-button size="small" type="primary">点击上传</el-button>
					<div slot="tip" class="el-upload__tip">
						只能上传xlsx/xls/csv文件，且不超过10MB
					</div>
				</el-upload>
				<div class="import-options">
					<el-checkbox v-model="importOptions.overwrite">
						覆盖已存在的规则
					</el-checkbox>
				</div>
				<div slot="footer" class="dialog-footer">
					<el-button @click="importDialogVisible = false">关闭</el-button>
					<el-button type="primary" @click="downloadTemplate">
						下载模板
					</el-button>
				</div>
			</el-dialog>
		</div>
	</Layout>
</template>

<script>
import Layout from '../components/Layout.vue';
import validationRules from '@/utils/validation';

export default {
	name: 'IpBlackWhiteListManagement',
	components: {
		Layout,
	},
	beforeCreate() {
		// 注册验证规则到Vue实例
		this.$validation = validationRules;
	},
	data() {
		return {
			// 列表数据
			ipRules: [],
			selectedRules: [],
			loading: false,
			submitting: false,

			// 分页
			pagination: {
				current: 1,
				size: 20,
				total: 0,
			},

			// 搜索表单
			searchForm: {
				tenantId: '',
				ipValue: '',
				ruleType: '',
				status: null,
			},

			// 对话框
			dialogVisible: false,
			importDialogVisible: false,
			isEdit: false,

			// 表单数据
			ruleForm: {
				tenantId: '',
				ruleName: '',
				ipValue: '',
				ruleType: 'BLACKLIST',
				limitCount: null,
				description: '',
				status: 1,
			},

			// 表单验证规则
			formRules: {},

			// 当前列表类型
			currentListType: 'blacklist',

			// 导入选项
			importOptions: {
				overwrite: false,
			},

			// 上传地址
			uploadUrl: '/api/ip-flow-rules/import',
		};
	},

	computed: {
		dialogTitle() {
			return this.isEdit ? '编辑IP规则' : '新增IP规则';
		},

		// 动态计算表单验证规则
		computedFormRules() {
			const isWhitelist = this.currentListType === 'whitelist';
			const rules = isWhitelist ? 
				this.$validation.ipWhitelistRules : 
				this.$validation.ipBlacklistRules;
			
			const formRules = {
				tenantId: rules.tenantId,
				ruleName: rules.ruleName,
				ipValue: rules.ipValue,
				ruleType: rules.ruleType,
				limitCount: [
					{
						validator: (rule, value, callback) => {
							if (this.ruleForm.ruleType === 'LIMIT') {
								if (!value) {
									callback(new Error('请输入限制数量'));
								} else if (value <= 0) {
									callback(new Error('限制数量必须大于0'));
								} else {
									callback();
								}
							} else {
								callback();
							}
						},
						trigger: 'blur'
					}
				]
			};
			
			return formRules;
		},
	},

	mounted() {
		this.loadRules();
	},

	methods: {
		// 加载IP规则列表
		async loadRules() {
			this.loading = true;
			try {
				const params = {
					current: this.pagination.current,
					size: this.pagination.size,
					...this.searchForm,
				};

				const response = await this.$api.ipFlowRules.getList(params);
				if (response.data.success) {
					this.ipRules = response.data.data.records || [];
					this.pagination.total = response.data.data.total || 0;
				} else {
					this.$message.error('获取IP规则列表失败');
				}
			} catch (error) {
				console.error('Error loading IP rules:', error);
				this.$message.error('获取IP规则列表失败');
			} finally {
				this.loading = false;
			}
		},

		// 搜索规则
		searchRules() {
			this.pagination.current = 1;
			this.loadRules();
		},

		// 重置搜索
		resetSearch() {
			this.searchForm = {
				tenantId: '',
				ipValue: '',
				ruleType: '',
				status: null,
			};
			this.pagination.current = 1;
			this.loadRules();
		},

		// 刷新列表
		refreshList() {
			this.loadRules();
		},

		// 显示新增对话框
		showAddDialog() {
			this.isEdit = false;
			this.resetForm();
			this.dialogVisible = true;
		},

		// 编辑规则
		editRule(rule) {
			this.isEdit = true;
			this.ruleForm = { ...rule };
			this.dialogVisible = true;
		},

		// 删除规则
		async deleteRule(rule) {
			try {
				await this.$confirm(
					`确定要删除IP规则 "${rule.ruleName}" 吗？`,
					'确认删除',
					{
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning',
					}
				);

				const response = await this.$api.ipFlowRules.delete(rule.id);
				if (response.data.success) {
					this.$message.success('删除成功');
					this.loadRules();
				} else {
					this.$message.error('删除失败');
				}
			} catch (error) {
				if (error !== 'cancel') {
					console.error('Error deleting IP rule:', error);
					this.$message.error('删除失败');
				}
			}
		},

		// 切换状态
		async toggleStatus(rule) {
			try {
				const newStatus = rule.status === 1 ? 0 : 1;
				const response = await this.$api.ipFlowRules.updateStatus(rule.id, newStatus);
				if (response.data.success) {
					this.$message.success(`${newStatus === 1 ? '启用' : '禁用'}成功`);
					this.loadRules();
				} else {
					this.$message.error('状态更新失败');
				}
			} catch (error) {
				console.error('Error updating status:', error);
				this.$message.error('状态更新失败');
			}
		},

		// 批量删除
		async batchDelete() {
			if (this.selectedRules.length === 0) {
				this.$message.warning('请选择要删除的规则');
				return;
			}

			try {
				await this.$confirm(
					`确定要删除选中的 ${this.selectedRules.length} 条规则吗？`,
					'确认批量删除',
					{
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning',
					}
				);

				const ids = this.selectedRules.map(rule => rule.id);
				const response = await this.$api.ipFlowRules.batchDelete(ids);
				if (response.data.success) {
					this.$message.success('批量删除成功');
					this.loadRules();
				} else {
					this.$message.error('批量删除失败');
				}
			} catch (error) {
				if (error !== 'cancel') {
					console.error('Error batch deleting:', error);
					this.$message.error('批量删除失败');
				}
			}
		},

		// 导出规则
		async exportRules() {
			try {
				const response = await this.$api.ipFlowRules.export(this.searchForm);
				// 处理文件下载
				const blob = new Blob([response.data]);
				const url = window.URL.createObjectURL(blob);
				const a = document.createElement('a');
				a.href = url;
				a.download = `ip-rules-${new Date().getTime()}.xlsx`;
				a.click();
				window.URL.revokeObjectURL(url);
				this.$message.success('导出成功');
			} catch (error) {
				console.error('Error exporting rules:', error);
				this.$message.error('导出失败');
			}
		},

		// 显示导入对话框
		showImportDialog() {
			this.importDialogVisible = true;
		},

		// 下载模板
		downloadTemplate() {
			// 创建模板数据
			const templateData = [
				['租户ID', '规则名称', 'IP地址', '规则类型', '限流数量', '描述', '状态'],
				['tenant001', '示例黑名单规则', '*************', 'BLACKLIST', '', '示例描述', '1'],
				['tenant001', '示例白名单规则', '***********/24', 'WHITELIST', '', '示例描述', '1'],
				['tenant001', '示例限流规则', '***********-*************', 'LIMIT', '100', '示例描述', '1'],
			];

			// 创建CSV内容
			const csvContent = templateData.map(row => row.join(',')).join('\n');
			const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = 'ip-rules-template.csv';
			a.click();
			window.URL.revokeObjectURL(url);
		},

		// 上传前检查
		beforeUpload(file) {
			const isValidType = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'].includes(file.type);
			const isLt10M = file.size / 1024 / 1024 < 10;

			if (!isValidType) {
				this.$message.error('只能上传 Excel 或 CSV 文件!');
				return false;
			}
			if (!isLt10M) {
				this.$message.error('文件大小不能超过 10MB!');
				return false;
			}
			return true;
		},

		// 导入成功
		handleImportSuccess(response) {
			if (response.success) {
				this.$message.success(`导入成功，共导入 ${response.data.count} 条规则`);
				this.importDialogVisible = false;
				this.loadRules();
			} else {
				this.$message.error(response.message || '导入失败');
			}
		},

		// 导入失败
		handleImportError(error) {
			console.error('Import error:', error);
			this.$message.error('导入失败');
		},

		// 关闭对话框
		closeDialog() {
			this.dialogVisible = false;
			this.resetForm();
		},

		// 提交表单
		async submitForm() {
			try {
				await this.$refs.ruleForm.validate();
			} catch (error) {
				return;
			}

			this.submitting = true;
			try {
				let response;
				if (this.isEdit) {
					response = await this.$api.ipFlowRules.update(this.ruleForm.id, this.ruleForm);
				} else {
					response = await this.$api.ipFlowRules.create(this.ruleForm);
				}

				if (response.data.success) {
					this.$message.success(this.isEdit ? '更新成功' : '创建成功');
					this.closeDialog();
					this.loadRules();
				} else {
					this.$message.error(response.data.message || '操作失败');
				}
			} catch (error) {
				console.error('Error saving rule:', error);
				this.$message.error('操作失败');
			} finally {
				this.submitting = false;
			}
		},

		// 重置表单
		resetForm() {
			this.ruleForm = {
				tenantId: '',
				ruleName: '',
				ipValue: '',
				ruleType: 'BLACKLIST',
				limitCount: null,
				description: '',
				status: 1,
			};
			if (this.$refs.ruleForm) {
				this.$refs.ruleForm.clearValidate();
			}
		},

		// 处理选择变化
		handleSelectionChange(selection) {
			this.selectedRules = selection;
		},

		// 分页处理
		handleSizeChange(size) {
			this.pagination.size = size;
			this.pagination.current = 1;
			this.loadRules();
		},

		handleCurrentChange(current) {
			this.pagination.current = current;
			this.loadRules();
		},

		// 获取规则类型标签类型
		getRuleTypeTagType(ruleType) {
			switch (ruleType) {
				case 'BLACKLIST':
					return 'danger';
				case 'WHITELIST':
					return 'success';
				case 'LIMIT':
					return 'warning';
				default:
					return 'info';
			}
		},

		// 获取规则类型文本
		getRuleTypeText(ruleType) {
			switch (ruleType) {
				case 'BLACKLIST':
					return '黑名单';
				case 'WHITELIST':
					return '白名单';
				case 'LIMIT':
					return '限流';
				default:
					return ruleType;
			}
		},

		// 格式化日期
		formatDate(date) {
			if (!date) return '-';
			return new Date(date).toLocaleString('zh-CN');
		},
	},
};
</script>

<style scoped>
.ip-management-content {
	padding: 20px;
	max-width: 100%;
	width: 100%;
}

.page-header {
	margin-bottom: 20px;
}

.page-header h1 {
	margin: 0 0 8px 0;
	font-size: 24px;
	color: #303133;
}

.page-header p {
	margin: 0;
	color: #909399;
	font-size: 14px;
}

.toolbar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding: 16px;
	background: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left,
.toolbar-right {
	display: flex;
	gap: 8px;
}

.filter-bar {
	margin-bottom: 20px;
	padding: 16px;
	background: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
	margin: 0;
}

.table-container {
	background: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	margin-bottom: 20px;
}

.pagination-container {
	display: flex;
	justify-content: center;
	padding: 20px;
	background: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-tip {
	font-size: 12px;
	color: #909399;
	margin-top: 4px;
}

.text-danger {
	color: #f56c6c !important;
}

.text-warning {
	color: #e6a23c !important;
}

.text-success {
	color: #67c23a !important;
}

.import-options {
	margin-top: 16px;
	padding-top: 16px;
	border-top: 1px solid #ebeef5;
}

.upload-demo {
	margin-bottom: 16px;
}
</style>