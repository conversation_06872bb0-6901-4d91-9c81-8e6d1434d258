@import './variables.scss';
@import './responsive.scss';
@import './themes.scss';

// 重置样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: $font-size-base;
  color: $text-primary;
  background-color: $background-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: $background-light;
}

::-webkit-scrollbar-thumb {
  background: $border-base;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: $border-light;
}

// 通用类
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.margin-top-10 {
  margin-top: 10px;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.margin-left-10 {
  margin-left: 10px;
}

.margin-right-10 {
  margin-right: 10px;
}

.padding-10 {
  padding: 10px;
}

.padding-20 {
  padding: 20px;
}

// 页面容器（移动到响应式部分）
// 卡片样式（移动到响应式部分）
// 表格操作按钮（移动到响应式部分）

// 分页组件统一样式
.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
  
  @media (max-width: 768px) {
    text-align: center;
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination {
  margin-top: 20px;
  text-align: right;
  
  @media (max-width: 768px) {
    text-align: center;
  }
}

// 操作按钮容器通用样式
.action-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  min-width: 0; /* 防止flex子项收缩问题 */
  width: 100%;
}

.action-buttons .el-button {
  margin-left: 0 !important; /* 覆盖Element UI默认margin */
  margin-right: 0 !important;
  white-space: nowrap; /* 防止按钮文字换行 */
  min-width: 60px; /* 设置按钮最小宽度 */
  padding: 7px 15px !important; /* 增加按钮内边距 */
  flex-shrink: 0; /* 防止按钮收缩 */
  
  @media (max-width: 768px) {
    font-size: 12px;
    padding: 5px 10px !important;
    min-width: 50px;
  }
}

// 表格容器样式优化
.rules-table, .table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  // 确保表格和分页之间有适当间距
  .el-table {
    margin-bottom: 0;
  }
}

// 状态标签
.status-tag {
  &.success {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
    border: 1px solid rgba($success-color, 0.2);
  }
  
  &.warning {
    background-color: rgba($warning-color, 0.1);
    color: $warning-color;
    border: 1px solid rgba($warning-color, 0.2);
  }
  
  &.danger {
    background-color: rgba($danger-color, 0.1);
    color: $danger-color;
    border: 1px solid rgba($danger-color, 0.2);
  }
  
  &.info {
    background-color: rgba($info-color, 0.1);
    color: $info-color;
    border: 1px solid rgba($info-color, 0.2);
  }
}

// 响应式布局优化
.page-container {
  @include responsive-spacing(10px, 15px, 20px);
  min-height: calc(100vh - #{$header-height});
  
  @include max-width($breakpoint-sm - 1px) {
    padding: 10px;
  }
}

.card {
  background: $background-white;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-base;
  margin-bottom: 20px;
  
  @include responsive-spacing(15px, 18px, 20px);
  
  @include max-width($breakpoint-sm - 1px) {
    margin-bottom: 15px;
  }
}

// 响应式表格
.el-table {
  @include max-width($breakpoint-md - 1px) {
    .el-table__header {
      display: none;
    }
    
    .el-table__body {
      tr {
        display: block;
        border: 1px solid $border-lighter;
        margin-bottom: 10px;
        border-radius: $border-radius-base;
        
        td {
          display: block;
          text-align: left !important;
          border: none;
          padding: 8px 15px;
          
          &:before {
            content: attr(data-label) ': ';
            font-weight: bold;
            color: $text-secondary;
          }
        }
      }
    }
  }
}

// 响应式表单
.el-form {
  @include max-width($breakpoint-sm - 1px) {
    .el-form-item {
      .el-form-item__label {
        text-align: left;
        padding-right: 0;
        padding-bottom: 5px;
      }
      
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}

// 响应式按钮组
.table-actions {
  margin-bottom: 20px;
  
  .el-button {
    margin-right: 10px;
    margin-bottom: 10px;
    
    @include max-width($breakpoint-sm - 1px) {
      width: 100%;
      margin-right: 0;
    }
  }
  
  @include max-width($breakpoint-sm - 1px) {
    text-align: center;
  }
}

// 响应式卡片网格
.card-grid {
  @include responsive-grid(1, 2, 3);
  
  @include respond-to(xl) {
    grid-template-columns: repeat(4, 1fr);
  }
}

// 响应式统计卡片
.stats-card {
  text-align: center;
  
  .stats-number {
    @include responsive-font-size(24px, 28px, 32px);
    font-weight: bold;
    color: $primary-color;
  }
  
  .stats-label {
    @include responsive-font-size(12px, 14px, 16px);
    color: $text-secondary;
    margin-top: 5px;
  }
}

// Element UI 样式覆盖
.el-table {
  .el-table__header {
    th {
      background-color: $background-base;
      color: $text-primary;
      font-weight: 600;
    }
  }
}

.el-card {
  border: 1px solid $border-lighter;
  box-shadow: $box-shadow-light;
}

.el-menu {
  border-right: none;
}

.el-breadcrumb {
  font-size: $font-size-base;
}

// 动画效果
.fade-enter-active, .fade-leave-active {
  transition: $transition-fade;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all .3s ease;
}

.slide-fade-leave-active {
  transition: all .8s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter, .slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

// 操作按钮样式优化
.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  width: 100%;
  
  .el-button {
    margin: 0 !important;
    white-space: nowrap;
    min-width: 70px;
    padding: 8px 16px !important;
    flex-shrink: 0;
    font-size: 14px;
  }
  
  // 小屏幕优化
  @media (max-width: 768px) {
    gap: 8px;
    
    .el-button {
      padding: 6px 12px !important;
      font-size: 12px;
      min-width: 60px;
    }
  }
}

// 强制操作列宽度
.el-table {
  .el-table-column--selection {
    width: 55px !important;
    min-width: 55px !important;
  }
  
  // 操作列强制宽度
  th:last-child,
  td:last-child {
    &[class*="操作"] {
      width: 280px !important;
      min-width: 280px !important;
      max-width: 280px !important;
    }
  }
  
  // 通过列标签强制宽度
  .el-table__header th[aria-label*="操作"],
  .el-table__body td[class*="操作"] {
    width: 280px !important;
    min-width: 280px !important;
    max-width: 280px !important;
  }
}