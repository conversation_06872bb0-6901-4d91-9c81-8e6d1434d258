package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.Result;
import com.example.admin.dto.IPWhitelistDTO;
import com.example.admin.service.IPWhitelistService;
import com.example.admin.vo.IPWhitelistVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * IP白名单控制器
 * 提供IP白名单的CRUD操作、批量管理、文件导入导出、IP匹配检查等功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "IP白名单管理")
@RestController
@RequestMapping("/api/ip-whitelists")
@Validated
public class IPWhitelistController {

    private static final Logger log = LoggerFactory.getLogger(IPWhitelistController.class);

    @Autowired
    private IPWhitelistService ipWhitelistService;

    /**
     * 分页查询IP白名单
     */
    @Operation(summary = "分页查询IP白名单")
    @GetMapping
    public Result<Page<IPWhitelistVO>> pageIPWhitelists(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "名单名称") @RequestParam(required = false) String listName,
            @Parameter(description = "IP类型") @RequestParam(required = false) String ipType,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled) {
        try {
            Page<IPWhitelistVO> pageParam = new Page<>(page, size);
            Page<IPWhitelistVO> result = ipWhitelistService.selectIPWhitelistPage(
                pageParam, tenantId, listName, ipType, enabled);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询IP白名单失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询IP白名单
     */
    @Operation(summary = "根据ID查询IP白名单")
    @GetMapping("/{id}")
    public Result<IPWhitelistVO> getIPWhitelistById(
            @Parameter(description = "名单ID") @PathVariable @NotNull Long id) {
        try {
            IPWhitelistVO result = ipWhitelistService.getIPWhitelistById(id);
            if (result == null) {
                return Result.error("IP白名单不存在");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询IP白名单失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建IP白名单
     */
    @Operation(summary = "创建IP白名单")
    @PostMapping
    public Result<String> createIPWhitelist(
            @Parameter(description = "IP白名单信息") @RequestBody @Valid IPWhitelistDTO ipListDTO) {
        try {
            boolean result = ipWhitelistService.createIPWhitelist(ipListDTO);
            if (result) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建IP白名单失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新IP白名单
     */
    @Operation(summary = "更新IP白名单")
    @PutMapping("/{id}")
    public Result<String> updateIPWhitelist(
            @Parameter(description = "名单ID") @PathVariable @NotNull Long id,
            @Parameter(description = "IP白名单信息") @RequestBody @Valid IPWhitelistDTO ipListDTO) {
        try {
            boolean result = ipWhitelistService.updateIPWhitelist(id, ipListDTO);
            if (result) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新IP白名单失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除IP白名单
     */
    @Operation(summary = "删除IP白名单")
    @DeleteMapping("/{id}")
    public Result<String> deleteIPWhitelist(
            @Parameter(description = "名单ID") @PathVariable @NotNull Long id) {
        try {
            boolean result = ipWhitelistService.deleteIPWhitelist(id);
            if (result) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除IP白名单失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除IP白名单
     */
    @Operation(summary = "批量删除IP白名单")
    @DeleteMapping("/batch")
    public Result<String> batchDeleteIPWhitelists(
            @Parameter(description = "名单ID列表") @RequestBody @NotEmpty List<Long> ids) {
        try {
            boolean result = ipWhitelistService.batchDeleteIPWhitelists(ids);
            if (result) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除IP白名单失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用IP白名单
     */
    @Operation(summary = "启用/禁用IP白名单")
    @PutMapping("/{id}/status")
    public Result<String> updateIPWhitelistStatus(
            @Parameter(description = "名单ID") @PathVariable @NotNull Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer enabled) {
        try {
            boolean result;
            if (enabled == 1) {
                result = ipWhitelistService.enableIPWhitelist(id);
            } else {
                result = ipWhitelistService.disableIPWhitelist(id);
            }
            if (result) {
                String message = enabled == 1 ? "启用成功" : "禁用成功";
                return Result.success(message);
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("更新IP白名单状态失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 批量启用/禁用IP白名单
     */
    @Operation(summary = "批量启用/禁用IP白名单")
    @PutMapping("/batch/status")
    public Result<String> batchUpdateIPWhitelistStatus(
            @Parameter(description = "名单ID列表") @RequestBody @NotEmpty List<Long> ids,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam @NotNull Integer enabled) {
        try {
            boolean result = ipWhitelistService.batchUpdateEnabled(ids, enabled);
            if (result) {
                String message = enabled == 1 ? "批量启用成功" : "批量禁用成功";
                return Result.success(message);
            } else {
                return Result.error("批量操作失败");
            }
        } catch (Exception e) {
            log.error("批量更新IP白名单状态失败", e);
            return Result.error("批量操作失败: " + e.getMessage());
        }
    }

    /**
     * 根据租户ID查询IP白名单
     */
    @Operation(summary = "根据租户ID查询IP白名单")
    @GetMapping("/tenant/{tenantId}")
    public Result<List<IPWhitelistVO>> getIPWhitelistsByTenantId(
            @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled,
            @Parameter(description = "限制数量") @RequestParam(required = false) Integer limit) {
        try {
            List<IPWhitelistVO> result = ipWhitelistService.getIPWhitelistsByTenantId(
                tenantId, enabled, limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据租户ID查询IP白名单失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建IP白名单
     */
    @Operation(summary = "批量创建IP白名单")
    @PostMapping("/batch")
    public Result<String> batchCreateIPWhitelists(
            @Parameter(description = "IP白名单列表") @RequestBody @NotEmpty List<IPWhitelistDTO> ipListDTOList) {
        try {
            boolean result = ipWhitelistService.batchCreateIPWhitelists(ipListDTOList);
            if (result) {
                return Result.success("批量创建成功，共创建" + ipListDTOList.size() + "条名单");
            } else {
                return Result.error("批量创建失败");
            }
        } catch (Exception e) {
            log.error("批量创建IP白名单失败", e);
            return Result.error("批量创建失败: " + e.getMessage());
        }
    }

    /**
     * 复制IP白名单
     */
    @Operation(summary = "复制IP白名单")
    @PostMapping("/{id}/copy")
    public Result<String> copyIPWhitelist(
            @Parameter(description = "源名单ID") @PathVariable @NotNull Long id,
            @Parameter(description = "新名单名称") @RequestParam @NotNull String newListName,
            @Parameter(description = "目标租户ID") @RequestParam(required = false) String targetTenantId) {
        try {
            boolean result = ipWhitelistService.copyIPWhitelist(id, newListName, targetTenantId);
            if (result) {
                return Result.success("复制成功");
            } else {
                return Result.error("复制失败");
            }
        } catch (Exception e) {
            log.error("复制IP白名单失败", e);
            return Result.error("复制失败: " + e.getMessage());
        }
    }

    /**
     * 批量复制到其他租户
     */
    @Operation(summary = "批量复制到其他租户")
    @PostMapping("/batch/copy")
    public Result<Map<String, Object>> batchCopyToTenant(
            @Parameter(description = "源名单ID列表") @RequestBody @NotEmpty List<Long> sourceIds,
            @Parameter(description = "目标租户ID") @RequestParam @NotNull String targetTenantId,
            @Parameter(description = "名称前缀") @RequestParam(defaultValue = "copy_") String namePrefix) {
        try {
            Map<String, Object> result = ipWhitelistService.batchCopyToTenant(
                sourceIds, targetTenantId, namePrefix);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量复制IP白名单失败", e);
            return Result.error("批量复制失败: " + e.getMessage());
        }
    }

    /**
     * 导入IP白名单
     */
    @Operation(summary = "导入IP白名单")
    @PostMapping("/import")
    public Result<Map<String, Object>> importIPWhitelists(
            @Parameter(description = "IP白名单列表") @RequestBody @NotEmpty List<IPWhitelistDTO> ipListDTOList,
            @Parameter(description = "是否覆盖已存在的名单") @RequestParam(defaultValue = "false") boolean overwrite) {
        try {
            Map<String, Object> result = ipWhitelistService.importIPWhitelists(ipListDTOList, overwrite);
            return Result.success(result);
        } catch (Exception e) {
            log.error("导入IP白名单失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出IP白名单
     */
    @Operation(summary = "导出IP白名单")
    @GetMapping("/export")
    public Result<List<IPWhitelistDTO>> exportIPWhitelists(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled) {
        try {
            List<IPWhitelistDTO> result = ipWhitelistService.exportIPWhitelists(
                tenantId, enabled);
            return Result.success(result);
        } catch (Exception e) {
            log.error("导出IP白名单失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    /**
     * 从文件导入IP地址
     */
    @Operation(summary = "从文件导入IP地址")
    @PostMapping("/import/file")
    public Result<Map<String, Object>> importIPsFromFile(
            @Parameter(description = "IP地址文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "租户ID") @RequestParam @NotNull String tenantId,
            @Parameter(description = "名单名称") @RequestParam @NotNull String listName,
            @Parameter(description = "IP类型") @RequestParam @NotNull String ipType,
            @Parameter(description = "优先级") @RequestParam(defaultValue = "100") Integer priority) {
        try {
            if (file.isEmpty()) {
                return Result.error("文件不能为空");
            }
            
            Map<String, Object> result = ipWhitelistService.importIPsFromFile(
                file, tenantId, listName, ipType, priority);
            return Result.success(result);
        } catch (Exception e) {
            log.error("从文件导入IP地址失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出IP地址到文件
     */
    @Operation(summary = "导出IP地址到文件")
    @GetMapping("/export/file")
    public ResponseEntity<String> exportIPsToFile(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled,
            @Parameter(description = "文件格式") @RequestParam(defaultValue = "txt") String format) {
        try {
            String content = ipWhitelistService.exportIPsToFile(tenantId, enabled, format);
            
            String filename = "ip_whitelist_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".txt";
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE + ";charset=UTF-8")
                .body(content);
        } catch (Exception e) {
            log.error("导出IP地址到文件失败", e);
            return ResponseEntity.badRequest()
                .body("导出失败: " + e.getMessage());
        }
    }

    /**
     * 验证IP白名单
     */
    @Operation(summary = "验证IP白名单")
    @PostMapping("/validate")
    public Result<Map<String, Object>> validateIPWhitelist(
            @Parameter(description = "IP白名单信息") @RequestBody @Valid IPWhitelistDTO ipListDTO) {
        try {
            Map<String, Object> result = ipWhitelistService.validateIPWhitelist(ipListDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("验证IP白名单失败", e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取启用状态统计
     */
    @Operation(summary = "获取启用状态统计")
    @GetMapping("/statistics/enabled")
    public Result<List<Map<String, Object>>> getEnabledStatistics(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        try {
            List<Map<String, Object>> result = ipWhitelistService.getEnabledStatistics(tenantId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取启用状态统计失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取IP类型统计
     */
    @Operation(summary = "获取IP类型统计")
    @GetMapping("/statistics/type")
    public Result<List<Map<String, Object>>> getIPTypeStatistics(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        try {
            List<Map<String, Object>> result = ipWhitelistService.getIPTypeStatistics(tenantId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取IP类型统计失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户名单分布统计
     */
    @Operation(summary = "获取租户名单分布统计")
    @GetMapping("/statistics/tenant")
    public Result<List<Map<String, Object>>> getTenantIPStatistics(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> result = ipWhitelistService.getTenantIPStatistics(limit);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取租户名单分布统计失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取最大优先级
     */
    @Operation(summary = "获取最大优先级")
    @GetMapping("/max-priority")
    public Result<Integer> getMaxPriority(
            @Parameter(description = "租户ID") @RequestParam @NotNull String tenantId) {
        try {
            Integer result = ipWhitelistService.getMaxPriority(tenantId);
            return Result.success(result != null ? result : 0);
        } catch (Exception e) {
            log.error("获取最大优先级失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按优先级范围查询白名单
     */
    @Operation(summary = "按优先级范围查询白名单")
    @GetMapping("/priority-range")
    public Result<List<IPWhitelistVO>> getWhitelistsByPriorityRange(
            @Parameter(description = "租户ID") @RequestParam @NotNull String tenantId,
            @Parameter(description = "最小优先级") @RequestParam @NotNull Integer minPriority,
            @Parameter(description = "最大优先级") @RequestParam @NotNull Integer maxPriority,
            @Parameter(description = "启用状态") @RequestParam(required = false) Integer enabled) {
        try {
            List<IPWhitelistVO> result = ipWhitelistService.getWhitelistsByPriorityRange(
                tenantId, minPriority, maxPriority, enabled);
            return Result.success(result);
        } catch (Exception e) {
            log.error("按优先级范围查询白名单失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取即将过期的白名单
     */
    @Operation(summary = "获取即将过期的白名单")
    @GetMapping("/expiring")
    public Result<List<IPWhitelistVO>> getExpiringWhitelists(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId,
            @Parameter(description = "小时数") @RequestParam(defaultValue = "24") Integer hours) {
        try {
            List<IPWhitelistVO> result = ipWhitelistService.getExpiringWhitelists(tenantId, hours);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取即将过期的白名单失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 禁用过期白名单
     */
    @Operation(summary = "禁用过期白名单")
    @PutMapping("/disable-expired")
    public Result<String> disableExpiredWhitelists() {
        try {
            int count = ipWhitelistService.disableExpiredWhitelists();
            return Result.success("成功禁用" + count + "条过期白名单");
        } catch (Exception e) {
            log.error("禁用过期白名单失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前有效白名单
     */
    @Operation(summary = "获取当前有效白名单")
    @GetMapping("/valid")
    public Result<List<IPWhitelistVO>> getValidWhitelists(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        try {
            List<IPWhitelistVO> result = ipWhitelistService.getValidWhitelists(tenantId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取当前有效白名单失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取IP白名单汇总
     */
    @Operation(summary = "获取IP白名单汇总")
    @GetMapping("/summary")
    public Result<Map<String, Object>> getIPWhitelistSummary(
            @Parameter(description = "租户ID") @RequestParam(required = false) String tenantId) {
        try {
            Map<String, Object> result = ipWhitelistService.getTenantIPSummary(tenantId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取IP白名单汇总失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取全局白名单汇总
     */
    @Operation(summary = "获取全局白名单汇总")
    @GetMapping("/summary/global")
    public Result<Map<String, Object>> getGlobalWhitelistSummary() {
        try {
            Map<String, Object> result = ipWhitelistService.getGlobalIPSummary();
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取全局白名单汇总失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 同步白名单到网关
     */
    @Operation(summary = "同步白名单到网关")
    @PostMapping("/sync/{tenantId}")
    public Result<String> syncWhitelistsToGateway(
            @Parameter(description = "租户ID") @PathVariable @NotNull String tenantId) {
        try {
            // 获取该租户的所有有效名单并同步到网关
            List<IPWhitelistVO> lists = ipWhitelistService.getValidWhitelists(tenantId);
            // 这里应该调用网关的名单同步接口
            return Result.success("同步成功，共同步" + lists.size() + "条白名单");
        } catch (Exception e) {
            log.error("同步白名单到网关失败", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 检查IP地址是否匹配白名单
     */
    @Operation(summary = "检查IP地址是否匹配白名单")
    @GetMapping("/check")
    public Result<Map<String, Object>> checkIPMatch(
            @Parameter(description = "IP地址") @RequestParam @NotNull String ipAddress,
            @Parameter(description = "租户ID") @RequestParam @NotNull String tenantId) {
        try {
            Map<String, Object> result = ipWhitelistService.checkIPMatch(ipAddress, tenantId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("检查IP白名单匹配失败", e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 批量IP地址匹配检查
     */
    @Operation(summary = "批量IP地址匹配检查")
    @PostMapping("/batch-check")
    public Result<List<Map<String, Object>>> batchCheckIPMatch(
            @Parameter(description = "租户ID") @RequestParam @NotNull String tenantId,
            @Parameter(description = "IP地址列表") @RequestBody @NotEmpty List<String> ipAddresses) {
        try {
            List<Map<String, Object>> result = ipWhitelistService.batchCheckIPMatch(tenantId, ipAddresses);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量检查IP白名单匹配失败", e);
            return Result.error("批量检查失败: " + e.getMessage());
        }
    }
}