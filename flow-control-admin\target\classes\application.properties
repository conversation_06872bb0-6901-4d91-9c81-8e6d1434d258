# Server configuration
server.port=8081
server.servlet.context-path=/
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# Spring application configuration
spring.application.name=flow-control-admin

# 修复中文乱码问题
spring.messages.encoding=UTF-8
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
spring.http.encoding.force-request=true
spring.http.encoding.force-response=true
# Windows控制台使用GBK编码，文件使用UTF-8
logging.charset.console=GBK
logging.charset.file=UTF-8

# 数据库连接编码
spring.datasource.hikari.connection-init-sql=SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci

# JSON编码配置
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.serialization.write-null-map-values=false

# Datasource configuration
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=***************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=aids520a

# SQL initialization
spring.sql.init.continue-on-error=true
# 强制重新初始化数据库表
spring.jpa.hibernate.ddl-auto=none

# Druid configuration
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=20
spring.datasource.druid.max-wait=60000
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.druid.filters=stat,wall,slf4j
spring.datasource.druid.connection-properties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.web-stat-filter.url-pattern=/*
spring.datasource.druid.web-stat-filter.exclusions=*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
spring.datasource.druid.stat-view-servlet.reset-enable=false
spring.datasource.druid.stat-view-servlet.login-username=admin
spring.datasource.druid.stat-view-servlet.login-password=admin123
spring.datasource.druid.stat-view-servlet.allow=
spring.datasource.druid.stat-view-servlet.deny=

# Redis configuration
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.database=0
spring.data.redis.timeout=10000ms
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-wait=-1ms
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0

# Jackson configuration
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.deserialization.fail-on-unknown-properties=false

# Nacos configuration - 完全禁用以便独立测试
spring.cloud.nacos.config.enabled=false
spring.cloud.nacos.discovery.enabled=false
# spring.cloud.nacos.config.server-addr=${NACOS_SERVER_ADDR:localhost:8848}
# spring.cloud.nacos.config.namespace=${NACOS_NAMESPACE:}
# spring.cloud.nacos.config.group=${NACOS_GROUP:DEFAULT_GROUP}
# spring.cloud.nacos.config.file-extension=yml
# spring.cloud.nacos.config.import-check.enabled=false
# spring.cloud.nacos.discovery.server-addr=${NACOS_SERVER_ADDR:localhost:8848}
# spring.cloud.nacos.discovery.namespace=${NACOS_NAMESPACE:}
# spring.cloud.nacos.discovery.group=${NACOS_GROUP:DEFAULT_GROUP}
# 临时禁用Nacos注册以便独立测试
# spring.cloud.nacos.discovery.register-enabled=false

# Sentinel configuration
spring.cloud.sentinel.transport.dashboard=localhost:8080
spring.cloud.sentinel.transport.port=8719
# spring.cloud.sentinel.datasource.nacos.server-addr=${spring.cloud.nacos.config.server-addr}
# spring.cloud.sentinel.datasource.nacos.namespace=${spring.cloud.nacos.config.namespace}
# spring.cloud.sentinel.datasource.nacos.group-id=SENTINEL_GROUP
# spring.cloud.sentinel.datasource.nacos.data-id=${spring.application.name}-flow-rules
# spring.cloud.sentinel.datasource.nacos.rule-type=flow

# MyBatis Plus configuration
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.example.common.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.call-setters-on-nulls=true
mybatis-plus.configuration.jdbc-type-for-null=null
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.id-type=AUTO
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0
mybatis-plus.global-config.db-config.table-underline=true

# Management endpoints configuration
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.sentinel.enabled=true

# Logging configuration
logging.level.com.example.admin=DEBUG
logging.level.com.alibaba.csp.sentinel=INFO
logging.level.org.springframework.cloud.alibaba=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n
logging.file.name=logs/flow-control-admin.log
logging.logback.rollingpolicy.max-file-size=100MB
logging.logback.rollingpolicy.max-history=30

# Custom flow-control configuration
flow-control.nacos.group=FLOW_CONTROL_GROUP
flow-control.nacos.timeout=3000
flow-control.default.qps-threshold=100
flow-control.default.thread-threshold=10
flow-control.monitor.enabled=true
flow-control.monitor.interval=60
flow-control.monitor.retention-days=30
flow-control.cache.rule-ttl=300
flow-control.cache.stats-ttl=60