{"test_time": "2025-08-31T15:19:39.802309", "total_tests": 4, "passed_tests": 2, "failed_tests": 2, "results": [{"test_type": "tenant_level", "rule_config": {"id": 1, "tenant_id": "tenant1", "rule_name": "租户1线程限流", "ref_resource": null, "grade": 0, "count": 5, "control_behavior": 0, "enabled": 1, "description": "租户1的线程数限流规则"}, "total_requests": 2803, "success_requests": 2267, "blocked_requests": 536, "error_requests": 0, "avg_response_time": 0.028082255078688972, "test_passed": false, "success_rate": 0.808776311095255, "block_rate": 0.19122368890474492}, {"test_type": "tenant_resource_level", "rule_config": {"id": 2, "tenant_id": "tenant1", "rule_name": "租户1接口限流", "ref_resource": "userInfo", "grade": 1, "count": 10, "control_behavior": 0, "enabled": 1, "description": "租户1的接口QPS限流规则"}, "total_requests": 672, "success_requests": 665, "blocked_requests": 7, "error_requests": 0, "avg_response_time": 0.019120651341619947, "test_passed": true, "success_rate": 0.9895833333333334, "block_rate": 0.010416666666666666}, {"test_type": "tenant_level", "rule_config": {"id": 3, "tenant_id": "tenant2", "rule_name": "租户2QPS限流", "ref_resource": null, "grade": 1, "count": 20, "control_behavior": 0, "enabled": 1, "description": "租户2的QPS限流规则"}, "total_requests": 680, "success_requests": 675, "blocked_requests": 5, "error_requests": 0, "avg_response_time": 0.017918299576815438, "test_passed": true, "success_rate": 0.9926470588235294, "block_rate": 0.007352941176470588}, {"test_type": "tenant_level", "rule_config": {"id": 4, "tenant_id": "tenant3", "rule_name": "租户3线程限流", "ref_resource": null, "grade": 0, "count": 3, "control_behavior": 0, "enabled": 1, "description": "租户3的线程数限流规则"}, "total_requests": 2972, "success_requests": 2472, "blocked_requests": 500, "error_requests": 0, "avg_response_time": 0.026348759556843487, "test_passed": false, "success_rate": 0.8317631224764468, "block_rate": 0.16823687752355315}]}