{"name": "flow-control-vue-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@types/node": "^20.0.0", "axios": "^1.6.0", "core-js": "^3.8.3", "echarts": "^6.0.0", "element-ui": "^2.15.13", "file-saver": "^2.0.5", "typescript": "^4.9.5", "v-charts": "^1.19.0", "vue": "^2.6.14", "vue-class-component": "^7.2.3", "vue-i18n": "^8.28.2", "vue-property-decorator": "^9.1.2", "vue-router": "^3.5.4", "vuex": "^3.6.2", "webpack": "^5.88.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.90.0", "sass-loader": "^16.0.5", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"vue/multi-word-component-names": "off", "no-unused-vars": "warn"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "main": "babel.config.js", "keywords": [], "author": "", "license": "ISC", "description": ""}