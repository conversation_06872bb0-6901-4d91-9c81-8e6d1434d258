2025-08-27 09:25:50.246 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-27 09:25:50.307 [main] INFO  com.example.admin.AdminApplication - Starting AdminApplication using Java 21.0.7 with PID 25372 (D:\java\openplatform\flow-control-admin\target\classes started by oywp2 in D:\java\openplatform\flow-control-admin)
2025-08-27 09:25:50.308 [main] DEBUG com.example.admin.AdminApplication - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-27 09:25:50.308 [main] INFO  com.example.admin.AdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 09:25:51.389 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-27 09:25:51.392 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-27 09:25:51.420 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-08-27 09:25:52.076 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-08-27 09:25:52.087 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-27 09:25:52.089 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 09:25:52.089 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-27 09:25:52.188 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-27 09:25:52.189 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1813 ms
2025-08-27 09:25:52.290 [main] INFO  com.example.admin.config.WebConfig - CORS configuration completed
2025-08-27 09:25:52.390 [main] INFO  com.example.admin.config.MybatisPlusConfig - MyBatis Plus plugin configuration completed
2025-08-27 09:25:52.939 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchUpdateEnabled] is ignored, because it exists, maybe from xml file
2025-08-27 09:25:52.944 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.countByTenantIdAndRuleName] is ignored, because it exists, maybe from xml file
2025-08-27 09:25:52.946 [main] ERROR c.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.admin.mapper.TenantFlowRuleMapper.batchDelete] is ignored, because it exists, maybe from xml file
2025-08-27 09:25:53.707 [main] INFO  com.example.admin.config.RedisConfig - RedisTemplate configuration completed
2025-08-27 09:25:53.743 [main] INFO  com.example.admin.config.RedisConfig - Redis cache manager configuration completed
2025-08-27 09:25:53.744 [main] INFO  com.example.admin.config.RedisConfig - Flow control rules RedisTemplate configuration completed
2025-08-27 09:25:53.746 [main] INFO  com.example.admin.config.RedisConfig - Monitoring data RedisTemplate configuration completed
2025-08-27 09:25:53.746 [main] INFO  com.example.admin.config.RedisConfig - System configuration RedisTemplate configuration completed
2025-08-27 09:25:53.833 [main] INFO  com.example.admin.config.WebConfig - Content negotiation configuration completed
2025-08-27 09:25:53.842 [main] INFO  com.example.admin.config.WebConfig - Path matching configuration completed
2025-08-27 09:25:53.857 [main] INFO  com.example.admin.config.WebConfig - Interceptor configuration completed
2025-08-27 09:25:53.939 [main] INFO  com.example.admin.config.WebConfig - View controller configuration completed
2025-08-27 09:25:53.949 [main] INFO  com.example.admin.config.WebConfig - ObjectMapper configuration completed with camelCase naming strategy
2025-08-27 09:25:53.965 [main] INFO  com.example.admin.config.WebConfig - Message converter configuration completed
2025-08-27 09:25:53.970 [main] INFO  com.example.admin.config.WebConfig - Static resource handling configuration completed
2025-08-27 09:25:54.439 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 14 endpoint(s) beneath base path '/actuator'
2025-08-27 09:25:54.536 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-27 09:25:54.576 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-08-27 09:25:54.588 [main] INFO  com.example.admin.AdminApplication - Started AdminApplication in 4.862 seconds (process running for 5.378)
2025-08-27 09:26:31.959 [http-nio-8081-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 09:26:31.960 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 09:26:31.961 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-27 09:26:31.985 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 0:0:0:0:0:0:0:1
2025-08-27 09:26:34.042 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2057ms
2025-08-27 09:26:34.042 [http-nio-8081-exec-1] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2057ms
2025-08-27 09:27:16.581 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:16.665 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:16.759 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:16.847 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:16.960 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:17.054 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:17.150 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:17.271 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:17.364 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:17.474 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:17.553 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:17.654 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:17.761 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:17.877 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:17.972 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:18.061 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:18.160 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:18.279 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:18.385 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:18.478 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:42.406 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:42.492 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:42.585 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:42.692 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:42.785 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:42.878 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:42.987 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:43.094 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:43.201 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:43.296 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:43.401 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:43.483 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:43.602 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:43.693 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:43.801 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:43.885 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.002 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.088 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.190 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.314 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.407 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.417 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:27:44.417 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:27:44.493 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2001ms
2025-08-27 09:27:44.494 [http-nio-8081-exec-22] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2001ms
2025-08-27 09:27:44.514 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.586 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2001ms
2025-08-27 09:27:44.587 [http-nio-8081-exec-23] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2001ms
2025-08-27 09:27:44.605 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.697 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:27:44.697 [http-nio-8081-exec-24] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:27:44.701 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.791 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:27:44.791 [http-nio-8081-exec-25] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:27:44.811 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.885 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:44.885 [http-nio-8081-exec-26] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:44.894 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:44.997 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:27:44.998 [http-nio-8081-exec-27] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:27:45.010 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:45.095 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:45.103 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:27:45.103 [http-nio-8081-exec-28] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:27:45.210 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:27:45.212 [http-nio-8081-exec-29] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:27:45.216 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:45.293 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:45.304 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:27:45.304 [http-nio-8081-exec-30] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:27:45.395 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:45.412 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:27:45.412 [http-nio-8081-exec-31] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:27:45.492 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:27:45.492 [http-nio-8081-exec-32] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:27:45.515 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:45.596 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:45.615 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:27:45.616 [http-nio-8081-exec-33] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:27:45.695 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:45.706 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:27:45.706 [http-nio-8081-exec-34] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:27:45.814 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:27:45.814 [http-nio-8081-exec-35] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:27:45.818 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:45.892 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:45.892 [http-nio-8081-exec-36] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:45.912 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:45.998 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:46.013 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:27:46.015 [http-nio-8081-exec-37] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:27:46.092 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:27:46.092 [http-nio-8081-exec-38] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:27:46.098 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:46.199 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:27:46.199 [http-nio-8081-exec-39] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:27:46.219 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:46.312 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:46.323 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:27:46.323 [http-nio-8081-exec-40] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:27:46.397 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:46.414 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:46.414 [http-nio-8081-exec-41] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:46.511 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:46.521 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:46.521 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:46.582 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30001ms
2025-08-27 09:27:46.582 [http-nio-8081-exec-4] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30001ms
2025-08-27 09:27:46.614 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:27:46.615 [http-nio-8081-exec-22] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:27:46.619 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:46.677 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30012ms
2025-08-27 09:27:46.677 [http-nio-8081-exec-3] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30012ms
2025-08-27 09:27:46.708 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:46.708 [http-nio-8081-exec-23] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:46.714 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:46.784 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30025ms
2025-08-27 09:27:46.790 [http-nio-8081-exec-7] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30025ms
2025-08-27 09:27:46.838 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2026ms
2025-08-27 09:27:46.848 [http-nio-8081-exec-24] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2026ms
2025-08-27 09:27:46.851 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30004ms
2025-08-27 09:27:46.851 [http-nio-8081-exec-6] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30004ms
2025-08-27 09:27:46.854 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:46.912 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2018ms
2025-08-27 09:27:46.912 [http-nio-8081-exec-25] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2018ms
2025-08-27 09:27:46.919 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:46.973 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30013ms
2025-08-27 09:27:46.973 [http-nio-8081-exec-5] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30013ms
2025-08-27 09:27:47.016 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:47.020 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:27:47.020 [http-nio-8081-exec-26] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:27:47.066 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30012ms
2025-08-27 09:27:47.066 [http-nio-8081-exec-10] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30012ms
2025-08-27 09:27:47.098 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:27:47.099 [http-nio-8081-exec-27] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:27:47.134 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:47.160 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30010ms
2025-08-27 09:27:47.160 [http-nio-8081-exec-9] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30010ms
2025-08-27 09:27:47.221 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:27:47.221 [http-nio-8081-exec-28] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:27:47.225 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:47.283 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30012ms
2025-08-27 09:27:47.283 [http-nio-8081-exec-8] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30012ms
2025-08-27 09:27:47.298 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:27:47.298 [http-nio-8081-exec-29] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:27:47.313 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:47.375 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30011ms
2025-08-27 09:27:47.375 [http-nio-8081-exec-1] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30011ms
2025-08-27 09:27:47.406 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:27:47.406 [http-nio-8081-exec-30] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:27:47.483 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30009ms
2025-08-27 09:27:47.484 [http-nio-8081-exec-2] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30009ms
2025-08-27 09:27:47.530 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:27:47.530 [http-nio-8081-exec-31] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:27:47.561 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30008ms
2025-08-27 09:27:47.561 [http-nio-8081-exec-11] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30008ms
2025-08-27 09:27:47.608 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:27:47.608 [http-nio-8081-exec-32] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:27:47.669 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30015ms
2025-08-27 09:27:47.669 [http-nio-8081-exec-12] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30015ms
2025-08-27 09:27:47.700 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:27:47.700 [http-nio-8081-exec-33] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:27:47.777 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30016ms
2025-08-27 09:27:47.777 [http-nio-8081-exec-13] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30016ms
2025-08-27 09:27:47.823 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:27:47.823 [http-nio-8081-exec-34] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:27:47.886 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30009ms
2025-08-27 09:27:47.886 [http-nio-8081-exec-14] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30009ms
2025-08-27 09:27:47.918 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:27:47.918 [http-nio-8081-exec-35] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:27:47.977 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30005ms
2025-08-27 09:27:47.977 [http-nio-8081-exec-15] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30005ms
2025-08-27 09:27:48.009 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:27:48.009 [http-nio-8081-exec-36] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:27:48.070 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30009ms
2025-08-27 09:27:48.070 [http-nio-8081-exec-16] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30009ms
2025-08-27 09:27:48.101 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:27:48.101 [http-nio-8081-exec-37] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:27:48.161 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30001ms
2025-08-27 09:27:48.161 [http-nio-8081-exec-17] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30001ms
2025-08-27 09:27:48.223 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:27:48.223 [http-nio-8081-exec-38] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:27:48.284 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30005ms
2025-08-27 09:27:48.284 [http-nio-8081-exec-18] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30005ms
2025-08-27 09:27:48.315 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:27:48.316 [http-nio-8081-exec-39] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:27:48.393 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30008ms
2025-08-27 09:27:48.393 [http-nio-8081-exec-19] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30008ms
2025-08-27 09:27:48.408 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:27:48.408 [http-nio-8081-exec-40] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:27:48.484 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 30006ms
2025-08-27 09:27:48.485 [http-nio-8081-exec-20] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 30006ms
2025-08-27 09:27:48.515 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:27:48.516 [http-nio-8081-exec-41] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:27:48.624 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:27:48.625 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:27:48.718 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:27:48.719 [http-nio-8081-exec-4] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:27:48.856 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:27:48.856 [http-nio-8081-exec-22] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:27:48.932 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:27:48.932 [http-nio-8081-exec-3] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:27:49.026 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:27:49.026 [http-nio-8081-exec-23] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:27:49.149 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:27:49.150 [http-nio-8081-exec-7] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:27:49.242 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2017ms
2025-08-27 09:27:49.242 [http-nio-8081-exec-24] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2017ms
2025-08-27 09:27:49.317 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:27:49.317 [http-nio-8081-exec-6] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:27:49.352 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:49.444 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:49.530 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:49.645 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:49.755 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:49.848 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:49.943 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:50.055 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:50.135 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:50.254 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:50.347 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:50.456 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:50.549 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:50.639 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:50.753 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:50.839 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:50.940 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:51.064 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:51.142 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:51.265 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:51.358 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:51.370 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2018ms
2025-08-27 09:27:51.370 [http-nio-8081-exec-25] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2018ms
2025-08-27 09:27:51.446 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:27:51.446 [http-nio-8081-exec-5] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:27:51.466 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:51.541 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:27:51.541 [http-nio-8081-exec-26] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:27:51.560 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:51.647 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:27:51.648 [http-nio-8081-exec-10] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:27:51.669 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:51.747 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:51.757 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:27:51.758 [http-nio-8081-exec-27] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:27:51.852 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:27:51.852 [http-nio-8081-exec-9] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:27:51.856 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:51.945 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:27:51.945 [http-nio-8081-exec-28] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:27:51.965 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:52.068 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:27:52.068 [http-nio-8081-exec-8] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:27:52.073 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:52.143 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:27:52.144 [http-nio-8081-exec-29] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:27:52.149 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:52.252 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:52.269 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:27:52.269 [http-nio-8081-exec-1] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:27:52.362 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:27:52.362 [http-nio-8081-exec-30] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:27:52.366 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:52.468 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:27:52.469 [http-nio-8081-exec-2] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:27:52.474 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:52.562 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:27:52.562 [http-nio-8081-exec-31] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:27:52.566 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:52.654 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:27:52.654 [http-nio-8081-exec-11] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:27:52.675 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:52.763 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:27:52.763 [http-nio-8081-exec-32] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:27:52.770 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:52.842 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:27:52.842 [http-nio-8081-exec-12] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:27:52.861 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:52.948 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:27:52.948 [http-nio-8081-exec-33] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:27:52.951 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.050 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.071 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:53.071 [http-nio-8081-exec-13] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:53.148 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:27:53.148 [http-nio-8081-exec-34] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:27:53.153 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.262 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.272 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:53.272 [http-nio-8081-exec-14] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:53.354 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.365 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:53.366 [http-nio-8081-exec-35] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:53.453 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.471 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:27:53.472 [http-nio-8081-exec-15] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:27:53.556 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.563 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:27:53.563 [http-nio-8081-exec-36] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:27:53.672 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:27:53.672 [http-nio-8081-exec-16] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:27:53.677 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.762 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:27:53.762 [http-nio-8081-exec-37] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:27:53.769 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.857 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.870 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2014ms
2025-08-27 09:27:53.870 [http-nio-8081-exec-17] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2014ms
2025-08-27 09:27:53.960 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:53.980 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:27:53.980 [http-nio-8081-exec-38] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:27:54.075 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:54.085 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:27:54.085 [http-nio-8081-exec-18] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:27:54.157 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:54.162 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:27:54.162 [http-nio-8081-exec-39] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:27:54.257 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:54.267 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:27:54.267 [http-nio-8081-exec-19] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:27:54.374 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:27:54.374 [http-nio-8081-exec-40] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:27:54.481 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:54.481 [http-nio-8081-exec-20] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:54.570 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:27:54.571 [http-nio-8081-exec-41] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:27:54.678 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:27:54.680 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:27:54.785 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2016ms
2025-08-27 09:27:54.785 [http-nio-8081-exec-4] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2016ms
2025-08-27 09:27:54.862 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2001ms
2025-08-27 09:27:54.862 [http-nio-8081-exec-22] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2001ms
2025-08-27 09:27:54.953 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:27:54.953 [http-nio-8081-exec-3] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:27:55.061 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:27:55.061 [http-nio-8081-exec-23] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:27:55.155 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:27:55.155 [http-nio-8081-exec-7] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:27:55.275 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:27:55.275 [http-nio-8081-exec-24] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:27:55.370 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2016ms
2025-08-27 09:27:55.370 [http-nio-8081-exec-6] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2016ms
2025-08-27 09:27:55.460 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:55.460 [http-nio-8081-exec-25] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:55.568 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:27:55.570 [http-nio-8081-exec-5] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:27:55.692 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:27:55.692 [http-nio-8081-exec-26] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:27:55.784 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:27:55.784 [http-nio-8081-exec-10] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:27:55.860 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:27:55.860 [http-nio-8081-exec-27] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:27:55.968 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:27:55.968 [http-nio-8081-exec-9] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:27:56.077 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:27:56.077 [http-nio-8081-exec-28] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:27:56.167 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:27:56.167 [http-nio-8081-exec-8] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:27:56.258 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2001ms
2025-08-27 09:27:56.258 [http-nio-8081-exec-29] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2001ms
2025-08-27 09:27:56.280 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:56.386 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:56.494 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:56.571 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:56.673 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:56.775 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:56.900 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:56.976 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:57.100 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:57.200 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:57.279 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:57.395 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:57.480 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:57.595 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:57.703 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:57.796 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:57.903 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:57.982 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:58.083 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:58.196 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:58.284 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:27:58.286 [http-nio-8081-exec-1] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:27:58.287 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:58.387 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:58.393 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:58.393 [http-nio-8081-exec-30] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:58.502 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:27:58.502 [http-nio-8081-exec-2] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:27:58.506 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:58.580 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:27:58.580 [http-nio-8081-exec-31] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:27:58.599 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:58.675 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:27:58.675 [http-nio-8081-exec-11] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:27:58.718 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:58.782 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:58.782 [http-nio-8081-exec-32] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:58.802 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:58.888 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:58.907 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:58.907 [http-nio-8081-exec-12] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:58.982 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:27:58.982 [http-nio-8081-exec-33] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:27:58.989 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:59.106 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:27:59.106 [http-nio-8081-exec-13] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:27:59.111 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:59.190 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:59.214 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2014ms
2025-08-27 09:27:59.214 [http-nio-8081-exec-34] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2014ms
2025-08-27 09:27:59.291 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:27:59.291 [http-nio-8081-exec-14] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:27:59.317 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:59.391 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:59.399 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:27:59.399 [http-nio-8081-exec-35] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:27:59.492 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:27:59.492 [http-nio-8081-exec-15] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:27:59.513 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:59.591 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:59.602 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:59.602 [http-nio-8081-exec-36] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:59.710 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:59.710 [http-nio-8081-exec-16] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:59.715 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:59.801 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:27:59.801 [http-nio-8081-exec-37] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:27:59.806 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:59.910 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:27:59.910 [http-nio-8081-exec-17] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:27:59.915 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:27:59.987 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:27:59.987 [http-nio-8081-exec-38] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:27:59.994 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:00.093 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:28:00.094 [http-nio-8081-exec-18] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:28:00.094 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:00.199 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:00.199 [http-nio-8081-exec-39] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:00.221 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:00.291 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:00.291 [http-nio-8081-exec-19] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:00.311 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:00.396 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:28:00.397 [http-nio-8081-exec-40] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:28:00.417 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:00.507 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:00.516 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:28:00.516 [http-nio-8081-exec-20] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:28:00.598 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:00.608 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:28:00.609 [http-nio-8081-exec-41] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:28:00.720 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:00.730 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:28:00.730 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:28:00.799 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:00.806 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:00.807 [http-nio-8081-exec-4] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:00.897 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:28:00.897 [http-nio-8081-exec-22] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:28:00.918 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:01.002 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:01.002 [http-nio-8081-exec-3] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:01.007 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:01.098 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:01.124 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:01.126 [http-nio-8081-exec-23] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:01.196 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:28:01.196 [http-nio-8081-exec-7] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:28:01.204 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:01.320 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:01.320 [http-nio-8081-exec-24] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:01.402 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:28:01.402 [http-nio-8081-exec-6] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:28:01.518 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:28:01.518 [http-nio-8081-exec-25] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:28:01.594 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:01.594 [http-nio-8081-exec-5] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:01.721 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:28:01.721 [http-nio-8081-exec-26] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:28:01.808 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:28:01.808 [http-nio-8081-exec-10] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:28:01.924 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:28:01.924 [http-nio-8081-exec-27] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:28:01.997 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:01.997 [http-nio-8081-exec-9] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:02.096 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:28:02.096 [http-nio-8081-exec-28] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:28:02.234 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:02.234 [http-nio-8081-exec-8] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:02.318 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:28:02.318 [http-nio-8081-exec-29] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:28:02.421 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:02.421 [http-nio-8081-exec-1] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:02.515 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:02.515 [http-nio-8081-exec-30] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:02.603 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:28:02.603 [http-nio-8081-exec-2] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:28:02.726 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:28:02.726 [http-nio-8081-exec-31] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:28:02.809 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:28:02.812 [http-nio-8081-exec-11] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:28:02.921 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:02.921 [http-nio-8081-exec-32] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:03.015 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:03.015 [http-nio-8081-exec-12] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:03.102 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:03.102 [http-nio-8081-exec-33] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:03.208 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:03.208 [http-nio-8081-exec-13] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:03.222 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:03.321 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:03.434 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:03.529 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:03.645 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:03.730 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:03.827 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:03.948 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:04.046 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:04.147 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:04.230 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:04.339 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:04.452 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:04.534 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:04.632 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:04.755 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:04.857 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:04.961 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:05.049 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:05.141 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:05.231 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:28:05.232 [http-nio-8081-exec-34] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:28:05.249 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:05.336 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:28:05.336 [http-nio-8081-exec-14] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:28:05.355 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:05.438 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:05.438 [http-nio-8081-exec-35] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:05.460 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:05.538 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:05.543 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2014ms
2025-08-27 09:28:05.543 [http-nio-8081-exec-15] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2014ms
2025-08-27 09:28:05.658 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:05.658 [http-nio-8081-exec-36] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:05.664 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:05.733 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:05.734 [http-nio-8081-exec-16] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:05.754 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:05.842 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:28:05.843 [http-nio-8081-exec-37] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:28:05.863 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:05.951 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:05.951 [http-nio-8081-exec-17] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:05.956 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.057 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:28:06.057 [http-nio-8081-exec-38] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:28:06.061 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.142 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.151 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:06.151 [http-nio-8081-exec-18] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:06.244 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2014ms
2025-08-27 09:28:06.244 [http-nio-8081-exec-39] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2014ms
2025-08-27 09:28:06.264 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.353 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2014ms
2025-08-27 09:28:06.353 [http-nio-8081-exec-19] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2014ms
2025-08-27 09:28:06.357 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.461 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:28:06.461 [http-nio-8081-exec-40] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:28:06.466 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.538 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:06.538 [http-nio-8081-exec-20] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:06.560 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.646 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.648 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2016ms
2025-08-27 09:28:06.648 [http-nio-8081-exec-41] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2016ms
2025-08-27 09:28:06.756 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2001ms
2025-08-27 09:28:06.756 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2001ms
2025-08-27 09:28:06.761 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.865 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:06.865 [http-nio-8081-exec-4] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:06.870 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.963 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:06.974 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:06.974 [http-nio-8081-exec-22] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:07.064 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:28:07.064 [http-nio-8081-exec-3] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:28:07.070 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:07.156 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:28:07.156 [http-nio-8081-exec-23] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:28:07.161 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:07.265 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2016ms
2025-08-27 09:28:07.266 [http-nio-8081-exec-7] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2016ms
2025-08-27 09:28:07.270 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:07.359 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:07.359 [http-nio-8081-exec-24] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:07.363 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:07.467 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:07.467 [http-nio-8081-exec-6] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:07.472 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:07.543 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:28:07.543 [http-nio-8081-exec-25] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:28:07.564 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:07.668 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:07.668 [http-nio-8081-exec-5] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:07.673 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:07.748 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:07.762 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:07.762 [http-nio-8081-exec-26] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:07.864 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2001ms
2025-08-27 09:28:07.864 [http-nio-8081-exec-10] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2001ms
2025-08-27 09:28:07.870 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:07.960 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:07.971 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:28:07.971 [http-nio-8081-exec-27] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:28:08.060 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:08.071 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:28:08.071 [http-nio-8081-exec-9] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:28:08.147 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:28:08.147 [http-nio-8081-exec-28] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:28:08.167 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:08.268 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:08.268 [http-nio-8081-exec-8] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:08.371 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2014ms
2025-08-27 09:28:08.371 [http-nio-8081-exec-29] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2014ms
2025-08-27 09:28:08.480 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2014ms
2025-08-27 09:28:08.480 [http-nio-8081-exec-1] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2014ms
2025-08-27 09:28:08.572 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:28:08.572 [http-nio-8081-exec-30] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:28:08.649 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:08.650 [http-nio-8081-exec-2] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:08.764 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:08.765 [http-nio-8081-exec-31] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:08.882 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:28:08.882 [http-nio-8081-exec-11] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:28:08.970 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:28:08.970 [http-nio-8081-exec-32] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:28:09.076 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:28:09.078 [http-nio-8081-exec-12] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:28:09.177 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2016ms
2025-08-27 09:28:09.178 [http-nio-8081-exec-33] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2016ms
2025-08-27 09:28:09.283 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:09.284 [http-nio-8081-exec-13] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:09.372 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:28:09.372 [http-nio-8081-exec-34] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:28:09.487 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:28:09.487 [http-nio-8081-exec-14] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:28:09.571 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:28:09.571 [http-nio-8081-exec-35] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:28:09.682 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:28:09.682 [http-nio-8081-exec-15] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:28:09.759 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:28:09.760 [http-nio-8081-exec-36] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:28:09.883 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:09.883 [http-nio-8081-exec-16] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:09.962 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:28:09.962 [http-nio-8081-exec-37] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:28:10.070 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:28:10.070 [http-nio-8081-exec-17] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:28:10.179 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:10.179 [http-nio-8081-exec-38] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:10.200 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:10.307 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:10.391 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:10.510 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:10.604 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:10.711 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:10.805 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:10.913 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:11.008 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:11.114 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:11.208 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:11.317 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:11.409 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:11.501 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:11.612 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:11.702 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:11.822 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:11.914 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:12.014 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:12.113 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:12.209 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:12.215 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:28:12.217 [http-nio-8081-exec-18] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:28:12.307 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:12.319 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:28:12.319 [http-nio-8081-exec-39] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:28:12.405 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2014ms
2025-08-27 09:28:12.405 [http-nio-8081-exec-19] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2014ms
2025-08-27 09:28:12.426 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:12.521 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:28:12.521 [http-nio-8081-exec-40] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:28:12.525 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:12.610 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:28:12.610 [http-nio-8081-exec-20] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:28:12.632 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:12.710 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:12.725 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2014ms
2025-08-27 09:28:12.725 [http-nio-8081-exec-41] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2014ms
2025-08-27 09:28:12.809 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:12.809 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:12.830 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:12.918 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:28:12.918 [http-nio-8081-exec-4] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:28:12.922 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:13.023 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:28:13.023 [http-nio-8081-exec-22] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:28:13.028 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:13.111 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:13.122 [http-nio-8081-exec-3] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:13.122 [http-nio-8081-exec-3] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:13.211 [http-nio-8081-exec-23] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:13.211 [http-nio-8081-exec-23] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:13.213 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:13.314 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:13.323 [http-nio-8081-exec-7] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:28:13.323 [http-nio-8081-exec-7] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:28:13.422 [http-nio-8081-exec-24] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:13.422 [http-nio-8081-exec-24] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:13.431 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:13.505 [http-nio-8081-exec-6] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:13.505 [http-nio-8081-exec-6] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:13.513 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:13.616 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:13.616 [http-nio-8081-exec-25] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:13.616 [http-nio-8081-exec-25] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:13.710 [http-nio-8081-exec-5] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:13.711 [http-nio-8081-exec-5] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:13.730 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:13.826 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:13.835 [http-nio-8081-exec-26] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:13.835 [http-nio-8081-exec-26] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:13.916 [http-nio-8081-exec-10] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:28:13.916 [http-nio-8081-exec-10] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:28:13.936 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.017 [http-nio-8081-exec-27] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:14.017 [http-nio-8081-exec-27] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:14.034 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.124 [http-nio-8081-exec-9] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2011ms
2025-08-27 09:28:14.126 [http-nio-8081-exec-9] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2011ms
2025-08-27 09:28:14.129 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.217 [http-nio-8081-exec-28] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:14.217 [http-nio-8081-exec-28] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:14.220 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.315 [http-nio-8081-exec-8] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:14.315 [http-nio-8081-exec-8] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:14.335 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.414 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.436 [http-nio-8081-exec-29] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:28:14.436 [http-nio-8081-exec-29] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:28:14.526 [http-nio-8081-exec-1] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2001ms
2025-08-27 09:28:14.526 [http-nio-8081-exec-1] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2001ms
2025-08-27 09:28:14.533 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.634 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.642 [http-nio-8081-exec-30] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:28:14.642 [http-nio-8081-exec-30] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:28:14.717 [http-nio-8081-exec-2] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:28:14.717 [http-nio-8081-exec-2] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:28:14.718 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.820 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.836 [http-nio-8081-exec-31] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:28:14.836 [http-nio-8081-exec-31] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:28:14.923 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:14.930 [http-nio-8081-exec-11] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:14.930 [http-nio-8081-exec-11] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:15.034 [http-nio-8081-exec-32] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:28:15.034 [http-nio-8081-exec-32] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:28:15.042 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:15.124 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:28:15.124 [http-nio-8081-exec-12] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:15.124 [http-nio-8081-exec-12] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:15.226 [http-nio-8081-exec-33] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:15.226 [http-nio-8081-exec-33] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:15.322 [http-nio-8081-exec-13] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2008ms
2025-08-27 09:28:15.322 [http-nio-8081-exec-13] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2008ms
2025-08-27 09:28:15.445 [http-nio-8081-exec-34] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2013ms
2025-08-27 09:28:15.445 [http-nio-8081-exec-34] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2013ms
2025-08-27 09:28:15.528 [http-nio-8081-exec-14] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2015ms
2025-08-27 09:28:15.529 [http-nio-8081-exec-14] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2015ms
2025-08-27 09:28:15.628 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:28:15.628 [http-nio-8081-exec-35] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:28:15.733 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:15.733 [http-nio-8081-exec-15] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:15.838 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2012ms
2025-08-27 09:28:15.838 [http-nio-8081-exec-36] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2012ms
2025-08-27 09:28:15.946 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2010ms
2025-08-27 09:28:15.946 [http-nio-8081-exec-16] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2010ms
2025-08-27 09:28:16.039 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2005ms
2025-08-27 09:28:16.039 [http-nio-8081-exec-37] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2005ms
2025-08-27 09:28:16.146 [http-nio-8081-exec-17] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2017ms
2025-08-27 09:28:16.146 [http-nio-8081-exec-17] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2017ms
2025-08-27 09:28:16.226 [http-nio-8081-exec-38] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2006ms
2025-08-27 09:28:16.226 [http-nio-8081-exec-38] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2006ms
2025-08-27 09:28:16.349 [http-nio-8081-exec-18] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2014ms
2025-08-27 09:28:16.350 [http-nio-8081-exec-18] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2014ms
2025-08-27 09:28:16.423 [http-nio-8081-exec-39] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:28:16.423 [http-nio-8081-exec-39] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:28:16.535 [http-nio-8081-exec-19] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:28:16.535 [http-nio-8081-exec-19] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:28:16.636 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2002ms
2025-08-27 09:28:16.636 [http-nio-8081-exec-40] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2002ms
2025-08-27 09:28:16.721 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2003ms
2025-08-27 09:28:16.721 [http-nio-8081-exec-20] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2003ms
2025-08-27 09:28:16.829 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2009ms
2025-08-27 09:28:16.829 [http-nio-8081-exec-41] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2009ms
2025-08-27 09:28:16.927 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2004ms
2025-08-27 09:28:16.927 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2004ms
2025-08-27 09:28:17.049 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2007ms
2025-08-27 09:28:17.050 [http-nio-8081-exec-4] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2007ms
2025-08-27 09:28:17.143 [http-nio-8081-exec-22] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 2019ms
2025-08-27 09:28:17.145 [http-nio-8081-exec-22] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 2019ms
2025-08-27 09:56:43.789 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:43.869 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:43.971 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:44.072 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:44.190 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:44.299 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:44.374 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:44.486 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:44.576 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:44.688 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:44.777 [http-nio-8081-exec-42] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:44.878 [http-nio-8081-exec-43] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:45.000 [http-nio-8081-exec-44] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:45.081 [http-nio-8081-exec-45] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:45.202 [http-nio-8081-exec-46] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:45.282 [http-nio-8081-exec-47] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:45.383 [http-nio-8081-exec-48] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:45.485 [http-nio-8081-exec-49] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:45.608 [http-nio-8081-exec-50] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:45.701 [http-nio-8081-exec-51] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:56:46.802 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3013ms
2025-08-27 09:56:46.804 [http-nio-8081-exec-16] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3013ms
2025-08-27 09:56:46.880 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3011ms
2025-08-27 09:56:46.880 [http-nio-8081-exec-20] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3011ms
2025-08-27 09:56:46.973 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3002ms
2025-08-27 09:56:46.973 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3002ms
2025-08-27 09:56:47.083 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3011ms
2025-08-27 09:56:47.083 [http-nio-8081-exec-41] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3011ms
2025-08-27 09:56:47.206 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3017ms
2025-08-27 09:56:47.206 [http-nio-8081-exec-4] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3017ms
2025-08-27 09:56:47.312 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3013ms
2025-08-27 09:56:47.312 [http-nio-8081-exec-40] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3013ms
2025-08-27 09:56:47.389 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3015ms
2025-08-27 09:56:47.389 [http-nio-8081-exec-15] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3015ms
2025-08-27 09:56:47.498 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3012ms
2025-08-27 09:56:47.498 [http-nio-8081-exec-37] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3012ms
2025-08-27 09:56:47.591 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3015ms
2025-08-27 09:56:47.591 [http-nio-8081-exec-35] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3015ms
2025-08-27 09:56:47.699 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3011ms
2025-08-27 09:56:47.699 [http-nio-8081-exec-36] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3011ms
2025-08-27 09:56:47.793 [http-nio-8081-exec-42] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3016ms
2025-08-27 09:56:47.793 [http-nio-8081-exec-42] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3016ms
2025-08-27 09:56:47.886 [http-nio-8081-exec-43] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3008ms
2025-08-27 09:56:47.886 [http-nio-8081-exec-43] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3008ms
2025-08-27 09:56:48.011 [http-nio-8081-exec-44] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3011ms
2025-08-27 09:56:48.011 [http-nio-8081-exec-44] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3011ms
2025-08-27 09:56:48.087 [http-nio-8081-exec-45] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3006ms
2025-08-27 09:56:48.087 [http-nio-8081-exec-45] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3006ms
2025-08-27 09:56:48.210 [http-nio-8081-exec-46] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3008ms
2025-08-27 09:56:48.210 [http-nio-8081-exec-46] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3008ms
2025-08-27 09:56:48.286 [http-nio-8081-exec-47] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3004ms
2025-08-27 09:56:48.286 [http-nio-8081-exec-47] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3004ms
2025-08-27 09:56:48.395 [http-nio-8081-exec-48] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3012ms
2025-08-27 09:56:48.395 [http-nio-8081-exec-48] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3012ms
2025-08-27 09:56:48.489 [http-nio-8081-exec-49] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3004ms
2025-08-27 09:56:48.489 [http-nio-8081-exec-49] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3004ms
2025-08-27 09:56:48.612 [http-nio-8081-exec-50] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3004ms
2025-08-27 09:56:48.612 [http-nio-8081-exec-50] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3004ms
2025-08-27 09:56:48.702 [http-nio-8081-exec-51] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3001ms
2025-08-27 09:56:48.702 [http-nio-8081-exec-51] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3001ms
2025-08-27 09:57:14.682 [http-nio-8081-exec-42] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:14.775 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:14.854 [http-nio-8081-exec-43] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:14.975 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:15.071 [http-nio-8081-exec-44] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:15.177 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:15.270 [http-nio-8081-exec-45] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:15.379 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:15.474 [http-nio-8081-exec-46] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:15.581 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:15.661 [http-nio-8081-exec-47] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:15.783 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:15.875 [http-nio-8081-exec-48] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:15.963 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:16.064 [http-nio-8081-exec-49] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:16.185 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:16.266 [http-nio-8081-exec-50] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:16.367 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:16.478 [http-nio-8081-exec-51] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:16.586 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 09:57:17.686 [http-nio-8081-exec-42] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3004ms
2025-08-27 09:57:17.686 [http-nio-8081-exec-42] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3004ms
2025-08-27 09:57:17.778 [http-nio-8081-exec-40] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3003ms
2025-08-27 09:57:17.778 [http-nio-8081-exec-40] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3003ms
2025-08-27 09:57:17.857 [http-nio-8081-exec-43] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3003ms
2025-08-27 09:57:17.857 [http-nio-8081-exec-43] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3003ms
2025-08-27 09:57:17.980 [http-nio-8081-exec-20] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3005ms
2025-08-27 09:57:17.980 [http-nio-8081-exec-20] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3005ms
2025-08-27 09:57:18.075 [http-nio-8081-exec-44] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3004ms
2025-08-27 09:57:18.075 [http-nio-8081-exec-44] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3004ms
2025-08-27 09:57:18.182 [http-nio-8081-exec-15] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3005ms
2025-08-27 09:57:18.182 [http-nio-8081-exec-15] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3005ms
2025-08-27 09:57:18.276 [http-nio-8081-exec-45] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3006ms
2025-08-27 09:57:18.276 [http-nio-8081-exec-45] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3006ms
2025-08-27 09:57:18.384 [http-nio-8081-exec-41] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3005ms
2025-08-27 09:57:18.385 [http-nio-8081-exec-41] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3005ms
2025-08-27 09:57:18.476 [http-nio-8081-exec-46] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3002ms
2025-08-27 09:57:18.476 [http-nio-8081-exec-46] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3002ms
2025-08-27 09:57:18.598 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3017ms
2025-08-27 09:57:18.598 [http-nio-8081-exec-37] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3017ms
2025-08-27 09:57:18.677 [http-nio-8081-exec-47] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3016ms
2025-08-27 09:57:18.677 [http-nio-8081-exec-47] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3016ms
2025-08-27 09:57:18.786 [http-nio-8081-exec-16] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3003ms
2025-08-27 09:57:18.786 [http-nio-8081-exec-16] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3003ms
2025-08-27 09:57:18.879 [http-nio-8081-exec-48] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3004ms
2025-08-27 09:57:18.879 [http-nio-8081-exec-48] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3004ms
2025-08-27 09:57:18.971 [http-nio-8081-exec-35] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3008ms
2025-08-27 09:57:18.971 [http-nio-8081-exec-35] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3008ms
2025-08-27 09:57:19.079 [http-nio-8081-exec-49] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3015ms
2025-08-27 09:57:19.079 [http-nio-8081-exec-49] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3015ms
2025-08-27 09:57:19.185 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3000ms
2025-08-27 09:57:19.185 [http-nio-8081-exec-4] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3000ms
2025-08-27 09:57:19.279 [http-nio-8081-exec-50] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3013ms
2025-08-27 09:57:19.279 [http-nio-8081-exec-50] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3013ms
2025-08-27 09:57:19.372 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3005ms
2025-08-27 09:57:19.372 [http-nio-8081-exec-36] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3005ms
2025-08-27 09:57:19.493 [http-nio-8081-exec-51] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3015ms
2025-08-27 09:57:19.494 [http-nio-8081-exec-51] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3015ms
2025-08-27 09:57:19.601 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3015ms
2025-08-27 09:57:19.601 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3015ms
2025-08-27 11:28:50.266 [http-nio-8081-exec-46] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:50.270 [http-nio-8081-exec-50] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:50.271 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:50.276 [http-nio-8081-exec-42] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:50.349 [http-nio-8081-exec-44] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:50.443 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:50.546 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:50.664 [http-nio-8081-exec-51] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:50.745 [http-nio-8081-exec-43] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:50.847 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:50.957 [http-nio-8081-exec-52] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:51.050 [http-nio-8081-exec-53] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:51.149 [http-nio-8081-exec-54] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:51.277 [http-nio-8081-exec-55] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:51.367 [http-nio-8081-exec-56] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:51.454 [http-nio-8081-exec-57] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:51.553 [http-nio-8081-exec-58] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:51.652 [http-nio-8081-exec-59] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:51.755 [http-nio-8081-exec-60] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:51.856 [http-nio-8081-exec-61] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/test, RemoteAddr: 127.0.0.1
2025-08-27 11:28:53.282 [http-nio-8081-exec-4] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3017ms
2025-08-27 11:28:53.282 [http-nio-8081-exec-4] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3017ms
2025-08-27 11:28:53.282 [http-nio-8081-exec-50] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3012ms
2025-08-27 11:28:53.282 [http-nio-8081-exec-46] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3017ms
2025-08-27 11:28:53.284 [http-nio-8081-exec-50] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3012ms
2025-08-27 11:28:53.284 [http-nio-8081-exec-46] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3017ms
2025-08-27 11:28:53.288 [http-nio-8081-exec-42] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3023ms
2025-08-27 11:28:53.288 [http-nio-8081-exec-42] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3023ms
2025-08-27 11:28:53.350 [http-nio-8081-exec-44] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3002ms
2025-08-27 11:28:53.351 [http-nio-8081-exec-44] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3002ms
2025-08-27 11:28:53.460 [http-nio-8081-exec-36] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3017ms
2025-08-27 11:28:53.460 [http-nio-8081-exec-36] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3017ms
2025-08-27 11:28:53.552 [http-nio-8081-exec-37] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3006ms
2025-08-27 11:28:53.552 [http-nio-8081-exec-37] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3006ms
2025-08-27 11:28:53.676 [http-nio-8081-exec-51] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3012ms
2025-08-27 11:28:53.676 [http-nio-8081-exec-51] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3012ms
2025-08-27 11:28:53.755 [http-nio-8081-exec-43] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3010ms
2025-08-27 11:28:53.755 [http-nio-8081-exec-43] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3010ms
2025-08-27 11:28:53.864 [http-nio-8081-exec-21] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3017ms
2025-08-27 11:28:53.864 [http-nio-8081-exec-21] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3017ms
2025-08-27 11:28:53.971 [http-nio-8081-exec-52] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3014ms
2025-08-27 11:28:53.972 [http-nio-8081-exec-52] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3014ms
2025-08-27 11:28:54.063 [http-nio-8081-exec-53] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3013ms
2025-08-27 11:28:54.063 [http-nio-8081-exec-53] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3013ms
2025-08-27 11:28:54.156 [http-nio-8081-exec-54] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3007ms
2025-08-27 11:28:54.156 [http-nio-8081-exec-54] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3007ms
2025-08-27 11:28:54.279 [http-nio-8081-exec-55] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3002ms
2025-08-27 11:28:54.279 [http-nio-8081-exec-55] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3002ms
2025-08-27 11:28:54.372 [http-nio-8081-exec-56] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3005ms
2025-08-27 11:28:54.372 [http-nio-8081-exec-56] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3005ms
2025-08-27 11:28:54.465 [http-nio-8081-exec-57] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3010ms
2025-08-27 11:28:54.465 [http-nio-8081-exec-57] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3010ms
2025-08-27 11:28:54.557 [http-nio-8081-exec-58] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3004ms
2025-08-27 11:28:54.559 [http-nio-8081-exec-58] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3004ms
2025-08-27 11:28:54.665 [http-nio-8081-exec-59] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3013ms
2025-08-27 11:28:54.665 [http-nio-8081-exec-59] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3013ms
2025-08-27 11:28:54.772 [http-nio-8081-exec-60] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3017ms
2025-08-27 11:28:54.772 [http-nio-8081-exec-60] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3017ms
2025-08-27 11:28:54.865 [http-nio-8081-exec-61] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/test, Status: 200, ExecuteTime: 3009ms
2025-08-27 11:28:54.865 [http-nio-8081-exec-61] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/test, ExecuteTime: 3009ms
2025-08-27 20:31:19.220 [http-nio-8081-exec-61] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-whitelists, RemoteAddr: 127.0.0.1
2025-08-27 20:31:19.578 [http-nio-8081-exec-61] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-27 20:31:20.319 [http-nio-8081-exec-61] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-whitelists, Status: 200, ExecuteTime: 1100ms
2025-08-27 20:31:20.319 [http-nio-8081-exec-61] WARN  c.e.admin.config.WebConfig$LoggingInterceptor - Slow request warning - Method: GET, URI: /api/ip-whitelists, ExecuteTime: 1100ms
2025-08-27 20:31:20.599 [http-nio-8081-exec-58] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/ip-flow-rules, RemoteAddr: 127.0.0.1
2025-08-27 20:31:20.892 [http-nio-8081-exec-58] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/ip-flow-rules, Status: 200, ExecuteTime: 293ms
2025-08-27 20:31:26.020 [http-nio-8081-exec-52] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-configs, RemoteAddr: 127.0.0.1
2025-08-27 20:31:26.020 [http-nio-8081-exec-62] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/flow-rules, RemoteAddr: 127.0.0.1
2025-08-27 20:31:26.051 [http-nio-8081-exec-52] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-configs, Status: 200, ExecuteTime: 31ms
2025-08-27 20:31:26.073 [http-nio-8081-exec-62] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/flow-rules, Status: 200, ExecuteTime: 53ms
2025-08-27 20:31:30.109 [http-nio-8081-exec-56] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request started - Method: GET, URI: /api/tenant-flow-rules, RemoteAddr: 127.0.0.1
2025-08-27 20:31:30.171 [http-nio-8081-exec-56] DEBUG c.e.admin.config.WebConfig$LoggingInterceptor - Request completed - Method: GET, URI: /api/tenant-flow-rules, Status: 200, ExecuteTime: 62ms
