package com.example.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 测试GlobalFilter是否被调用
 */
@Slf4j
@Component
public class TestGlobalFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        log.info("🔥🔥🔥 TestGlobalFilter.filter() 被调用！！！ 🔥🔥🔥");
        log.info("Request URI: {}", exchange.getRequest().getURI());
        log.info("Request Path: {}", exchange.getRequest().getPath().value());
        
        return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        // 设置最高优先级
        return Ordered.HIGHEST_PRECEDENCE + 100;
    }
}
