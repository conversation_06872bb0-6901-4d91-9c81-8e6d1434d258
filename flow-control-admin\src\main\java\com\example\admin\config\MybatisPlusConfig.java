package com.example.admin.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.time.LocalDateTime;

/**
 * MyBatis Plus配置类 配置分页插件、乐观锁插件、防全表更新删除插件、审计字段自动填充等
 */
@Configuration
@EnableTransactionManagement
public class MybatisPlusConfig {

	private static final Logger log = LoggerFactory.getLogger(MybatisPlusConfig.class);

	/**
	 * 配置MyBatis Plus插件
	 */
	@Bean
	public MybatisPlusInterceptor mybatisPlusInterceptor() {
		MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

		// 分页插件
		PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
		// 设置数据库类型
		paginationInnerInterceptor.setDbType(DbType.MYSQL);
		// 设置最大单页限制数量，默认 500 条，-1 不受限制
		paginationInnerInterceptor.setMaxLimit(1000L);
		// 溢出总页数后是否进行处理
		paginationInnerInterceptor.setOverflow(false);
		// 生成 countSql 优化掉 join 现在只支持左连接
		paginationInnerInterceptor.setOptimizeJoin(true);
		interceptor.addInnerInterceptor(paginationInnerInterceptor);

		// 乐观锁插件
		interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

		// 防全表更新与删除插件
		interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());

		log.info("MyBatis Plus plugin configuration completed");
		return interceptor;
	}

	/**
	 * 审计字段自动填充处理器
	 */
	@Bean
	public MetaObjectHandler metaObjectHandler() {
		return new AuditMetaObjectHandler();
	}

	/**
	 * 审计字段自动填充实现类
	 */
	public static class AuditMetaObjectHandler implements MetaObjectHandler {

		/**
		 * 插入时自动填充
		 */
		@Override
		public void insertFill(MetaObject metaObject) {
			LocalDateTime now = LocalDateTime.now();

			// 自动填充创建时间
			this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
			this.strictInsertFill(metaObject, "createdTime", LocalDateTime.class, now);
			this.strictInsertFill(metaObject, "gmtCreate", LocalDateTime.class, now);

			// 自动填充更新时间
			this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
			this.strictInsertFill(metaObject, "updatedTime", LocalDateTime.class, now);
			this.strictInsertFill(metaObject, "gmtModified", LocalDateTime.class, now);

			// 自动填充创建者（如果有用户上下文的话）
			String currentUser = getCurrentUser();
			if (currentUser != null) {
				this.strictInsertFill(metaObject, "createBy", String.class, currentUser);
				this.strictInsertFill(metaObject, "createdBy", String.class, currentUser);
				this.strictInsertFill(metaObject, "updateBy", String.class, currentUser);
				this.strictInsertFill(metaObject, "updatedBy", String.class, currentUser);
			}

			// 自动填充版本号
			this.strictInsertFill(metaObject, "version", Integer.class, 1);

			// 自动填充删除标记
			this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
			this.strictInsertFill(metaObject, "isDeleted", Integer.class, 0);

			// 自动填充状态
			if (!metaObject.hasGetter("status") || metaObject.getValue("status") == null) {
				this.strictInsertFill(metaObject, "status", Integer.class, 1);
			}

			// 使用静态方法获取日志
			LoggerFactory.getLogger(AuditMetaObjectHandler.class).debug("Auto-fill insert fields completed");
		}

		/**
		 * 更新时自动填充
		 */
		@Override
		public void updateFill(MetaObject metaObject) {
			LocalDateTime now = LocalDateTime.now();

			// 自动填充更新时间
			this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, now);
			this.strictUpdateFill(metaObject, "updatedTime", LocalDateTime.class, now);
			this.strictUpdateFill(metaObject, "gmtModified", LocalDateTime.class, now);

			// 自动填充更新者（如果有用户上下文的话）
			String currentUser = getCurrentUser();
			if (currentUser != null) {
				this.strictUpdateFill(metaObject, "updateBy", String.class, currentUser);
				this.strictUpdateFill(metaObject, "updatedBy", String.class, currentUser);
			}

			// 使用静态方法获取日志
			LoggerFactory.getLogger(AuditMetaObjectHandler.class).debug("Auto-fill update fields completed");
		}

		/**
		 * 获取当前用户 TODO: 集成Spring Security或其他认证框架后实现
		 */
		private String getCurrentUser() {
			// 这里可以从SecurityContextHolder或其他地方获取当前用户
			// 暂时返回系统用户
			return "system";
		}
	}

	/**
	 * SQL性能规范插件配置 用于开发环境检测慢SQL
	 */
	/*
	 * @Bean
	 * 
	 * @Profile({"dev", "test"}) public PerformanceInterceptor
	 * performanceInterceptor() { PerformanceInterceptor performanceInterceptor =
	 * new PerformanceInterceptor(); // 设置SQL执行最大时长，超过自动停止运行，有助于发现问题
	 * performanceInterceptor.setMaxTime(1000); // SQL是否格式化，默认false
	 * performanceInterceptor.setFormat(true); return performanceInterceptor; }
	 */

	/**
	 * 数据权限插件配置 TODO: 根据实际需求实现数据权限控制
	 */
	/*
	 * @Bean public DataPermissionInterceptor dataPermissionInterceptor() {
	 * DataPermissionInterceptor dataPermissionInterceptor = new
	 * DataPermissionInterceptor(); // 添加数据权限处理器
	 * dataPermissionInterceptor.setDataPermissionHandler(new
	 * DataPermissionHandler() {
	 * 
	 * @Override public Expression getSqlSegment(Expression where, String
	 * mappedStatementId) { // 实现数据权限逻辑 return where; } }); return
	 * dataPermissionInterceptor; }
	 */

	/**
	 * 多租户插件配置 TODO: 根据实际需求实现多租户支持
	 */
	/*
	 * @Bean public TenantLineInnerInterceptor tenantLineInnerInterceptor() {
	 * TenantLineInnerInterceptor tenantLineInnerInterceptor = new
	 * TenantLineInnerInterceptor();
	 * tenantLineInnerInterceptor.setTenantLineHandler(new TenantLineHandler() {
	 * 
	 * @Override public Expression getTenantId() { // 返回当前租户ID return new
	 * LongValue(getCurrentTenantId()); }
	 * 
	 * @Override public String getTenantIdColumn() { // 租户字段名 return "tenant_id"; }
	 * 
	 * @Override public boolean ignoreTable(String tableName) { // 忽略多租户的表 return
	 * "system_config".equalsIgnoreCase(tableName); } }); return
	 * tenantLineInnerInterceptor; }
	 * 
	 * private String getCurrentTenantId() { // 从上下文获取当前租户ID return "1"; }
	 */

	/**
	 * 动态表名插件配置 TODO: 根据实际需求实现动态表名支持
	 */
	/*
	 * @Bean public DynamicTableNameInnerInterceptor
	 * dynamicTableNameInnerInterceptor() { DynamicTableNameInnerInterceptor
	 * dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
	 * dynamicTableNameInnerInterceptor.setTableNameHandler((sql, tableName) -> { //
	 * 根据业务逻辑动态返回表名 if ("flow_control_log".equals(tableName)) { // 按月分表 return
	 * tableName + "_" +
	 * LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMM")); } return
	 * tableName; }); return dynamicTableNameInnerInterceptor; }
	 */
}