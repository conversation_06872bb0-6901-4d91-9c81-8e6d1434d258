<template>
	<div class="flow-rules">
		<layout>
			<div class="flow-rules-content">
				<div class="page-header">
					<h1>流量规则管理</h1>
					<p>配置和管理系统流量控制规则</p>
					<div class="header-actions">
						<el-button
							type="danger"
							:disabled="!hasSelectedRules"
							@click="handleBatchDelete"
						>
							批量删除 ({{ selectedRulesCount }})
						</el-button>

						<el-button
							type="primary"
							icon="el-icon-plus"
							@click="showCreateDialog"
						>
							新增规则
						</el-button>
						
					</div>
				</div>

				<!-- 搜索和筛选区域 -->
				<div class="search-filters">
					<el-row :gutter="20">
						<el-col :span="6">
							<el-input
								v-model="searchKeyword"
								placeholder="搜索资源名称或应用名称"
								prefix-icon="el-icon-search"
								clearable
								@input="handleSearch"
							/>
						</el-col>
						<el-col :span="4">
							<el-select
								v-model="filters.status"
								placeholder="状态筛选"
								clearable
								@change="handleFilterChange"
							>
								<el-option label="启用" value="enabled" />
								<el-option label="禁用" value="disabled" />
							</el-select>
						</el-col>
						<el-col :span="4">
							<el-select
								v-model="filters.thresholdType"
								placeholder="阈值类型"
								clearable
								@change="handleFilterChange"
							>
								<el-option label="QPS限流" value="QPS" />
								<el-option label="并发限流" value="THREAD" />
							</el-select>
						</el-col>
						<el-col :span="4">
							<el-select
								v-model="filters.controlBehavior"
								placeholder="流控效果"
								clearable
								@change="handleFilterChange"
							>
								<el-option label="快速失败" value="0" />
								<el-option label="Warm Up" value="1" />
								<el-option label="排队等待" value="2" />
							</el-select>
						</el-col>
						<el-col :span="6">
							<div class="stats-info">
								<span>启用: {{ enabledRulesCount }}</span>
								<span>禁用: {{ disabledRulesCount }}</span>
								<span>总计: {{ pagination.total }}</span>
							</div>
						</el-col>
					</el-row>
				</div>

				<!-- 操作栏 -->
				<div class="toolbar"></div>

				<!-- 规则列表 -->
				<div class="rules-table">
					<el-table
						:data="paginatedRules"
						:loading="isLoading"
						style="width: 100%"
						stripe
						@selection-change="handleSelectionChange"
					>
						<el-table-column
							type="selection"
							width="55"
						></el-table-column>
						<el-table-column
							prop="id"
							label="ID"
							width="80"
						></el-table-column>
						<el-table-column
							prop="resource"
							label="资源名称"
							min-width="150"
							show-overflow-tooltip
						></el-table-column>
						<el-table-column
							prop="limitApp"
							label="来源应用"
							min-width="120"
							show-overflow-tooltip
						></el-table-column>
						<el-table-column
							prop="grade"
							label="限流模式"
							width="110"
						>
							<template slot-scope="scope">
								<el-tag
									size="mini"
									:type="
										scope.row.grade === 1
											? 'primary'
											: 'info'
									"
								>
									{{ getGradeName(scope.row.grade) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="count" label="阈值" width="80">
							<template slot-scope="scope">
								<span class="count-value">{{
									scope.row.count
								}}</span>
							</template>
						</el-table-column>
						<el-table-column
							prop="strategy"
							label="流控模式"
							width="110"
						>
							<template slot-scope="scope">
								<el-tag size="mini" type="warning">
									{{ getStrategyName(scope.row.strategy) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column
							prop="controlBehavior"
							label="流控效果"
							width="120"
						>
							<template slot-scope="scope">
								<el-tag
									size="mini"
									:type="
										getBehaviorTagType(
											scope.row.controlBehavior
										)
									"
								>
									{{
										getControlBehaviorName(
											scope.row.controlBehavior
										)
									}}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="enabled" label="状态" width="80">
							<template slot-scope="scope">
								<el-switch
									v-model="scope.row.enabled"
									@change="toggleRuleStatus(scope.row)"
									active-color="#13ce66"
									inactive-color="#ff4949"
								>
								</el-switch>
							</template>
						</el-table-column>
						<el-table-column
							prop="createTime"
							label="创建时间"
							min-width="160"
							show-overflow-tooltip
						>
							<template slot-scope="scope">
								{{ formatTime(scope.row.createTime) }}
							</template>
						</el-table-column>
						<el-table-column label="操作" width="280" fixed="right">
							<template slot-scope="scope">
								<div class="action-buttons">
									<el-button
										size="mini"
										icon="el-icon-edit"
										@click="editRule(scope.row)"
										>编辑</el-button
									>
									<el-button
										size="mini"
										type="danger"
										icon="el-icon-delete"
										@click="deleteRule(scope.row.id)"
										>删除</el-button
									>
								</div>
							</template>
						</el-table-column>
					</el-table>

					<!-- 分页 -->
					<div class="pagination-wrapper">
						<el-pagination
							@size-change="handleSizeChange"
							@current-change="handleCurrentChange"
							:current-page="pagination.currentPage"
							:page-sizes="[10, 20, 50, 100]"
							:page-size="pagination.pageSize"
							layout="total, sizes, prev, pager, next, jumper"
							:total="totalFilteredCount"
							:prev-text="$t('pagination.prevPage')"
							:next-text="$t('pagination.nextPage')"
						>
						</el-pagination>
					</div>
				</div>

				<!-- 创建/编辑对话框 -->
				<el-dialog
					:title="dialogTitle"
					:visible.sync="dialogVisible"
					width="600px"
					@close="resetForm"
				>
					<el-form
						ref="ruleForm"
						:model="ruleForm"
						:rules="ruleRules"
						label-width="120px"
					>
						<el-form-item label="租户ID" prop="tenantId">
							<el-input
								v-model="ruleForm.tenantId"
								placeholder="请输入租户ID"
								clearable
							/>
						</el-form-item>
						<el-form-item label="资源名称" prop="resource">
							<el-input
								v-model="ruleForm.resource"
								placeholder="请输入资源名称"
							></el-input>
						</el-form-item>
						<el-form-item label="限流模式" prop="grade">
							<el-select
								v-model="ruleForm.grade"
								placeholder="请选择限流模式"
							>
								<el-option
									label="QPS限流"
									:value="1"
								></el-option>
								<el-option
									label="并发限流"
									:value="0"
								></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="阈值" prop="count">
							<el-input-number
								v-model="ruleForm.count"
								:min="1"
								:max="10000"
							></el-input-number>
						</el-form-item>
						<el-form-item label="流控模式" prop="strategy">
							<el-select
								v-model="ruleForm.strategy"
								placeholder="请选择流控模式"
							>
								<el-option label="直接" :value="0"></el-option>
								<el-option label="关联" :value="1"></el-option>
								<el-option label="链路" :value="2"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="流控效果" prop="controlBehavior">
							<el-select
								v-model="ruleForm.controlBehavior"
								placeholder="请选择流控效果"
							>
								<el-option
									label="快速失败"
									:value="0"
								></el-option>
								<el-option
									label="Warm Up"
									:value="1"
								></el-option>
								<el-option
									label="排队等待"
									:value="2"
								></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="启用状态">
							<el-switch v-model="ruleForm.enabled"></el-switch>
						</el-form-item>
					</el-form>
					<div slot="footer" class="dialog-footer">
						<el-button @click="dialogVisible = false"
							>取消</el-button
						>
						<el-button type="primary" @click="saveRule"
							>确定</el-button
						>
					</div>
				</el-dialog>
			</div>
		</layout>
	</div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import Layout from '../components/Layout.vue';

export default {
	name: 'FlowRules',
	components: {
		Layout,
	},
	data() {
		return {
			dialogVisible: false,
			isEdit: false,
			searchKeyword: '',
			selectedRules: [],
			publishLoading: false,
			filters: {
				status: '',
				thresholdType: '',
				controlBehavior: '',
			},
			pagination: {
				total: 0,
				currentPage: 1,
				pageSize: 10,
			},
			ruleForm: {
				id: null,
				tenantId: '',
				resource: '',
				grade: 1,
				count: 1,
				strategy: 0,
				controlBehavior: 0,
				enabled: true,
			},
			ruleRules: {
				tenantId: [
					{
						required: true,
						message: '请输入租户ID',
						trigger: 'blur',
					},
				],
				resource: [
					{
						required: true,
						message: '请输入资源名称',
						trigger: 'blur',
					},
				],
				grade: [
					{
						required: true,
						message: '请选择限流模式',
						trigger: 'change',
					},
				],
				count: [
					{ required: true, message: '请输入阈值', trigger: 'blur' },
				],
				strategy: [
					{
						required: true,
						message: '请选择流控模式',
						trigger: 'change',
					},
				],
				controlBehavior: [
					{
						required: true,
						message: '请选择流控效果',
						trigger: 'change',
					},
				],
			},
		};
	},
	computed: {
		...mapGetters('tenantFlowRules', ['allRules', 'isLoading']),
		dialogTitle() {
			return this.isEdit ? '编辑流量规则' : '新增流量规则';
		},
		filteredRules() {
			let rules = [...this.allRules];

			// 搜索过滤
			if (this.searchKeyword) {
				const keyword = this.searchKeyword.toLowerCase();
				rules = rules.filter(
					(rule) =>
						rule.resource.toLowerCase().includes(keyword) ||
						rule.limitApp.toLowerCase().includes(keyword)
				);
			}

			// 状态过滤
			if (this.filters.status) {
				const enabled = this.filters.status === 'enabled';
				rules = rules.filter((rule) => rule.enabled === enabled);
			}

			// 限流模式过滤
			if (this.filters.thresholdType) {
				const gradeValue = this.filters.thresholdType === 'QPS' ? 1 : 0;
				rules = rules.filter((rule) => rule.grade === gradeValue);
			}

			// 流控行为过滤
			if (this.filters.controlBehavior) {
				const behaviorValue = parseInt(this.filters.controlBehavior);
				rules = rules.filter(
					(rule) => rule.controlBehavior === behaviorValue
				);
			}

			return rules;
		},
		totalFilteredCount() {
			return this.filteredRules.length;
		},
		hasSelectedRules() {
			return this.selectedRules.length > 0;
		},
		selectedRulesCount() {
			return this.selectedRules.length;
		},
		enabledRulesCount() {
			return this.filteredRules.filter((rule) => rule.enabled).length;
		},
		disabledRulesCount() {
			return this.filteredRules.filter((rule) => !rule.enabled).length;
		},
		paginatedRules() {
			const start =
				(this.pagination.currentPage - 1) * this.pagination.pageSize;
			const end = start + this.pagination.pageSize;
			return this.filteredRules.slice(start, end);
		},
	},
	mounted() {
		this.fetchRules();
	},
	methods: {
		...mapActions('tenantFlowRules', [
			'fetchRules',
			'createRule',
			'updateRule',
			'deleteRule',
		]),
		showCreateDialog() {
			this.isEdit = false;
			this.dialogVisible = true;
		},
		editRule(rule) {
			this.isEdit = true;
			// 映射后端数据到前端表单字段
			this.ruleForm = {
				id: rule.id,
				tenantId: rule.tenantId || '',
				resource: rule.resource || rule.resourceName,
				grade: rule.grade || rule.limitMode,
				count: rule.count || rule.threshold,
				strategy: rule.strategy || 0,
				controlBehavior: rule.controlBehavior || rule.behavior || 0,
				enabled:
					rule.enabled !== undefined
						? rule.enabled
						: rule.status === 1,
			};
			this.dialogVisible = true;
		},
		async saveRule() {
			this.$refs.ruleForm.validate(async (valid) => {
				if (valid) {
					try {
						// 映射前端字段到后端期望的字段格式
						const ruleData = {
							ruleName: this.ruleForm.resource, // 使用资源名作为规则名
							resourceName: this.ruleForm.resource,
							tenantId: this.ruleForm.tenantId,
							limitMode: this.ruleForm.grade, // 限流模式
							threshold: this.ruleForm.count, // 阈值
							strategy: this.ruleForm.strategy, // 流控策略
							behavior: this.ruleForm.controlBehavior, // 流控行为
							status: this.ruleForm.enabled ? 1 : 0, // 启用状态
						};

						if (this.isEdit) {
							ruleData.id = this.ruleForm.id;
							await this.updateRule(ruleData);
							this.$message.success('更新规则成功');
						} else {
							await this.createRule(ruleData);
							this.$message.success('创建规则成功');
						}
						this.dialogVisible = false;
						this.fetchRules();
					} catch (error) {
						console.error('保存规则失败:', error);
						this.$message.error(
							'操作失败: ' + (error.message || '未知错误')
						);
					}
				}
			});
		},
		async deleteRule(ruleId) {
			try {
				await this.$confirm('确定要删除这条规则吗？', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				});
				await this.$store.dispatch('tenantFlowRules/deleteRule', ruleId);
				this.$message.success('删除成功');
				this.fetchRules();
			} catch (error) {
				if (error !== 'cancel') {
					this.$message.error('删除失败');
				}
			}
		},
		async toggleRuleStatus(rule) {
			try {
				await this.updateRule(rule);
				this.$message.success(
					`规则已${rule.enabled ? '启用' : '禁用'}`
				);
			} catch (error) {
				this.$message.error('操作失败');
				rule.enabled = !rule.enabled; // 回滚状态
			}
		},
		resetForm() {
			this.ruleForm = {
				id: null,
				tenantId: '',
				resource: '',
				grade: 1,
				count: 1,
				strategy: 0,
				controlBehavior: 0,
				enabled: true,
			};
			if (this.$refs.ruleForm) {
				this.$refs.ruleForm.resetFields();
			}
		},

		getGradeType(grade) {
			return grade === 1 ? 'success' : 'info';
		},
		getGradeName(grade) {
			return grade === 1 ? 'QPS限流' : '并发限流';
		},
		getStrategyName(strategy) {
			const names = { 0: '直接', 1: '关联', 2: '链路' };
			return names[strategy] || '未知';
		},
		getControlBehaviorName(behavior) {
			const names = { 0: '快速失败', 1: 'Warm Up', 2: '排队等待' };
			return names[behavior] || '未知';
		},
		handleSearch() {
			// 搜索逻辑已在computed中处理
		},
		handleFilterChange() {
			// 筛选逻辑已在computed中处理
		},
		handleSelectionChange(selection) {
			this.selectedRules = selection;
		},
		async handleBatchDelete() {
			if (!this.hasSelectedRules) return;

			try {
				await this.$confirm(
					`确定要删除选中的 ${this.selectedRulesCount} 条规则吗？`,
					'批量删除',
					{
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning',
					}
				);

				const deletePromises = this.selectedRules.map((rule) =>
					this.$store.dispatch('tenantFlowRules/deleteRule', rule.id)
				);
				await Promise.all(deletePromises);

				this.$message.success(
					`成功删除 ${this.selectedRulesCount} 条规则`
				);
				this.selectedRules = [];
				this.fetchRules();
			} catch (error) {
				if (error !== 'cancel') {
					this.$message.error('批量删除失败');
				}
			}
		},
		handleSizeChange(val) {
			this.pagination.pageSize = val;
			this.pagination.currentPage = 1;
		},
		handleCurrentChange(val) {
			this.pagination.currentPage = val;
		},
		getBehaviorTagType(behavior) {
			const types = { 0: 'danger', 1: 'warning', 2: 'info' };
			return types[behavior] || 'default';
		},
		formatTime(time) {
			if (!time) return '-';
			// 统一使用 YYYY-MM-DD HH:mm:ss 格式
			const date = new Date(time);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},
	},
};
</script>

<style scoped>
.flow-rules-content {
	padding: 20px;
	max-width: 100%;
	width: 100%;
}

.page-header {
	margin-bottom: 30px;
}

.page-header h1 {
	color: #333;
	margin-bottom: 5px;
}

.page-header p {
	color: #666;
	font-size: 14px;
}

.toolbar {
	margin-bottom: 20px;
}

.rules-table {
	background: white;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	width: 100%;
	max-width: 100%;
}

.dialog-footer {
	text-align: right;
}

.header-actions {
	display: flex;
	gap: 10px;
	align-items: center;
	margin-top: 10px;
}

.search-filters {
	background: white;
	padding: 20px;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	margin-bottom: 20px;
}

.stats-info {
	display: flex;
	gap: 15px;
	align-items: center;
	font-size: 14px;
	color: #666;
}

.stats-info span {
	padding: 4px 8px;
	background: #f5f5f5;
	border-radius: 4px;
}

.pagination-wrapper {
	margin-top: 20px;
	text-align: right;
}

@media (max-width: 768px) {
	.pagination-wrapper {
		text-align: center;
	}
}
</style>
