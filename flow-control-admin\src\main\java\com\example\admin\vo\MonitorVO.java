package com.example.admin.vo;



import java.time.LocalDateTime;
import java.util.List;

/**
 * 监控数据VO
 */
public class MonitorVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 统计时间
     */
    private LocalDateTime statTime;
    
    /**
     * 资源名称
     */
    private String resourceName;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 租户名称
     */
    private String tenantName;
    
    /**
     * 总请求数
     */
    private Long totalRequests;
    
    /**
     * 通过请求数
     */
    private Long passRequests;
    
    /**
     * 阻塞请求数
     */
    private Long blockRequests;
    
    /**
     * 排队请求数
     */
    private Long queueRequests;
    
    /**
     * 总响应时间（毫秒）
     */
    private Long totalRt;
    
    /**
     * 平均响应时间（毫秒）
     */
    private Double avgRt;
    
    /**
     * 最大响应时间（毫秒）
     */
    private Integer maxRt;
    
    /**
     * 最小响应时间（毫秒）
     */
    private Integer minRt;
    
    /**
     * 统计类型：HOURLY-小时统计，DAILY-日统计
     */
    private String statType;
    
    /**
     * 统计类型名称
     */
    private String statTypeName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getStatTime() {
        return statTime;
    }

    public void setStatTime(LocalDateTime statTime) {
        this.statTime = statTime;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public Long getTotalRequests() {
        return totalRequests;
    }

    public void setTotalRequests(Long totalRequests) {
        this.totalRequests = totalRequests;
    }

    public Long getPassRequests() {
        return passRequests;
    }

    public void setPassRequests(Long passRequests) {
        this.passRequests = passRequests;
    }

    public Long getBlockRequests() {
        return blockRequests;
    }

    public void setBlockRequests(Long blockRequests) {
        this.blockRequests = blockRequests;
    }

    public Long getQueueRequests() {
        return queueRequests;
    }

    public void setQueueRequests(Long queueRequests) {
        this.queueRequests = queueRequests;
    }

    public Long getTotalRt() {
        return totalRt;
    }

    public void setTotalRt(Long totalRt) {
        this.totalRt = totalRt;
    }

    public Double getAvgRt() {
        return avgRt;
    }

    public void setAvgRt(Double avgRt) {
        this.avgRt = avgRt;
    }

    public Integer getMaxRt() {
        return maxRt;
    }

    public void setMaxRt(Integer maxRt) {
        this.maxRt = maxRt;
    }

    public Integer getMinRt() {
        return minRt;
    }

    public void setMinRt(Integer minRt) {
        this.minRt = minRt;
    }

    public String getStatType() {
        return statType;
    }

    public void setStatType(String statType) {
        this.statType = statType;
    }

    public String getStatTypeName() {
        return statTypeName;
    }

    public void setStatTypeName(String statTypeName) {
        this.statTypeName = statTypeName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    /**
     * 计算通过率
     */
    public Double getPassRate() {
        if (totalRequests == null || totalRequests == 0) {
            return 0.0;
        }
        return Math.round((double) passRequests / totalRequests * 10000.0) / 100.0;
    }
    
    /**
     * 计算阻塞率
     */
    public Double getBlockRate() {
        if (totalRequests == null || totalRequests == 0) {
            return 0.0;
        }
        return Math.round((double) blockRequests / totalRequests * 10000.0) / 100.0;
    }
    
    /**
     * 计算排队率
     */
    public Double getQueueRate() {
        if (totalRequests == null || totalRequests == 0) {
            return 0.0;
        }
        return Math.round((double) queueRequests / totalRequests * 10000.0) / 100.0;
    }
    
    /**
     * 获取QPS（每秒请求数）
     */
    public Double getQps() {
        if ("HOURLY".equals(statType)) {
            return totalRequests / 3600.0; // 1小时 = 3600秒
        } else if ("DAILY".equals(statType)) {
            return totalRequests / 86400.0; // 1天 = 86400秒
        }
        return totalRequests.doubleValue();
    }
    
    /**
     * 获取监控摘要
     */
    public String getMonitorSummary() {
        return String.format("总请求: %d, 通过: %d(%.2f%%), 阻塞: %d(%.2f%%), 平均RT: %.2fms",
                totalRequests, passRequests, getPassRate(), blockRequests, getBlockRate(), avgRt);
    }
    
    /**
     * 监控趋势数据
     */
    public static class TrendData {
        /**
         * 时间点
         */
        private LocalDateTime time;
        
        /**
         * QPS值
         */
        private Double qps;
        
        /**
         * 平均响应时间
         */
        private Double avgRt;
        
        /**
         * 通过率
         */
        private Double passRate;
        
        /**
         * 阻塞率
         */
        private Double blockRate;
    }
    
    /**
     * 实时监控数据
     */
    public static class RealtimeData {
        /**
         * 当前QPS
         */
        private Double currentQps;
        
        /**
         * 当前响应时间
         */
        private Double currentRt;
        
        /**
         * 当前通过率
         */
        private Double currentPassRate;
        
        /**
         * 趋势数据列表
         */
        private List<TrendData> trendData;
        
        /**
         * 最后更新时间
         */
        private LocalDateTime lastUpdateTime;
    }
}