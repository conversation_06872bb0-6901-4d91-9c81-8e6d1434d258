package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.entity.FlowRule;
import com.example.admin.vo.FlowRuleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流量规则Mapper接口
 */
@Mapper
public interface FlowRuleMapper extends BaseMapper<FlowRule> {
    
    /**
     * 分页查询流量规则（带租户名称）
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @param status 规则状态
     * @return 流量规则VO列表
     */
    Page<FlowRuleVO> selectFlowRuleVOPage(Page<FlowRuleVO> page, 
                                          @Param("tenantId") String tenantId,
                                          @Param("resourceName") String resourceName,
                                          @Param("status") Integer status);
    
    /**
     * 根据租户ID查询流量规则
     *
     * @param tenantId 租户ID
     * @return 流量规则列表
     */
    List<FlowRule> selectByTenantId(@Param("tenantId") String tenantId);
    
    /**
     * 根据资源名称查询流量规则
     *
     * @param resourceName 资源名称
     * @return 流量规则列表
     */
    List<FlowRule> selectByResourceName(@Param("resourceName") String resourceName);
    
    /**
     * 根据租户ID和资源名称查询流量规则
     *
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @return 流量规则列表
     */
    List<FlowRule> selectByTenantIdAndResourceName(@Param("tenantId") String tenantId,
                                                   @Param("resourceName") String resourceName);
    
    /**
     * 根据规则名称查询流量规则（用于重名检查）
     *
     * @param ruleName 规则名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 流量规则
     */
    FlowRule selectByRuleName(@Param("ruleName") String ruleName, 
                              @Param("excludeId") Long excludeId);
    
    /**
     * 批量更新规则状态
     *
     * @param ids 规则ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, 
                          @Param("status") Integer status,
                          @Param("updateBy") String updateBy);
    
    /**
     * 根据租户ID统计规则数量
     *
     * @param tenantId 租户ID
     * @return 规则数量
     */
    int countByTenantId(@Param("tenantId") String tenantId);
    
    /**
     * 根据租户ID统计启用的规则数量
     *
     * @param tenantId 租户ID
     * @return 启用的规则数量
     */
    int countEnabledByTenantId(@Param("tenantId") String tenantId);
    
    /**
     * 查询所有启用的流量规则
     *
     * @return 启用的流量规则列表
     */
    List<FlowRule> selectAllEnabled();
    
    /**
     * 根据优先级排序查询流量规则
     *
     * @param tenantId 租户ID
     * @param resourceName 资源名称
     * @return 按优先级排序的流量规则列表
     */
    List<FlowRule> selectByPriorityOrder(@Param("tenantId") String tenantId,
                                         @Param("resourceName") String resourceName);
    
    /**
     * 查询即将过期的规则（如果有过期时间字段的话）
     *
     * @param hours 小时数
     * @return 即将过期的规则列表
     */
    List<FlowRule> selectExpiringSoon(@Param("hours") int hours);
    
    /**
     * 统计各种状态的规则数量
     *
     * @return 状态统计结果
     */
    List<java.util.Map<String, Object>> selectStatusStatistics();
    
    /**
     * 统计各租户的规则数量
     *
     * @return 租户规则统计结果
     */
    List<java.util.Map<String, Object>> selectTenantRuleStatistics();
}