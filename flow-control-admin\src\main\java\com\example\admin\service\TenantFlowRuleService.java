package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.TenantFlowRuleDTO;
import com.example.common.entity.TenantFlowRule;
import com.example.admin.vo.TenantFlowRuleVO;

import java.util.List;
import java.util.Map;

/**
 * 租户流量规则服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface TenantFlowRuleService extends IService<TenantFlowRule> {
    
    /**
     * 分页查询租户流量规则
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param ruleName 规则名称
     * @param enabled 启用状态
     * @param controlBehavior 流控效果
     * @return 租户流量规则VO分页结果
     */
    Page<TenantFlowRuleVO> selectTenantFlowRulePage(Page<TenantFlowRuleVO> page, String tenantId, String ruleName, 
                                           Integer enabled, Integer controlBehavior);
    
    /**
     * 根据ID查询租户流量规则详情
     *
     * @param id 规则ID
     * @return 租户流量规则VO
     */
    TenantFlowRuleVO getTenantFlowRuleById(Long id);
    
    /**
     * 创建租户流量规则
     *
     * @param tenantFlowRuleDTO 租户流量规则DTO
     * @return 是否成功
     */
    boolean createTenantFlowRule(TenantFlowRuleDTO tenantFlowRuleDTO);
    
    /**
     * 更新租户流量规则
     *
     * @param id 规则ID
     * @param tenantFlowRuleDTO 租户流量规则DTO
     * @return 是否成功
     */
    boolean updateTenantFlowRule(Long id, TenantFlowRuleDTO tenantFlowRuleDTO);
    
    /**
     * 删除租户流量规则
     *
     * @param id 规则ID
     * @return 是否成功
     */
    boolean deleteTenantFlowRule(Long id);
    
    /**
     * 批量删除租户流量规则
     *
     * @param ids 规则ID列表
     * @return 是否成功
     */
    boolean batchDeleteTenantFlowRules(List<Long> ids);
    
    /**
     * 启用租户流量规则
     *
     * @param id 规则ID
     * @return 是否成功
     */
    boolean enableTenantFlowRule(Long id);
    
    /**
     * 禁用租户流量规则
     *
     * @param id 规则ID
     * @return 是否成功
     */
    boolean disableTenantFlowRule(Long id);
    
    /**
     * 批量更新流量规则状态
     *
     * @param ids 规则ID列表
     * @param enabled 启用状态
     * @return 是否成功
     */
    boolean batchUpdateEnabled(List<Long> ids, Integer enabled);
    
    /**
     * 根据租户ID查询租户流量规则
     *
     * @param tenantId 租户ID
     * @param enabled 启用状态（可选）
     * @param limit 限制数量
     * @return 租户流量规则列表
     */
    List<TenantFlowRuleVO> getTenantFlowRulesByTenantId(String tenantId, Integer enabled, Integer limit);
    
    // 租户限流为全局生效，移除资源匹配模式相关方法
    // List<TenantFlowRuleVO> getTenantFlowRulesByResourcePattern(String resourcePattern, String tenantId, Integer enabled, Integer limit);
    
    /**
     * 检查流量规则名称是否存在
     *
     * @param tenantId 租户ID
     * @param ruleName 规则名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByRuleName(String tenantId, String ruleName, Long excludeId);
    
    /**
     * 统计租户流量规则数量
     *
     * @param tenantId 租户ID
     * @param enabled 启用状态（可选）
     * @return 规则数量
     */
    int countByTenantId(String tenantId, Integer enabled);
    
    /**
     * 查询启用的流量规则（按优先级排序）
     *
     * @param tenantId 租户ID（可选）
     * @param limit 限制数量
     * @return 启用的流量规则列表
     */
    List<TenantFlowRuleVO> getEnabledRules(String tenantId, Integer limit);
    
    /**
     * 查询优先级排序的流量规则
     *
     * @param tenantId 租户ID（可选）
     * @param enabled 启用状态（可选）
     * @param limit 限制数量
     * @return 优先级排序的流量规则列表
     */
    List<TenantFlowRuleVO> getRulesByPriorityOrder(String tenantId, Integer enabled, Integer limit);
    
    /**
     * 统计流量规则状态分布
     *
     * @param tenantId 租户ID（可选）
     * @return 状态统计结果
     */
    List<Map<String, Object>> getEnabledStatistics(String tenantId);
    
    /**
     * 统计租户流量规则分布
     *
     * @param limit 限制数量
     * @return 租户流量规则统计结果
     */
    List<Map<String, Object>> getTenantFlowRuleStatistics(Integer limit);
    
    /**
     * 统计流控行为分布
     *
     * @param tenantId 租户ID（可选）
     * @return 流控行为统计结果
     */
    List<Map<String, Object>> getFlowBehaviorStatistics(String tenantId);
    
    /**
     * 批量创建租户流量规则
     *
     * @param tenantFlowRuleDTOList 租户流量规则DTO列表
     * @return 是否成功
     */
    boolean batchCreateTenantFlowRules(List<TenantFlowRuleDTO> tenantFlowRuleDTOList);
    
    /**
     * 复制租户流量规则
     *
     * @param id 原规则ID
     * @param newRuleName 新规则名称
     * @param targetTenantId 目标租户ID（可选，为空则复制到同一租户）
     * @return 是否成功
     */
    boolean copyTenantFlowRule(Long id, String newRuleName, String targetTenantId);
    
    /**
     * 导入租户流量规则
     *
     * @param tenantFlowRuleDTOList 租户流量规则DTO列表
     * @param overwrite 是否覆盖已存在的规则
     * @return 导入结果
     */
    Map<String, Object> importTenantFlowRules(List<TenantFlowRuleDTO> tenantFlowRuleDTOList, boolean overwrite);
    
    /**
     * 导出租户流量规则
     *
     * @param tenantId 租户ID（可选）
     * @param enabled 启用状态（可选）
     * @return 租户流量规则DTO列表
     */
    List<TenantFlowRuleDTO> exportTenantFlowRules(String tenantId, Integer enabled);
    
    /**
     * 验证租户流量规则配置
     *
     * @param tenantFlowRuleDTO 租户流量规则DTO
     * @return 验证结果
     */
    Map<String, Object> validateTenantFlowRule(TenantFlowRuleDTO tenantFlowRuleDTO);
    
    /**
     * 获取租户流量规则的最大优先级
     *
     * @param tenantId 租户ID
     * @return 最大优先级
     */
    Integer getMaxPriority(String tenantId);
    
    /**
     * 按优先级范围查询流量规则
     *
     * @param tenantId 租户ID
     * @param minPriority 最小优先级
     * @param maxPriority 最大优先级
     * @param enabled 启用状态（可选）
     * @return 规则列表
     */
    List<TenantFlowRuleVO> getRulesByPriorityRange(String tenantId, Integer minPriority, Integer maxPriority, Integer enabled);
    
    /**
     * 查询即将过期的流量规则
     *
     * @param tenantId 租户ID（可选）
     * @param hours 提前小时数
     * @return 即将过期的流量规则列表
     */
    List<TenantFlowRuleVO> getExpiringRules(String tenantId, Integer hours);
    
    /**
     * 自动禁用过期流量规则
     *
     * @return 禁用的规则数量
     */
    int disableExpiredRules();
    
    /**
     * 获取有效流量规则（当前时间在生效时间范围内且启用的规则）
     *
     * @param tenantId 租户ID
     * @return 有效流量规则列表
     */
    List<TenantFlowRuleVO> getValidRules(String tenantId);
    
    /**
     * 批量复制规则到其他租户
     *
     * @param sourceIds 源规则ID列表
     * @param targetTenantId 目标租户ID
     * @param namePrefix 新规则名称前缀
     * @return 复制结果
     */
    Map<String, Object> batchCopyToTenant(List<Long> sourceIds, String targetTenantId, String namePrefix);
    
    /**
     * 获取租户流量规则汇总统计
     *
     * @param tenantId 租户ID
     * @return 汇总统计结果
     */
    Map<String, Object> getTenantFlowRuleSummary(String tenantId);
    
    /**
     * 获取全局流量规则汇总统计
     *
     * @return 全局汇总统计结果
     */
    Map<String, Object> getGlobalFlowRuleSummary();
}