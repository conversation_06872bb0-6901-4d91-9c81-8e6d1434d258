package com.example.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 租户流量规则实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@TableName("tenant_flow_rules")
public class TenantFlowRule {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;



    /**
     * 限流阈值类型：0-线程数，1-QPS
     */
    @TableField("grade")
    private Integer grade;

    /**
     * 限流阈值
     */
    @TableField("count")
    private Double count;

    /**
     * 流控针对的调用来源策略：0-直接，1-关联，2-链路
     */
    @TableField("strategy")
    private Integer strategy;

    /**
     * 关联资源
     */
    @TableField("ref_resource")
    private String refResource;

    /**
     * 流控效果：0-快速失败，1-Warm Up，2-排队等待，3-Warm Up + 排队等待
     */
    @TableField("control_behavior")
    private Integer controlBehavior;

    /**
     * Warm Up预热时间（秒）
     */
    @TableField("warm_up_period_sec")
    private Integer warmUpPeriodSec;

    /**
     * 排队等待超时时间（毫秒）
     */
    @TableField("max_queueing_time_ms")
    private Integer maxQueueingTimeMs;

    /**
     * 是否集群模式
     */
    @TableField("cluster_mode")
    private Boolean clusterMode;

    /**
     * 集群配置信息
     */
    @TableField("cluster_config")
    private String clusterConfig;

    /**
     * 优先级，数值越大优先级越高
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 是否启用：0-禁用，1-启用
     */
    @TableField("enabled")
    private Integer enabled;

    /**
     * 生效开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 规则描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    // Constructors
    public TenantFlowRule() {}

    public TenantFlowRule(String tenantId, String ruleName, Integer grade, Double count) {
        this.tenantId = tenantId;
        this.ruleName = ruleName;
        this.grade = grade;
        this.count = count;
        this.strategy = 0;
        this.controlBehavior = 0;
        this.warmUpPeriodSec = 10;
        this.maxQueueingTimeMs = 500;
        this.clusterMode = false;
        this.priority = 1;
        this.enabled = 1;
        this.deleted = 0;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }



    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public Double getCount() {
        return count;
    }

    public void setCount(Double count) {
        this.count = count;
    }

    public Integer getStrategy() {
        return strategy;
    }

    public void setStrategy(Integer strategy) {
        this.strategy = strategy;
    }

    public String getRefResource() {
        return refResource;
    }

    public void setRefResource(String refResource) {
        this.refResource = refResource;
    }

    public Integer getControlBehavior() {
        return controlBehavior;
    }

    public void setControlBehavior(Integer controlBehavior) {
        this.controlBehavior = controlBehavior;
    }

    public Integer getWarmUpPeriodSec() {
        return warmUpPeriodSec;
    }

    public void setWarmUpPeriodSec(Integer warmUpPeriodSec) {
        this.warmUpPeriodSec = warmUpPeriodSec;
    }

    public Integer getMaxQueueingTimeMs() {
        return maxQueueingTimeMs;
    }

    public void setMaxQueueingTimeMs(Integer maxQueueingTimeMs) {
        this.maxQueueingTimeMs = maxQueueingTimeMs;
    }

    public Boolean getClusterMode() {
        return clusterMode;
    }

    public void setClusterMode(Boolean clusterMode) {
        this.clusterMode = clusterMode;
    }

    public String getClusterConfig() {
        return clusterConfig;
    }

    public void setClusterConfig(String clusterConfig) {
        this.clusterConfig = clusterConfig;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TenantFlowRule that = (TenantFlowRule) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(tenantId, that.tenantId) &&
                Objects.equals(ruleName, that.ruleName) &&
                Objects.equals(grade, that.grade) &&
                Objects.equals(count, that.count) &&
                Objects.equals(strategy, that.strategy) &&
                Objects.equals(refResource, that.refResource) &&
                Objects.equals(controlBehavior, that.controlBehavior) &&
                Objects.equals(warmUpPeriodSec, that.warmUpPeriodSec) &&
                Objects.equals(maxQueueingTimeMs, that.maxQueueingTimeMs) &&
                Objects.equals(clusterMode, that.clusterMode) &&
                Objects.equals(clusterConfig, that.clusterConfig) &&
                Objects.equals(priority, that.priority) &&
                Objects.equals(enabled, that.enabled) &&
                Objects.equals(startTime, that.startTime) &&
                Objects.equals(endTime, that.endTime) &&
                Objects.equals(description, that.description) &&
                Objects.equals(createTime, that.createTime) &&
                Objects.equals(updateTime, that.updateTime) &&
                Objects.equals(createBy, that.createBy) &&
                Objects.equals(updateBy, that.updateBy) &&
                Objects.equals(deleted, that.deleted);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, tenantId, ruleName, grade, count,
                strategy, refResource, controlBehavior, warmUpPeriodSec, maxQueueingTimeMs,
                clusterMode, clusterConfig, priority, enabled, startTime, endTime, description,
                createTime, updateTime, createBy, updateBy, deleted);
    }

    @Override
    public String toString() {
        return "TenantFlowRule{"
                + "id=" + id +
                ", tenantId='" + tenantId + "'"
                + ", ruleName='" + ruleName + "'"
                + ", grade=" + grade +
                ", count=" + count +
                ", strategy=" + strategy +
                ", refResource='" + refResource + "'"
                + ", controlBehavior=" + controlBehavior +
                ", warmUpPeriodSec=" + warmUpPeriodSec +
                ", maxQueueingTimeMs=" + maxQueueingTimeMs +
                ", clusterMode=" + clusterMode +
                ", clusterConfig='" + clusterConfig + "'"
                + ", priority=" + priority +
                ", enabled=" + enabled +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", description='" + description + "'"
                + ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + "'"
                + ", updateBy='" + updateBy + "'"
                + ", deleted=" + deleted +
                '}';
    }
}
