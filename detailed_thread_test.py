#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的线程限流测试脚本
测试所有租户的线程限流功能，并提供详细的分析报告
"""

import requests
import threading
import time
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from collections import defaultdict
import json
from datetime import datetime

class DetailedThreadFlowTester:
    def __init__(self, base_url, timeout=5):
        self.base_url = base_url
        self.timeout = timeout
        self.results = defaultdict(list)
        self.lock = threading.Lock()
        
    def make_request(self, tenant_id, thread_id, request_id):
        """发送单个请求"""
        headers = {
            'X-Tenant-Id': tenant_id,
            'User-Agent': f'ThreadTest-{thread_id}-{request_id}'
        }
        
        start_time = time.time()
        try:
            response = requests.get(self.base_url, headers=headers, timeout=self.timeout)
            end_time = time.time()
            
            result = {
                'tenant_id': tenant_id,
                'thread_id': thread_id,
                'request_id': request_id,
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'success': response.status_code == 200,
                'blocked': response.status_code == 429,
                'timestamp': datetime.now().isoformat(),
                'response_headers': dict(response.headers),
                'response_body': response.text[:200] if response.text else ''
            }
            
            # 检查是否是限流响应
            if response.status_code == 429:
                try:
                    response_data = response.json()
                    result['limit_type'] = response_data.get('limitType', 'UNKNOWN')
                    result['limit_message'] = response_data.get('message', '')
                except:
                    result['limit_type'] = 'PARSE_ERROR'
                    result['limit_message'] = response.text[:100]
            
            return result
            
        except requests.exceptions.Timeout:
            end_time = time.time()
            return {
                'tenant_id': tenant_id,
                'thread_id': thread_id,
                'request_id': request_id,
                'status_code': 0,
                'response_time': end_time - start_time,
                'success': False,
                'blocked': False,
                'error': 'TIMEOUT',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            end_time = time.time()
            return {
                'tenant_id': tenant_id,
                'thread_id': thread_id,
                'request_id': request_id,
                'status_code': 0,
                'response_time': end_time - start_time,
                'success': False,
                'blocked': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def test_tenant_thread_limit(self, tenant_id, concurrent_threads, test_duration, requests_per_thread=50):
        """测试单个租户的线程限流"""
        print(f"\n=== 测试租户 {tenant_id} 的线程限流 ===")
        print(f"并发线程数: {concurrent_threads}, 测试时长: {test_duration}秒, 每线程请求数: {requests_per_thread}")
        
        tenant_results = []
        
        def worker_thread(thread_id):
            """工作线程函数"""
            thread_results = []
            start_time = time.time()
            request_count = 0
            
            while time.time() - start_time < test_duration and request_count < requests_per_thread:
                result = self.make_request(tenant_id, thread_id, request_count)
                thread_results.append(result)
                request_count += 1
                
                # 短暂延迟，模拟真实请求间隔
                time.sleep(0.01)
            
            with self.lock:
                tenant_results.extend(thread_results)
            
            return thread_results
        
        # 启动并发线程
        with ThreadPoolExecutor(max_workers=concurrent_threads) as executor:
            futures = [executor.submit(worker_thread, i) for i in range(concurrent_threads)]
            
            # 等待所有线程完成
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"线程执行异常: {e}")
        
        return self.analyze_tenant_results(tenant_id, tenant_results, concurrent_threads)
    
    def analyze_tenant_results(self, tenant_id, results, concurrent_threads):
        """分析单个租户的测试结果"""
        if not results:
            return {
                'tenant_id': tenant_id,
                'total_requests': 0,
                'success_count': 0,
                'blocked_count': 0,
                'error_count': 0,
                'success_rate': 0,
                'block_rate': 0,
                'passed': False,
                'message': '没有请求结果'
            }
        
        total_requests = len(results)
        success_count = sum(1 for r in results if r['success'])
        blocked_count = sum(1 for r in results if r.get('blocked', False))
        error_count = sum(1 for r in results if r.get('error'))
        
        success_rate = (success_count / total_requests) * 100
        block_rate = (blocked_count / total_requests) * 100
        
        # 分析限流类型
        limit_types = defaultdict(int)
        for r in results:
            if r.get('blocked') and r.get('limit_type'):
                limit_types[r['limit_type']] += 1
        
        # 分析响应时间
        response_times = [r['response_time'] for r in results if 'response_time' in r]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # 线程限流验证逻辑：
        # 1. 并发线程数 > 5 时，应该有显著的限流
        # 2. 期望限流率应该 > 10%（因为线程限制为5，而我们用了更多并发）
        expected_block_rate = max(0, (concurrent_threads - 5) / concurrent_threads * 100)
        passed = block_rate >= expected_block_rate * 0.5  # 允许50%的误差
        
        result = {
            'tenant_id': tenant_id,
            'concurrent_threads': concurrent_threads,
            'total_requests': total_requests,
            'success_count': success_count,
            'blocked_count': blocked_count,
            'error_count': error_count,
            'success_rate': round(success_rate, 2),
            'block_rate': round(block_rate, 2),
            'expected_block_rate': round(expected_block_rate, 2),
            'avg_response_time': round(avg_response_time, 3),
            'limit_types': dict(limit_types),
            'passed': passed,
            'message': f'线程限流测试{"通过" if passed else "失败"} - 实际限流率: {block_rate:.2f}%, 期望限流率: {expected_block_rate:.2f}%'
        }
        
        return result
    
    def run_comprehensive_test(self, tenants, concurrent_threads, test_duration):
        """运行综合测试"""
        print(f"\n开始详细线程限流测试...")
        print(f"目标URL: {self.base_url}")
        print(f"测试租户: {tenants}")
        print(f"并发线程数: {concurrent_threads}")
        print(f"测试时长: {test_duration}秒")
        print(f"预期行为: 每个租户最多允许5个并发线程，超出部分应被限流")
        
        all_results = []
        
        for tenant_id in tenants:
            result = self.test_tenant_thread_limit(tenant_id, concurrent_threads, test_duration)
            all_results.append(result)
            
            # 打印详细结果
            print(f"\n租户 {tenant_id} 测试结果:")
            print(f"  总请求数: {result['total_requests']}")
            print(f"  成功请求: {result['success_count']} ({result['success_rate']:.2f}%)")
            print(f"  被限流请求: {result['blocked_count']} ({result['block_rate']:.2f}%)")
            print(f"  错误请求: {result['error_count']}")
            print(f"  平均响应时间: {result['avg_response_time']:.3f}秒")
            print(f"  限流类型统计: {result['limit_types']}")
            print(f"  测试结果: {result['message']}")
            
            # 租户间测试间隔
            time.sleep(1)
        
        # 生成总结报告
        self.generate_summary_report(all_results, concurrent_threads)
        
        return all_results
    
    def generate_summary_report(self, results, concurrent_threads):
        """生成总结报告"""
        print(f"\n{'='*60}")
        print(f"详细线程限流测试总结报告")
        print(f"{'='*60}")
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r['passed'])
        pass_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"测试配置:")
        print(f"  并发线程数: {concurrent_threads}")
        print(f"  线程限制: 5 (每租户)")
        print(f"  测试租户数: {total_tests}")
        
        print(f"\n总体结果:")
        print(f"  通过测试: {passed_tests}/{total_tests}")
        print(f"  通过率: {pass_rate:.1f}%")
        
        print(f"\n各租户详细结果:")
        for result in results:
            status = "✓" if result['passed'] else "✗"
            print(f"  {status} {result['tenant_id']}: 限流率 {result['block_rate']:.2f}% (期望 ≥{result['expected_block_rate']:.2f}%)")
        
        if pass_rate < 100:
            print(f"\n问题分析:")
            print(f"  - 线程限流可能未正确配置或实现")
            print(f"  - 检查Sentinel规则是否正确加载 (grade=0表示线程限流)")
            print(f"  - 检查Entry的创建和释放逻辑")
            print(f"  - 验证资源名称是否正确匹配")
        else:
            print(f"\n✓ 所有线程限流测试通过！")

def main():
    parser = argparse.ArgumentParser(description='详细线程限流测试工具')
    parser.add_argument('--url', default='http://localhost:8088/api/test', help='测试URL')
    parser.add_argument('--concurrent', type=int, default=10, help='并发线程数')
    parser.add_argument('--test-duration', type=int, default=10, help='测试持续时间（秒）')
    parser.add_argument('--tenants', nargs='+', default=['tenant1', 'tenant2', 'tenant3', 'tenant4', 'tenant5'], help='要测试的租户列表')
    
    args = parser.parse_args()
    
    tester = DetailedThreadFlowTester(args.url)
    results = tester.run_comprehensive_test(args.tenants, args.concurrent, args.test_duration)
    
    # 保存详细结果到文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'thread_flow_test_results_{timestamp}.json'
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细测试结果已保存到: {filename}")

if __name__ == '__main__':
    main()