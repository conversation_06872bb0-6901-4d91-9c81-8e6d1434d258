package com.example.gateway.service;

import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.example.common.constant.FlowControlConstants;
import com.example.common.entity.FlowRuleEntity;
import com.example.common.entity.TenantRuleEntity;
import com.example.gateway.mapper.FlowRuleMapper;
import com.example.gateway.mapper.TenantRuleMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据库规则服务 负责从数据库加载规则并转换为Sentinel规则
 */
@Slf4j
@Service
public class DatabaseRuleService {

	@Autowired
	private FlowRuleMapper flowRuleMapper;

	@Autowired
	private TenantRuleMapper tenantRuleMapper;




	/**
	 * 从数据库加载所有启用的流量规则
	 */
	public List<FlowRule> loadFlowRulesFromDatabase() {
		try {
			List<FlowRule> allRules = new ArrayList<>();

			// 加载传统流量规则
			List<FlowRuleEntity> flowEntities = flowRuleMapper.selectEnabledRules();
			List<FlowRule> flowRules = convertToFlowRules(flowEntities);
			allRules.addAll(flowRules);

			// 加载租户规则
			List<TenantRuleEntity> tenantEntities = tenantRuleMapper.selectEnabledRules();
			log.debug("Loaded {} tenant rule entities from database", tenantEntities.size());
			for (TenantRuleEntity entity : tenantEntities) {
				log.debug("Tenant rule entity: id={}, ruleName={}, tenantId={}, grade={}, count={}", 
						entity.getId(), entity.getRuleName(), entity.getTenantId(), entity.getGrade(), entity.getCount());
			}
			List<FlowRule> tenantRules = convertTenantRulesToFlowRules(tenantEntities);
			log.debug("Converted {} tenant rules to Sentinel FlowRules", tenantRules.size());
			allRules.addAll(tenantRules);

			log.info("Loaded {} flow rules and {} tenant rules from database", 
					flowRules.size(), tenantRules.size());
			return allRules;
		} catch (Exception e) {
			log.error("Failed to load flow rules from database", e);
			return new ArrayList<>();
		}
	}

	/**
	 * 根据租户ID从数据库加载流量规则
	 */
	public List<FlowRule> loadFlowRulesByTenant(String tenantId) {
		try {
			List<FlowRuleEntity> entities = flowRuleMapper.selectEnabledRulesByTenant(tenantId);
			List<FlowRule> rules = convertToFlowRules(entities);
			log.info("Loaded {} flow rules for tenant {} from database", rules.size(), tenantId);
			return rules;
		} catch (Exception e) {
			log.error("Failed to load flow rules for tenant {} from database", tenantId, e);
			return new ArrayList<>();
		}
	}



	/**
	 * 将数据库实体转换为Sentinel流量规则
	 */
	private List<FlowRule> convertToFlowRules(List<FlowRuleEntity> entities) {
		if (CollectionUtils.isEmpty(entities)) {
			return new ArrayList<>();
		}

		return entities.stream().map(this::convertToFlowRule).collect(Collectors.toList());
	}

	/**
	 * 将单个数据库实体转换为Sentinel流量规则
	 */
	private FlowRule convertToFlowRule(FlowRuleEntity entity) {
		FlowRule rule = new FlowRule();

		// 构建正确的资源标识
		String resourceName = buildResourceName(entity);
		rule.setResource(resourceName);

		// 限流模式：0-线程数，1-QPS，直接使用limitMode的值
		int sentinelGrade = entity.getLimitMode();

		log.debug("Converting rule: {} -> resource: {}, threshold: {}, limitMode: {} -> grade: {}",
				entity.getRuleName(), resourceName, entity.getThreshold(), entity.getLimitMode(), sentinelGrade);

		rule.setCount(entity.getThreshold().doubleValue());
		rule.setGrade(sentinelGrade);

		rule.setLimitApp("default"); // 默认来源应用
		rule.setStrategy(entity.getStrategy() != null ? entity.getStrategy() : 0);
		rule.setControlBehavior(entity.getBehavior() != null ? entity.getBehavior() : 0);

		// 预热时长
		if (entity.getWarmUpPeriod() != null && entity.getWarmUpPeriod() > 0) {
			rule.setWarmUpPeriodSec(entity.getWarmUpPeriod());
		}

		// 排队等待超时时间
		if (entity.getQueueTimeout() != null && entity.getQueueTimeout() > 0) {
			rule.setMaxQueueingTimeMs(entity.getQueueTimeout());
		}

		// 集群模式暂不支持，使用单机模式
		rule.setClusterMode(false);

		return rule;
	}

	/**
	 * 根据规则类型构建正确的资源标识
	 */
	private String buildResourceName(FlowRuleEntity entity) {
		String tenantId = entity.getTenantId();
		String resourceName = entity.getResourceName();

		// 如果是租户相关的规则，需要构建租户资源标识
		if (tenantId != null && !tenantId.equals("default")) {
			// 检查是否是总QPS限制
			if ("tenant:total".equals(resourceName) || resourceName.contains(":total")) {
				return FlowControlConstants.ResourcePrefix.TENANT + tenantId + ":total";
			}
			// 否则是接口级限制
			else {
				return FlowControlConstants.ResourcePrefix.TENANT + tenantId + ":" + resourceName;
			}
		}

		// 默认规则直接使用资源名称
		return resourceName;
	}



	/**
	 * 将租户规则转换为Sentinel流量规则
	 */
	private List<FlowRule> convertTenantRulesToFlowRules(List<TenantRuleEntity> tenantEntities) {
		if (CollectionUtils.isEmpty(tenantEntities)) {
			return new ArrayList<>();
		}

		return tenantEntities.stream().map(this::convertTenantRuleToFlowRule).collect(Collectors.toList());
	}

	/**
	 * 将单个租户规则转换为Sentinel流量规则
	 */
	private FlowRule convertTenantRuleToFlowRule(TenantRuleEntity entity) {
		FlowRule rule = new FlowRule();

		// 构建租户级别的资源标识
		String resourceName = buildTenantResourceName(entity);
		rule.setResource(resourceName);

		// 限流模式：0-线程数，1-QPS 不需要进行转换，直接使用数据库中的值
		// 添加null值检查，默认使用线程数模式（数据库值0）
		Integer limitMode = entity.getGrade();
		if (limitMode == null) {
			limitMode = 0; // 默认线程数模式
			log.warn("Tenant rule {} has null limitMode, using default thread mode (0)", entity.getRuleName());
		}
		int sentinelGrade = limitMode;

		log.debug("Converting tenant rule: {} -> resource: {}, threshold: {}, limitMode: {} -> grade: {}",
				entity.getRuleName(), resourceName, entity.getCount(), limitMode, sentinelGrade);
		
		// 设置限流阈值和等级
		// 添加threshold的null值检查
		Double threshold = entity.getCount();
		if (threshold == null) {
			threshold = 1.0; // 默认阈值
			log.warn("Tenant rule {} has null threshold, using default value 1.0", entity.getRuleName());
		}
		
		// 输出详细的转换信息
		log.debug("Tenant rule conversion details: tenantId={}, ruleName={}, refResource={}, finalResource={}, count={}, grade={}",
				entity.getTenantId(), entity.getRuleName(), entity.getRefResource(), resourceName, threshold, sentinelGrade);

		rule.setCount(threshold);
		rule.setGrade(sentinelGrade);

		// 设置默认来源应用
		rule.setLimitApp("default");
		
		// 设置流控策略
		rule.setStrategy(entity.getStrategy() != null ? entity.getStrategy() : 0);
		
		// 设置流控行为
		rule.setControlBehavior(entity.getControlBehavior() != null ? entity.getControlBehavior() : 0);

		// 预热时长设置
		if (entity.getWarmUpPeriodSec() != null && entity.getWarmUpPeriodSec() > 0) {
			rule.setWarmUpPeriodSec(entity.getWarmUpPeriodSec());
		}

		// 排队等待超时时间设置
		if (entity.getMaxQueueingTimeMs() != null && entity.getMaxQueueingTimeMs() > 0) {
			rule.setMaxQueueingTimeMs(entity.getMaxQueueingTimeMs());
		}

		// 集群模式暂不支持，使用单机模式
		rule.setClusterMode(false);

		return rule;
	}

	/**
	 * 构建租户级别的资源标识
	 * 支持两层限流架构：
	 * 1. 租户级别限流：tenant:<tenantId>
	 * 2. 租户+接口级别限流：tenant:<tenantId>:<resourceName>
	 */
	private String buildTenantResourceName(TenantRuleEntity entity) {
		String tenantId = entity.getTenantId();
		String resourceName = entity.getRefResource();

		// 如果租户ID为空或为默认值，直接使用资源名称
		if (tenantId == null || "default".equals(tenantId)) {
			return resourceName != null ? resourceName : "default";
		}

		// 如果没有指定具体资源或资源为空，则为租户级别限流
		if (resourceName == null || resourceName.trim().isEmpty() || "*".equals(resourceName)) {
			log.debug("Building tenant-level resource: tenant:{}", tenantId);
			return FlowControlConstants.ResourcePrefix.TENANT + tenantId;
		}
		// 否则为租户+接口级别限流
		else {
			log.debug("Building tenant-interface resource: tenant:{}:{}", tenantId, resourceName);
			return FlowControlConstants.ResourcePrefix.TENANT + tenantId + ":" + resourceName;
		}
	}


	/**
	 * 确保租户有默认规则
	 * 如果租户没有配置任何规则，则为其加载默认的租户级别限流规则
	 */
	public void ensureDefaultTenantRule(String tenantId) {
		try {
			// 检查租户是否已有规则
			List<TenantRuleEntity> tenantRules = tenantRuleMapper.selectEnabledRulesByTenant(tenantId);
            
            if (tenantRules.isEmpty()) {
				log.debug("No rules found for tenant {}, checking for default rule", tenantId);
				
				// 查找默认租户规则（tenantId为"default"的规则）
				List<TenantRuleEntity> defaultRules = tenantRuleMapper.selectEnabledRulesByTenant("default");
				
				if (!defaultRules.isEmpty()) {
					// 为当前租户创建基于默认规则的临时规则
					for (TenantRuleEntity defaultRule : defaultRules) {
						// 创建临时的租户规则并加载到Sentinel
						FlowRule tempRule = createTempTenantRule(tenantId, defaultRule);
						
						// 获取当前所有规则
						List<FlowRule> currentRules = new ArrayList<>(com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.getRules());
						
						// 检查是否已存在相同资源的规则
						boolean ruleExists = currentRules.stream()
								.anyMatch(rule -> rule.getResource().equals(tempRule.getResource()));
						
						if (!ruleExists) {
							currentRules.add(tempRule);
							com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.loadRules(currentRules);
							log.info("Applied default rule for tenant {}: resource={}, count={}, grade={}", 
									tenantId, tempRule.getResource(), tempRule.getCount(), tempRule.getGrade());
						}
					}
				} else {
					log.warn("No default tenant rules found, tenant {} will have no flow control", tenantId);
				}
			}
		} catch (Exception e) {
			log.error("Failed to ensure default tenant rule for tenant {}", tenantId, e);
		}
	}

	/**
	 * 创建临时租户规则
	 */
	private FlowRule createTempTenantRule(String tenantId, TenantRuleEntity defaultRule) {
		FlowRule rule = new FlowRule();
		
		// 构建租户级别的资源标识
		String resourceName = FlowControlConstants.ResourcePrefix.TENANT + tenantId;
		rule.setResource(resourceName);
		
		// 使用默认规则的配置
		rule.setCount(defaultRule.getCount() != null ? defaultRule.getCount() : 10.0);
		rule.setGrade(defaultRule.getGrade() != null ? defaultRule.getGrade() : 0); // 默认线程数模式
		rule.setLimitApp("default");
		rule.setStrategy(defaultRule.getStrategy() != null ? defaultRule.getStrategy() : 0);
		rule.setControlBehavior(defaultRule.getControlBehavior() != null ? defaultRule.getControlBehavior() : 0);
		
		if (defaultRule.getWarmUpPeriodSec() != null && defaultRule.getWarmUpPeriodSec() > 0) {
			rule.setWarmUpPeriodSec(defaultRule.getWarmUpPeriodSec());
		}
		
		if (defaultRule.getMaxQueueingTimeMs() != null && defaultRule.getMaxQueueingTimeMs() > 0) {
			rule.setMaxQueueingTimeMs(defaultRule.getMaxQueueingTimeMs());
		}
		
		rule.setClusterMode(false);
		
		return rule;
	}

	/**
	 * 刷新规则缓存
	 */
	public void refreshRules() {
		try {
			// 重新加载流量规则
			List<FlowRule> flowRules = loadFlowRulesFromDatabase();
			com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager.loadRules(flowRules);

			log.info("Rules refreshed successfully. Flow rules: {}", flowRules.size());
		} catch (Exception e) {
			log.error("Failed to refresh rules", e);
		}
	}
}
